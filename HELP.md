# Getting Started

### Reference Documentation

For further reference, please consider the following sections:

* [Official Apache Maven documentation](https://maven.apache.org/guides/index.html)
* [Spring Boot Maven Plugin Reference Guide](https://docs.spring.io/spring-boot/docs/2.1.6.RELEASE/maven-plugin/)
* [自定义集群限流](https://docs.xiaohongshu.com/doc/ef4369a565ddabfc5ff6e0dc0587ba43)
* [自定义熔断降级](https://docs.xiaohongshu.com/doc/ff8bc0f08636e4f21c14cf579a55c3d8)
* [RedSchedule定时任务](https://docs.xiaohongshu.com/doc/e81d36906dd230ba050daa46bcb87485)
* [Redis-Jedis](https://docs.xiaohongshu.com/doc/903c53e6454d06f1376eead189174e7e)
* [Apollo 配置中心](https://docs.xiaohongshu.com/doc/5b87d1c7c94bc12daaabe50108dd441e)
* [动态线程池](https://docs.xiaohongshu.com/doc/2b98146c4eb42f1987af8dce3b2ea54a)
* [本地缓存](https://docs.xiaohongshu.com/doc/6341ddda7b09b52d732b65c9466d81b3)
* [可观测性](https://docs.xiaohongshu.com/doc/682d1f93cc8b0a47f22fd40a95192929)
* [Building a RESTful Web Service](https://spring.io/guides/gs/rest-service/)

# 快速使用

**注意⚠️：仅限本地环境测试验证**

1. 配置环境变量

配置以下环境变量（仅本地环境需要）：

- `APPID`：应用名称，如 infrademo
- `XHS_SERVICE`：服务名称，如 infrademo-service-default
- `XHS_ENV`：环境，如 sit
- `ENV`：环境，如 sit
- `XHS_ZONE`：服务所在的区，如 qcsh4

示例：`APPID=infrademo;XHS_SERVICE=infrademo-service-default;XHS_ENV=sit;ENV=sit;XHS_ZONE=qcsh4`

2. 修改 `app.properties`

`app.properties` 用于 Apollo 拉取配置，默认配置为应用名

3. 启动服务

- 在 IDEA 启动

为服务的启动类配置环境变量，如 APPID=infrademo;XHS_SERVICE=infrademo-service-default;XHS_ENV=sit;ENV=sit;XHS_ZONE=qcsh4，然后启动即可

- 在命令行启动

```shell
mvn package -DskipTests && APPID=infrademo XHS_SERVICE=infrademo-service-default XHS_ENV=sit ENV=sit XHS_ZONE=qcsh4 java -jar target/ragserver.jar
```

4. 调用接口验证

```shell
curl http://127.0.0.1:8080/hello\?name=infrademo
```