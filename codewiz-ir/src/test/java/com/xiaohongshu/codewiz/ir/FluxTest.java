package com.xiaohongshu.codewiz.ir;

import java.util.Objects;

import org.junit.Test;

import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2025/3/6
 */
@Slf4j
public class FluxTest {
    @Test
    public void testFlux() {
        Flux.just("1", "2", "3").map(s -> {
                    if (Objects.equals(s, "3")) {
                        throw new RuntimeException(s);
                    }
                    return s;
                })
                .concatWithValues("4")
                .onErrorResume(e -> Flux.just("3"))
                .subscribe(System.out::println, e -> log.error("处理过程中发生错误: {}", e.getMessage()), () -> {
                    log.info("处理完成");
                });
    }
}
