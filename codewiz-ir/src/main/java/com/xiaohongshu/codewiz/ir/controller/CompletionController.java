package com.xiaohongshu.codewiz.ir.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.xiaohongshu.codewiz.core.entity.common.SingleResponse;
import com.xiaohongshu.codewiz.ir.dto.plugin.CodeGenerateRequestDTO;
import com.xiaohongshu.codewiz.ir.dto.plugin.CodeGenerateResponseDTO;
import com.xiaohongshu.codewiz.ir.service.complete.CodeCompletionService;

/**
 * Author: liukunpeng Date: 2025-03-10 Description:
 */
@Validated
@RestController
public class CompletionController {
    @Autowired
    private CodeCompletionService codeCompletionService;

    @PostMapping("/api/generateV2")
    public SingleResponse<CodeGenerateResponseDTO> generateV2(@Validated @RequestBody CodeGenerateRequestDTO request) {
        return SingleResponse.of(codeCompletionService.codeCompletionV2(request));
    }
}
