package com.xiaohongshu.codewiz.ir.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.xiaohongshu.codewiz.core.entity.chat.ChatSessionDetailResponse;
import com.xiaohongshu.codewiz.core.entity.chat.ChatSessionListRequest;
import com.xiaohongshu.codewiz.core.entity.chat.ChatSessionListResponse;
import com.xiaohongshu.codewiz.core.entity.chat.ChatSessionRequest;
import com.xiaohongshu.codewiz.core.entity.chat.ChatSessionResponse;
import com.xiaohongshu.codewiz.core.entity.common.SingleResponse;
import com.xiaohongshu.codewiz.core.service.chat.ChatSessionService;

/**
 * Author: liukunpeng Date: 2025-03-10 Description:
 */
@Validated
@RestController("/api/chat/session")
public class ChatSessionController {
    @Autowired
    private ChatSessionService chatSessionService;

    //@PostMapping("/create")
    public SingleResponse<ChatSessionResponse> sessionCreate(@RequestBody(required = false) ChatSessionRequest request) {
        // 兼容request为空场景
        String workspaceId = null;
        if (null != request) {
            workspaceId = request.getWorkspaceId();
        }
        return SingleResponse.of(chatSessionService.createChatSession(workspaceId));
    }

    //@PostMapping("/delete")
    public SingleResponse<?> sessionDelete(@RequestBody ChatSessionRequest request) {
        chatSessionService.delSession(request.getSessionId());
        return SingleResponse.buildSuccess();
    }

    //@PostMapping("/edit")
    public SingleResponse<?> sessionEdit(@RequestBody ChatSessionRequest request) {
        chatSessionService.editSessionName(request);
        return SingleResponse.buildSuccess();
    }

    //@PostMapping("/list")
    public SingleResponse<ChatSessionListResponse> sessionList(@RequestBody ChatSessionListRequest request) {
        return SingleResponse.of(chatSessionService.sessionList(request));
    }

    //@GetMapping("/detail")
    public SingleResponse<ChatSessionDetailResponse> sessionDetail(ChatSessionRequest request) {
        return SingleResponse.of(chatSessionService.sessionDetail(request));
    }
}
