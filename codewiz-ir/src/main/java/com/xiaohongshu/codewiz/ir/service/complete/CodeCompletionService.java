package com.xiaohongshu.codewiz.ir.service.complete;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfig;
import com.dianping.cat.Cat;
import com.dianping.cat.metrics.prometheus.builder.GaugeBuilder;
import com.dianping.cat.metrics.prometheus.builder.TimerBuilder;
import com.google.gson.JsonElement;
import com.google.gson.JsonParser;
import com.xiaohongshu.codewiz.core.constant.ErrorCodeConstant;
import com.xiaohongshu.codewiz.core.entity.allin.ChatCompletionRequestDTO;
import com.xiaohongshu.codewiz.core.entity.allin.ChatCompletionResponseDTO;
import com.xiaohongshu.codewiz.core.entity.common.SingleResponse;
import com.xiaohongshu.codewiz.core.exception.BizException;
import com.xiaohongshu.codewiz.core.feign.LLMAdapterFeignClient;
import com.xiaohongshu.codewiz.core.utils.FreeMarkerUtils;
import com.xiaohongshu.codewiz.core.utils.JsonMapperUtils;
import com.xiaohongshu.codewiz.ir.dto.plugin.CodeGenerateRequestDTO;
import com.xiaohongshu.codewiz.ir.dto.plugin.CodeGenerateResponseDTO;
import com.xiaohongshu.xray.logging.LogConstants;
import com.xiaohongshu.xray.logging.LogTags;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2025/2/27
 */
@Slf4j
@Service
public class CodeCompletionService {
    @ApolloConfig()
    private Config config;
    @Resource
    private LLMAdapterFeignClient llmAdapterFeignClient;

    private static final String MARKDOWN_CODE_KEYWORD = "```";
    private static final String NEWLINE_PATTERN = "\n";

    @Value("${codewiz.completion.model:codewiz-code-completion}")
    private String completionModel;

    public String codeCompletion(CodeGenerateRequestDTO dto) {
        List<ChatCompletionRequestDTO.ChatMessage> messages = List.of(
                ChatCompletionRequestDTO.ChatMessage.builder().role("system").content(
                                "您需要严格遵循以下双重验证流程：\n1. 首先生成纯代码填充<fim_middle>，保持与上下文完全一致的代码风格\n2. 执行最终检查：\n"
                                        + "   a) 绝对禁止任何```代码块或Markdown标记\n   b) 删除所有注释和解释文本\n   c) 确保缩进与上下文精确匹配\n"
                                        + "若发现违规必须立即修正，最终只输出通过验证的纯代码")
                        .build(),
                ChatCompletionRequestDTO.ChatMessage.builder().role("user")
                        .content("<fim_prefix>def fibonacci(n):\n    a = 0\n    b = 1<fim_suffix>    return a<fim_middle>").build(),
                ChatCompletionRequestDTO.ChatMessage.builder().role("assistant")
                        .content("    for _ in range(n):\n        a, b = b, a + b\n").build(),
                ChatCompletionRequestDTO.ChatMessage.builder().role("user").content(dto.getInputs()).build()
        );
        ChatCompletionRequestDTO chatRequest =
                ChatCompletionRequestDTO.builder()
                        .maxTokens(Optional.ofNullable(dto.getMaxTokens()).orElse(512))
                        .temperature(Optional.ofNullable(dto.getTemperature()).orElse(0.1))
                        .stop(dto.getStop())
                        .model("codewiz-code-completion").stream(false)
                        .messages(messages)
                        .build();
        TimerBuilder timer = Cat.timer("http_request_duration", "代码补全")
                .addTag("scene", "code_completion")
                .addTag(LogConstants.XRAY_TRACE_ID, MDC.get(LogConstants.XRAY_TRACE_ID))
                .addTag(LogConstants.CAT_MESSAGE_ID, MDC.get(LogConstants.CAT_MESSAGE_ID))
                .addTag("uuid", Optional.ofNullable(dto.getUuid()).orElse(StringUtils.EMPTY))
                .publishDefaultPercentiles();
        GaugeBuilder usageGauge = Cat.gauge("llm_token_usage", "大模型Token使用情况")
                .addTag(LogConstants.XRAY_TRACE_ID, MDC.get(LogConstants.XRAY_TRACE_ID))
                .addTag(LogConstants.CAT_MESSAGE_ID, MDC.get(LogConstants.CAT_MESSAGE_ID))
                .addTag("uuid", Optional.ofNullable(dto.getUuid()).orElse(StringUtils.EMPTY));
        GaugeBuilder timePerToken = Cat.gauge("time_per_token", "Token耗时")
                .addTag(LogConstants.XRAY_TRACE_ID, MDC.get(LogConstants.XRAY_TRACE_ID))
                .addTag(LogConstants.CAT_MESSAGE_ID, MDC.get(LogConstants.CAT_MESSAGE_ID))
                .addTag("uuid", Optional.ofNullable(dto.getUuid()).orElse(StringUtils.EMPTY));
        long startMillis = System.currentTimeMillis();
        ResponseEntity<SingleResponse<ChatCompletionResponseDTO>> response;
        int completionTokens = 0;
        try {
            response = llmAdapterFeignClient.chatCompletions(chatRequest, Optional.ofNullable(dto.getTestFlag()).orElse(false),
                    System.getenv("XHS_SERVICE"));
            ChatCompletionResponseDTO chatCompletionResponseDTO =
                    Optional.ofNullable(response).map(HttpEntity::getBody).map(SingleResponse::getData).orElse(null);
            String model = Optional.ofNullable(chatCompletionResponseDTO).map(ChatCompletionResponseDTO::getModel)
                    .orElse(StringUtils.EMPTY);
            timer.addTag("model", model);
            completionTokens = Optional.ofNullable(chatCompletionResponseDTO).map(ChatCompletionResponseDTO::getUsage)
                    .map(ChatCompletionResponseDTO.Usage::getCompletionTokens).orElse(0);
            // token使用上报
            usageGauge.addTag("model", model).setValue(completionTokens);
            timePerToken.addTag("model", model);
        } catch (Exception e) {
            timer.addTag("error", e.getMessage());
            throw new BizException(ErrorCodeConstant.CODE_COMPLETION_SERVICE_ERROR);
        } finally {
            // 补全耗时上报
            long duration = System.currentTimeMillis() - startMillis;
            timer.record(duration, TimeUnit.MILLISECONDS);
            if (completionTokens != 0) {
                timePerToken.setValue((double) duration / completionTokens);
            }
        }

        return Optional.ofNullable(response).map(HttpEntity::getBody)
                .map(SingleResponse::getData).map(ChatCompletionResponseDTO::getChoices).stream().flatMap(Collection::stream).findFirst()
                .map(ChatCompletionResponseDTO.Choice::getMessage).map(ChatCompletionResponseDTO.ChatMessage::getContent)
                .map(content -> Arrays.stream(content.split(NEWLINE_PATTERN)).filter(line -> !line.startsWith(MARKDOWN_CODE_KEYWORD))
                        .collect(Collectors.joining(NEWLINE_PATTERN))).orElse(StringUtils.EMPTY);
    }

    public CodeGenerateResponseDTO codeCompletionV2(CodeGenerateRequestDTO dto) {
        long startMillis = System.currentTimeMillis();
        String model = Optional.ofNullable(dto.getModel()).orElse(completionModel);
        String baseUrl = dto.getBaseUrl();
        List<ChatCompletionRequestDTO.ChatMessage> codeGeneratePrompt = codeGeneratePrompt(dto);
        ChatCompletionRequestDTO chatRequest =
                ChatCompletionRequestDTO.builder().maxTokens(dto.getMaxTokens()).temperature(dto.getTemperature()).topP(dto.getTopP())
                        .stream(false).messages(codeGeneratePrompt).model(model).baseUrl(baseUrl).build();
        String resStr = null;
        String output = StringUtils.EMPTY;
        ChatCompletionResponseDTO chatResp = null;
        log.info("codeGenerateV2 req:{} model:{} baseUrl:{}", JsonMapperUtils.toJson(chatRequest), model, baseUrl);
        String exceptionMsg = null;
        try {
            chatResp = Optional.ofNullable(
                            llmAdapterFeignClient.chatCompletions(chatRequest, dto.getTestFlag(), System.getenv("XHS_SERVICE")))
                    .map(HttpEntity::getBody).map(SingleResponse::getData).orElse(null);
            resStr = Optional.ofNullable(chatResp).map(ChatCompletionResponseDTO::getChoices).stream().flatMap(Collection::stream)
                    .findFirst().map(ChatCompletionResponseDTO.Choice::getMessage).map(ChatCompletionResponseDTO.ChatMessage::getContent)
                    .map(content -> Arrays.stream(content.split(NEWLINE_PATTERN)).filter(line -> !line.startsWith(MARKDOWN_CODE_KEYWORD))
                            .collect(Collectors.joining(NEWLINE_PATTERN))).orElse(StringUtils.EMPTY);
            JsonElement element = JsonParser.parseString(resStr);
            String outputJsonPath = dto.getOutputJsonPath();
            if (element.isJsonObject() && element.getAsJsonObject().has(outputJsonPath)) {
                output = element.getAsJsonObject().get(outputJsonPath).getAsString();
            }
        } catch (Exception e) {
            log.error("codeGenerateV2 error res: {}", resStr, e);
            exceptionMsg = e.getMessage();
            output = resStr;
        }
        log.info(LogTags.of(Map.of("uuid", dto.getUuid(), "cost", System.currentTimeMillis() - startMillis)), "codeGenerateReport res: {}",
                resStr);
        CodeGenerateResponseDTO resp = new CodeGenerateResponseDTO();
        resp.setGeneratedText(output);
        if (BooleanUtils.isTrue(dto.getTestFlag())) {
            resp.setPrompt(codeGeneratePrompt);
            resp.setResponse(chatResp);
            resp.setExceptionMsg(exceptionMsg);
            resp.setModel(Optional.ofNullable(chatResp).map(ChatCompletionResponseDTO::getModel).orElse(null));
            resp.setBaseUrl(Optional.ofNullable(chatResp).map(ChatCompletionResponseDTO::getBaseUrl).orElse(null));
        }
        return resp;
    }

    public List<ChatCompletionRequestDTO.ChatMessage> codeGeneratePrompt(CodeGenerateRequestDTO dto) {
        List<ChatCompletionRequestDTO.ChatMessage> promptTemplate = dto.getPromptTemplate();
        if (CollectionUtils.isNotEmpty(promptTemplate)) {
            for (ChatCompletionRequestDTO.ChatMessage m : promptTemplate) {
                m.setContent(FreeMarkerUtils.renderTemplate(m.getContent(), dto));
            }
            return promptTemplate;
        } else {
            return List.of(
                    ChatCompletionRequestDTO.ChatMessage.builder().role("system").content(constructSys())
                            .build(),
                    ChatCompletionRequestDTO.ChatMessage.builder().role("user").content(constructUser(dto.getInputs())).build()
            );
        }
    }

    private String constructSys() {
        String tmp = config.getProperty("code_completion.system_prompt", StringUtils.EMPTY);
        return FreeMarkerUtils.renderTemplate(tmp, null);
    }

    private String constructUser(String inputs) {
        String tmp = config.getProperty("code_completion.user_prompt", StringUtils.EMPTY);
        return FreeMarkerUtils.renderTemplate(tmp, Map.of("inputs", inputs));
    }
}
