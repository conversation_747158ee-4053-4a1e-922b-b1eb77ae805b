package com.xiaohongshu.codewiz.ir.service.complete;

import java.io.IOException;
import java.time.Duration;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Random;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Marker;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.async.DeferredResult;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import com.xiaohongshu.codewiz.core.entity.allin.ChatCompletionRequestDTO;
import com.xiaohongshu.codewiz.core.entity.allin.ChatCompletionResponseDTO;
import com.xiaohongshu.codewiz.core.entity.common.SingleResponse;
import com.xiaohongshu.codewiz.core.service.webflux.LLMAdapterWebFluxService;
import com.xiaohongshu.codewiz.core.utils.JsonMapperUtils;
import com.xiaohongshu.codewiz.ir.dto.plugin.ChatDialogRequestDTO;
import com.xiaohongshu.codewiz.ir.dto.plugin.ChatDialogResponseDTO;
import com.xiaohongshu.xray.logging.LogTags;

import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2025/3/3
 */
@Slf4j
@Service
public class ChatCompletionService {
    @Resource
    private LLMAdapterWebFluxService llmAdapterWebFluxService;

    private static final Random RANDOM = new Random();

    private static final String SERVER_BUSY = "服务器繁忙，请稍后再试。";

    public Object chatCompletion(ChatDialogRequestDTO request) {
        List<ChatCompletionRequestDTO.ChatMessage> messages = List.of(
                ChatCompletionRequestDTO.ChatMessage.builder().role("user").content(
                        Optional.ofNullable(request.getContent()).map(ChatDialogRequestDTO.Content::getUserContent)
                                .orElse(StringUtils.EMPTY)).build()
        );
        // 默认模型兜底
        String model = Optional.ofNullable(request.getModel()).orElse("codewiz-chat");
        ChatCompletionRequestDTO chatRequest =
                ChatCompletionRequestDTO.builder().maxTokens(512).temperature(0.1).model(model)
                        .stream(request.getStream())
                        .messages(messages)
                        .build();
        Boolean stream = Optional.ofNullable(chatRequest.getStream()).orElse(false);
        WebClient.ResponseSpec responseSpec =
                llmAdapterWebFluxService.chatCompletion(chatRequest, request.getTestFlag(), System.getenv("XHS_SERVICE"));
        if (stream) {
            // 流式SSE输出
            return handleEventStream(responseSpec, request);
        } else {
            // 非流式JSON输出
            return handleJson(responseSpec, request);
        }
    }

    public ChatDialogResponseDTO buildResponse(String msg, ChatDialogRequestDTO request) {
        return ChatDialogResponseDTO.builder().sessionId(request.getSessionId()).userContent(
                Optional.ofNullable(request.getContent()).map(ChatDialogRequestDTO.Content::getUserContent)
                        .orElse(StringUtils.EMPTY)).assistantContent(msg).createTime(System.currentTimeMillis()).build();
    }

    private String extractMsg(ChatCompletionResponseDTO dto) {
        Optional<ChatCompletionResponseDTO.Choice> firstChoice =
                Optional.ofNullable(dto).map(ChatCompletionResponseDTO::getChoices).stream().flatMap(Collection::stream).findFirst();
        return firstChoice.map(ChatCompletionResponseDTO.Choice::getMessage)
                .or(() -> firstChoice.map(ChatCompletionResponseDTO.Choice::getDelta))
                .map(ChatCompletionResponseDTO.ChatMessage::getContent).orElse(StringUtils.EMPTY);
    }

    private SseEmitter handleEventStream(WebClient.ResponseSpec responseSpec,
                                         ChatDialogRequestDTO request) {
        SseEmitter sseEmitter = new SseEmitter();
        long startMillis = System.currentTimeMillis();
        // 未捕获的异常兜底处理
        responseSpec.bodyToFlux(String.class)
                // 流式处理5s无数据，客户端终止监听数据
                .timeout(Duration.ofSeconds(5))
                .takeWhile(data -> !LLMAdapterWebFluxService.DONE.equals(data))
                .map(content -> JsonMapperUtils.fromJson(content, ChatCompletionResponseDTO.class))
                .map(dto -> JsonMapperUtils.toJson(SingleResponse.of(buildResponse(extractMsg(dto), request))))
                .onErrorResume(e -> {
                    log.error("chatCompletionStream fluxError", e);
                    return Flux.just(JsonMapperUtils.toJson(buildResponse(SERVER_BUSY, request)));
                })
                .concatWithValues(LLMAdapterWebFluxService.DONE)
                .subscribe(
                        data -> {
                            try {
                                sseEmitter.send(data);
                            } catch (IOException ex) {
                                log.error("chatCompletionStream SseError", ex);
                            }
                        },
                        e -> {
                            log.error(buildMarker(request, startMillis), "chatCompletionStream unknownError", e);
                            sseEmitter.completeWithError(e);
                        },
                        () -> {
                            log.info(buildMarker(request, startMillis), "chatCompletionStream");
                            sseEmitter.complete();
                        }
                );
        return sseEmitter;
    }

    private DeferredResult<ResponseEntity<SingleResponse<?>>> handleJson(WebClient.ResponseSpec responseSpec,
                                                                         ChatDialogRequestDTO request) {
        long startTime = System.currentTimeMillis();
        DeferredResult<ResponseEntity<SingleResponse<?>>> deferredResult = new DeferredResult<>(30_000L);
        // DeferredResult 超时（兜底）
        deferredResult.onTimeout(() -> {
            log.error(buildMarker(request, startTime), "chatCompletionJsonTimeout");
            if (!deferredResult.isSetOrExpired()) { // 防御性检查
                deferredResult.setResult(ResponseEntity.ok().contentType(MediaType.APPLICATION_JSON)
                        .body(SingleResponse.buildFailure("SERVER_BUSY", SERVER_BUSY)));
            }
        });
        responseSpec.bodyToMono(new ParameterizedTypeReference<SingleResponse<ChatCompletionResponseDTO>>() {
                }).map(SingleResponse::getData).map(dto -> buildResponse(extractMsg(dto), request))
                .subscribe(
                        response -> {
                            // TODO：VSCode插件要求不为空，先random赋值，待多轮对话开发时候补齐
                            response.setDialogId(RANDOM.nextLong());
                            if (!deferredResult.isSetOrExpired()) {
                                deferredResult.setResult(
                                        ResponseEntity.ok().contentType(MediaType.APPLICATION_JSON).body(SingleResponse.of(response)));
                            }
                            log.info(buildMarker(request, startTime), "chatCompletionJson");
                        }, e -> {
                            log.error(buildMarker(request, startTime), "chatCompletionJsonError", e);
                            if (!deferredResult.isSetOrExpired()) {
                                deferredResult.setResult(ResponseEntity.ok().contentType(MediaType.APPLICATION_JSON)
                                        .body(SingleResponse.buildFailure("SERVER_BUSY", SERVER_BUSY)));
                            }
                        }
                );
        return deferredResult;
    }

    private Marker buildMarker(ChatDialogRequestDTO request, long startTime) {
        return LogTags.of(Map.of(
                "sessionId", Optional.ofNullable(request.getSessionId()).orElse(StringUtils.EMPTY),
                "userId", Optional.ofNullable(request.getUserId()).orElse(StringUtils.EMPTY),
                "stream", Optional.ofNullable(request.getStream()).orElse(false),
                "cost", System.currentTimeMillis() - startTime
        ));
    }
}
