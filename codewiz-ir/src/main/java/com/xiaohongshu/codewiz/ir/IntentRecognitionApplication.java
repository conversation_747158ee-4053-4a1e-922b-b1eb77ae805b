package com.xiaohongshu.codewiz.ir;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2025/3/3
 */
@SpringBootApplication(scanBasePackages = {"com.xiaohongshu.infra", "com.xiaohongshu.codewiz"})
public class IntentRecognitionApplication {

    public static void main(String[] args) {
        System.setProperty("XHS_ENV", "sit");
        System.setProperty("XHS_SERVICE,", "codewiz-ir-default");
        System.setProperty("XHS_ZONE,", "qcsh4");
        System.setProperty("APPID", "codewiz");

        SpringApplication.run(IntentRecognitionApplication.class, args);
    }
}
