package com.xiaohongshu.codewiz.ir.dto.plugin;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;

import lombok.Data;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2025/2/27
 */
@Data
public class ChatDialogRequestDTO {
    private String userId;
    private String sessionId;
    private Boolean stream;
    private Integer type;
    @Valid
    private Content content;
    private PluginExtraDTO extra;
    private Boolean testFlag;
    private String model;

    @Data
    public static class Content {
        @NotEmpty
        private String userContent;
    }
}
