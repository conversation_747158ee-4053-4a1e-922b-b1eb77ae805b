package com.xiaohongshu.codewiz.ir.dto.plugin;

import java.util.List;

import lombok.Data;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2025/2/27
 */
@Data
public class PluginExtraDTO {
    private String filename;
    private String lang;
    private List<String> invocationCodeList;
    private List<ChatReference> chatReferences;

    @Data
    public static class ChatReference {
        private String filename;
        private String text;
        private Range range;
        private String languageId;
    }

    @Data
    public static class Range {
        private Position start;
        private Position end;
    }

    @Data
    public static class Position {
        private Integer line;
        private Integer character;
    }
}
