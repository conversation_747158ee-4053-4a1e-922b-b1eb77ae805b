package com.xiaohongshu.codewiz.ir.dto.plugin;

import java.util.List;

import com.xiaohongshu.codewiz.core.entity.allin.ChatCompletionRequestDTO;
import com.xiaohongshu.codewiz.core.entity.allin.ChatCompletionResponseDTO;

import lombok.Data;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2025/2/27
 */
@Data
public class CodeGenerateResponseDTO {
    private String generatedText;
    private List<ChatCompletionRequestDTO.ChatMessage> prompt;
    private ChatCompletionResponseDTO response;
    private String exceptionMsg;
    private String baseUrl;
    private String model;
}
