package com.xiaohongshu.codewiz.ir.controller;

import java.util.UUID;

import javax.annotation.Resource;

import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.xiaohongshu.codewiz.core.entity.common.SingleResponse;
import com.xiaohongshu.codewiz.ir.dto.plugin.ChatDialogRequestDTO;
import com.xiaohongshu.codewiz.ir.dto.plugin.CodeGenerateRequestDTO;
import com.xiaohongshu.codewiz.ir.dto.plugin.CodeGenerateResponseDTO;
import com.xiaohongshu.codewiz.ir.dto.plugin.SessionCancelRequestDTO;
import com.xiaohongshu.codewiz.ir.dto.plugin.SessionCreateRequestDTO;
import com.xiaohongshu.codewiz.ir.dto.plugin.SessionCreateResponseDTO;
import com.xiaohongshu.codewiz.ir.dto.plugin.SessionDeleteRequestDTO;
import com.xiaohongshu.codewiz.ir.dto.plugin.SessionEditRequestDTO;
import com.xiaohongshu.codewiz.ir.dto.plugin.SessionListRequestDTO;
import com.xiaohongshu.codewiz.ir.dto.plugin.SessionPageResponseDTO;
import com.xiaohongshu.codewiz.ir.service.complete.ChatCompletionService;
import com.xiaohongshu.codewiz.ir.service.complete.CodeCompletionService;


/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2025/3/3
 */
@Validated
@RestController
public class PluginController {
    @Resource
    private CodeCompletionService codeCompletionService;
    @Resource
    private ChatCompletionService chatCompletionService;

    @PostMapping("/api/generate")
    public SingleResponse<CodeGenerateResponseDTO> generate(@Validated @RequestBody CodeGenerateRequestDTO request) {
        CodeGenerateResponseDTO dto = new CodeGenerateResponseDTO();
        dto.setGeneratedText(codeCompletionService.codeCompletion(request));
        return SingleResponse.of(dto);
    }

    @PostMapping("/api/generate/record/operation")
    public SingleResponse<?> recordOperation() {
        return SingleResponse.buildSuccess();
    }

    @PostMapping("/api/generate/cancel")
    public SingleResponse<?> cancel() {
        return SingleResponse.buildSuccess();
    }

    @PostMapping("/api/chat/session/create")
    public SingleResponse<SessionCreateResponseDTO> createSession(@RequestBody SessionCreateRequestDTO request) {
        SessionCreateResponseDTO dto = new SessionCreateResponseDTO();
        dto.setWorkspaceId(request.getWorkspaceId());
        dto.setSessionId(UUID.randomUUID().toString());
        return SingleResponse.of(dto);
    }

    @PostMapping(value = "/api/chat/session/dialog", produces = {MediaType.TEXT_EVENT_STREAM_VALUE, MediaType.APPLICATION_JSON_VALUE})
    public Object dialogSession(@Validated @RequestBody ChatDialogRequestDTO request) {
        return chatCompletionService.chatCompletion(request);
    }

    @PostMapping("/api/chat/session/delete")
    public SingleResponse<?> deleteSession(@RequestBody SessionDeleteRequestDTO request) {
        return SingleResponse.buildSuccess();
    }

    @PostMapping("/api/chat/session/edit")
    public SingleResponse<?> editSession(@RequestBody SessionEditRequestDTO request) {
        return SingleResponse.buildSuccess();
    }

    @GetMapping("/api/chat/session/list")
    public SingleResponse<SessionPageResponseDTO> listSession(SessionListRequestDTO request) {
        return SingleResponse.of(new SessionPageResponseDTO());
    }

    @PostMapping("/api/chat/session/cancelStream")
    public SingleResponse<?> sessionCancelStream(@RequestBody SessionCancelRequestDTO request) {
        return SingleResponse.buildSuccess();
    }

    @PostMapping("/api/chat/feedback")
    public SingleResponse<?> chatFeedback(@RequestBody ChatDialogRequestDTO request) {
        return SingleResponse.buildSuccess();
    }
}
