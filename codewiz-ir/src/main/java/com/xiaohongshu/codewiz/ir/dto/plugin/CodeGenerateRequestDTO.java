package com.xiaohongshu.codewiz.ir.dto.plugin;

import java.util.List;

import javax.validation.constraints.NotEmpty;

import com.xiaohongshu.codewiz.core.entity.allin.ChatCompletionRequestDTO;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2025/2/27
 */
@Data
public class CodeGenerateRequestDTO {
    @NotEmpty
    @Schema(description = "唯一标识符")
    private String uuid;

    @NotEmpty
    @Schema(description = "输入参数")
    private String inputs;

    @Schema(description = "最大输出长度", defaultValue = "128")
    private Integer maxTokens = 128;

    @Schema(description = "温度", defaultValue = "0.1")
    private Double temperature = 0.1;

    @Schema(description = "topP", defaultValue = "0.85")
    private Double topP = 0.85;

    @Schema(description = "停止词")
    private List<String> stop;

    @Schema(description = "是否为测试", defaultValue = "false")
    private Boolean testFlag = false;
    // 评测调试参数
    @Schema(description = "模型")
    private String model;
    @Schema(description = "模型url")
    private String baseUrl;
    @Schema(description = "提示词模板")
    private List<ChatCompletionRequestDTO.ChatMessage> promptTemplate;
    @Schema(description = "输出json路径")
    private String outputJsonPath = "output";
}
