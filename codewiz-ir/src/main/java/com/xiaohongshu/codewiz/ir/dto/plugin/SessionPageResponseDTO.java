package com.xiaohongshu.codewiz.ir.dto.plugin;

import java.util.List;

import lombok.Data;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2025/2/27
 */
@Data
public class SessionPageResponseDTO {
    private String sessionId;
    private List<Dialog> dialogs;
    private PluginExtraDTO extra;

    @Data
    public static class Dialog {
        private Long dialogId;
        private String sessionId;
        private String userContent;
        private String assistantContent;
        private Integer type;
        private Long createTime;
        private Integer feedbackType;
    }
}
