package com.xiaohongshu.codewiz.rag;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.xiaohongshu.codewiz.core.constant.enums.OpenKnowledgeTypeEnum;
import com.xiaohongshu.codewiz.core.entity.rag.open.BizKnowledgeCreateReq;
import com.xiaohongshu.codewiz.core.entity.rag.open.BizKnowledgeCreateResp;
import com.xiaohongshu.codewiz.core.entity.rag.open.BizKnowledgeDeleteReq;
import com.xiaohongshu.codewiz.core.exception.BizException;
import com.xiaohongshu.codewiz.core.service.milvus.BizMilvusService;
import com.xiaohongshu.codewiz.core.service.rag.open.BizKnowledgeBaseService;

/**
 * <AUTHOR>
 * @date 2025/3/25 20:41
 */
public class OpenRagTest extends SpringBaseTest {

    @Autowired
    private BizKnowledgeBaseService bizKnowledgeBaseService;

    @Autowired
    private BizMilvusService bizMilvusService;

    /**
     * 测试创建公共知识库
     */
    @Test
    public void testCreatePublicKnowledgeBase() {
        // 准备测试数据
        BizKnowledgeCreateReq req = new BizKnowledgeCreateReq();
        req.setBizId("test_biz_id");
        req.setKbType(OpenKnowledgeTypeEnum.PUBLIC.getValue());

        // 执行测试
        BizKnowledgeCreateResp resp = bizKnowledgeBaseService.createBizKnowledgeBase(req);

        // 验证结果
        assertThat(resp).isNotNull();
        assertThat(resp.getKbId()).isEqualTo("test_biz_id_public");

        // 清理测试数据
        // BizKnowledgeDeleteReq deleteReq = new BizKnowledgeDeleteReq();
        // deleteReq.setKbId(resp.getKbId());
        // bizKnowledgeBaseService.deleteBizKnowledgeBase(deleteReq);
    }

    /**
     * 测试创建私有知识库
     */
    @Test
    public void testCreatePrivateKnowledgeBase() {
        // 准备测试数据
        BizKnowledgeCreateReq req = new BizKnowledgeCreateReq();
        req.setBizId("test_biz_id");
        req.setUserId("test_user_id");
        req.setProjectId("test_project_id");
        req.setKbType(OpenKnowledgeTypeEnum.PRIVATE.getValue());

        // 计算预期的知识库ID
        int hash = Math.abs((req.getUserId() + req.getProjectId()).hashCode() % 10);
        String expectedKbId = "test_biz_id_private_" + hash;

        // 执行测试
        BizKnowledgeCreateResp resp = bizKnowledgeBaseService.createBizKnowledgeBase(req);

        // 验证结果
        assertThat(resp).isNotNull();
        assertThat(resp.getKbId()).isEqualTo(expectedKbId);

        // 清理测试数据
        // BizKnowledgeDeleteReq deleteReq = new BizKnowledgeDeleteReq();
        // deleteReq.setKbId(resp.getKbId());
        // bizKnowledgeBaseService.deleteBizKnowledgeBase(deleteReq);
    }

    /**
     * 测试创建知识库时业务线ID为空的情况
     */
    @Test
    public void testCreateKnowledgeBaseWithEmptyBizId() {
        // 准备测试数据
        BizKnowledgeCreateReq req = new BizKnowledgeCreateReq();
        req.setKbType(OpenKnowledgeTypeEnum.PUBLIC.getValue());

        // 执行测试并验证异常
        assertThatThrownBy(() -> {
            bizKnowledgeBaseService.createBizKnowledgeBase(req);
        }).isInstanceOf(BizException.class);
    }

    /**
     * 测试创建知识库时知识库类型为空的情况
     */
    @Test
    public void testCreateKnowledgeBaseWithEmptyKbType() {
        // 准备测试数据
        BizKnowledgeCreateReq req = new BizKnowledgeCreateReq();
        req.setBizId("test_biz_id");

        // 执行测试并验证异常
        assertThatThrownBy(() -> {
            bizKnowledgeBaseService.createBizKnowledgeBase(req);
        }).isInstanceOf(BizException.class);
    }

    /**
     * 测试创建私有知识库时用户ID为空的情况
     */
    @Test
    public void testCreatePrivateKnowledgeBaseWithEmptyUserId() {
        // 准备测试数据
        BizKnowledgeCreateReq req = new BizKnowledgeCreateReq();
        req.setBizId("test_biz_id");
        req.setProjectId("test_project_id");
        req.setKbType(OpenKnowledgeTypeEnum.PRIVATE.getValue());

        // 执行测试并验证异常
        assertThatThrownBy(() -> {
            bizKnowledgeBaseService.createBizKnowledgeBase(req);
        }).isInstanceOf(BizException.class);
    }

    /**
     * 测试创建私有知识库时项目ID为空的情况
     */
    @Test
    public void testCreatePrivateKnowledgeBaseWithEmptyProjectId() {
        // 准备测试数据
        BizKnowledgeCreateReq req = new BizKnowledgeCreateReq();
        req.setBizId("test_biz_id");
        req.setUserId("test_user_id");
        req.setKbType(OpenKnowledgeTypeEnum.PRIVATE.getValue());

        // 执行测试并验证异常
        assertThatThrownBy(() -> {
            bizKnowledgeBaseService.createBizKnowledgeBase(req);
        }).isInstanceOf(BizException.class);
    }

    /**
     * 测试知识库删除功能
     */
    @Test
    public void testDeleteKnowledgeBase() {
        // 准备测试数据 - 先创建一个公共知识库
        BizKnowledgeCreateReq createReq = new BizKnowledgeCreateReq();
        createReq.setBizId("test_biz_delete");
        createReq.setKbType(OpenKnowledgeTypeEnum.PUBLIC.getValue());
        BizKnowledgeCreateResp createResp = bizKnowledgeBaseService.createBizKnowledgeBase(createReq);

        // 确保创建成功
        assertThat(createResp).isNotNull();
        assertThat(createResp.getKbId()).isEqualTo("test_biz_delete_public");

        // 执行删除操作
        BizKnowledgeDeleteReq deleteReq = new BizKnowledgeDeleteReq();
        deleteReq.setKbId(createResp.getKbId());

        // 公共知识库不支持删除，应该抛出异常
        assertThatThrownBy(() -> {
            bizKnowledgeBaseService.deleteBizKnowledgeBase(deleteReq);
        }).isInstanceOf(BizException.class);
    }

    /**
     * 测试私有知识库删除功能
     */
    @Test
    public void testDeletePrivateKnowledgeBase() {
        // 准备测试数据 - 先创建一个私有知识库
        String testUserId = "test_user_id";
        String testProjectId = "test_project_id";

        BizKnowledgeCreateReq createReq = new BizKnowledgeCreateReq();
        createReq.setBizId("test_biz_delete_private");
        createReq.setKbType(OpenKnowledgeTypeEnum.PRIVATE.getValue());
        createReq.setUserId(testUserId);
        createReq.setProjectId(testProjectId);
        BizKnowledgeCreateResp createResp = bizKnowledgeBaseService.createBizKnowledgeBase(createReq);

        // 确保创建成功
        assertThat(createResp).isNotNull();
        assertThat(createResp.getKbId()).contains("test_biz_delete_private_private_");

        // 执行删除操作 - 不带必要参数的情况
        BizKnowledgeDeleteReq deleteReqWithoutUserId = new BizKnowledgeDeleteReq();
        deleteReqWithoutUserId.setKbId(createResp.getKbId());
        deleteReqWithoutUserId.setBizId("test_biz_delete_private");

        // 缺少用户ID，应该抛出异常
        assertThatThrownBy(() -> {
            bizKnowledgeBaseService.deleteBizKnowledgeBase(deleteReqWithoutUserId);
        }).isInstanceOf(BizException.class);

        // 执行删除操作 - 带有必要参数的情况
        BizKnowledgeDeleteReq deleteReq = new BizKnowledgeDeleteReq();
        deleteReq.setKbId(createResp.getKbId());
        deleteReq.setBizId("test_biz_delete_private");
        deleteReq.setUserId(testUserId);
        deleteReq.setProjectId(testProjectId);

        // 应该成功删除记录
        bizKnowledgeBaseService.deleteBizKnowledgeBase(deleteReq);

        // 再次删除应该不会有问题（因为是基于过滤条件删除记录，不会抛异常）
        bizKnowledgeBaseService.deleteBizKnowledgeBase(deleteReq);
    }
}
