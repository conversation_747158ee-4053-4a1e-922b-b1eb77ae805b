package com.xiaohongshu.codewiz.rag;

import javax.annotation.Resource;

import org.junit.Test;

import com.xiaohongshu.codewiz.core.service.rag.RagCodeExplainService;

/**
 * <AUTHOR>
 * @date 2025/3/11 15:21
 */
public class LlmAdapterReqTest extends SpringBaseTest {

    @Resource
    private RagCodeExplainService ragCodeExplainService;

    @Test
    public void testExplainCode() {
        String code =
                "public WebClient.ResponseSpec chatCompletion(ChatCompletionRequestDTO request, Boolean testFlag,\n                                                 String serviceName) {\n        return llmAdapterWebClient.post()\n                .uri(URL_CHAT_COMPLETION)\n                .header(\"test-flag\", Optional.ofNullable(testFlag).orElse(false).toString())\n                .header(\"service-name\", serviceName)\n                .body(Mono.just(request), ChatCompletionRequestDTO.class)\n                .retrieve();\n    }\n";
        String result = ragCodeExplainService.explainCode(code);
        System.out.println(result);
    }
}

