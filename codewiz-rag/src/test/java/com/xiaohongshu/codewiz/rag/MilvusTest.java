package com.xiaohongshu.codewiz.rag;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.junit.Test;

import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.xiaohongshu.codewiz.core.client.CaseMilvusClient;
import com.xiaohongshu.codewiz.core.client.EmbeddingClient;
import com.xiaohongshu.codewiz.core.client.OpenMilvusClient;
import com.xiaohongshu.codewiz.core.service.milvus.CaseMilvusService;

import io.milvus.v2.service.vector.request.InsertReq;
import io.milvus.v2.service.vector.request.SearchReq;
import io.milvus.v2.service.vector.request.data.EmbeddedText;
import io.milvus.v2.service.vector.response.InsertResp;
import io.milvus.v2.service.vector.response.SearchResp;

/**
 * <AUTHOR>
 * @date 2025/2/26 20:58
 */
public class MilvusTest extends SpringBaseTest {

    @Resource
    private CaseMilvusClient caseMilvusClient;
    @Resource
    private EmbeddingClient embeddingClient;
    @Resource
    private CaseMilvusService caseMilvusService;
    @Resource
    private OpenMilvusClient openMilvusClient;

    private final List<String> outputFields = Lists.newArrayList(
            "item_data",
            "field_name",
            "field_content",
            "knowledge_base",
            "item_id");

    @Test
    public void testSearch() {
        String collectionName = "codeWiz_case_v1";
        String annsField = "field_embedding";
        List<Float> embedding = embeddingClient.getBgeEmbeddingVector("test");
        SearchResp searchResp = caseMilvusService.search(collectionName, embedding, 10, annsField, null);
        System.out.println(searchResp);
    }

    // add
    @Test
    public void testAdd() {
        String collectionName = "codeWiz_case_v1";
        caseMilvusService.insertData(collectionName, Lists.newArrayList());
    }

    @Test
    public void createCollection() {
        String collectionName = "codeWiz_case_v2";
        caseMilvusService.createCaseCollection(collectionName);
    }

    @Test
    public void testSearch2() {
        String collectionName = "codeWiz_cr_sit_test";
        String codeQuery = "searchParams.put(\"min_score\", 0.0);";
        Map<String, Object> searchParams = new HashMap<>();
        searchParams.put("drop_ratio_search", 0.0);
        searchParams.put("min_score", 0.0);
        searchParams.put("weight_threshold", 0.5);
        searchParams.put("max_candidates", 100);
        searchParams.put("min_term_frequency", 1);
        SearchResp search = caseMilvusClient.search(SearchReq.builder()
                .collectionName(collectionName)
                .data(Collections.singletonList(new EmbeddedText(codeQuery)))
                .annsField("field_sparse")
                .topK(10)
                .searchParams(searchParams)
                .outputFields(outputFields)
                .build());
        System.out.println(search);
    }

    @Test
    public void testHybridSearch() {
        String collectionName = "codeWiz_cr_sit_v1";
        String codeQuery = "AgiChatRequestDTO.builder().appId(\"kairos\").botId(kairosSwitcher.getAgiShortNameConfig().getBotId()).\n" +
                "uid(\"6217734313b92500014a5daf\")\n" +
                ".conversationId(conversationId).query(Collections.singletonList(AgiChatRequestDTO.ContentDTO.builder()\n" +
                ".contentType(\"text\").content(content).build())).build();";
        List<Float> embeddingVector = embeddingClient.getBgeEmbeddingVector(codeQuery);
        SearchResp searchResp = caseMilvusService.hybridSearch(collectionName,
                "field_embedding",
                embeddingVector,
                "field_sparse", codeQuery, 10);
        System.out.println();
    }

    @Test
    public void testDynamicField() {
        String collectionName = "codewiz_dynamic_field";

        // CreateCollectionReq.CollectionSchema schema = openMilvusClient.createSchema();
        // schema.addField(AddFieldReq.builder()
        //         .fieldName("id")
        //         .dataType(DataType.Int64)
        //         .isPrimaryKey(true)
        //         .autoID(false)
        //         .build());
        //
        // schema.addField(AddFieldReq.builder()
        //         .fieldName("vector")
        //         .dataType(DataType.FloatVector)
        //         .dimension(5)
        //         .build());
        //
        // CreateCollectionReq request = CreateCollectionReq.builder()
        //         .collectionName(collectionName)
        //         .dimension(5)
        //         // .collectionSchema(schema)
        //         .enableDynamicField(true)
        //         .autoID(false)
        //         .build();
        //
        // caseMilvusClient.createCollection(request);
        System.out.println("create collection success");

        Gson gson = new Gson();
        List<JsonObject> data = Arrays.asList(
                gson.fromJson(
                        "{\"id\": 0, \"vector\": [0.3580376395471989, -0.6023495712049978, 0.18414012509913835, -0.26286205330961354, 0.9029438446296592], \"color\": \"pink_8682\"}",
                        JsonObject.class),
                gson.fromJson(
                        "{\"id\": 1, \"vector\": [0.19886812562848388, 0.06023560599112088, 0.6976963061752597, 0.2614474506242501, 0.838729485096104], \"color\": \"red_7025\"}",
                        JsonObject.class),
                gson.fromJson(
                        "{\"id\": 2, \"vector\": [0.43742130801983836, -0.5597502546264526, 0.6457887650909682, 0.7894058910881185, 0.20785793220625592], \"color\": \"orange_6781\"}",
                        JsonObject.class),
                gson.fromJson(
                        "{\"id\": 3, \"vector\": [0.3172005263489739, 0.9719044792798428, -0.36981146090600725, -0.4860894583077995, 0.95791889146345], \"color\": \"pink_9298\"}",
                        JsonObject.class),
                gson.fromJson(
                        "{\"id\": 4, \"vector\": [0.4452349528804562, -0.8757026943054742, 0.8220779437047674, 0.46406290649483184, 0.30337481143159106], \"color\": \"red_4794\"}",
                        JsonObject.class),
                gson.fromJson(
                        "{\"id\": 5, \"vector\": [0.985825131989184, -0.8144651566660419, 0.6299267002202009, 0.1206906911183383, -0.1446277761879955], \"color\": \"yellow_4222\"}",
                        JsonObject.class),
                gson.fromJson(
                        "{\"id\": 6, \"vector\": [0.8371977790571115, -0.015764369584852833, -0.31062937026679327, -0.562666951622192, -0.8984947637863987], \"color\": \"red_9392\"}",
                        JsonObject.class),
                gson.fromJson(
                        "{\"id\": 7, \"vector\": [-0.33445148015177995, -0.2567135004164067, 0.8987539745369246, 0.9402995886420709, 0.5378064918413052], \"color\": \"grey_8510\"}",
                        JsonObject.class),
                gson.fromJson(
                        "{\"id\": 8, \"vector\": [0.39524717779832685, 0.4000257286739164, -0.5890507376891594, -0.8650502298996872, -0.6140360785406336], \"color\": \"white_9381\"}",
                        JsonObject.class),
                gson.fromJson(
                        "{\"id\": 9, \"vector\": [0.5718280481994695, 0.24070317428066512, -0.3737913482606834, -0.06726932177492717, -0.6980531615588608], \"color\": \"purple_4976\"}",
                        JsonObject.class)
        );

        InsertReq insertReq = InsertReq.builder()
                .collectionName(collectionName)
                .data(data)
                .build();

        InsertResp insert = caseMilvusClient.insert(insertReq);
        System.out.println(insert);
    }
}
