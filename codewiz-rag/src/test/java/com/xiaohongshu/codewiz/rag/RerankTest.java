package com.xiaohongshu.codewiz.rag;

import java.util.ArrayList;
import java.util.List;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.xiaohongshu.codewiz.core.client.RerankClient;
import com.xiaohongshu.codewiz.core.entity.rerank.RerankRequestDto;
import com.xiaohongshu.codewiz.core.entity.rerank.RerankResultDto;
import com.xiaohongshu.codewiz.core.entity.rerank.RerankType;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2025/4/9 21:12
 */
@Slf4j
public class RerankTest extends SpringBaseTest {

    @Autowired
    private RerankClient rerankClient;

    @Test
    public void testRerankDispatch() {
        // 准备测试数据
        List<RerankRequestDto> requests = new ArrayList<>();
        RerankRequestDto dto = new RerankRequestDto();
        dto.setQuery("如何实现一个Java单例模式？");
        dto.setPassage("Java单例模式的实现方法有哪些？");
        requests.add(dto);

        // 测试不同的Rerank类型
        for (RerankType rerankType : RerankType.values()) {
            log.info("测试 {} Rerank，URL: {}", rerankType, rerankType.getUrl());
            List<RerankResultDto> results = rerankClient.getRerankResult(requests, rerankType);
            log.info("{} Rerank结果: {}", rerankType, results);
        }

        // 测试默认类型（BGE_RERANK）
        List<RerankResultDto> defaultResults = rerankClient.getRerankResult(requests);
        log.info("默认Rerank结果: {}", defaultResults);
    }
}
