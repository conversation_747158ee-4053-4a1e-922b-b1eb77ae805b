package com.xiaohongshu.codewiz.rag;


import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.mapping.KeywordProperty;
import co.elastic.clients.elasticsearch._types.mapping.LongNumberProperty;
import co.elastic.clients.elasticsearch._types.mapping.Property;
import co.elastic.clients.elasticsearch._types.mapping.TextProperty;
import co.elastic.clients.elasticsearch._types.query_dsl.MatchQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.Query;
import co.elastic.clients.elasticsearch.core.BulkRequest;
import co.elastic.clients.elasticsearch.core.BulkResponse;
import co.elastic.clients.elasticsearch.core.SearchRequest;
import co.elastic.clients.elasticsearch.core.SearchResponse;
import co.elastic.clients.elasticsearch.core.search.Hit;
import co.elastic.clients.elasticsearch.indices.CreateIndexResponse;
import co.elastic.clients.elasticsearch.indices.ExistsRequest;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2025/3/27 18:48
 */
@Slf4j
public class EsTest extends SpringBaseTest {

    @Autowired
    private ElasticsearchClient elasticsearchClient;

    private static final String TEST_INDEX_NAME = "cmt_rule_test";

    /**
     * 测试创建索引
     */
    @Test
    public void testCreateIndex() throws IOException {
        // 检查索引是否存在，如果存在先删除
        boolean exists = elasticsearchClient.indices()
                .exists(ExistsRequest.of(r -> r.index(TEST_INDEX_NAME)))
                .value();

        if (exists) {
            elasticsearchClient.indices().delete(d -> d.index(TEST_INDEX_NAME));
            log.info("删除已存在的索引: {}", TEST_INDEX_NAME);
        }

        // 构建映射
        Map<String, Property> properties = new HashMap<>();

        // 设置cmt_rule字段为text类型，用于全文检索
        properties.put("cmt_rule", Property.of(p -> p.text(TextProperty.of(t -> t
                .analyzer("standard")))));

        // 设置create_at字段为long类型
        properties.put("create_at", Property.of(p -> p.long_(LongNumberProperty.of(l -> l))));

        // 设置language字段为keyword类型
        properties.put("language", Property.of(p -> p.keyword(KeywordProperty.of(k -> k))));

        // meta字段需要嵌套属性
        Map<String, Property> metaProperties = new HashMap<>();
        metaProperties.put("tag", Property.of(p -> p.keyword(KeywordProperty.of(k -> k))));
        metaProperties.put("cmt_id", Property.of(p -> p.keyword(KeywordProperty.of(k -> k))));
        metaProperties.put("mr_id", Property.of(p -> p.keyword(KeywordProperty.of(k -> k))));
        metaProperties.put("repo_id", Property.of(p -> p.keyword(KeywordProperty.of(k -> k))));

        // 添加meta字段
        properties.put("meta", Property.of(p -> p.object(o -> o.properties(metaProperties))));

        // 创建索引
        CreateIndexResponse response = elasticsearchClient.indices()
                .create(c -> c
                        .index(TEST_INDEX_NAME)
                        .mappings(m -> m.properties(properties))
                );

        // assertTrue(response.acknowledged(), "索引创建应当成功");
        log.info("索引 {} 创建成功", TEST_INDEX_NAME);
    }

    /**
     * 测试插入数据
     */
    @Test
    public void testInsertData() throws IOException {
        // 确保索引存在
        boolean exists = elasticsearchClient.indices()
                .exists(ExistsRequest.of(r -> r.index(TEST_INDEX_NAME)))
                .value();

        if (!exists) {
            testCreateIndex(); // 如果索引不存在，先创建索引
        }

        // 创建批量请求
        BulkRequest.Builder br = new BulkRequest.Builder();

        // 准备测试数据
        for (int i = 0; i < 1; i++) {
            Map<String, Object> document = new HashMap<>();
            String id = UUID.randomUUID().toString();
            document.put("id", id);
            document.put("cmt_rule", "实现细节不可见，尤其是对于该方法" + i);
            document.put("create_at", System.currentTimeMillis());
            document.put("language", "java");

            Map<String, String> meta = new HashMap<>();
            meta.put("tag", "test-tag-" + i);
            meta.put("cmt_id", "cmt-" + i);
            meta.put("mr_id", "mr-" + i);
            meta.put("repo_id", "repo-" + i);
            document.put("meta", meta);

            br.operations(op -> op
                    .index(idx -> idx
                            .index(TEST_INDEX_NAME)
                            .id(id)
                            .document(document)
                    )
            );

            log.info("准备测试数据: {}", document);
        }

        // 执行批量插入
        BulkResponse response = elasticsearchClient.bulk(br.build());

        // assertFalse(response.errors(), "批量插入不应有错误");
        log.info("批量插入成功，文档数量: {}", response.items().size());

        // 等待索引刷新
        elasticsearchClient.indices().refresh(r -> r.index(TEST_INDEX_NAME));
    }

    /**
     * 测试查询数据
     */
    @Test
    public void testQueryData() throws IOException {
        // 确保有数据可查询
        testInsertData(); // 先插入数据

        // 等待索引刷新
        elasticsearchClient.indices().refresh(r -> r.index(TEST_INDEX_NAME));

        // 构建查询
        Query query = Query.of(q -> q
                .match(MatchQuery.of(m -> m
                        .field("cmt_rule")
                        .query("代码中调用了`isValidMongoIDWithOutComma`和`parseIDs`方法来处理用户输入的`keyword`，但这些方法的实现细节不可见。如果这些方法没有正确验证和清理用户输入，可能会导致NoSQL注入攻击。MongoDB的查询是基于JSON的，恶意构造的输入可能会改变查询的结构或行为。\n\n例如，如果`parseIDs`方法简单地按逗号分割字符串而不进行充分的验证和转义，攻击者可能会注入特殊字符或操作符，从而执行未授权的查询操作。\n\n建议确保这些方法对用户输入进行严格的验证，只接受有效的MongoDB ID格式，并在转换过程中拒绝任何可能的恶意输入。")
                ))
        );

        // 执行搜索
        SearchRequest request = SearchRequest.of(r -> r
                .index(TEST_INDEX_NAME)
                .query(query)
        );

        SearchResponse<Map> response = elasticsearchClient.search(request, Map.class);

        // 打印查询结果
        log.info("查询结果总数: {}", response.hits().total().value());

        List<Hit<Map>> hits = response.hits().hits();
        // assertTrue(hits.size() > 0, "应该返回至少一条记录");

        for (Hit<Map> hit : hits) {
            Map<String, Object> source = hit.source();
            Double score = hit.score();

            log.info("文档ID: {}, 得分: {}, 内容: {}", hit.id(), score, source);

            // 验证文档得分
            // assertTrue(score > 0, "搜索结果得分应该大于");

            // 验证文档内容
            // assertTrue(source.containsKey("cmt_rule"), "文档应包含cmt_rule字段");
            // assertTrue(source.containsKey("create_at"), "文档应包含create_at字段");
            // assertTrue(source.containsKey("language"), "文档应包含language字段");
            // assertTrue(source.containsKey("meta"), "文档应包含meta字段");

            // 检查cmt_rule字段内容
            String cmtRule = source.get("cmt_rule").toString();
            // assertTrue(cmtRule.contains("负向评论"), "cmt_rule字段应包含'负向评论'文本");
        }
    }
}
