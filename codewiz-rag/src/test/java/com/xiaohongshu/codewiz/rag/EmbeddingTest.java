package com.xiaohongshu.codewiz.rag;

import java.util.List;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.xiaohongshu.codewiz.core.client.EmbeddingClient;

/**
 * <AUTHOR>
 * @date 2025/2/26 20:17
 */
public class EmbeddingTest extends SpringBaseTest {

    @Autowired
    private EmbeddingClient embeddingClient;

    @Test
    public void testEmbedding() {
        List<Float> test = embeddingClient.getBgeEmbeddingVector(
                "38     public int updateSelective(AppMsgFailDO appMsgFailDO){\n39         AppMsgFailDO appMsg = selectByMsgId(appMsgFailDO.getMsgId());\n40         if (Objects.isNull(appMsg)){\n41             return 0;\n42         }\n43         appMsgFailDO.setUpdateTime(new Date());\n44         appMsgFailDO.setId(appMsg.getId());\n45         appMsgFailDO.setCreateTime(appMsg.getCreateTime());\n46         return appMsgFailMapper.updateByPrimaryKey(appMsgFailDO);\n47     }");
        System.out.println(test);
    }
}
