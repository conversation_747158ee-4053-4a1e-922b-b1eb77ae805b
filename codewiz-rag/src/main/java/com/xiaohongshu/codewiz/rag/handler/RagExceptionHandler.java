package com.xiaohongshu.codewiz.rag.handler;

import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import com.xiaohongshu.codewiz.core.entity.common.SingleResponse;
import com.xiaohongshu.codewiz.core.exception.BizException;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2025/3/1 10:00
 */
@Slf4j
@ControllerAdvice
public class RagExceptionHandler {

    @ExceptionHandler(BizException.class)
    @ResponseBody
    public SingleResponse<?> handleRagServerException(BizException e) {
        log.error("RagServerException: ", e);
        return SingleResponse.buildFailure(String.valueOf(e.getStatus()), e.getMessage());
    }
} 