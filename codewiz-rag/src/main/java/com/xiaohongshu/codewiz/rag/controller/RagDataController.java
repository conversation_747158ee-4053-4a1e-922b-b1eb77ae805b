package com.xiaohongshu.codewiz.rag.controller;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.xiaohongshu.codewiz.core.entity.common.SingleResponse;
import com.xiaohongshu.codewiz.core.entity.rag.RagDataAddRequest;
import com.xiaohongshu.codewiz.core.entity.rag.RagDataQueryRequest;
import com.xiaohongshu.codewiz.core.entity.rag.RagDataQueryResponse;
import com.xiaohongshu.codewiz.core.entity.rag.RagDocument;
import com.xiaohongshu.codewiz.core.service.milvus.CaseMilvusService;
import com.xiaohongshu.codewiz.core.service.rag.RagDataDriver;

/**
 * <AUTHOR>
 * @date 2025/2/25 19:11
 */
@RestController
@RequestMapping("/rag/data")
public class RagDataController {

    @Resource
    private CaseMilvusService caseMilvusService;
    @Value("${rag.collection.cr}")
    private String crCollection;
    @Autowired
    private RagDataDriver ragDataDriver;

    @PostMapping("/query")
    public SingleResponse<RagDataQueryResponse<RagDocument>> query(@RequestBody @Validated RagDataQueryRequest request) {
        return SingleResponse.of(ragDataDriver.queryData(request));
    }

    // add post
    @PostMapping("/add")
    public SingleResponse add(@RequestBody @Validated RagDataAddRequest request) {

        return SingleResponse.of(ragDataDriver.addData(request));
    }

    @PostMapping("/create/cr")
    public SingleResponse createCollection() {
        caseMilvusService.createCaseCollection(crCollection);
        return SingleResponse.buildSuccess();
    }


}
