package com.xiaohongshu.codewiz.rag.open;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.xiaohongshu.codewiz.core.entity.common.SingleResponse;
import com.xiaohongshu.codewiz.core.entity.rag.open.BizSearchReq;
import com.xiaohongshu.codewiz.core.entity.rag.open.BizSearchResp;
import com.xiaohongshu.codewiz.core.service.rag.open.BizSearchService;

/**
 * <AUTHOR>
 * @date 2025/3/24 15:44
 */
@RestController
@RequestMapping("/rag/data/open")
public class BizSearchController {

    @Resource
    private BizSearchService bizSearchService;

    @PostMapping("/search")
    public SingleResponse<BizSearchResp> search(@RequestBody BizSearchReq req) {
        return SingleResponse.of(bizSearchService.search(req));
    }
}
