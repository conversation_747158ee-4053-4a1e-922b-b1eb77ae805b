package com.xiaohongshu.codewiz.rag.open;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.xiaohongshu.codewiz.core.entity.common.SingleResponse;
import com.xiaohongshu.codewiz.core.entity.rag.open.BizDocumentChunkDeleteReq;
import com.xiaohongshu.codewiz.core.entity.rag.open.BizDocumentChunkUpdateReq;
import com.xiaohongshu.codewiz.core.entity.rag.open.BizDocumentChunkUpdateResp;
import com.xiaohongshu.codewiz.core.entity.rag.open.BizDocumentChunksResp;
import com.xiaohongshu.codewiz.core.entity.rag.open.BizDocumentCreateOrUpdateReq;
import com.xiaohongshu.codewiz.core.entity.rag.open.BizDocumentDeleteReq;
import com.xiaohongshu.codewiz.core.entity.rag.open.BizDocumentListResp;
import com.xiaohongshu.codewiz.core.entity.rag.open.BizDocumentUpdateResp;
import com.xiaohongshu.codewiz.core.entity.rag.open.BizDocumentUploadResp;
import com.xiaohongshu.codewiz.core.service.rag.open.BizDocumentService;

/**
 * <AUTHOR>
 * @date 2025/3/24 15:44
 */
@RestController
@RequestMapping("/rag/data/open/document")
public class BizDocumentController {

    @Resource
    private BizDocumentService bizDocumentService;

    @PostMapping("/upload")
    public SingleResponse<BizDocumentUploadResp> upload(@RequestBody BizDocumentCreateOrUpdateReq req) {
        return SingleResponse.of(bizDocumentService.uploadDocument(req));
    }

    @PostMapping("/update")
    public SingleResponse<BizDocumentUpdateResp> update(@RequestBody BizDocumentCreateOrUpdateReq req) {
        return SingleResponse.of(bizDocumentService.updateDocument(req));
    }

    @PostMapping("/delete")
    public SingleResponse<Void> delete(@RequestBody BizDocumentDeleteReq req) {
        bizDocumentService.deleteDocument(req);
        return SingleResponse.ok();
    }

    @GetMapping("/list")
    public SingleResponse<BizDocumentListResp> list(@RequestParam("biz_id") String bizId,
                                                    @RequestParam("user_id") String userId,
                                                    @RequestParam("kb_id") String kbId,
                                                    @RequestParam(value = "page", required = false) Integer page,
                                                    @RequestParam(value = "page_size", required = false) Integer pageSize) {
        return SingleResponse.of(bizDocumentService.listDocuments(bizId, userId, kbId, page, pageSize));
    }

    @GetMapping("/chunks")
    public SingleResponse<BizDocumentChunksResp> chunks(@RequestParam("biz_id") String bizId,
                                                        @RequestParam("user_id") String userId,
                                                        @RequestParam("kb_id") String kbId,
                                                        @RequestParam("doc_id") String docId) {
        return SingleResponse.of(bizDocumentService.getDocumentChunks(bizId, userId, kbId, docId));
    }

    @PostMapping("/chunk/delete")
    public SingleResponse<Void> deleteChunk(@RequestBody BizDocumentChunkDeleteReq req) {
        bizDocumentService.deleteDocumentChunk(req);
        return SingleResponse.ok();
    }

    @PostMapping("/chunk/update")
    public SingleResponse<BizDocumentChunkUpdateResp> updateChunk(@RequestBody BizDocumentChunkUpdateReq req) {
        return SingleResponse.of(bizDocumentService.updateDocumentChunk(req));
    }
}
