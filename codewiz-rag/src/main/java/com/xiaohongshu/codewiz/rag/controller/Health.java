package com.xiaohongshu.codewiz.rag.controller;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2025/2/25 17:55
 */
@RestController
@RequestMapping("/api/health")
public class Health {

    @Value("${rag.collection.cr}")
    private String crCollection;

    @GetMapping
    public String health() {
        return "ok";
    }

    @GetMapping("/crCollection")
    public String crCollection() {
        return crCollection;
    }
}
