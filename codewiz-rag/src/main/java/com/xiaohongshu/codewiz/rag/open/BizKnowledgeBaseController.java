package com.xiaohongshu.codewiz.rag.open;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.xiaohongshu.codewiz.core.entity.common.SingleResponse;
import com.xiaohongshu.codewiz.core.entity.rag.open.BizKnowledgeCreateReq;
import com.xiaohongshu.codewiz.core.entity.rag.open.BizKnowledgeCreateResp;
import com.xiaohongshu.codewiz.core.entity.rag.open.BizKnowledgeDeleteReq;
import com.xiaohongshu.codewiz.core.service.rag.open.BizKnowledgeBaseService;

/**
 * <AUTHOR>
 * @date 2025/3/24 15:44
 */
@RestController
@RequestMapping("/rag/data/open/knowledgebase")
public class BizKnowledgeBaseController {

    @Resource
    private BizKnowledgeBaseService bizKnowledgeBaseService;

    @PostMapping("/create")
    public SingleResponse<BizKnowledgeCreateResp> create(@RequestBody BizKnowledgeCreateReq bizKnowledge) {
        return SingleResponse.of(bizKnowledgeBaseService.createBizKnowledgeBase(bizKnowledge));
    }

    @PostMapping("/delete")
    public SingleResponse<String> delete(@RequestBody BizKnowledgeDeleteReq req) {
        bizKnowledgeBaseService.deleteBizKnowledgeBase(req);
        return SingleResponse.ok();
    }
}
