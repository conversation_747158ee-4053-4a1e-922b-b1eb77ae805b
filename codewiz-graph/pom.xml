<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.xiaohongshu</groupId>
        <artifactId>codewiz-server</artifactId>
        <version>${revision}</version>
    </parent>
    <artifactId>codewiz-graph</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.xiaohongshu</groupId>
            <artifactId>codewiz-core</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.xiaohongshu</groupId>
            <artifactId>tree-sitter-parser</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xiaohongshu</groupId>
            <artifactId>codewiz-tree-sitter-idl</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xiaohongshu</groupId>
            <artifactId>infra-framework-rpc-spring</artifactId>
        </dependency>
        <dependency>
            <groupId>io.github.java-diff-utils</groupId>
            <artifactId>java-diff-utils</artifactId>
            <version>4.15</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
