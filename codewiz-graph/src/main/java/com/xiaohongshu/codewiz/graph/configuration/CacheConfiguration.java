package com.xiaohongshu.codewiz.graph.configuration;

import java.util.concurrent.TimeUnit;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.xiaohongshu.codewiz.core.remote.GitlabMediator;
import com.xiaohongshu.codewiz.graph.client.GraphNodeS3Client;
import com.xiaohongshu.codewiz.graph.vo.ProjectGraphFile;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * Created on 2025/4/7
 */
@Slf4j
@Configuration
public class CacheConfiguration {
    @Bean(destroyMethod = "cleanUp")
    public LoadingCache<Long, ProjectGraphFile> projectGraphFileCache(@Autowired GraphNodeS3Client graphS3Service) {
        return Caffeine.newBuilder()
                .maximumSize(32)
                .expireAfterWrite(6, TimeUnit.HOURS) // 对应原来的timeToLiveExpiration(Duration.ofHours(6))
                .recordStats() // 记录缓存统计信息
                .build(projectId -> {
                    long start = System.currentTimeMillis();
                    ProjectGraphFile projectGraphFile = graphS3Service.getProjectGraphFile(projectId, null);
                    log.info("projectGraphFileCache, projectId: {}, cost: {}ms", projectId, System.currentTimeMillis() - start);
                    return projectGraphFile;
                });
    }

    @Bean(destroyMethod = "cleanUp")
    public LoadingCache<String, ProjectGraphFile> projectCommitGraphFileCache(@Autowired GraphNodeS3Client graphS3Service) {
        return Caffeine.newBuilder()
                .maximumSize(8)
                .expireAfterWrite(6, TimeUnit.HOURS) // 对应原来的timeToLiveExpiration(Duration.ofHours(6))
                .recordStats() // 记录缓存统计信息
                .build(key -> {
                    String[] split = key.split(":");
                    if (split.length != 2) {
                        return null;
                    }
                    Long projectId = Long.valueOf(split[0]);
                    String commitSha = split[1];
                    long start = System.currentTimeMillis();
                    ProjectGraphFile projectGraphFile = graphS3Service.getProjectGraphFile(projectId, commitSha);
                    log.info("projectCommitGraphFileCache, projectId: {}, commitSha: {}, cost: {}ms", projectId, commitSha,
                            System.currentTimeMillis() - start);
                    return projectGraphFile;
                });
    }

    @Bean(destroyMethod = "cleanUp")
    public LoadingCache<String, String> projectFileContentCache(@Autowired GitlabMediator gitlabMediator) {
        return Caffeine.newBuilder()
                .maximumSize(1024)
                .recordStats() // 记录缓存统计信息
                .build(key -> {
                    String[] split = key.split(":");
                    if (split.length != 3) {
                        return null;
                    }
                    Long projectId = Long.valueOf(split[0]);
                    String filePath = split[1];
                    String commitSha = split[2];
                    return gitlabMediator.loadFileContent(projectId, filePath, commitSha).orElse(null);
                });
    }
}
