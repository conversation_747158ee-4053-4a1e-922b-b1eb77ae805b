package com.xiaohongshu.codewiz.graph.utils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.commons.lang3.StringUtils;

import com.github.difflib.DiffUtils;
import com.github.difflib.patch.ChangeDelta;
import com.github.difflib.patch.Chunk;
import com.github.difflib.patch.Patch;

/**
 * <AUTHOR>
 * Created on 2025/7/2
 */
public class RedUnifiedDiffUtils {

    private static final Pattern UNIFIED_DIFF_CHUNK_REGEXP = Pattern
            .compile("^@@\\s+-(\\d+)(?:,(\\d+))?\\s+\\+(\\d+)(?:,(\\d+))?\\s+@@");
    private static final String NULL_FILE_INDICATOR = "/dev/null";

    /**
     * Parse the given text in unified format and creates the list of deltas for it.
     *
     * @param diff the text in unified format
     * @return the patch with deltas.
     */
    public static Patch<String> parseUnifiedDiff(List<String> diff) {
        boolean inPrelude = true;
        List<String[]> rawChunk = new ArrayList<>();
        Patch<String> patch = new Patch<>();

        int oldLn = 0;
        int newLn = 0;
        String tag;
        String rest;
        for (String line : diff) {
            // Skip leading lines until after we've seen one starting with '+++'
            if (inPrelude) {
                if (line.startsWith("+++")) {
                    inPrelude = false;
                }
                continue;
            }
            Matcher m = UNIFIED_DIFF_CHUNK_REGEXP.matcher(line);
            if (m.find()) {
                // Process the lines in the previous chunk
                processLinesInPrevChunk(rawChunk, patch, oldLn, newLn);
                // Parse the @@ header
                oldLn = m.group(1) == null ? 1 : Integer.parseInt(m.group(1));
                newLn = m.group(3) == null ? 1 : Integer.parseInt(m.group(3));

                if (oldLn == 0) {
                    oldLn = 1;
                }
                if (newLn == 0) {
                    newLn = 1;
                }
            } else {
                if (!line.isEmpty()) {
                    tag = line.substring(0, 1);
                    rest = line.substring(1);
                    if (" ".equals(tag) || "+".equals(tag) || "-".equals(tag)) {
                        rawChunk.add(new String[]{tag, rest});
                    }
                } else {
                    rawChunk.add(new String[]{" ", ""});
                }
            }
        }

        // Process the lines in the last chunk
        processLinesInPrevChunk(rawChunk, patch, oldLn, newLn);

        return patch;
    }

    private static void processLinesInPrevChunk(List<String[]> rawChunk, Patch<String> patch, int oldLn, int newLn) {
        String tag;
        String rest;
        if (!rawChunk.isEmpty()) {
            List<String> oldChunkLines = new ArrayList<>();
            List<String> newChunkLines = new ArrayList<>();

            List<Integer> removePosition = new ArrayList<>();
            List<Integer> addPosition = new ArrayList<>();
            int removeNum = 0;
            int addNum = 0;
            for (String[] rawLine : rawChunk) {
                tag = rawLine[0];
                rest = rawLine[1];
                if (" ".equals(tag) || "-".equals(tag)) {
                    removeNum++;
                    oldChunkLines.add(rest);
                    if ("-".equals(tag)) {
                        removePosition.add(oldLn - 1 + removeNum);
                    }
                }
                if (" ".equals(tag) || "+".equals(tag)) {
                    addNum++;
                    newChunkLines.add(rest);
                    if ("+".equals(tag)) {
                        addPosition.add(newLn - 1 + addNum);
                    }
                }
            }
            patch.addDelta(new ChangeDelta<>(new Chunk<>(
                    oldLn - 1, oldChunkLines, removePosition), new Chunk<>(
                    newLn - 1, newChunkLines, addPosition)));
            rawChunk.clear();
        }
    }

    public static String generateUnifiedDiff(String oldPath, String newPath, String diff) {
        return "--- a/" + Optional.ofNullable(oldPath).orElse(NULL_FILE_INDICATOR) + "\n"
                + "+++ b/" + Optional.ofNullable(newPath).orElse(NULL_FILE_INDICATOR) + "\n"
                + Optional.ofNullable(diff).orElse(StringUtils.EMPTY);
    }

    /**
     * 将unified diff应用到原始内容上，返回patch后的内容。
     * diff为null或diff内容为空时直接返回原内容。
     *
     * @param oldContent  原始内容
     * @param unifiedDiff unified diff
     * @return 应用diff后的内容
     */
    public static String contentPatchDiff(String oldContent, String unifiedDiff) {
        if (StringUtils.isBlank(unifiedDiff)) {
            return oldContent;
        }
        List<String> originalLines =
                oldContent == null ? Collections.emptyList() : Arrays.asList(oldContent.split("\\r?\\n", -1));
        List<String> diffLines = unifiedDiff.isEmpty() ? Collections.emptyList() : Arrays.asList(unifiedDiff.split("\\r?\\n"));
        try {
            Patch<String> patch = parseUnifiedDiff(diffLines);
            List<String> patchedLines = DiffUtils.patch(originalLines, patch);
            return String.join("\n", patchedLines);
        } catch (Exception e) {
            return oldContent;
        }
    }
}
