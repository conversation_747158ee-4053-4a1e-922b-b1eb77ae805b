package com.xiaohongshu.codewiz.graph.rpc;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.xiaohongshu.codewiz.graph.service.GraphService;
import com.xiaohongshu.codewiz.ts.graph.dto.ProjectCallGraphRequest;
import com.xiaohongshu.codewiz.ts.graph.dto.ProjectCallGraphResponse;
import com.xiaohongshu.codewiz.ts.graph.service.CodeWizGraphService;
import com.xiaohongshu.infra.rpc.base.Context;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * Created on 2025/3/15
 */
@Slf4j
@Service
public class CodeWizGraphRpcServiceImpl implements CodeWizGraphService.Iface {
    @Resource
    private GraphService graphService;

    @Override
    public ProjectCallGraphResponse queryCallGraph(Context context, ProjectCallGraphRequest request) {
        return graphService.queryCallGraph(request, true);
    }

    @Override
    public void ping() {
    }
}
