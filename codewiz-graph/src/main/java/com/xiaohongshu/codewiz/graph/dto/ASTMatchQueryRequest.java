package com.xiaohongshu.codewiz.graph.dto;

import java.util.List;

import javax.validation.constraints.NotBlank;

import com.xiaohongshu.codewiz.graph.dto.ast.Range;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * Created on 2025/4/24
 */
@Data
public class ASTMatchQueryRequest {
    @NotBlank
    @Schema(description = "源代码")
    private String source;
    @NotBlank
    @Schema(description = "源代码语言", example = "java")
    private String extension;
    @Schema(description = "匹配标签", example = "definition.class")
    private List<String> tags;
    @Schema(description = "匹配范围")
    private List<Range> ranges;
}
