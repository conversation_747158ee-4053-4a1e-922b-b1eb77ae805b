package com.xiaohongshu.codewiz.graph.dto.ast;

import java.util.List;

import lombok.Data;

/**
 * <AUTHOR>
 * Created on 2025/5/13
 */
@Data
public class CallGraphQueryResponse {
    private List<String> targetNodeIds;
    private List<GraphNode> nodes;

    @Data
    public static class GraphNode {
        private String id;
        private String signature;
        private String type;
        private String filePath;
        private Range range;
        private String codeSnippet;
        private List<String> predecessorIds;
        private List<String> successorIds;
        private List<ReferenceRange> predecessorReferences;
        private List<ReferenceRange> successorReferences;
        private String tag;
    }

    @Data
    public static class ReferenceRange {
        private String id;
        private Range range;
        private List<String> imports;
    }
}
