package com.xiaohongshu.codewiz.graph.convertor;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import com.xiaohongshu.codewiz.graph.dto.ast.QueryMatch;

/**
 * <AUTHOR>
 * Created on 2025/4/24
 */
@Mapper(uses = QueryCaptureMapper.class)
public interface QueryMatchMapper {
    QueryMatchMapper INSTANCE = Mappers.getMapper(QueryMatchMapper.class);

    QueryMatch fromIdlQueryMatch(com.xiaohongshu.codewiz.ts.parser.domain.QueryMatch queryCapture);

    com.xiaohongshu.codewiz.ts.parser.domain.QueryMatch toIdlCapture(QueryMatch queryCapture);
}
