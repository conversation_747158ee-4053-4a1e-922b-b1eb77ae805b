package com.xiaohongshu.codewiz.graph.service;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.ExecutorService;
import java.util.function.BiFunction;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.gitlab4j.api.GitLabApiException;
import org.gitlab4j.api.models.CompareResults;
import org.springframework.stereotype.Service;

import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.xiaohongshu.codewiz.core.constant.enums.OrderDirectionEnum;
import com.xiaohongshu.codewiz.core.entity.graph.CallGraphConfig;
import com.xiaohongshu.codewiz.core.entity.graph.CallGraphConfig.SignatureRule;
import com.xiaohongshu.codewiz.core.remote.GitlabMediator;
import com.xiaohongshu.codewiz.core.utils.CompletableFutureUtil;
import com.xiaohongshu.codewiz.graph.convertor.GraphNodeMapper;
import com.xiaohongshu.codewiz.graph.dto.ast.GraphNodeDTO;
import com.xiaohongshu.codewiz.graph.dto.ast.GraphNodeFileDTO;
import com.xiaohongshu.codewiz.graph.utils.RedUnifiedDiffUtils;
import com.xiaohongshu.codewiz.graph.vo.FileDiffPatchVO;
import com.xiaohongshu.codewiz.graph.vo.GitLabCompareResult;
import com.xiaohongshu.codewiz.graph.vo.GraphQueryResult;
import com.xiaohongshu.codewiz.graph.vo.GraphVO;
import com.xiaohongshu.codewiz.graph.vo.ProjectGraphFile;
import com.xiaohongshu.codewiz.ts.graph.domain.Graph;
import com.xiaohongshu.codewiz.ts.graph.domain.GraphNode;
import com.xiaohongshu.codewiz.ts.graph.domain.Node;
import com.xiaohongshu.codewiz.ts.graph.dto.Diff;
import com.xiaohongshu.codewiz.ts.graph.dto.FileRange;
import com.xiaohongshu.codewiz.ts.graph.dto.Point;
import com.xiaohongshu.codewiz.ts.graph.dto.ProjectCallGraphRequest;
import com.xiaohongshu.codewiz.ts.graph.dto.ProjectCallGraphResponse;
import com.xiaohongshu.codewiz.ts.graph.dto.Range;
import com.xiaohongshu.codewiz.ts.graph.dto.Refence;
import com.xiaohongshu.codewiz.ts.graph.utils.GraphUtils;
import com.xiaohongshu.codewiz.ts.parser.enums.GraphLanguageEnum;
import com.xiaohongshu.codewiz.ts.parser.enums.Language;
import com.xiaohongshu.codewiz.ts.parser.factory.LanguageMappingFactory;
import com.xiaohongshu.codewiz.ts.parser.parser.GraphNodeParser;
import com.xiaohongshu.codewiz.ts.parser.utils.FileUtils;
import com.xiaohongshu.infra.rpc.base.Result;
import com.xiaohongshu.infra.utils.ObjectMapperUtils;
import com.xiaohongshu.xray.logging.LogTags;

import lombok.extern.slf4j.Slf4j;
import redis.clients.jedis.Jedis;

/**
 * <AUTHOR>
 * Created on 2025/5/8
 */
@Slf4j
@Service
public class GraphService {
    @Resource
    private GitlabMediator gitlabMediator;
    @Resource
    private PipelineService pipelineService;
    @Resource(name = "codewizJedis")
    private Jedis jedis;
    @Resource
    private LoadingCache<Long, ProjectGraphFile> projectGraphFileCache;
    @Resource
    private LoadingCache<String, ProjectGraphFile> projectCommitGraphFileCache;
    @Resource
    private LoadingCache<String, String> projectFileContentCache;
    @Resource
    private ExecutorService callGraphExecutor;
    @Resource
    private ExecutorService astParseExecutor;
    @ApolloJsonValue("${call_graph.config}")
    private CallGraphConfig callGraphConfig;

    private static final GraphNodeParser GRAPH_NODE_PARSER = new GraphNodeParser();

    public ProjectCallGraphResponse queryCallGraph(ProjectCallGraphRequest request, boolean triggerPipeline) {
        log.info("queryCallGraph request:{}", ObjectMapperUtils.toJSON(request));
        long projectId = request.getProjectId();
        ProjectCallGraphResponse response = new ProjectCallGraphResponse();
        // 0. 校验入参
        if (request.getFileRanges().isEmpty()) {
            return response.setResult(new Result().setSuccess(false).setMessage("Empty FileRange"));
        }
        if (request.getFileRanges().stream().map(FileRange::getFilePath).noneMatch(this::canBeParsed)) {
            return response.setResult(new Result().setSuccess(false).setMessage("Unsupported FileType"));
        }
        // 1. 触发流水线更新图
        if (triggerPipeline) {
            triggerPipelineIfNecessary(projectId);
        }
        // 2. 获取项目图
        GraphVO graphVO = getGraphVO(projectId, request.getCommitHash(), request.getDiffs());
        Graph graph = graphVO.getGraph();
        String headCommitSha = graphVO.getHeadCommitSha();
        GitLabCompareResult gitLabCompareResult = graphVO.getGitLabCompareResult();
        // 3. 查询图结果
        GraphQueryResult graphQueryResult = queryGraph(graph, request);
        List<String> targetNodeIds = graphQueryResult.getTargetNodeIds();
        if (targetNodeIds == null || targetNodeIds.isEmpty()) {
            log.error("[Nodes Not Found]");
            return response.setResult(new Result().setSuccess(false).setMessage("Nodes Not Found"));
        }
        Set<GraphNode> resultNodes = graphQueryResult.getResultNodes();

        // 4. 构建响应结果
        List<com.xiaohongshu.codewiz.ts.graph.dto.GraphNode> graphNodeDtos =
                buildNodeRespList(projectId, headCommitSha, resultNodes, gitLabCompareResult.getFilePathContentMap(),
                        this::buildGraphNodeDtos);
        if (graphNodeDtos == null) {
            log.error("[Build Resp Error]");
            return response.setResult(new Result().setSuccess(false).setMessage("Build Resp Error"));
        }
        return response.setNodes(graphNodeDtos).setTargetNodeIds(targetNodeIds).setResult(new Result().setSuccess(true));
    }

    private boolean canBeParsed(String filePath) {
        String extension = Optional.ofNullable(FileUtils.getFileExtension(filePath)).map(String::toLowerCase).orElse(null);
        Language language = LanguageMappingFactory.getGraphLanguage(extension);
        return language != null;
    }

    private void triggerPipelineIfNecessary(Long projectId) {
        String redisKey = StringUtils.joinWith(":", "codewiz-graph", "project-graph", projectId);
        String locked = jedis.set(redisKey, String.valueOf(System.currentTimeMillis()), "NX", "EX", 12 * 60 * 60);
        if (StringUtils.equalsAnyIgnoreCase(locked, "OK")) {
            log.info(LogTags.of(Map.of("projectId", projectId)), "pipelineRun");
            pipelineService.triggerProjectGraphPipeline(projectId);
        }
    }

    private ProjectGraphFile fetchProjectGraphFile(Long projectId, String commitSha) {
        Transaction transaction = Cat.newTransaction("CodeWizGraphService", "RemoteHistoryFetch");
        ProjectGraphFile projectGraphFile = null;
        try {
            projectGraphFile = projectCommitGraphFileCache.get(StringUtils.joinWith(":", projectId, commitSha));
            if (projectGraphFile == null) {
                projectGraphFile = projectGraphFileCache.get(projectId);
            } else {
                log.info("[projectCommitGraphFileCache hit] projectId:{} commitSha:{}", projectId, commitSha);
            }
            transaction.setSuccessStatus();
        } catch (Exception e) {
            transaction.setStatus(e);
            log.error("RemoteHistoryFetch error", e);
            Cat.logError(e);
        } finally {
            log.info(LogTags.of(Map.of(
                    "projectId", projectId,
                    "nodeSize", Optional.ofNullable(projectGraphFile).map(ProjectGraphFile::getNodes).map(List::size).orElse(0),
                    "durationInMicros", transaction.getDurationInMicros(),
                    "success", transaction.isSuccess()
            )), "RemoteHistoryFetch");
            transaction.complete();
        }
        return projectGraphFile;
    }

    private GitLabCompareResult compareProjectGraphFile(Long projectId, String baseCommitSha, String headCommitSha, List<Diff> diffs) {
        Transaction transaction = Cat.newTransaction("CodeWizGraphService", "GitLabCommitCompare");
        ConcurrentMap<String, Optional<String>> filePathContentMap = new ConcurrentHashMap<>();
        Set<String> deletedFilePaths = new HashSet<>();
        GitLabCompareResult gitLabCompareResult = new GitLabCompareResult(deletedFilePaths, filePathContentMap);
        // 如果baseCommitSha和headCommitSha相等，同时diffs为空，则不需要比较
        if (StringUtils.equals(baseCommitSha, headCommitSha) && CollectionUtils.isEmpty(diffs)) {
            return gitLabCompareResult;
        }
        try {
            diffFilePathMap(projectId, baseCommitSha, headCommitSha, deletedFilePaths, filePathContentMap, diffs);
        } catch (Exception e) {
            log.error("GitLabCommitCompare error", e);
            transaction.setStatus(e);
            Cat.logError(e);
            return null;
        } finally {
            log.info(LogTags.of(Map.of(
                    "projectId", projectId,
                    "baseCommitSha", baseCommitSha,
                    "headCommitSha", headCommitSha,
                    "addedSize", filePathContentMap.size(),
                    "deletedSize", deletedFilePaths.size(),
                    "durationInMicros", transaction.getDurationInMicros(),
                    "success", transaction.isSuccess()
            )), "GitLabCompare");
            transaction.complete();
        }
        return gitLabCompareResult;
    }

    /**
     * 根据项目语言和节点数量计算最小签名长度
     *
     * @param primaryLanguage 项目主要语言
     * @param nodeCount       节点数量
     * @return 最小签名长度，如果不需要限制则返回null
     */
    private Integer calculateMinSignatureLength(String primaryLanguage, int nodeCount) {
        return Optional.ofNullable(callGraphConfig)
                .map(CallGraphConfig::getNodeSignatureStrategy)
                .flatMap(strategy -> {
                    // 如果语言在排除列表中，返回空
                    boolean isExcluded = Optional.ofNullable(primaryLanguage)
                            .map(lang -> Optional.ofNullable(strategy.getExcludeLanguages())
                                    .map(excludes -> excludes.contains(lang))
                                    .orElse(false))
                            .orElse(false);

                    if (isExcluded) {
                        return Optional.empty();
                    }

                    // 查找匹配的规则
                    return Optional.ofNullable(strategy.getRules())
                            .flatMap(rules -> rules.stream()
                                    .filter(rule -> nodeCount >= rule.getStartNodeCount() && nodeCount < rule.getEndNodeCount())
                                    .findFirst()
                                    .map(SignatureRule::getMinSignatureLength));
                })
                .orElse(null);
    }

    private Graph buildIncrementalGraph(ProjectGraphFile projectGraphFile, GitLabCompareResult compareResult) {
        Transaction transaction = Cat.newTransaction("CodeWizGraphService", "GraphBuild");
        Map<String, Optional<String>> filePathContentMap = compareResult.getFilePathContentMap();
        Set<String> filePaths = filePathContentMap.keySet();
        Set<String> deletedFilePaths = compareResult.getDeletedFilePaths();
        Long projectId = projectGraphFile.getProjectId();
        boolean enableCallGraph = callGraphConfig.isProjectInGrayList(projectId);
        Graph graph = null;
        try {
            // 转换成可变列表
            List<Node> nodes = new ArrayList<>(projectGraphFile.getNodes());
            // 删除删除/修改旧文件
            nodes.removeIf(node -> filePaths.contains(node.getFilePath()));
            nodes.removeIf(node -> deletedFilePaths.contains(node.getFilePath()));
            // 并行添加新增/修改新文件
            List<CompletableFuture<List<Node>>> futures = new ArrayList<>();
            for (Map.Entry<String, Optional<String>> entry : filePathContentMap.entrySet()) {
                String filePath = entry.getKey();
                String content = entry.getValue().orElse(null);
                if (StringUtils.isEmpty(content)) {
                    continue;
                }
                futures.add(CompletableFuture.supplyAsync(() -> {
                    try {
                        return GRAPH_NODE_PARSER.parse(filePath, content, enableCallGraph);
                    } catch (Exception e) {
                        log.error("ast parse {} error", filePath, e);
                        return Collections.emptyList();
                    }
                }, astParseExecutor));
            }
            nodes.addAll(CompletableFutureUtil.syncGetJoinResult(futures).stream().filter(Objects::nonNull).flatMap(Collection::stream)
                    .collect(Collectors.toList()));
            // 图构建
            String primaryLanguage = gitlabMediator.getPrimaryLanguage(projectId).orElse(null);
            Integer minSignatureLength = calculateMinSignatureLength(primaryLanguage, nodes.size());
            graph = Graph.fromNodeList(nodes, minSignatureLength);
            log.info(LogTags.of(Map.of(
                    "projectId", projectId,
                    "nodeSize", nodes.size(),
                    "edgeCount", graph.getEdgeCount(),
                    "primaryLanguage", Optional.ofNullable(primaryLanguage).orElse(StringUtils.EMPTY),
                    "filterMinSignatureLength", Optional.ofNullable(minSignatureLength).orElse(-1),
                    "nodesAvgSignatureLength", nodes.stream().map(Node::getSignature).collect(Collectors.averagingInt(String::length))
            )), "GraphBuild");
        } catch (Exception e) {
            log.error("GraphBuild error", e);
            transaction.setStatus(e);
            Cat.logError(e);
        } finally {
            log.info(LogTags.of(Map.of(
                    "parseFileSize", filePathContentMap.size(),
                    "deletedFileSize", deletedFilePaths.size(),
                    "durationInMicros", transaction.getDurationInMicros(),
                    "success", transaction.isSuccess()
            )), "GraphBuild");
            transaction.complete();
        }
        return graph;
    }

    private GraphQueryResult queryGraph(Graph graph, ProjectCallGraphRequest request) {
        Transaction transaction = Cat.newTransaction("CodeWizGraphService", "GraphQuery");
        GraphQueryResult graphQueryResult = new GraphQueryResult();
        Set<GraphNode> resultNodes = new HashSet<>();
        List<String> targetNodeIds = new ArrayList<>();
        graphQueryResult.setResultNodes(resultNodes);
        graphQueryResult.setTargetNodeIds(targetNodeIds);
        try {
            Set<GraphNode> targetNodes = new HashSet<>();
            List<FileRange> fileRanges = request.getFileRanges();
            for (FileRange fileRange : fileRanges) {
                String filePath = fileRange.getFilePath();
                if (fileRange.isSetRanges()) {
                    for (Range range : fileRange.getRanges()) {
                        Point startPoint = range.getStartPoint();
                        Point endPoint = range.getEndPoint();
                        targetNodes.addAll(
                                graph.query(filePath, startPoint.getRow(), startPoint.getColumn(), endPoint.getRow(),
                                        endPoint.getColumn()));
                    }
                } else {
                    targetNodes.addAll(graph.query(filePath));
                }
            }
            targetNodeIds.addAll(targetNodes.stream().map(GraphNode::getId).collect(Collectors.toList()));
            resultNodes.addAll(targetNodes);
            Integer maxNodeReferenceCount = callGraphConfig.getMaxNodeReferenceCount();
            for (GraphNode targetNode : targetNodes) {
                Set<GraphNode> refNodes = new HashSet<>();
                GraphUtils.traverse(targetNode, request.getPredecessorDepth(), false, refNodes::add);
                GraphUtils.traverse(targetNode, request.getSuccessorDepth(), true, refNodes::add);
                if (maxNodeReferenceCount == null || refNodes.size() <= maxNodeReferenceCount) {
                    // 不限制引用数量或者引用数量小于限制
                    resultNodes.addAll(refNodes);
                } else {
                    Node node = targetNode.getNode();
                    log.warn(
                            "GraphQuery node reference count exceeds limit, projectId:{}, filePath:{}, signature:{}, range:{}, "
                                    + "referenceCount:{}", request.getProjectId(), node.getFilePath(), node.getSignature(),
                            node.getRange(), refNodes.size());
                }
            }
        } catch (Exception e) {
            log.error("GraphQuery error", e);
            transaction.setStatus(e);
            Cat.logError(e);
        } finally {
            log.info(LogTags.of(Map.of(
                    "targetNodeIds", targetNodeIds,
                    "resultNodeSize", resultNodes.size(),
                    "durationInMicros", transaction.getDurationInMicros(),
                    "success", transaction.isSuccess()
            )), "GraphQuery");
            transaction.complete();
        }
        return graphQueryResult;
    }

    /**
     * 计算baseCommit到headCommit再叠加本地diff后的文件增删改情况。
     * 1. 先用gitlab接口获取base和head之间的diff，记录文件的新增、删除、重命名。
     * 2. 再应用本地diff，进一步更新文件的状态（如本地新增、删除、重命名、修改）。
     * 3. 最终得到deletedFilePaths（被删除文件）和sourceMap（新增/修改文件及其最新内容）。
     *
     * @param projectId         项目ID
     * @param baseCommitSha     基准commit
     * @param headCommitSha     目标commit
     * @param deletedFilePaths  输出：被删除的文件路径集合
     * @param sourceMap         输出：每个文件最新内容（已应用所有diff）
     * @param diffs             本地diff列表（unified diff格式）
     */
    private void diffFilePathMap(Long projectId, String baseCommitSha, String headCommitSha, Collection<String> deletedFilePaths,
                                 Map<String, Optional<String>> sourceMap, List<Diff> diffs) throws GitLabApiException {
        // 1. 先获取base和head之间的diff，初步构建fileMap
        CompareResults compareResults = gitlabMediator.loadMrSnapshotInfo(projectId, baseCommitSha, headCommitSha);
        Map<String, FileDiffPatchVO> fileMap = new HashMap<>();
        compareResults.getDiffs().forEach(diff -> {
            if (diff.getDeletedFile()) {
                // 远端被删除
                putOrRemoveFileDiff(diff.getOldPath(), new FileDiffPatchVO(false, null, null), fileMap);
            } else if (diff.getNewFile()) {
                // 远端新增
                putOrRemoveFileDiff(diff.getNewPath(), new FileDiffPatchVO(true, null, diff.getNewPath()), fileMap);
            } else if (diff.getRenamedFile()) {
                // 远端重命名：新文件内容来源于oldPath，旧文件被删除
                putOrRemoveFileDiff(diff.getOldPath(), new FileDiffPatchVO(false, null, null), fileMap);
                putOrRemoveFileDiff(diff.getNewPath(), new FileDiffPatchVO(true, null, diff.getNewPath()), fileMap);
            } else {
                // 远端普通修改
                putOrRemoveFileDiff(diff.getNewPath(), new FileDiffPatchVO(true, null, diff.getNewPath()), fileMap);
            }
        });
        // 2. 叠加本地diff，进一步更新fileMap
        if (CollectionUtils.isNotEmpty(diffs)) {
            diffs.forEach(diff -> {
                if (diff.getOldPath() != null && diff.getNewPath() == null) { // 本地删除
                    putOrRemoveFileDiff(diff.getOldPath(), new FileDiffPatchVO(false, null, null), fileMap);
                } else if (diff.getOldPath() == null && diff.getNewPath() != null) { // 本地新增
                    // headCommitFilePath为null，表示本地新建
                    putOrRemoveFileDiff(diff.getNewPath(), new FileDiffPatchVO(true, diff, null), fileMap);
                } else if (diff.getNewPath() != null && diff.getOldPath() != null) {
                    if (diff.getNewPath().equals(diff.getOldPath())) { // 本地修改
                        putOrRemoveFileDiff(diff.getNewPath(), new FileDiffPatchVO(true, diff, diff.getNewPath()), fileMap);
                    } else { // 本地重命名
                        // 新文件内容来源于oldPath+diff，旧文件被删除
                        putOrRemoveFileDiff(diff.getNewPath(), new FileDiffPatchVO(true, diff, diff.getOldPath()), fileMap);
                        putOrRemoveFileDiff(diff.getOldPath(), new FileDiffPatchVO(false, null, null), fileMap);
                    }
                }
            });
        }
        // 3. 统计被删除的文件
        deletedFilePaths.addAll(fileMap.entrySet().stream().filter(entry -> !entry.getValue().isExist()).map(Map.Entry::getKey)
                .collect(Collectors.toList()));
        // 4. 统计需要获取内容的文件（新增/修改/重命名）
        List<Map.Entry<String, FileDiffPatchVO>> addedFileDiffs = fileMap.entrySet().stream().filter(entry -> entry.getValue().isExist())
                .map(entry -> Map.entry(entry.getKey(), entry.getValue())).collect(Collectors.toList());
        // 5. 并发获取每个文件的最新内容（先取headCommit内容，再应用本地diff）
        CompletableFuture.allOf(addedFileDiffs.stream()
                        .map(entry -> CompletableFuture.runAsync(() -> {
                            String path = entry.getKey();
                            FileDiffPatchVO fileDiffPatchVO = entry.getValue();
                            String headCommitFilePath = fileDiffPatchVO.getHeadCommitFilePath();
                            String unifiedDiff = Optional.ofNullable(fileDiffPatchVO.getDiff())
                                    .map(d -> RedUnifiedDiffUtils.generateUnifiedDiff(d.getOldPath(), d.getNewPath(), d.getDiff()))
                                    .orElse(null);
                            if (StringUtils.isBlank(headCommitFilePath)) {
                                // 本地新建文件，直接用diff内容
                                sourceMap.put(path, Optional.of(RedUnifiedDiffUtils.contentPatchDiff(null, unifiedDiff)));
                            } else {
                                try {
                                    // 先取headCommit内容，再应用本地diff
                                    sourceMap.put(path, Optional.ofNullable(projectFileContentCache.get(
                                                    buildFileContentCacheKey(projectId, headCommitFilePath, headCommitSha)))
                                            .map(content -> RedUnifiedDiffUtils.contentPatchDiff(content, unifiedDiff)));
                                } catch (Exception e) {
                                    log.error("Failed to load: {}", path, e);
                                    sourceMap.put(path, Optional.empty());
                                }
                            }
                        }, callGraphExecutor)).toArray(CompletableFuture[]::new))
                .exceptionally(ex -> {
                    log.error("全局错误", ex);
                    return null;
                }).join(); // 阻塞等待任务执行完成
    }

    /**
     * 只对支持的文件类型进行处理
     */
    private void supportedExtensionFileFilter(String filePath, Consumer<String> consumer) {
        GraphLanguageEnum.getSupportExtensions().forEach(extension -> {
            if (filePath.endsWith("." + extension)) {
                consumer.accept(filePath);
            }
        });
    }

    /**
     * 向fileMap中添加或移除文件的diff信息（只处理支持的文件类型）
     */
    private void putOrRemoveFileDiff(String filePath, FileDiffPatchVO fileDiffPatchVO, Map<String, FileDiffPatchVO> fileMap) {
        supportedExtensionFileFilter(filePath, newPath -> fileMap.put(newPath, fileDiffPatchVO));
    }

    public Range toRangeDto(com.xiaohongshu.codewiz.ts.graph.domain.Range range) {
        Range dto = new Range();
        com.xiaohongshu.codewiz.ts.graph.domain.Point startPoint = range.getStartPoint();
        com.xiaohongshu.codewiz.ts.graph.domain.Point endPoint = range.getEndPoint();
        return dto.setStartPoint(new Point().setRow(startPoint.getLine()).setColumn(startPoint.getColumn()))
                .setEndPoint(new Point().setRow(endPoint.getLine()).setColumn(endPoint.getColumn()));
    }

    private List<com.xiaohongshu.codewiz.ts.graph.dto.GraphNode> buildGraphNodeDtos(Collection<GraphNode> allNodes,
                                                                                    Map<String, Optional<String>> filePathContentMap) {
        Set<String> nodeIdSet = allNodes.stream().map(GraphNode::getId).collect(Collectors.toSet());
        return allNodes.stream().map(graphNode -> {
            com.xiaohongshu.codewiz.ts.graph.dto.GraphNode dto = new com.xiaohongshu.codewiz.ts.graph.dto.GraphNode();
            List<Refence> predecessorReferences =
                    Optional.ofNullable(graphNode.getUpstream()).orElse(new HashMap<>()).entrySet().stream()
                            .filter(entry -> nodeIdSet.contains(entry.getKey().getId()))
                            .map(entry -> {
                                String id = entry.getKey().getId();
                                return entry.getValue().stream().map(child -> {
                                    Refence refence = new Refence();
                                    refence.setId(id);
                                    refence.setRange(toRangeDto(child.getRange()));
                                    refence.setImports(child.getImportsList());
                                    return refence;
                                }).collect(Collectors.toList());
                            }).flatMap(Collection::stream).collect(Collectors.toList());
            List<Refence> successorReferences =
                    Optional.ofNullable(graphNode.getDownstream()).orElse(new HashMap<>()).entrySet().stream().map(entry -> {
                        Range rangeDto = toRangeDto(entry.getKey().getRange());
                        List<String> importsList = entry.getKey().getImportsList();
                        return entry.getValue().stream().map(GraphNode::getId).distinct()
                                .filter(nodeIdSet::contains).map(id -> {
                                    Refence refence = new Refence();
                                    refence.setId(id);
                                    refence.setRange(rangeDto);
                                    refence.setImports(importsList);
                                    return refence;
                                }).collect(Collectors.toList());
                    }).flatMap(Collection::stream).collect(Collectors.toList());
            com.xiaohongshu.codewiz.ts.graph.domain.Node node = graphNode.getNode();
            String codeSnippet = getCodeSnippet(node, filePathContentMap);
            return dto.setId(graphNode.getId()).setSignature(node.getSignature()).setType(node.getType()).setFilePath(node.getFilePath())
                    .setRange(toRangeDto(node.getRange())).setCodeSnippet(codeSnippet).setPredecessorIds(new ArrayList<>())
                    .setSuccessorIds(new ArrayList<>()).setPredecessorReferences(predecessorReferences)
                    .setSuccessorReferences(successorReferences).setTag(node.getTag());
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    private String getCodeSnippet(com.xiaohongshu.codewiz.ts.graph.domain.Node node, Map<String, Optional<String>> filePathContentMap) {
        String filePath = node.getFilePath();
        com.xiaohongshu.codewiz.ts.graph.domain.Range range = node.getRange();
        Optional<String> fileOptional = filePathContentMap.get(filePath);
        if (fileOptional.isEmpty()) {
            log.error("filePath {} not found", filePath);
            return null;
        }
        String codeSnippet = null;
        try {
            codeSnippet = new String(fileOptional.get().getBytes(StandardCharsets.UTF_8), range.getStartByte(),
                    range.getEndByte() - range.getStartByte());
        } catch (Exception e) {
            log.error("Failed to extract code snippet from file: {}, start: {}, end: {}", filePath, range.getStartByte(),
                    range.getEndByte(), e);
        }
        return codeSnippet;
    }

    public static String buildFileContentCacheKey(Long projectId, String filePath, String commitSha) {
        return StringUtils.joinWith(":", projectId, filePath, commitSha);
    }

    public GraphVO getGraphVO(Long projectId, String commitSha, List<Diff> diffs) {
        log.info("[Get Graph] projectId:{} commitSha: {}", projectId, commitSha);
        if (gitlabMediator.getCommit(projectId, commitSha).isEmpty()) {
            throw new RuntimeException("Commit Not Found");
        }
        // 1. 拉取图信息
        ProjectGraphFile projectGraphFile = fetchProjectGraphFile(projectId, commitSha);
        if (projectGraphFile == null) {
            log.error("[Graph Not Found] projectId:{}", projectId);
            throw new RuntimeException("Graph Not Found");
        }
        // 2. 比较图commit和最新commit的差异
        String baseCommitSha = projectGraphFile.getCommitSha();
        String headCommitSha = StringUtils.isBlank(commitSha) ? baseCommitSha : commitSha;
        GitLabCompareResult gitLabCompareResult = compareProjectGraphFile(projectId, baseCommitSha, headCommitSha, diffs);
        if (gitLabCompareResult == null) {
            log.error("[Graph Compare Error] projectId:{} baseCommitSha:{} headCommitSha:{}", projectId, baseCommitSha,
                    headCommitSha);
            throw new RuntimeException("Graph Compare Error");
        }
        // 3. 增量更新图
        Graph graph = buildIncrementalGraph(projectGraphFile, gitLabCompareResult);
        if (graph == null) {
            log.error("[Graph Build Error] baseCommitSha: {}", baseCommitSha);
            throw new RuntimeException("Graph Build Error");
        }
        GraphVO graphVO = new GraphVO();
        graphVO.setGraph(graph);
        graphVO.setProjectId(projectId);
        graphVO.setBaseCommitId(baseCommitSha);
        graphVO.setHeadCommitSha(headCommitSha);
        graphVO.setGitLabCompareResult(gitLabCompareResult);
        return graphVO;
    }

    public List<GraphNodeDTO> listNodes(Long projectId, String commitSha, CallGraphOrderByEnum orderBy, OrderDirectionEnum orderDirection,
                                        Integer offset, Integer limit) {
        GraphVO graphVO = getGraphVO(projectId, commitSha, null);
        Graph graph = graphVO.getGraph();
        String headCommitSha = graphVO.getHeadCommitSha();
        GitLabCompareResult gitLabCompareResult = graphVO.getGitLabCompareResult();
        List<GraphNode> resultNodes = graph.getGraphNodes().stream().sorted((n1, n2) -> {
            Comparator<GraphNode> comparator = ASC_UPSTREAM_GRAPH_NODE;
            if (CallGraphOrderByEnum.downstream.equals(orderBy)) {
                comparator = ASC_DOWNSTREAM_GRAPH_NODE;
            }
            if (OrderDirectionEnum.desc.equals(orderDirection)) {
                comparator = comparator.reversed();
            }
            return comparator.compare(n1, n2);
        }).skip(offset).limit(limit).collect(Collectors.toList());
        List<GraphNodeDTO> graphNodeDtos =
                buildNodeRespList(projectId, headCommitSha, resultNodes, gitLabCompareResult.getFilePathContentMap(),
                        this::buildCallGraphDTOs);
        if (graphNodeDtos == null) {
            log.error("[Build Resp Error]");
            throw new RuntimeException("Build Resp Error");
        }
        return graphNodeDtos;
    }

    private static final Comparator<GraphNode> ASC_UPSTREAM_GRAPH_NODE = Comparator
            .nullsLast(Comparator.comparingLong(GraphNode::getUpstreamCount));

    private static final Comparator<GraphNode> ASC_DOWNSTREAM_GRAPH_NODE = Comparator
            .nullsLast(Comparator.comparingLong(GraphNode::getDownstreamCount));

    private <T> List<T> buildNodeRespList(Long projectId, String headCommitSha, Collection<GraphNode> resultNodes,
                                          ConcurrentMap<String, Optional<String>> filePathContentMap,
                                          BiFunction<Collection<GraphNode>, ConcurrentMap<String, Optional<String>>, List<T>> function) {
        Transaction transaction = Cat.newTransaction("CodeWizGraphService", "BuildResp");
        List<T> result = null;
        try {
            // 并行获取上下游节点的源码
            CompletableFuture.allOf(resultNodes.stream()
                            .map(GraphNode::getNode)
                            .map(Node::getFilePath)
                            .distinct()
                            .map(filePath -> CompletableFuture.runAsync(() -> filePathContentMap.computeIfAbsent(filePath,
                                            k -> Optional.ofNullable(
                                                    projectFileContentCache.get(
                                                            buildFileContentCacheKey(projectId, filePath, headCommitSha)))),
                                    callGraphExecutor))
                            .toArray(CompletableFuture[]::new))
                    .exceptionally(ex -> {
                        log.error("并行任务中的错误", ex);
                        return null;
                    })
                    .join(); // 如果需要阻塞等待所有任务完成
            result = function.apply(resultNodes, filePathContentMap);
        } catch (Exception e) {
            log.error("BuildResp error", e);
            transaction.setStatus(e);
            Cat.logError(e);
        } finally {
            log.info(LogTags.of(Map.of(
                    "projectId", projectId,
                    "headCommitSha", headCommitSha,
                    "resultNodeSize", resultNodes.size(),
                    "durationInMicros", transaction.getDurationInMicros(),
                    "success", transaction.isSuccess()
            )), "BuildResp");
            transaction.complete();
        }
        return result;
    }

    private List<GraphNodeDTO> buildCallGraphDTOs(Collection<GraphNode> resultNodes,
                                                  ConcurrentMap<String, Optional<String>> filePathContentMap) {
        return resultNodes.stream().map(graphNode -> {
            com.xiaohongshu.codewiz.ts.graph.domain.Node node = graphNode.getNode();
            String codeSnippet = getCodeSnippet(node, filePathContentMap);
            return GraphNodeMapper.INSTANCE.fromGraphNode(graphNode, codeSnippet);
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    public List<GraphNodeFileDTO> listFiles(Long projectId, String commitSha, GraphService.CallGraphOrderByEnum orderBy,
                                            OrderDirectionEnum orderDirection, Integer offset, Integer limit) {
        GraphVO graphVO = getGraphVO(projectId, commitSha, null);
        Graph graph = graphVO.getGraph();
        return graph.getGraphNodes().stream().collect(Collectors.groupingBy(graphNode -> graphNode.getNode().getFilePath())).entrySet()
                .stream()
                .map(entry -> {
                    String filePath = entry.getKey();
                    // 计算下游总和，处理 null 为 0
                    long downstreamSum = entry.getValue().stream().mapToLong(GraphNode::getDownstreamCount).sum();
                    // 计算上游总和，处理 null 为 0
                    long upstreamSum = entry.getValue().stream().mapToLong(GraphNode::getUpstreamCount).sum();
                    return new GraphNodeFileDTO(filePath, downstreamSum, upstreamSum);
                }).sorted((n1, n2) -> {
                    Comparator<GraphNodeFileDTO> comparator = ASC_UPSTREAM_GRAPH_NODE_FILE;
                    if (CallGraphOrderByEnum.downstream.equals(orderBy)) {
                        comparator = ASC_DOWNSTREAM_GRAPH_NODE_FILE;
                    }
                    if (OrderDirectionEnum.desc.equals(orderDirection)) {
                        comparator = comparator.reversed();
                    }
                    return comparator.compare(n1, n2);
                }).skip(offset).limit(limit)
                .collect(Collectors.toList());
    }

    private static final Comparator<GraphNodeFileDTO> ASC_UPSTREAM_GRAPH_NODE_FILE = Comparator
            .nullsLast(Comparator.comparingLong(GraphNodeFileDTO::getUpstreamCount));

    private static final Comparator<GraphNodeFileDTO> ASC_DOWNSTREAM_GRAPH_NODE_FILE = Comparator
            .nullsLast(Comparator.comparingLong(GraphNodeFileDTO::getDownstreamCount));

    public enum CallGraphOrderByEnum {
        upstream,
        downstream;
    }
}
