package com.xiaohongshu.codewiz.graph.controller;

import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.xiaohongshu.codewiz.core.constant.enums.OrderDirectionEnum;
import com.xiaohongshu.codewiz.core.entity.common.SingleResponse;
import com.xiaohongshu.codewiz.graph.convertor.CallGraphQueryResponseMapper;
import com.xiaohongshu.codewiz.graph.dto.CodeContextRequest;
import com.xiaohongshu.codewiz.graph.dto.CodeContextResponse;
import com.xiaohongshu.codewiz.graph.dto.ast.GraphNodeDTO;
import com.xiaohongshu.codewiz.graph.dto.ast.GraphNodeFileDTO;
import com.xiaohongshu.codewiz.graph.service.GraphService;
import com.xiaohongshu.codewiz.ts.graph.dto.FileRange;
import com.xiaohongshu.codewiz.ts.graph.dto.Point;
import com.xiaohongshu.codewiz.ts.graph.dto.ProjectCallGraphRequest;
import com.xiaohongshu.codewiz.ts.graph.dto.ProjectCallGraphResponse;
import com.xiaohongshu.codewiz.ts.graph.dto.Range;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * Created on 2025/4/12
 */
@Validated
@Slf4j
@RestController
@RequestMapping("/api/call-graph")
public class CallGraphController {
    @Resource
    private GraphService graphService;

    @PostMapping("/evaluation/context")
    public SingleResponse<CodeContextResponse> queryCallGraph(@Valid @RequestBody CodeContextRequest request) {
        List<Range> ranges = null;
        if (request.getRowRange() != null) {
            ranges = List.of(new Range()
                    .setStartPoint(new Point()
                            .setRow(request.getRowRange().getStartRow())
                            .setColumn(0)
                    )
                    .setEndPoint(new Point()
                            .setRow(request.getRowRange().getEndRow() + 1)
                            .setColumn(0)
                    ));
        }
        ProjectCallGraphResponse graphResponse = graphService.queryCallGraph(
                new ProjectCallGraphRequest().setProjectId(Long.parseLong(request.getRepoId())).setCommitHash(request.getCommitId())
                        .setFileRanges(List.of(new FileRange().setFilePath(request.getFilePath()).setRanges(ranges))),
                Boolean.TRUE.equals(request.getTriggerPipeline()));
        if (!graphResponse.getResult().isSuccess()) {
            String message = graphResponse.getResult().getMessage();
            log.error("evaluationContextQuery error projectId: {}, commitId: {} path: {} errorMessage: {} ", request.getRepoId(),
                    request.getCommitId(), request.getFilePath(), message);
            return SingleResponse.buildFailure(message, message);
        }
        CodeContextResponse responseDto = new CodeContextResponse();
        responseDto.setRequest(request);
        responseDto.setResponse(CallGraphQueryResponseMapper.INSTANCE.toCallGraphQueryResponse(graphResponse));
        return SingleResponse.of(responseDto);
    }

    @GetMapping("/nodes")
    public SingleResponse<List<GraphNodeDTO>> nodes(
            @RequestParam @NotNull Long projectId,
            @RequestParam(required = false) String commitSha,
            @RequestParam(defaultValue = "upstream") GraphService.CallGraphOrderByEnum orderBy,
            @RequestParam(defaultValue = "asc") OrderDirectionEnum orderDirection,
            @RequestParam(defaultValue = "0") Integer offset,
            @RequestParam(defaultValue = "10") Integer limit) {
        List<GraphNodeDTO> nodes = graphService.listNodes(projectId, commitSha, orderBy, orderDirection, offset, limit);
        return SingleResponse.of(nodes);
    }

    @GetMapping("/files")
    public SingleResponse<List<GraphNodeFileDTO>> files(
            @RequestParam @NotNull Long projectId,
            @RequestParam(required = false) String commitSha,
            @RequestParam(defaultValue = "upstream") GraphService.CallGraphOrderByEnum orderBy,
            @RequestParam(defaultValue = "asc") OrderDirectionEnum orderDirection,
            @RequestParam(defaultValue = "0") Integer offset,
            @RequestParam(defaultValue = "10") Integer limit) {
        List<GraphNodeFileDTO> nodes = graphService.listFiles(projectId, commitSha, orderBy, orderDirection, offset, limit);
        return SingleResponse.of(nodes);
    }

}
