package com.xiaohongshu.codewiz.graph.vo;

import com.xiaohongshu.codewiz.ts.graph.dto.Diff;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * 用于描述某个文件在增量diff应用过程中的状态和内容来源。
 *
 * <AUTHOR>
 * Created on 2025/6/18
 */
@Data
@AllArgsConstructor
public class FileDiffPatchVO {
    // 文件是否存在（false表示被删除）
    private boolean exist;
    // 需要应用到headCommit内容上的本地diff（unified diff格式），null表示无本地变更
    private Diff diff;
    // 获取headCommit内容时用的路径（rename时为oldPath，其它为自身路径），null表示本地新增文件
    private String headCommitFilePath;
}
