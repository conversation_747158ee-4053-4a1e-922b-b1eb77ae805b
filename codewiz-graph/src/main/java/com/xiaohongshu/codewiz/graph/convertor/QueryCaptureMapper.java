package com.xiaohongshu.codewiz.graph.convertor;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import com.xiaohongshu.codewiz.graph.dto.ast.QueryCapture;


/**
 * <AUTHOR>
 * Created on 2025/4/24
 */
@Mapper(uses = RangeMapper.class)
public interface QueryCaptureMapper {
    QueryCaptureMapper INSTANCE = Mappers.getMapper(QueryCaptureMapper.class);

    QueryCapture toQueryCapture(com.xiaohongshu.codewiz.ts.parser.domain.QueryCapture queryCapture);

    com.xiaohongshu.codewiz.ts.parser.domain.QueryCapture fromCapture(QueryCapture queryCapture);
}
