package com.xiaohongshu.codewiz.graph.convertor;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import com.xiaohongshu.codewiz.graph.dto.ast.GraphNodeDTO;
import com.xiaohongshu.codewiz.ts.graph.domain.GraphNode;

@Mapper
public interface GraphNodeMapper {
    GraphNodeMapper INSTANCE = Mappers.getMapper(GraphNodeMapper.class);

    @Mapping(target = "signature", source = "graphNode.node.signature")
    @Mapping(target = "type", source = "graphNode.node.type")
    @Mapping(target = "filePath", source = "graphNode.node.filePath")
    @Mapping(target = "range", source = "graphNode.node.range")
    @Mapping(target = "codeSnippet", source = "codeSnippet")
    @Mapping(target = "upstreamCount", source = "graphNode.upstreamCount")
    @Mapping(target = "downstreamCount", source = "graphNode.downstreamCount")
    GraphNodeDTO fromGraphNode(GraphNode graphNode, String codeSnippet);
}
