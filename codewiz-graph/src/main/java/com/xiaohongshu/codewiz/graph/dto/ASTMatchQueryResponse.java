package com.xiaohongshu.codewiz.graph.dto;

import java.util.List;

import com.xiaohongshu.codewiz.graph.dto.ast.QueryMatch;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * Created on 2025/4/24
 */
@Data
@Builder
public class ASTMatchQueryResponse {
    @Schema(description = "源代码语言")
    private String language;
    @Schema(description = "AST查询匹配结果")
    private List<QueryMatch> list;
}
