package com.xiaohongshu.codewiz.graph.controller;

import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;

import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.xiaohongshu.codewiz.core.entity.common.SingleResponse;

import lombok.extern.slf4j.Slf4j;
import redis.clients.jedis.Jedis;

/**
 * <AUTHOR>
 * Created on 2025/6/26
 */
@Validated
@Slf4j
@RestController
@RequestMapping("/api/devops")
public class DevOpsController {
    @Resource(name = "codewizJedis")
    private Jedis jedis;

    @PostMapping("graphCache/delete")
    public SingleResponse<Long> clearRedisCache(@RequestBody @Valid @NotEmpty List<Long> projectIds) {
        Long delCount = jedis.del(
                projectIds.stream().map(projectId -> StringUtils.joinWith(":", "codewiz-graph", "project-graph", projectId))
                        .toArray(String[]::new));
        return SingleResponse.of(delCount);
    }
}
