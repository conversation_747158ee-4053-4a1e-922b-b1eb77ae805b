package com.xiaohongshu.codewiz.graph.convertor;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import com.xiaohongshu.codewiz.graph.dto.ast.Point;

/**
 * <AUTHOR>
 * Created on 2025/4/24
 */
@Mapper
public interface PointMapper {
    PointMapper INSTANCE = Mappers.getMapper(PointMapper.class);

    @Mapping(target = "row", source = "line")
    @Mapping(target = "column", source = "column")
    Point fromIdlPoint(com.xiaohongshu.codewiz.ts.graph.domain.Point point);

    @Mapping(target = "line", source = "row")
    @Mapping(target = "column", source = "column")
    com.xiaohongshu.codewiz.ts.graph.domain.Point toIdlPoint(Point point);
}
