package com.xiaohongshu.codewiz.graph.convertor;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import com.xiaohongshu.codewiz.graph.dto.ast.CallGraphQueryResponse;
import com.xiaohongshu.codewiz.ts.graph.dto.ProjectCallGraphResponse;
import com.xiaohongshu.codewiz.ts.parser.mapper.PointMapper;

@Mapper(uses = PointMapper.class)
public interface CallGraphQueryResponseMapper {
    CallGraphQueryResponseMapper INSTANCE = Mappers.getMapper(CallGraphQueryResponseMapper.class);

    CallGraphQueryResponse toCallGraphQueryResponse(ProjectCallGraphResponse response);
}
