package com.xiaohongshu.codewiz.graph.client;

import java.io.InputStream;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.amazonaws.services.s3.model.S3Object;
import com.xiaohongshu.codewiz.graph.vo.ProjectGraphFile;
import com.xiaohongshu.codewiz.ts.graph.domain.NodeList;
import com.xiaohongshu.xray.logging.LogTags;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * Created on 2025/3/15
 */
@Slf4j
@Service
public class GraphNodeS3Client {
    private static final String CALL_GRAPH_KEY_PATTERN = "tscg-v2/%d";
    private static final String CALL_GRAPH_KEY_FIX_PATTERN = "tscg-v2/%d/%s";
    private static final String META_COMMIT_SHA = "commit-sha";

    @Value("${ros.bucket}")
    private String bucket;

    @Resource
    private AmazonS3 amazonS3;

    public ProjectGraphFile getProjectGraphFile(Long projectId, String commitSha) {
        String key = StringUtils.isBlank(commitSha) ? String.format(CALL_GRAPH_KEY_PATTERN, projectId)
                : String.format(CALL_GRAPH_KEY_FIX_PATTERN, projectId, commitSha);
        try (S3Object s3Object = amazonS3.getObject(bucket, key)) {
            ProjectGraphFile projectGraphFile = new ProjectGraphFile();
            try (InputStream inputStream = s3Object.getObjectContent()) {
                NodeList nodeList = NodeList.parseFrom(inputStream);
                projectGraphFile.setNodes(nodeList.getNodesList());
                ObjectMetadata metadata = s3Object.getObjectMetadata();
                Map<String, String> userMetadata = metadata.getUserMetadata();
                projectGraphFile.setCommitSha(userMetadata.get(META_COMMIT_SHA));
                projectGraphFile.setProjectId(projectId);
                return projectGraphFile;
            } catch (Exception e) {
                log.error(LogTags.of(Map.of("projectId", projectId, "key", key)), "readGraphFileError", e);
            }
        } catch (Exception e) {
            log.error(LogTags.of(Map.of("projectId", projectId, "key", key)), "getGraphFileError", e);
        }
        return null;
    }
}
