package com.xiaohongshu.codewiz.graph.configuration;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.xiaohongshu.codewiz.graph.rpc.CodeWizGraphRpcServiceImpl;
import com.xiaohongshu.codewiz.ts.graph.service.CodeWizGraphService;
import com.xiaohongshu.infra.rpc.server.ServiceBuilder;

/**
 * <AUTHOR>
 * Created on 2025/3/15
 */
@Configuration
public class RpcServiceConfiguration {
    @Bean
    public ServiceBuilder<CodeWizGraphService.Iface> codewizGraphService(@Autowired CodeWizGraphRpcServiceImpl graphServiceImpl) {
        return ServiceBuilder.fromInstance(CodeWizGraphService.Iface.class, 10086, graphServiceImpl)
                .withThreadSize(80); // 线程数一般推荐 CPU核数*30 ~ CPU核数*40
    }
}
