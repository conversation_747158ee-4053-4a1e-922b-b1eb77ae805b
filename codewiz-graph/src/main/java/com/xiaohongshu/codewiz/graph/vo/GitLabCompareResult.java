package com.xiaohongshu.codewiz.graph.vo;

import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ConcurrentMap;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * <AUTHOR>
 * Created on 2025/4/23
 */
@Data
@AllArgsConstructor
public class GitLabCompareResult {
    private Set<String> deletedFilePaths;
    // 并发map用于多线程获取文件内容
    private ConcurrentMap<String, Optional<String>> filePathContentMap;
}
