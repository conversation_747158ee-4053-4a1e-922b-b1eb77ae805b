package com.xiaohongshu.codewiz.graph.service;

import java.util.List;
import java.util.Optional;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.gitlab4j.api.models.Project;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.xiaohongshu.codewiz.core.entity.graph.CallGraphConfig;
import com.xiaohongshu.codewiz.core.entity.pipeline.PipelineRunRequest;
import com.xiaohongshu.codewiz.core.entity.pipeline.PipelineRunResponse;
import com.xiaohongshu.codewiz.core.feign.PipelineFeignClient;
import com.xiaohongshu.codewiz.core.remote.GitlabMediator;

/**
 * <AUTHOR>
 * Created on 2025/3/17
 */
@Service
public class PipelineService {
    @Value("${feign-client.pipeline.projectId}")
    private String pipelineProjectId;
    @Value("${feign-client.pipeline.pipelineId}")
    private String pipelineId;
    @ApolloJsonValue("${call_graph.config}")
    private CallGraphConfig callGraphConfig;

    @Resource
    private PipelineFeignClient pipelineFeignClient;
    @Resource
    private GitlabMediator gitlabMediator;

    public boolean triggerProjectGraphPipeline(Long projectId) {
        if (projectId == null) {
            return false;
        }
        String sshUrl = gitlabMediator.loadProject(projectId).map(Project::getSshUrlToRepo).orElse(null);
        if (StringUtils.isEmpty(sshUrl)) {
            return false;
        }
        PipelineRunRequest request = new PipelineRunRequest();
        request.setPipelineId(pipelineId);
        request.setPipelineInfoId(pipelineId);
        request.setProjectId(pipelineProjectId);
        request.setBranch("master");
        request.setUserId("codewiz");
        List<PipelineRunRequest.PipelineVariable> variables = List.of(
                new PipelineRunRequest.PipelineVariable("PIPELINE_SSH_URL", sshUrl),
                new PipelineRunRequest.PipelineVariable("PIPELINE_PROJECT_ID", projectId.toString()),
                new PipelineRunRequest.PipelineVariable("PIPELINE_ENABLE_CALL_GRAPH",
                        callGraphConfig.isProjectInGrayList(projectId) ? "true" : "false")
        );
        request.setPipelineVariableList(variables);
        ResponseEntity<PipelineRunResponse> resp = pipelineFeignClient.pipelineRun(request);
        return Optional.ofNullable(resp).map(HttpEntity::getBody).map(PipelineRunResponse::getSuccess).orElse(false);
    }
}
