package com.xiaohongshu.codewiz.graph.convertor;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import com.xiaohongshu.codewiz.graph.dto.ast.Range;

/**
 * <AUTHOR>
 * Created on 2025/4/24
 */
@Mapper(uses = PointMapper.class)
public interface RangeMapper {
    RangeMapper INSTANCE = Mappers.getMapper(RangeMapper.class);

    @Mapping(target = "startPoint", source = "startPoint")
    @Mapping(target = "endPoint", source = "endPoint")
    Range fromIdlRange(com.xiaohongshu.codewiz.ts.graph.domain.Range range);

    @Mapping(target = "startPoint", source = "startPoint")
    @Mapping(target = "endPoint", source = "endPoint")
    com.xiaohongshu.codewiz.ts.graph.domain.Range toIdlRange(Range range);
}