package com.xiaohongshu.codewiz.graph.dto.ast;

import java.util.List;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * Created on 2025/4/24
 */
@Data
public class QueryMatch {
    @Schema(description = "匹配标签", example = "definition.method")
    private String tag;
    @Schema(description = "匹配模式")
    private String pattern;
    @Schema(description = "匹配捕获列表，匹配中包含一个捕获列表")
    private List<QueryCapture> captures;
}
