package com.xiaohongshu.codewiz.graph.dto;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

import lombok.Data;

/**
 * <AUTHOR>
 * Created on 2025/4/12
 */
@Data
public class CodeContextRequest {
    @NotNull
    private String repoId;
    @NotNull
    private String commitId;
    @NotNull
    private String filePath;
    private RowRange rowRange;

    @Data
    @Valid
    public static class RowRange {
        @NotNull
        private Integer startRow;
        @NotNull
        private Integer endRow;
    }

    private Boolean triggerPipeline;
}
