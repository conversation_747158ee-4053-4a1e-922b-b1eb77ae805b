package com.xiaohongshu.codewiz.graph;

import java.io.InputStream;
import java.time.Duration;
import java.util.List;
import java.util.Optional;

import org.apache.thrift.TException;
import org.gitlab4j.api.GitLabApiException;
import org.gitlab4j.api.models.CompareResults;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.xiaohongshu.codewiz.core.remote.GitlabMediator;
import com.xiaohongshu.codewiz.graph.service.GraphService;
import com.xiaohongshu.codewiz.graph.vo.ProjectGraphFile;
import com.xiaohongshu.codewiz.ts.graph.dto.Diff;
import com.xiaohongshu.codewiz.ts.graph.dto.FileRange;
import com.xiaohongshu.codewiz.ts.graph.dto.ProjectCallGraphRequest;
import com.xiaohongshu.codewiz.ts.graph.dto.ProjectCallGraphResponse;
import com.xiaohongshu.codewiz.ts.graph.service.CodeWizGraphService;
import com.xiaohongshu.infra.rpc.base.Context;
import com.xiaohongshu.infra.rpc.client.ClientBuilder;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * Created on 2025/3/15
 */
@Slf4j
@SpringBootTest
@RunWith(SpringRunner.class)
public class GraphServiceTest {
    @Autowired
    private GraphService graphService;
    @Autowired
    private GitlabMediator gitlabMediator;
    @Autowired
    private LoadingCache<Long, ProjectGraphFile> projectGraphFileCache;
    @Autowired
    private LoadingCache<String, String> projectFileContentCache;

    @Test
    public void testRpcService() throws Exception {
        ProjectCallGraphResponse resp;
        // 读取diff.json
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        InputStream is = Thread.currentThread().getContextClassLoader().getResourceAsStream("diff.json");
        List<Diff> diffs = objectMapper.readValue(is, new TypeReference<List<Diff>>(){});
        ProjectCallGraphRequest request = new ProjectCallGraphRequest()
                .setProjectId(194)
                .setCommitHash("03f6e225acd36b57d4ad3b1d520bae47ef9bf827")
                .setFileRanges(List.of(
                        new FileRange().setFilePath("notedetail/external/src/main/java/com/xingin/external/hot_rank/card/HotRankCardPresenter.kt")
                ))
                .setDiffs(diffs); // 赋值diffs
        resp = graphService.queryCallGraph(request, false);
        System.out.println(resp);
    }

    @Test
    public void testDiff() {
        CompareResults compareResults;
        try {
            compareResults = gitlabMediator.loadMrSnapshotInfo(9172L, "292fa0fb7879825b601e977e6f970041274c43da",
                    "5de87951756ebabc3b0b6620f25f86fa605dfda9");
        } catch (GitLabApiException e) {
            throw new RuntimeException(e);
        }
        System.out.println(compareResults);
    }

    @Test
    public void testLanguage() {
        Optional<String> primaryLanguage = gitlabMediator.getPrimaryLanguage(9172L);
        System.out.println(primaryLanguage);
    }

    @Test
    public void testRpcCall() {
        CodeWizGraphService.Iface stub = ClientBuilder.create(CodeWizGraphService.Iface.class, "codewiz-graph-rpc")
                .withTimeout(Duration.ofMillis(10000))
                .buildStub();
        ProjectCallGraphResponse projectCallGraphResponse;
        try {
            projectCallGraphResponse = stub.queryCallGraph(new Context(), new ProjectCallGraphRequest()
                    .setProjectId(9172L)
                    .setCommitHash("891b540d27a1bcfab2350d5c6b1c6b4ac7612265")
                    .setFileRanges(List.of(new FileRange().setFilePath(
                            "redcopilot-smartcr/src/main/java/com/xiaohongshu/redcopilot/smartcr/service/handler/MrChecker.java")))
            );
            System.out.println(projectCallGraphResponse);
        } catch (TException e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    public void testS3() {
        ProjectGraphFile projectGraphFile = projectGraphFileCache.get(12989L);
        log.info("commitId:{} size: {}", projectGraphFile.getCommitSha(), projectGraphFile.getNodes().size());
    }

    @Test
    public void testFileContentCache() {
        System.out.println(System.getProperty("java.io.tmpdir"));
        String content;
        content = projectFileContentCache.get(GraphService.buildFileContentCacheKey(9172L,
                "redcopilot-smartcr/src/main/java/com/xiaohongshu/redcopilot/smartcr/service/handler/MrChecker.java",
                "891b540d27a1bcfab2350d5c6b1c6b4ac7612265"));
        System.out.println(content);
        content = projectFileContentCache.get(GraphService.buildFileContentCacheKey(9172L,
                "redcopilot-smartcr/src/main/java/com/xiaohongshu/redcopilot/smartcr/service/handler/MrChecker.java",
                "891b540d27a1bcfab2350d5c6b1c6b4ac7612265"));
        System.out.println(content);
    }
}
