package com.xiaohongshu.codewiz.graph;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.ListObjectsV2Request;
import com.amazonaws.services.s3.model.ListObjectsV2Result;
import com.amazonaws.services.s3.model.S3ObjectSummary;
import com.xiaohongshu.codewiz.graph.client.GraphNodeS3Client;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * Created on 2025/4/12
 */
@Slf4j
@SpringBootTest
@RunWith(SpringRunner.class)
public class ProjectGraphFileTest {
    @Resource
    private GraphNodeS3Client graphNodeS3Client;
    @Resource
    private AmazonS3 amazonS3;

    @Test
    public void testDeserialize() {
        List<Long> projectIds = List.of(
                51284L,
                21806L,
                24075L,
                21160L,
                45262L,
                6930L,
                33763L,
                17719L,
                46682L,
                33469L
        );
        for (Long projectId : projectIds) {
            try {
                long l = System.currentTimeMillis();
                graphNodeS3Client.getProjectGraphFile(projectId, null);
                log.info("load:{} cost:{}", projectId, System.currentTimeMillis() - l);
            } catch (Exception e) {
                log.error("Failed to load:{}", projectId, e);
            }
        }
    }

    public static List<String> listAllObjects(AmazonS3 amazonS3, String bucketName, String prefix) {
        List<String> list = new ArrayList<>();
        String continuationToken = null;
        do {
            // 创建请求，使用 continuationToken 进行分页
            ListObjectsV2Request request = new ListObjectsV2Request()
                    .withBucketName(bucketName)
                    .withPrefix(prefix)
                    .withContinuationToken(continuationToken);

            // 获取当前页的对象列表
            ListObjectsV2Result result = amazonS3.listObjectsV2(request);

            // 遍历当前页的对象
            for (S3ObjectSummary objectSummary : result.getObjectSummaries()) {
                list.add(objectSummary.getKey());
            }

            // 更新 continuationToken，以便获取下一页
            continuationToken = result.getNextContinuationToken();

        } while (continuationToken != null);  // 继续循环直到没有更多页
        return list;
    }

    @Test
    public void testFetchExistedProjectIds() {
        List<String> extractedIds = listAllObjects(amazonS3, "codewiz", "tscg-v2/")
                .stream()
                .map(key -> key.replaceFirst("^tscg-v2/", "")) // 去掉前缀
                .map(path -> path.split("/")[0]) // 提取第一个目录名（即1016）
                .distinct() // 可选：去重
                .collect(Collectors.toList());
        System.out.println(extractedIds);
    }
}
