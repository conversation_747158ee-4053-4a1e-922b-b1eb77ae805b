package com.xiaohongshu.codewiz.graph;

import java.io.IOException;
import java.nio.file.Path;
import java.util.List;
import java.util.Objects;

import org.junit.Test;
import org.junit.platform.commons.util.StringUtils;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.xiaohongshu.codewiz.core.utils.JsonMapperUtils;
import com.xiaohongshu.codewiz.graph.service.PipelineService;

import lombok.Data;

/**
 * <AUTHOR>
 * Created on 2025/3/17
 */
@SpringBootTest
@RunWith(SpringRunner.class)
public class PipelineTest {
    @Autowired
    private PipelineService pipelineService;

    @Data
    public static class Row {
        @JsonProperty("project_id")
        private Long projectId;
    }

    @Test
    public void testPipelineRun2() {
        System.out.println(pipelineService.triggerProjectGraphPipeline(9172L));
    }

    @Test
    public void testPipelineRun() throws IOException {
        String input = System.getenv("input");
        if (StringUtils.isBlank(input)) {
            throw new RuntimeException("input is empty");
        }
        List<Row> rows = JsonMapperUtils.mapper().readValue(Path.of(input).toFile(), new TypeReference<List<Row>>() {
        });
        Lists.partition(rows, 50).forEach(partition -> {
            partition.stream().map(Row::getProjectId).filter(Objects::nonNull).forEach(pipelineService::triggerProjectGraphPipeline);
            try {
                Thread.sleep(120 * 1000);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        });
    }
}
