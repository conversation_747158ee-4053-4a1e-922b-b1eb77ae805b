[{"deletedFile": false, "diff": "@@ -2,7 +2,6 @@ package com.xiaohongshu.codewiz.graph.service;\n \n import java.nio.charset.StandardCharsets;\n import java.util.ArrayList;\n-import java.util.Arrays;\n import java.util.Collection;\n import java.util.Collections;\n import java.util.Comparator;\n@@ -33,15 +32,13 @@ import org.springframework.stereotype.Service;\n import com.dianping.cat.Cat;\n import com.dianping.cat.message.Transaction;\n import com.github.benmanes.caffeine.cache.LoadingCache;\n-import com.github.difflib.DiffUtils;\n-import com.github.difflib.UnifiedDiffUtils;\n-import com.github.difflib.patch.Patch;\n import com.xiaohongshu.codewiz.core.constant.enums.OrderDirectionEnum;\n import com.xiaohongshu.codewiz.core.remote.GitlabMediator;\n import com.xiaohongshu.codewiz.core.utils.CompletableFutureUtil;\n import com.xiaohongshu.codewiz.graph.convertor.GraphNodeMapper;\n import com.xiaohongshu.codewiz.graph.dto.ast.GraphNodeDTO;\n import com.xiaohongshu.codewiz.graph.dto.ast.GraphNodeFileDTO;\n+import com.xiaohongshu.codewiz.graph.utils.DiffPatchUtils;\n import com.xiaohongshu.codewiz.graph.vo.FileDiffPatchVO;\n import com.xiaohongshu.codewiz.graph.vo.GitLabCompareResult;\n import com.xiaohongshu.codewiz.graph.vo.GraphQueryResult;\n@@ -345,13 +342,13 @@ public class GraphService {\n                     putOrRemoveFileDiff(diff.getOldPath(), new FileDiffPatchVO(false, null, null), fileMap);\n                 } else if (diff.getOldPath() == null && diff.getNewPath() != null) { // 本地新增\n                     // headCommitFilePath为null，表示本地新建\n-                    putOrRemoveFileDiff(diff.getNewPath(), new FileDiffPatchVO(true, diff.getDiff(), null), fileMap);\n+                    putOrRemoveFileDiff(diff.getNewPath(), new FileDiffPatchVO(true, diff, null), fileMap);\n                 } else if (diff.getNewPath() != null && diff.getOldPath() != null) {\n                     if (diff.getNewPath().equals(diff.getOldPath())) { // 本地修改\n-                        putOrRemoveFileDiff(diff.getNewPath(), new FileDiffPatchVO(true, diff.getDiff(), diff.getNewPath()), fileMap);\n+                        putOrRemoveFileDiff(diff.getNewPath(), new FileDiffPatchVO(true, diff, diff.getNewPath()), fileMap);\n                     } else { // 本地重命名\n                         // 新文件内容来源于oldPath+diff，旧文件被删除\n-                        putOrRemoveFileDiff(diff.getNewPath(), new FileDiffPatchVO(true, diff.getDiff(), diff.getOldPath()), fileMap);\n+                        putOrRemoveFileDiff(diff.getNewPath(), new FileDiffPatchVO(true, diff, diff.getOldPath()), fileMap);\n                         putOrRemoveFileDiff(diff.getOldPath(), new FileDiffPatchVO(false, null, null), fileMap);\n                     }\n                 }\n@@ -369,16 +366,16 @@ public class GraphService {\n                             String path = entry.getKey();\n                             FileDiffPatchVO fileDiffPatchVO = entry.getValue();\n                             String headCommitFilePath = fileDiffPatchVO.getHeadCommitFilePath();\n-                            String diff = fileDiffPatchVO.getDiff();\n+                            Diff diff = fileDiffPatchVO.getDiff();\n                             if (StringUtils.isBlank(headCommitFilePath)) {\n                                 // 本地新建文件，直接用diff内容\n-                                sourceMap.put(path, Optional.ofNullable(diff).map(content -> contentPatchDiff(content, diff)));\n+                                sourceMap.put(path, Optional.ofNullable(diff).map(d -> DiffPatchUtils.contentPatchDiff(null, d)));\n                             } else {\n                                 try {\n                                     // 先取headCommit内容，再应用本地diff\n                                     sourceMap.put(path, Optional.ofNullable(projectFileContentCache.get(\n                                                     buildFileContentCacheKey(projectId, headCommitFilePath, headCommitSha)))\n-                                            .map(content -> contentPatchDiff(content, diff)));\n+                                            .map(content -> DiffPatchUtils.contentPatchDiff(content, diff)));\n                                 } catch (Exception e) {\n                                     log.error(\"Failed to load: {}\", path, e);\n                                     sourceMap.put(path, Optional.empty());\n@@ -409,35 +406,6 @@ public class GraphService {\n         supportedExtensionFileFilter(filePath, newPath -> fileMap.put(newPath, fileDiffPatchVO));\n     }\n \n-    /**\n-     * 将unified diff应用到原始内容上，返回patch后的内容。\n-     * diff为空时直接返回原内容。\n-     *\n-     * @param content 原始内容\n-     * @param diff    unified diff\n-     * @return 应用diff后的内容\n-     */\n-    private String contentPatchDiff(String content, String diff) {\n-        if (StringUtils.isBlank(diff)) {\n-            return content;\n-        }\n-        // 按行分割原始内容\n-        List<String> originalLines = Arrays.asList(content.split(\"\\\\r?\\\\n\", -1));\n-        List<String> diffLines = Arrays.asList(diff.split(\"\\\\r?\\\\n\"));\n-        try {\n-            // 解析unified diff\n-            Patch<String> patch = UnifiedDiffUtils.parseUnifiedDiff(diffLines);\n-            // 应用patch\n-            List<String> patchedLines = DiffUtils.patch(originalLines, patch);\n-            // 合并为字符串，保持原有换行风格\n-            return String.join(\"\\n\", patchedLines);\n-        } catch (Exception e) {\n-            // 失败时返回原内容，并记录日志\n-            log.error(\"Failed to apply diff\", e);\n-            return content;\n-        }\n-    }\n-\n     public Range toRangeDto(com.xiaohongshu.codewiz.ts.graph.domain.Range range) {\n         Range dto = new Range();\n         com.xiaohongshu.codewiz.ts.graph.domain.Point startPoint = range.getStartPoint();", "newFile": false, "newPath": "codewiz-graph/src/main/java/com/xiaohongshu/codewiz/graph/service/GraphService.java", "oldPath": "codewiz-graph/src/main/java/com/xiaohongshu/codewiz/graph/service/GraphService.java", "renamedFile": false, "a_mode": "100644", "b_mode": "100644"}, {"deletedFile": false, "diff": "@@ -0,0 +1,58 @@\n+package com.xiaohongshu.codewiz.graph.utils;\n+\n+import java.util.Arrays;\n+import java.util.Collections;\n+import java.util.List;\n+import java.util.Optional;\n+\n+import com.github.difflib.DiffUtils;\n+import com.github.difflib.UnifiedDiffUtils;\n+import com.github.difflib.patch.Patch;\n+import com.xiaohongshu.codewiz.ts.graph.dto.Diff;\n+\n+import lombok.extern.slf4j.Slf4j;\n+\n+@Slf4j\n+public class DiffPatchUtils {\n+    private DiffPatchUtils() {\n+    }\n+\n+    /**\n+     * 生成unified diff文本（带文件头）\n+     */\n+    public static String generateUnifiedDiff(Diff diff) {\n+        if (diff == null) {\n+            return \"\";\n+        }\n+        return \"--- a/\" + Optional.ofNullable(diff.getOldPath()).orElse(\"dev/null\") + \"\\n\"\n+                + \"+++ b/\" + Optional.ofNullable(diff.getNewPath()).orElse(\"dev/null\") + \"\\n\"\n+                + Optional.ofNullable(diff.getDiff()).orElse(\"\");\n+    }\n+\n+    /**\n+     * 将unified diff应用到原始内容上，返回patch后的内容。\n+     * diff为null或diff内容为空时直接返回原内容。\n+     *\n+     * @param content 原始内容\n+     * @param diff    unified diff\n+     * @return 应用diff后的内容\n+     */\n+    public static String contentPatchDiff(String content, Diff diff) {\n+        if (diff == null || diff.getDiff() == null || diff.getDiff().isEmpty()) {\n+            return content;\n+        }\n+        List<String> originalLines =\n+                content == null ? Collections.emptyList() : Arrays.asList(content.split(\"\\\\r?\\\\n\", -1));\n+        String unifiedDiff = generateUnifiedDiff(diff);\n+        List<String> diffLines = unifiedDiff.isEmpty() ? Collections.emptyList() : Arrays.asList(unifiedDiff.split(\"\\\\r?\\\\n\"));\n+        try {\n+            Patch<String> patch = UnifiedDiffUtils.parseUnifiedDiff(diffLines);\n+            List<String> patchedLines = DiffUtils.patch(originalLines, patch);\n+            return String.join(\"\\n\", patchedLines);\n+        } catch (Exception e) {\n+            log.error(\"Failed to apply diff. oldPath: {}, newPath: {}, diff: {}, error: {}\",\n+                    diff.getOldPath(), diff.getNewPath(), diff.getDiff(), e.getMessage(), e);\n+            return content;\n+        }\n+    }\n+}\n\\ No newline at end of file", "newFile": true, "newPath": "codewiz-graph/src/main/java/com/xiaohongshu/codewiz/graph/utils/DiffPatchUtils.java", "oldPath": null, "renamedFile": false, "a_mode": null, "b_mode": "100644"}, {"deletedFile": false, "diff": "@@ -1,5 +1,7 @@\n package com.xiaohongshu.codewiz.graph.vo;\n \n+import com.xiaohongshu.codewiz.ts.graph.dto.Diff;\n+\n import lombok.AllArgsConstructor;\n import lombok.Data;\n \n@@ -15,7 +17,7 @@ public class FileDiffPatchVO {\n     // 文件是否存在（false表示被删除）\n     private boolean exist;\n     // 需要应用到headCommit内容上的本地diff（unified diff格式），null表示无本地变更\n-    private String diff;\n+    private Diff diff;\n     // 获取headCommit内容时用的路径（rename时为oldPath，其它为自身路径），null表示本地新增文件\n     private String headCommitFilePath;\n }", "newFile": false, "newPath": "codewiz-graph/src/main/java/com/xiaohongshu/codewiz/graph/vo/FileDiffPatchVO.java", "oldPath": "codewiz-graph/src/main/java/com/xiaohongshu/codewiz/graph/vo/FileDiffPatchVO.java", "renamedFile": false, "a_mode": "100644", "b_mode": "100644"}, {"deletedFile": false, "diff": "@@ -0,0 +1,49 @@\n+package com.xiaohongshu.codewiz.graph;\n+\n+import static org.junit.Assert.assertEquals;\n+\n+import org.junit.Test;\n+\n+import com.xiaohongshu.codewiz.graph.utils.DiffPatchUtils;\n+import com.xiaohongshu.codewiz.ts.graph.dto.Diff;\n+\n+public class DiffUtilsTest {\n+    @Test\n+    public void testContentPatchDiff() {\n+        // 1. 原内容+空diff，结果应等于原内容\n+        String content = \"a\\nb\\nc\";\n+        Diff diff = new Diff().setOldPath(\"a\").setNewPath(\"a\").setDiff(\"\");\n+        assertEquals(content, DiffPatchUtils.contentPatchDiff(content, diff));\n+\n+        // 2. 新增一行\n+        diff = new Diff().setOldPath(\"a\").setNewPath(\"a\").setDiff(\"@@ -1,3 +1,4 @@\\n a\\n+b\\nb\\nc\");\n+        assertEquals(\"a\\nb\\nb\\nc\", DiffPatchUtils.contentPatchDiff(content, diff));\n+\n+        // 3. 删除一行\n+        diff = new Diff().setOldPath(\"a\").setNewPath(\"a\").setDiff(\"@@ -2,2 +2,1 @@\\n-b\\nc\");\n+        assertEquals(\"a\\nc\", DiffPatchUtils.contentPatchDiff(content, diff));\n+\n+        // 4. 修改一行（b->B）\n+        diff = new Diff().setOldPath(\"a\").setNewPath(\"a\").setDiff(\"@@ -2,1 +2,1 @@\\n-b\\n+B\");\n+        assertEquals(\"a\\nB\\nc\", DiffPatchUtils.contentPatchDiff(content, diff));\n+\n+        // 5. 多hunk diff\n+        content = \"a\\nb\\nc\\nd\\ne\";\n+        diff = new Diff().setOldPath(\"a\").setNewPath(\"a\").setDiff(\"@@ -2,2 +2,2 @@\\n-b\\n+c\\n c\\n@@ -4,2 +4,3 @@\\n d\\n+X\\n e\");\n+        assertEquals(\"a\\nc\\nc\\nd\\nX\\ne\", DiffPatchUtils.contentPatchDiff(content, diff));\n+\n+        // 6. diff格式错误，返回原内容\n+        diff = new Diff().setOldPath(\"a\").setNewPath(\"a\").setDiff(\"not a diff\");\n+        assertEquals(content, DiffPatchUtils.contentPatchDiff(content, diff));\n+\n+        // 7. oldPath/newPath为null的情况\n+        diff = new Diff().setOldPath(null).setNewPath(null).setDiff(\"@@ -1,1 +1,2 @@\\n a\\n+b\");\n+        content = \"a\";\n+        assertEquals(\"a\\nb\", DiffPatchUtils.contentPatchDiff(content, diff));\n+\n+        // 8. content为null的情况（等价于新增文件）\n+        diff = new Diff().setOldPath(null).setNewPath(\"a\").setDiff(\"@@ -0,0 +1,2 @@\\n+a\\n+b\");\n+        content = null;\n+        assertEquals(\"a\\nb\", DiffPatchUtils.contentPatchDiff(content, diff));\n+    }\n+}", "newFile": true, "newPath": "codewiz-graph/src/test/java/com/xiaohongshu/codewiz/graph/DiffUtilsTest.java", "oldPath": null, "renamedFile": false, "a_mode": null, "b_mode": "100644"}, {"deletedFile": false, "diff": "@@ -47,7 +47,7 @@ public class GraphServiceTest {\n         ProjectCallGraphResponse resp;\n         resp = graphService.queryCallGraph(new ProjectCallGraphRequest()\n                 .setProjectId(51284)\n-                .setCommitHash(\"6649b3671ffd277beea629c36c942d73c7f891ee\")\n+                .setCommitHash(\"6a46eb633d711de599bff7614e5f692737286f08\")\n                 .setFileRanges(List.of(\n                         new FileRange().setFilePath(\"codewiz-ir/src/main/java/com/xiaohongshu/codewiz/ir/controller/ChatSessionController.java\")\n                 )), false", "newFile": false, "newPath": "codewiz-graph/src/test/java/com/xiaohongshu/codewiz/graph/GraphServiceTest.java", "oldPath": "codewiz-graph/src/test/java/com/xiaohongshu/codewiz/graph/GraphServiceTest.java", "renamedFile": false, "a_mode": "100644", "b_mode": "100644"}]