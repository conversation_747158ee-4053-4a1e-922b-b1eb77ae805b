<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.xiaohongshu</groupId>
        <artifactId>infra-parent</artifactId>
        <version>0.0.1-SNAPSHOT</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>
    <artifactId>codewiz-server</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>
    <description>CodeWiz Server</description>

    <properties>
        <!-- 项目属性 -->
        <revision>1.0.0-SNAPSHOT</revision>
        <java.version>11</java.version>

        <!-- root-pom 版本 -->
        <root-pom.version>3.3.0-SNAPSHOT</root-pom.version>

        <!-- 插件属性 -->
        <maven-resources-plugin.version>3.3.1</maven-resources-plugin.version>
        <flatten-maven-plugin.version>1.7.0</flatten-maven-plugin.version>
        <maven-compiler-plugin.version>3.8.1</maven-compiler-plugin.version>
        <maven-jar-plugin.version>3.4.2</maven-jar-plugin.version>
        <org.mapstruct.version>1.6.3</org.mapstruct.version>
        <spring-boot-maven-plugin.version>2.1.6.RED-SNAPSHOT</spring-boot-maven-plugin.version>

        <!-- 依赖属性 -->
        <lombok.version>1.18.24</lombok.version>
        <protobuf-java.version>3.25.3</protobuf-java.version>
        <mybatis-plus.version>3.4.2</mybatis-plus.version>
        <mybatis.version>3.5.6</mybatis.version>
        <mybatis-spring.version>2.0.5</mybatis-spring.version>
        <tree-sitter-parser.version>1.0.0-SNAPSHOT</tree-sitter-parser.version>
        <gitlab4j-api.version>5.3.0</gitlab4j-api.version>
        <tree-sitter-idl.version>1.1.5</tree-sitter-idl.version>
        <springdoc-openapi.version>1.8.0</springdoc-openapi.version>
    </properties>

    <modules>
        <module>codewiz-core</module>
        <module>codewiz-rag</module>
        <module>codewiz-complete</module>
        <module>codewiz-ir</module>
        <module>codewiz-graph</module>
        <module>codewiz-account</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.xiaohongshu</groupId>
                <artifactId>infra-root-pom</artifactId>
                <version>${root-pom.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>com.xiaohongshu</groupId>
                <artifactId>tree-sitter-parser</artifactId>
                <version>${tree-sitter-parser.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xiaohongshu</groupId>
                <artifactId>codewiz-tree-sitter-idl</artifactId>
                <version>${tree-sitter-idl.version}</version>
            </dependency>
            <dependency>
                <groupId>org.gitlab4j</groupId>
                <artifactId>gitlab4j-api</artifactId>
                <version>${gitlab4j-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis-spring</artifactId>
                <version>${mybatis-spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis</artifactId>
                <version>${mybatis.version}</version>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.google.protobuf</groupId>
                <artifactId>protobuf-java</artifactId>
                <version>${protobuf-java.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-ui</artifactId>
                <version>${springdoc-openapi.version}</version>
            </dependency>
            <dependency>
                <groupId>co.elastic.clients</groupId>
                <artifactId>elasticsearch-java</artifactId>
                <version>8.13.3</version>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch.client</groupId>
                <artifactId>elasticsearch-rest-client</artifactId>
                <version>8.13.3</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${org.mapstruct.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>${flatten-maven-plugin.version}</version>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>flatten.clean</id>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>

        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>${maven-resources-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>${maven-compiler-plugin.version}</version>
                    <configuration>
                        <source>${java.version}</source>
                        <target>${java.version}</target>
                        <encoding>UTF-8</encoding>
                        <annotationProcessorPaths>
                            <path>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok</artifactId>
                                <version>${lombok.version}</version>
                            </path>
                            <path>
                                <groupId>org.mapstruct</groupId>
                                <artifactId>mapstruct-processor</artifactId>
                                <version>${org.mapstruct.version}</version>
                            </path>
                        </annotationProcessorPaths>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>${maven-jar-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring-boot-maven-plugin.version}</version>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>
</project>
