package com.xiaohongshu.codewiz.account.enums;

import lombok.Getter;

/**
 * @ClassName DepqtureMessageTypeEnum
 * @Description
 * @Date 2025/5/20 19:59
 * <AUTHOR>
 */
@Getter
public enum DepqtureMessageTypeEnum {

    CREATE("CREATE", "账号创建"),
    ENABLE("ENABLE", "账号启用"),
    UPDATE("UPDATE", "账号信息修改"),
    PWD_UPDATE("PWD_UPDATE", "账号密码修改"),
    DISABLE("DISABLE", "账号临时禁用"),
    CLOSE("CLOSE", "账号永久关闭");

    private final String eventType;
    private final String description;

    DepqtureMessageTypeEnum(String eventType, String description) {
        this.eventType = eventType;
        this.description = description;
    }

}
