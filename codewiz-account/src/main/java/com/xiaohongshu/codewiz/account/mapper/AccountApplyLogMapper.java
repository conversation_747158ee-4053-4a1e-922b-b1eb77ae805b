package com.xiaohongshu.codewiz.account.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xiaohongshu.codewiz.account.po.AccountApplyLogInfo;
import com.xiaohongshu.codewiz.account.po.AccountApplyProcessTempInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @ClassName AccountApplyLogMapper
 * @Description
 * @Date 2025/5/22 11:11
 * <AUTHOR>
 */
@Mapper
public interface AccountApplyLogMapper extends BaseMapper<AccountApplyLogInfo> {

    /**
     * 根据用户邮箱、服务名称查询管理员账号信息
     */
    default List<AccountApplyLogInfo> findByServiceAndEmail(@Param("serviceName") String serviceName,
                                                      @Param("userEmail") String userEmail) {
        LambdaQueryWrapper<AccountApplyLogInfo> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(AccountApplyLogInfo::getUserEmail, userEmail)
                .eq(AccountApplyLogInfo::getServiceName, serviceName);
        return this.selectList(wrapper);
    }

    /**
     * 根据用户邮箱、服务名称查询管理员账号信息
     */
    default List<AccountApplyLogInfo> findByRequestId(@Param("requestId") String requestId) {
        LambdaQueryWrapper<AccountApplyLogInfo> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(AccountApplyLogInfo::getRequestId, requestId);
        return this.selectList(wrapper);
    }

    /**
     * 根据用户邮箱、服务名称查询管理员账号信息
     */
    default AccountApplyLogInfo findByServiceAndEmailAndRequestId(@Param("serviceName") String serviceName,
                                                               @Param("userEmail") String userEmail,
                                                               @Param("requestId") String requestId) {
        LambdaQueryWrapper<AccountApplyLogInfo> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(AccountApplyLogInfo::getUserEmail, userEmail)
                .eq(AccountApplyLogInfo::getServiceName, serviceName)
                .eq(AccountApplyLogInfo::getRequestId, requestId)
                .last("LIMIT 1");
        return this.selectOne(wrapper);
    }

    /**
     * 根据服务名称/用户名查询管理员账号信息
     */
    default AccountApplyLogInfo findByServiceAndName(@Param("serviceName") String serviceName,
                                                     @Param("username") String username) {
        LambdaQueryWrapper<AccountApplyLogInfo> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(AccountApplyLogInfo::getServiceName, serviceName)
                .eq(AccountApplyLogInfo::getUsername, username)
                .last("LIMIT 1");
        return this.selectOne(wrapper);
    }


    /**
     * 根据服务名称查询管理员账号信息
     */
    default List<AccountApplyLogInfo> findAllByService(@Param("serviceName") String serviceName) {
        LambdaQueryWrapper<AccountApplyLogInfo> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(AccountApplyLogInfo::getServiceName, serviceName);
        return this.selectList(wrapper);
    }

    /**
     * 根据部门id查询账号信息
     */
    default List<AccountApplyLogInfo> findAllByDepartmentId(@Param("departmentId") String departmentId) {
        LambdaQueryWrapper<AccountApplyLogInfo> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(AccountApplyLogInfo::getDepartmentId, departmentId);
        return this.selectList(wrapper);
    }


    default Boolean delAccountApplyLogInfo(AccountApplyLogInfo accountApplyLogInfo) {
        LambdaQueryWrapper<AccountApplyLogInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AccountApplyLogInfo::getServiceName, accountApplyLogInfo.getServiceName());
        queryWrapper.eq(AccountApplyLogInfo::getUserEmail, accountApplyLogInfo.getUserEmail());
        return this.delete(queryWrapper) >= 1;
    }

    /**
     * 新增用户信息
     *
     * @param accountApplyLogInfo 管理员信息
     */
    default Boolean updateAccountApplyLogInfo(AccountApplyLogInfo accountApplyLogInfo) {
        LambdaQueryWrapper<AccountApplyLogInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AccountApplyLogInfo::getServiceName, accountApplyLogInfo.getServiceName());
        queryWrapper.eq(AccountApplyLogInfo::getUserEmail, accountApplyLogInfo.getUserEmail());
        return this.update(accountApplyLogInfo, queryWrapper) >= 1;
    }

}
