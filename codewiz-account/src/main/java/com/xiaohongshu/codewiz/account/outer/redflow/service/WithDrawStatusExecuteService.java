package com.xiaohongshu.codewiz.account.outer.redflow.service;

import com.xiaohongshu.codewiz.account.bo.RedFlowProcessMessageBodyBO;
import com.xiaohongshu.codewiz.account.enums.AccountStatusEnum;
import com.xiaohongshu.codewiz.account.po.AccountApplyLogInfo;
import com.xiaohongshu.codewiz.account.po.AccountApplyProcessTempInfo;
import com.xiaohongshu.codewiz.account.po.UserAccountInfo;
import com.xiaohongshu.codewiz.account.service.impl.AccountApplyLogInfoServiceImpl;
import com.xiaohongshu.codewiz.account.service.impl.AccountApplyProcessTempInfoServiceImpl;
import com.xiaohongshu.codewiz.account.service.impl.UserAccountInfoServiceImpl;
import com.xiaohongshu.codewiz.account.utils.ProcessUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @ClassName WithDrawStatusExecuteService
 * @Description
 * @Date 2025/5/26 16:22
 * <AUTHOR>
 */
@Slf4j
@Service
public class WithDrawStatusExecuteService {


    @Resource
    private AccountApplyProcessTempInfoServiceImpl accountApplyProcessTempInfoService;

    @Resource
    private AccountApplyLogInfoServiceImpl accountApplyLogInfoService;

    @Resource
    private ProcessUtils processUtils;

    @Resource
    private UserAccountInfoServiceImpl userAccountInfoServiceImpl;

    /**
     * RedFlow流程撤回事件处理
     *
     * @param message MQ 流程消息
     */
    public Boolean withDrawStatusExecute(RedFlowProcessMessageBodyBO message) {
        AccountApplyLogInfo accountApplyLogInfo = AccountApplyLogInfo.builder().build();
        try {
            String formNo = message.getFormNo();
            String startUserEmail = message.getStartUserEmail();
            String serviceName = StringUtils.isEmpty(message.getServiceName()) ? "cursor" : message.getServiceName();
            // 1. 获取相同单据的log日志
            accountApplyLogInfo = processUtils.getLogProcessInfo(message, "[流程撤回事件]");

            // 2. 删除流程中临时表数据
            AccountApplyProcessTempInfo withDrawProcess = AccountApplyProcessTempInfo.builder()
//                    .requestId(formNo)
                    .userEmail(startUserEmail)
                    .serviceName(serviceName)
                    .build();
            accountApplyProcessTempInfoService.delAccountApplyProcessTempInfo(withDrawProcess);
            // 用户表更新
            UserAccountInfo byServiceAndEmail = userAccountInfoServiceImpl.getByServiceAndEmail(serviceName, startUserEmail);
            if (byServiceAndEmail != null) {
                byServiceAndEmail.setStatus(3);
                byServiceAndEmail.setCloseReason("流程撤回，开通失败");
                Boolean updateUserAccountInfo = userAccountInfoServiceImpl.updateUserAccountInfo(byServiceAndEmail);
                if (!updateUserAccountInfo) {
                    log.error("[RedFlow流程撤回事件]更新用户账户信息失败，serviceName: {}, userEmail: {}", serviceName, startUserEmail);
                }
            }

            // 3. 流程撤回 日志更新记录
            accountApplyLogInfo.setEndStatus(AccountStatusEnum.FAIL.getDescription());
            accountApplyLogInfo.setStatus(AccountStatusEnum.FAIL.getAccountStatus());
            accountApplyLogInfo.setEndReason("流程被撤回，已废除单据");
        } catch (Exception e) {
            log.error("[RedFlow流程撤回事件]处理失败，出现异常", e);
            if (accountApplyLogInfo != null) {
                accountApplyLogInfo.setEndReason("处理失败，出现异常，请联系AD域和研效组处理");
                accountApplyLogInfo.setDescription(e.getMessage());
            }
        } finally {
            accountApplyLogInfoService.saveLog(accountApplyLogInfo, AccountStatusEnum.FAIL);
        }
        return true;
    }


}
