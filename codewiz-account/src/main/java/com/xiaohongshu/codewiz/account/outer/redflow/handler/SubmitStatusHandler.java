package com.xiaohongshu.codewiz.account.outer.redflow.handler;

import com.alibaba.fastjson.JSON;
import com.xiaohongshu.codewiz.account.bo.RedFlowProcessMessageBodyBO;
import com.xiaohongshu.codewiz.account.enums.RedFlowProcessStatusEnum;
import com.xiaohongshu.codewiz.account.outer.redflow.RedFlowStatusHandler;
import com.xiaohongshu.codewiz.account.outer.redflow.service.SubmitStatusExecuteService;
import com.xiaohongshu.events.client.consumer.ConsumeStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @ClassName SubmitStatusHandler
 * @Description RedFlow流程提交事件
 * @Date 2025/5/21 22:00
 * <AUTHOR>
 */
@Component
@Slf4j
public class SubmitStatusHandler implements RedFlowStatusHandler {

    @Resource
    private SubmitStatusExecuteService submitStatusExecuteService;

    /**
     * 提交事件处理
     *
     * @param message 订阅的消息体
     * @return 消费成功
     */
    @Override
    public ConsumeStatus statusExecute(RedFlowProcessMessageBodyBO message) {
        long startTime = System.currentTimeMillis();
        try {
            // 提交事件处理
            log.info("[RedFlow流程提交事件] userEmail: {}, userId: {}, formNo: {}, process status:{}", message.getStartUserEmail(), message.getStartUserId(), message.getFormNo(), message);
            Boolean submitStatusHandler = submitStatusExecuteService.submitStatusExecute(message);
            return submitStatusHandler ? ConsumeStatus.SUCCESS : ConsumeStatus.FAIL;
        } catch (Exception e) {
            log.error("[RedFlow流程提交事件] userEmail: {}, userId: {}, formNo: {}, process status:{}", message.getStartUserEmail(), message.getStartUserId(), message.getFormNo(), message, e);
            return ConsumeStatus.FAIL;
        } finally {
            long endTime = System.currentTimeMillis();
            log.info("[RedFlow流程提交事件]costMsTime: {}ms, userEmail: {}, userId: {}, formNo: {}, process status: {}", (endTime - startTime), message.getStartUserEmail(), message.getStartUserId(), message.getFormNo(), JSON.toJSONString(message));
        }

    }

    /**
     * 判断当前事件是否是提交事件
     *
     * @param auditStatus    单据维度：执行状态
     * @param processOperate 节点维度：流程状态
     * @param processEnd     节点维度：是否结束
     * @return true：是提交事件，false：不是提交事件
     */
    @Override
    public Boolean judgeStatusProcess(String auditStatus, String processOperate, Boolean processEnd, String flowKey) {
        return RedFlowProcessStatusEnum.SUBMIT.matches(processOperate) && "Activity_submit".equals(flowKey);
    }
}
