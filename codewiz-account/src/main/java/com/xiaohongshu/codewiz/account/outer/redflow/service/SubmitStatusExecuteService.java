package com.xiaohongshu.codewiz.account.outer.redflow.service;

import com.alibaba.fastjson.JSON;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.xiaohongshu.codewiz.account.bo.RedFlowProcessMessageBodyBO;
import com.xiaohongshu.codewiz.account.config.CodexAccountGeneralConfig;
import com.xiaohongshu.codewiz.account.enums.AccountStatusEnum;
import com.xiaohongshu.codewiz.account.outer.redflow.RedFlowProcessService;
import com.xiaohongshu.codewiz.account.outer.stopone.AccountAssignedService;
import com.xiaohongshu.codewiz.account.outer.stopone.AccountStandDetailSearchService;
import com.xiaohongshu.codewiz.account.po.AccountApplyLogInfo;
import com.xiaohongshu.codewiz.account.po.AccountApplyProcessTempInfo;
import com.xiaohongshu.codewiz.account.po.UserAccountInfo;
import com.xiaohongshu.codewiz.account.service.impl.AccountApplyLogInfoServiceImpl;
import com.xiaohongshu.codewiz.account.service.impl.AccountApplyProcessTempInfoServiceImpl;
import com.xiaohongshu.codewiz.account.service.impl.UserAccountInfoServiceImpl;
import com.xiaohongshu.codewiz.account.utils.ProcessUtils;
import com.xiaohongshu.force.lobot.thrift.dto.RuleVerificationResult;
import com.xiaohongshu.force.paploo.thrift.dto.AccountDetailBean;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @ClassName SubmitStatusExecuteService
 * @Description
 * @Date 2025/5/26 16:15
 * <AUTHOR>
 */
@Service
@Slf4j
public class SubmitStatusExecuteService {

    /**
     * 流程中最大席位数
     */
    @ApolloJsonValue("${codex.account.general.config:}")
    private CodexAccountGeneralConfig codexAccountGeneralConfig;

    @Resource
    private RedFlowProcessService redFlowProcessService;

    @Resource
    private AccountAssignedService accountAssignedService;

    @Resource
    private AccountApplyProcessTempInfoServiceImpl accountApplyProcessTempInfoService;

    @Resource
    private AccountStandDetailSearchService accountStandDetailSearchService;

    @Resource
    private AccountApplyLogInfoServiceImpl accountApplyLogInfoService;

    @Resource
    private ProcessUtils processUtils;

    @Resource
    private UserAccountInfoServiceImpl userAccountInfoServiceImpl;

    /**
     * RedFlow流程提交事件处理
     *
     * @param message MQ 流程消息
     */
    public Boolean submitStatusExecute(RedFlowProcessMessageBodyBO message) {
        AccountApplyLogInfo accountApplyLogInfo = AccountApplyLogInfo.builder().build();
        boolean success = true;
        try {
            String formNo = message.getFormNo();
            String startUserEmail = message.getStartUserEmail();
            String serviceName = StringUtils.isEmpty(message.getServiceName()) ? "cursor" : message.getServiceName();


            AccountDetailBean singleAccountDetail = accountStandDetailSearchService.getSingleAccountDetail(message.getStartUserEmail());
            accountApplyLogInfo = processUtils.buildAccountApplyLogInfo(message, singleAccountDetail);
            accountApplyLogInfo.setRequestReason(message.getRequestReason());
            if (singleAccountDetail == null) {
                redFlowProcessService.abandonProcess(formNo, "WORKFLOW_SYSTEM_USER", "申请失败，获取用户账号详情失败，请联系AD域和研效组处理");
                log.error("[][RedFlow流程提交事件]获取用户账号详情失败，用户邮箱：{}", message.getStartUserEmail());
                accountApplyLogInfo.setEndReason("获取用户账号详情失败，请联系AD域和研效组处理");
                success = false;
                return true;
            }
            // 开关控制
            if(!codexAccountGeneralConfig.getOpenSwitch()){
                redFlowProcessService.abandonProcess(formNo, "WORKFLOW_SYSTEM_USER", "当前席位已满，申请通道暂时关闭，将在席位随时释放后自动开启，请自行关注。");
                accountApplyLogInfo.setEndReason("[申请开关关闭] 当前席位已满，申请通道暂时关闭，将在席位随时释放后自动开启，请自行关注。");
                success = false;
                return true;
            }

            // 1. 有效席位判断
            String userId = singleAccountDetail.getChannelUserId();
            boolean validateSeatAvailability = validateSeatAvailability(serviceName, startUserEmail);
            if (!validateSeatAvailability) {
                redFlowProcessService.abandonProcess(formNo, "WORKFLOW_SYSTEM_USER", "当前席位已满，申请通道暂时关闭，将在席位随时释放后自动开启，请自行关注。");
                accountApplyLogInfo.setEndReason("当前席位已满，申请通道暂时关闭，将在席位随时释放后自动开启，请自行关注。");
                success = false;
                return true;
            }
            // 2. 重复判断 2.1. 判断是否存在流程中的用户 2.2. 判断是否已经关联了账号
            // 2.1. 重复判断: 判断是否存在流程中的用户
            List<AccountApplyProcessTempInfo> applyProcessTempInfos = accountApplyProcessTempInfoService.getByServiceAndEmail(serviceName, startUserEmail);
            if (!CollectionUtils.isEmpty(applyProcessTempInfos)) {
                log.info("[][RedFlow流程提交事件] submit 事件，存在流程中的用户，跳过处理，serviceName = {}, userEmail = {}", serviceName, startUserEmail);
                redFlowProcessService.abandonProcess(formNo, "WORKFLOW_SYSTEM_USER", "已在流程中，存在重复申请，请联系管理员处理");
                accountApplyLogInfo.setEndReason("已在流程中，存在重复申请，请联系管理员处理");
                success = false;
                return true;
            }
            // 2.2. 重复判断: 判断是否已经关联了账号
            RuleVerificationResult ruleVerificationResult = accountAssignedService.checkAccountHasAssigned(serviceName, startUserEmail);
            if (ruleVerificationResult == null || ruleVerificationResult.isRuleParseResult()) {
                log.warn("[核查点][RedFlow流程提交事件][重复判断]：存在重复申请情况，废除单据，serviceName = {}", serviceName);
                accountApplyLogInfo.setEndReason("账号已开通，存在重复申请，请联系管理员处理");
                redFlowProcessService.abandonProcess(formNo, "WORKFLOW_SYSTEM_USER", "账号已开通，存在重复申请，请联系管理员处理");
                success = false;
                return true;
            }
            // 3. 用户流程中落库
            AccountApplyProcessTempInfo accountApplyProcessTempInfo = processUtils.buildAccountApplyProcessTempInfo(message, singleAccountDetail);
            if (accountApplyProcessTempInfo == null) {
                log.error("[核查点][RedFlow流程提交事件]构建账户申请流程中实体信息 失败，用户邮箱：{}", startUserEmail);
                accountApplyLogInfo.setEndReason("构建账户申请流程中实体信息失败，请联系AD域和研效组处理");
                redFlowProcessService.abandonProcess(formNo, "WORKFLOW_SYSTEM_USER", "构建账户申请流程中实体信息失败，请联系AD域和研效组处理");
                success = false;
                return true;
            }
            accountApplyProcessTempInfoService.saveAccountApplyProcessTempInfo(accountApplyProcessTempInfo);
            // 用户表里也存一下
            UserAccountInfo userExisted = userAccountInfoServiceImpl.getByServiceAndEmail(serviceName, startUserEmail);
            if(userExisted == null){
                log.warn("[RedFlow流程提交事件], user表不存在，进行新增，用户邮箱：{}，服务名称：{}，单据号：{}", startUserEmail, serviceName, formNo);
                UserAccountInfo userAccountInfo = processUtils.buildUserAccountInfo(message, singleAccountDetail);
                userAccountInfo.setStatus(AccountStatusEnum.PROCESSING.getAccountStatus());
                userAccountInfoServiceImpl.addUserAccountInfo(userAccountInfo);
            } else {
                log.warn("[RedFlow流程提交事件], user表中数据已存在，状态切换为流程中，用户邮箱：{}，服务名称：{}，单据号：{}", startUserEmail, serviceName, formNo);
                userExisted.setStatus(AccountStatusEnum.PROCESSING.getAccountStatus());
                userAccountInfoServiceImpl.updateUserAccountInfo(userExisted);
            }
            // 4. 系统审核：节点审核通过，提交成功
            redFlowProcessService.completeTask(message.getFlowId(), "WORKFLOW_SYSTEM_USER", "有效席位和重复申请核验通过", formNo);
            accountApplyLogInfo.setStatus(AccountStatusEnum.PROCESSING.getAccountStatus());
            log.info("[RedFlow流程提交事件],席位和重复申请检验通过, 流程提交成功，用户邮箱：{}，服务名称：{}，单据号：{}", startUserEmail, serviceName, formNo);
        } catch (Exception e) {
            log.error("[RedFlow流程提交事件]submitStatusHandler]处理失败，出现异常", e);
            accountApplyLogInfo.setEndReason("处理失败，出现异常，请联系AD域和研效组处理");
        } finally {
            accountApplyLogInfoService.saveLog(accountApplyLogInfo, success ? AccountStatusEnum.PROCESSING : AccountStatusEnum.FAIL);
        }
        return true;
    }

    /**
     * 有效席位判断
     *
     * @param serviceName    服务名称
     * @param startUserEmail 用户邮箱
     * @return 是否存在有效席位
     */
    public boolean validateSeatAvailability(String serviceName, String startUserEmail) {
        // 用户账号表查询
        Integer userAccountOpenSum = userAccountInfoServiceImpl.getByServiceAndStatusSumCount(serviceName, AccountStatusEnum.OPEN.getAccountStatus());
        List<String> allPageAccountInfos = accountAssignedService.getAllPageAccountInfos(serviceName);
        if (allPageAccountInfos == null || allPageAccountInfos.size() != userAccountOpenSum) {
            log.warn("[核查点][RedFlow流程提交事件][有效席位判断]：用户表数据与AD域数量不一致，请尽快核查，serviceName = {}", serviceName);
        }
        // 流程中数量
        Integer processCount = accountApplyProcessTempInfoService.getProcessCountByServiceName(serviceName);
        log.info("[RedFlow流程提交事件][有效席位判断]：数量统计，serviceName = {}, 流程中数量 = {}, 已开通账号数量 = {}, 设置的最大席位数量 = {}", serviceName, processCount,userAccountOpenSum, codexAccountGeneralConfig.getMaxCount());
        // 有效席位判断
        if (userAccountOpenSum + processCount >= codexAccountGeneralConfig.getMaxCount()) {
            log.warn("[RedFlow流程提交事件][有效席位超额]，当前用户邮箱：{}，服务名称：{}，已开通席位数：{}，流程中数量：{}，最大席位数：{}", startUserEmail, serviceName, userAccountOpenSum, processCount, codexAccountGeneralConfig.getMaxCount());
            return false;
        }
        return true;
    }

}
