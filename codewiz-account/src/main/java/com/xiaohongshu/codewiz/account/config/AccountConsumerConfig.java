package com.xiaohongshu.codewiz.account.config;

import lombok.Data;

/**
 * @ClassName AccountConsumerConfig
 * @Description
 * @Date 2025/5/21 20:51
 * <AUTHOR>
 */
@Data
public class AccountConsumerConfig {

    private String stopOneDepartureTopic;

    private String stopOneDepartureConsumerGroup;

    private Integer stopOneDepartureClientTimeoutMs;

    private String redFlowProcessTopic;

    private String redFlowProcessConsumerGroup;

    private Integer redFlowProcessClientTimeoutMs;

}
