package com.xiaohongshu.codewiz.account.controller;

import com.alibaba.fastjson.JSON;
import com.xiaohongshu.codewiz.account.outer.redflow.service.SubmitStatusExecuteService;
import com.xiaohongshu.codewiz.account.po.AccountApplyLogInfo;
import com.xiaohongshu.codewiz.account.service.impl.AccountApplyLogInfoServiceImpl;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @ClassName AccountLogController
 * @Description
 * @Date 2025/5/26 17:42
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/codexaccount/api/account/validate")
public class AccountValidateController {

    @Resource
    private SubmitStatusExecuteService submitStatusExecuteService;

    /**
     * 获取所有账号申请日志信息
     *
     * @param serviceName 服务名称
     * @return 账号申请日志信息列表
     */
    @Operation(summary = "获取所有账号申请日志信息")
    @GetMapping("/seat")
    private ResponseEntity<?> getAllAccountApplyLogInfo(@RequestParam("serviceName") String serviceName,@RequestParam(defaultValue = "userEmail", required = false) String userEmail) {
        Boolean validateResult = submitStatusExecuteService.validateSeatAvailability(serviceName, userEmail);
        return ResponseEntity.ok(validateResult);
    }



}
