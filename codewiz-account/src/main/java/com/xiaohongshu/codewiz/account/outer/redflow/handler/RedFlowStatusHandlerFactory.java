package com.xiaohongshu.codewiz.account.outer.redflow.handler;

import com.xiaohongshu.codewiz.account.outer.redflow.RedFlowStatusHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @ClassName RedFlowStatusHandlerFactory
 * @Description RedFlow流程状态处理器工厂类
 * @Date 2025/5/21 22:14
 * <AUTHOR>
 */
@Component
@Slf4j
public class RedFlowStatusHandlerFactory {

    private final List<RedFlowStatusHandler> handlers;

    @Autowired
    public RedFlowStatusHandlerFactory(List<RedFlowStatusHandler> handlers) {
        this.handlers = handlers;
    }

    /**
     * 根据状态参数获取匹配的处理器
     *
     * @param auditStatus    审批状态
     * @param processOperate 操作类型
     * @param processEnd     流程结束标志
     * @return 匹配的处理器，若不存在则返回null
     */
    public RedFlowStatusHandler getHandler(String auditStatus, String processOperate, Boolean processEnd, String flowKey) {

        return handlers.stream()
                .filter(handler -> handler.judgeStatusProcess(auditStatus, processOperate, processEnd, flowKey))
                .findFirst()
                .orElse(null);
    }

}
