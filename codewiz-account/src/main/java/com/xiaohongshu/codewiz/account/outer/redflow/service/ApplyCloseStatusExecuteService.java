package com.xiaohongshu.codewiz.account.outer.redflow.service;

import com.xiaohongshu.codewiz.account.bo.RedFlowProcessMessageBodyBO;
import com.xiaohongshu.codewiz.account.enums.AccountStatusEnum;
import com.xiaohongshu.codewiz.account.outer.redflow.RedFlowProcessService;
import com.xiaohongshu.codewiz.account.outer.stopone.AccountAssignedService;
import com.xiaohongshu.codewiz.account.outer.stopone.AccountStandDetailSearchService;
import com.xiaohongshu.codewiz.account.po.AccountApplyLogInfo;
import com.xiaohongshu.codewiz.account.po.UserAccountInfo;
import com.xiaohongshu.codewiz.account.service.impl.AccountApplyLogInfoServiceImpl;
import com.xiaohongshu.codewiz.account.service.impl.UserAccountInfoServiceImpl;
import com.xiaohongshu.codewiz.account.utils.ProcessUtils;
import com.xiaohongshu.force.lobot.thrift.dto.res.Response;
import com.xiaohongshu.force.paploo.thrift.dto.AccountDetailBean;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * @ClassName ApplyCloseStatusExecuteService
 * @Description
 * @Date 2025/5/26 16:17
 * <AUTHOR>
 */
@Slf4j
@Service
public class ApplyCloseStatusExecuteService {


    @Resource
    private AccountAssignedService accountAssignedService;

    @Resource
    private AccountApplyLogInfoServiceImpl accountApplyLogInfoService;

    @Resource
    private ProcessUtils processUtils;

    @Resource
    private AccountStandDetailSearchService accountStandDetailSearchService;

    @Resource
    private UserAccountInfoServiceImpl userAccountInfoService;

    @Resource
    private RedFlowProcessService redFlowProcessService;

    /**
     * RedFlow申请关闭事件处理
     *
     * @param message MQ 流程消息
     */
    public Boolean applyCloseExecute(RedFlowProcessMessageBodyBO message) {
        AccountApplyLogInfo accountApplyLogInfo = AccountApplyLogInfo.builder().build();
        try {
            String startUserEmail = message.getStartUserEmail();
            String serviceName = StringUtils.isEmpty(message.getServiceName()) ? "cursor" : message.getServiceName();
            // 1. 组装log对象
            AccountDetailBean singleAccountDetail = accountStandDetailSearchService.getSingleAccountDetail(message.getStartUserEmail());
            accountApplyLogInfo = processUtils.buildAccountApplyLogInfo(message, singleAccountDetail);
            // 2. 解除关联
            Response response = accountAssignedService.removeAccount(serviceName, startUserEmail);
            if (response == null || !response.result.success) {
                log.error("[RedFlow关闭账号事件]处理失败，移除账号失败，响应信息: {}", response);
                accountApplyLogInfo.setEndReason("移除账号失败，请联系AD域和研效组处理");
                accountApplyLogInfo.setDescription(response != null ? response.getResult().getMessage() : "响应信息为空");
                return false;
            }
            // 通知审批通过
            redFlowProcessService.completeTask(message.getFlowId(), "WORKFLOW_SYSTEM_USER", "申请关闭审核通过，账号已解除关联", message.getFormNo());
            // 3. 更新用户表
            UserAccountInfo userAccountInfo = userAccountInfoService.getByServiceAndEmail(serviceName, startUserEmail);
            if (userAccountInfo == null) {
                log.warn("[RedFlow申请关闭事件]处理失败，未找到对应的用户账号信息，serviceName: {}, email: {}", serviceName, startUserEmail);
                return false;
            }
            userAccountInfo.setStatus(AccountStatusEnum.CLOSE.getAccountStatus());
            userAccountInfo.setCloseReason("申请关闭");
            userAccountInfo.setCloseTime(LocalDateTime.now());
            userAccountInfoService.updateUserAccountInfo(userAccountInfo);
            // 4.  日志记录
            accountApplyLogInfo.setEndStatus(AccountStatusEnum.CLOSE.getDescription());
            accountApplyLogInfo.setStatus(AccountStatusEnum.CLOSE.getAccountStatus());
            accountApplyLogInfo.setEndReason("申请关闭，已解除关联");
        } catch (Exception e) {
            log.error("[RedFlow申请关闭事件]处理失败，出现异常", e);
            if (accountApplyLogInfo != null) {
                accountApplyLogInfo.setEndReason("处理失败，出现异常，请联系AD域和研效组处理");
                accountApplyLogInfo.setDescription(e.getMessage());
            }
        } finally {
            accountApplyLogInfoService.saveLog(accountApplyLogInfo, AccountStatusEnum.CLOSE);
        }
        return true;
    }

}
