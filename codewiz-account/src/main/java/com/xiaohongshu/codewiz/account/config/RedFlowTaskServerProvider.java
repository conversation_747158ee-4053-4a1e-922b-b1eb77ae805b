package com.xiaohongshu.codewiz.account.config;

import com.xiaohongshu.fls.finance.rpc.workflow.process.operator.OaFlowRuntimeProcessRpc;
import com.xiaohongshu.fls.finance.rpc.workflow.task.operator.OaFlowRuntimeTaskRpc;
import com.xiaohongshu.infra.rpc.client.ClientBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;

/**
 * @ClassName RedFlowTaskServerProvider
 * @Description
 * @Date 2025/5/21 13:16
 * <AUTHOR>
 */
@Configuration
public class RedFlowTaskServerProvider {

    //创建 Client Bean
    @Bean
    public OaFlowRuntimeProcessRpc.Iface oaFlowRuntimeProcessRpc() {
        return ClientBuilder.create(OaFlowRuntimeProcessRpc.Iface.class, "xhsoaworkflow-service-newrpc")
                .withTimeout(Duration.ofMillis(2000))
                .buildStub();
    }


    @Bean
    public OaFlowRuntimeTaskRpc.Iface oaFlowRuntimeTaskRpc() {
        return ClientBuilder.create(OaFlowRuntimeTaskRpc.Iface.class, "xhsoaworkflow-service-newrpc")
                .withTimeout(Duration.ofMillis(2000))
                .buildStub();
    }


}
