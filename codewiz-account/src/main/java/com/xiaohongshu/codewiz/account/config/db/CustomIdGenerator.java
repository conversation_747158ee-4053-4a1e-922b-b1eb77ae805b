package com.xiaohongshu.codewiz.account.config.db;

import com.baomidou.mybatisplus.core.incrementer.IdentifierGenerator;
import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

/**
 * @ClassName CustomIdGenerator
 * @Description id生成器
 * @Date 2025/5/22 20:04
 * <AUTHOR>
 */
@Slf4j
@Component
public class CustomIdGenerator implements IdentifierGenerator {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * 生成ID
     * @param entity 实体对象
     * @return 生成的ID
     */
    @Override
    public Long nextId(Object entity) {
        Class<?> entityClass = null;
        try {
            // 优先从注解获取表名
            entityClass = entity.getClass();
            IdGeneratorTable annotation = entityClass.getAnnotation(IdGeneratorTable.class);
            if (annotation != null) {
                return generateIdFromTable(annotation.value());
            }
            // 回退到通过TableInfo获取
            TableInfo tableInfo = TableInfoHelper.getTableInfo(entityClass);
            if (tableInfo != null) {
                return generateIdFromTable(tableInfo.getTableName());
            }
        } catch (Exception e) {
            log.error("无法确定表名: entityClass = {}", entityClass, e);
            throw new RuntimeException(e);
        }
        return null;
    }

    /**
     * 根据表名生成ID
     * @param tableName 表名
     * @return 生成的ID
     */
    private Long generateIdFromTable(String tableName) {
        String sql = "SELECT IFNULL(MAX(id), 0) + 1 FROM " + tableName;
        return jdbcTemplate.queryForObject(sql, Long.class);
    }

}
