package com.xiaohongshu.codewiz.account.outer.stopone;

import com.alibaba.fastjson.JSON;
import com.xiaohongshu.force.paploo.thrift.api.AccountPubService;
import com.xiaohongshu.force.paploo.thrift.dto.AccountDetailBean;
import com.xiaohongshu.force.paploo.thrift.dto.req.AccountReq;
import com.xiaohongshu.force.paploo.thrift.dto.res.AccountDetailRes;
import com.xiaohongshu.infra.rpc.base.Context;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @ClassName AccountStandSearchService
 * @Description
 * @Date 2025/5/20 11:21
 * <AUTHOR>
 */
@Service
@Slf4j
public class AccountStandDetailSearchService {

    /**
     * 账号查询接口提供的能力：
     * <p>
     * 批量查询账号-RPC AccountPubService#batchGet
     * 分页条件查询账号信息-RPC AccountPubService#searchAccountByPage
     * 关键词检索账号-RPC AccountPubService#searchAccountByKeywords
     * 单个账号信息查询-RPC AccountPubService#getAccountDetail
     * 批量查询账号(主副账号/国内海外账号)-RPC  AccountPubService#batchGetByDomainMail
     */
    @Resource
    private AccountPubService.Iface accountStandSearchService;

    /**
     * 查询单个账号的所有信息
     *
     * @param userEmail 用户邮箱
     * @return AccountDetailBean单个账号的所有信息
     */
    public AccountDetailBean getSingleAccountDetail(String userEmail) {
        long startTime = System.currentTimeMillis();
        AccountReq accountReq = null;
        AccountDetailRes accountDetail = null;
        try {
            // 调用StopOne服务获取单个账号详情
            accountReq = buildAccountReq(userEmail);
            accountDetail = accountStandSearchService.getAccountDetail(new Context(),accountReq );
            if (accountDetail == null || accountDetail.getAccountDetailBean() == null) {
                log.warn("[获取单个账号详情], userEmail : {}, 调用StopOne服务失败，返回结果为: {}", userEmail, JSON.toJSONString(accountDetail));
                return null;
            }
            AccountDetailBean accountDetailBean = accountDetail.getAccountDetailBean();
            log.info("[获取单个账号详情]调用StopOne服务成功，返回结果：{}", accountDetailBean);
            return accountDetailBean;
        } catch (Exception e) {
            log.error("[获取单个账号详情], userEmail : {}, 调用StopOne服务失败，出现异常：", userEmail, e);
            return null;
        }finally {
            log.info("[获取单个账号详情]执行完毕, costMsTime: {}, req: {}, resp：{}", System.currentTimeMillis() - startTime, JSON.toJSONString(accountReq), JSON.toJSONString(accountDetail));
        }
    }

    /**
     * {
     * // 以下参数都不是必传的，但是要必传其一
     * "accountName": "jingyunhe",
     * "accountMail": "<EMAIL>",
     * "porchId": "sdsd7asd6ta87s6d8a76sd",
     * "accountNo": "*********",
     * "channelUserId": "*********",
     * // 以下是测试账号，不传查所有
     * "dataIsolationTypeList":[
     * 1
     * ]
     *
     * @param userEmail 用户邮箱
     * @return AccountReq
     */
    private AccountReq buildAccountReq(String userEmail) {
        AccountReq accountReq = new AccountReq();
        accountReq.setAccountMail(userEmail);
        return accountReq;
    }

}
