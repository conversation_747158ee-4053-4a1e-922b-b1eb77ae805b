package com.xiaohongshu.codewiz.account.mq;

import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.xiaohongshu.codewiz.account.config.AccountConsumerConfig;
import com.xiaohongshu.events.client.consumer.EventsPushConsumer;
import com.xiaohongshu.events.client.producer.EventsProducer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @ClassName MessageConfiguration
 * @Description
 * @Date 2025/5/20 11:59
 * <AUTHOR>
 */
@Configuration
public class MessageConfiguration {

    @ApolloJsonValue("${codex.account.events.consumer.config}")
    private AccountConsumerConfig accountConsumerConfig;

    @Bean
    public MessageLifecycleListener eventOrderConfiguration() {
        return new MessageLifecycleListener();
    }

    /////////////////////////////////stopOne 消息配置///////////////////////////////////////////////////

    @Bean
    public EventsProducer departureTopicProducer() {
        return new EventsProducer(accountConsumerConfig.getStopOneDepartureTopic());
    }

    @Bean
    public DepartureMessageProcessor accountMessageProcessor() {
        return new DepartureMessageProcessor();
    }

    @Bean
    public EventsPushConsumer departureTopicConsumer(DepartureMessageProcessor departureMessageProcessor) {

        EventsPushConsumer consumer = new EventsPushConsumer();
        consumer.setTopic(accountConsumerConfig.getStopOneDepartureTopic());
        consumer.setGroup(accountConsumerConfig.getStopOneDepartureConsumerGroup());
        consumer.setMessageProcessor(departureMessageProcessor);
        consumer.setClientTimeoutMs(accountConsumerConfig.getStopOneDepartureClientTimeoutMs()); // 拉取超时时间
        return consumer;
    }

    /////////////////////////////////redFlow 消息配置///////////////////////////////////////////////////

    @Bean
    public EventsProducer redFlowTopicProducer() {
        return new EventsProducer(accountConsumerConfig.getStopOneDepartureTopic());
    }


    /**
     * RedFlow消息处理器
     *
     * @param messageProcessor 消息处理器
     * @return 消费bean
     */
    @Bean
    public EventsPushConsumer redFlowTopicConsumer(RedFlowMessageProcessor messageProcessor) {

        EventsPushConsumer consumer = new EventsPushConsumer();
        consumer.setTopic(accountConsumerConfig.getRedFlowProcessTopic());
        consumer.setGroup(accountConsumerConfig.getRedFlowProcessConsumerGroup());
        consumer.setMessageProcessor(messageProcessor);
        consumer.setClientTimeoutMs(accountConsumerConfig.getStopOneDepartureClientTimeoutMs()); // 拉取超时时间：60s（默认 15秒）
        return consumer;
    }


}
