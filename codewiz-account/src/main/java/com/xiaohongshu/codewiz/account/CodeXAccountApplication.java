package com.xiaohongshu.codewiz.account;
import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

@SpringBootApplication(scanBasePackages = {"com.xiaohongshu.infra", "com.xiaohongshu.codewiz.account"})
@EnableApolloConfig
public class CodeXAccountApplication {

	public static void main(String[] args) {
		SpringApplication.run(CodeXAccountApplication.class, args);
	}

}
