package com.xiaohongshu.codewiz.account.config;

import com.xiaohongshu.force.lobot.thrift.api.LobotThriftService;
import com.xiaohongshu.force.paploo.thrift.api.AccountPubService;
import com.xiaohongshu.infra.rpc.client.ClientBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;

/**
 * @ClassName StopOneThriftServerAddressProvider
 * @Description
 * @Date 2025/5/19 20:32
 * <AUTHOR>
 */
@Configuration
public class StopOneThriftServerAddressProvider {

    //创建 Client Bean
    @Bean
    public LobotThriftService.Iface lobotPubService() {
        return ClientBuilder.create(LobotThriftService.Iface.class, "lobot-service-default")
                .withTimeout(Duration.ofMillis(2000))
                .buildStub();
    }


    @Bean
    public AccountPubService.Iface accountStandSearchService() {
        return ClientBuilder.create(AccountPubService.Iface.class, "plo-middle-default")
                .withTimeout(Duration.ofMillis(2000))
                .buildStub();
    }

}
