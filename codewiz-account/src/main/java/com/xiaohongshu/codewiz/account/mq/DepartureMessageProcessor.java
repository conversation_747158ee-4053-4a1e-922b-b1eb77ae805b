package com.xiaohongshu.codewiz.account.mq;

import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xiaohongshu.codewiz.account.bo.DepartureMessageBodyBO;
import com.xiaohongshu.codewiz.account.enums.AccountStatusEnum;
import com.xiaohongshu.codewiz.account.enums.DepqtureMessageTypeEnum;
import com.xiaohongshu.codewiz.account.outer.redflow.RedFlowProcessService;
import com.xiaohongshu.codewiz.account.outer.stopone.AccountAssignedService;
import com.xiaohongshu.codewiz.account.po.AccountApplyLogInfo;
import com.xiaohongshu.codewiz.account.po.AccountApplyProcessTempInfo;
import com.xiaohongshu.codewiz.account.po.UserAccountInfo;
import com.xiaohongshu.codewiz.account.service.impl.AccountApplyLogInfoServiceImpl;
import com.xiaohongshu.codewiz.account.service.impl.AccountApplyProcessTempInfoServiceImpl;
import com.xiaohongshu.codewiz.account.service.impl.UserAccountInfoServiceImpl;
import com.xiaohongshu.events.client.MessageExt;
import com.xiaohongshu.events.client.api.MessageProcessor;
import com.xiaohongshu.events.client.consumer.ConsumeContext;
import com.xiaohongshu.events.client.consumer.ConsumeStatus;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @ClassName AccountMessageProcessor
 * @Description
 * @Date 2025/5/20 12:02
 * <AUTHOR>
 */
@Service
@Slf4j
public class DepartureMessageProcessor implements MessageProcessor {

    private final ObjectMapper objectMapper = new ObjectMapper().findAndRegisterModules();

    @Resource
    private UserAccountInfoServiceImpl userAccountInfoService;

    @Resource
    private AccountAssignedService accountAssignedService;

    @Resource
    private AccountApplyLogInfoServiceImpl accountApplyLogInfoService;

    @Resource
    private AccountApplyProcessTempInfoServiceImpl accountApplyProcessTempInfoService;

    @Resource
    private RedFlowProcessService redFlowProcessService;

    @Override
    public ConsumeStatus process(MessageExt messageExt, ConsumeContext consumeContext) {
        DepartureMessageBodyBO departureMessageBodyBO = null;
        long startTime = System.currentTimeMillis();
        boolean consumeSuccess = false;
        try {
            String jsonStr = new String(messageExt.getBody(), StandardCharsets.UTF_8);
//            log.info("[人事][消息消费]收到消息: messageExt = {}; consumeContext = {}", jsonStr, JSON.toJSONString(consumeContext));
            departureMessageBodyBO = objectMapper.readValue(jsonStr, DepartureMessageBodyBO.class);
            if (departureMessageBodyBO == null || !DepqtureMessageTypeEnum.CLOSE.getEventType().equals(departureMessageBodyBO.getEventType())) {
                return ConsumeStatus.FAIL;
            }
            consumeSuccess = true;
            log.info("[人事][离职消息消费]收到离职事件消息: message = {}", JSON.toJSONString(departureMessageBodyBO));
            // 1. 离职账号信息查询
            List<UserAccountInfo> openUsers = userAccountInfoService.getByEmailAndStatus(departureMessageBodyBO.getAccountMail(), AccountStatusEnum.OPEN.getAccountStatus());
            if (CollectionUtils.isEmpty(openUsers)) {
                log.info("[人事][离职消息消费]员工未开通账号，不做处理， userEmail = {}", departureMessageBodyBO.getAccountMail());
                return ConsumeStatus.SUCCESS;
            }
            for (UserAccountInfo userAccountInfo : openUsers) {
                // 2.1 解除AD关联
                accountAssignedService.removeAccount(userAccountInfo.getServiceName(), userAccountInfo.getUserEmail());
                // 2.2 还在流程中的，直接删除掉
                List<AccountApplyProcessTempInfo> processingList = accountApplyProcessTempInfoService.getByServiceAndEmail(userAccountInfo.getServiceName(), userAccountInfo.getUserEmail());
                if (!CollectionUtils.isEmpty(processingList)) {
                    log.info("[人事][离职消息消费]员工账号还在流程中，终止流程，删除数据: processingList = {}", JSON.toJSONString(processingList));
                    for (AccountApplyProcessTempInfo accountApplyProcessTempInfo : processingList) {
                        // 2.2.1 终止申请中的流程---审核拒绝
                        redFlowProcessService.abandonProcess(accountApplyProcessTempInfo.getRequestId(), accountApplyProcessTempInfo.getUserId(), "员工已离职，流程废弃");
                        // 2.2.2 删除临时表数据
                        accountApplyProcessTempInfoService.delAccountApplyProcessTempInfo(accountApplyProcessTempInfo);
                    }
                }
                // 2.3 离职账号信息更新
                userAccountInfo.setStatus(AccountStatusEnum.CLOSE.getAccountStatus());
                userAccountInfo.setCloseTime(LocalDateTime.now());
                userAccountInfo.setCloseReason("员工已离职");
                userAccountInfoService.updateById(userAccountInfo);
                log.info("[人事][离职消息消费], 账号关闭信息更新成功: userAccountInfo = {}", JSON.toJSONString(userAccountInfo));
                // 2.3 记录日志信息
                AccountApplyLogInfo accountApplyLogInfo = buildAccountApplyLogInfo(userAccountInfo);
                accountApplyLogInfoService.addAccountApplyLogInfo(accountApplyLogInfo);
            }
            log.info("[人事][离职消息消费][账号解除], 完成: message = {}", JSON.toJSONString(departureMessageBodyBO));
            return ConsumeStatus.SUCCESS;
        } catch (Exception e) {
            log.info("[人事][离职消息消费]消息处理失败:, 出现异常, messageExt = {}; consumeContext = {}", messageExt, JSON.toJSONString(consumeContext), e);
            return ConsumeStatus.FAIL;
        } finally {
            if (consumeSuccess) {
                log.info("[离职消息消费]消费处理完成: costMsTime = {}, messageExt = {}; consumeContext = {}", System.currentTimeMillis() - startTime, messageExt, JSON.toJSONString(consumeContext));
            }
        }
    }


    /**
     * 构建账号申请日志信息
     *
     * @param userAccountInfo 用户账号信息
     * @return AccountApplyLogInfo
     */
    private AccountApplyLogInfo buildAccountApplyLogInfo(UserAccountInfo userAccountInfo) {
        return AccountApplyLogInfo.builder()
                .userEmail(userAccountInfo.getUserEmail())
                .serviceName(userAccountInfo.getServiceName())
                .username(userAccountInfo.getUsername())
                .requestReason("用户离职，账号处理")
                .departmentId(userAccountInfo.getDepartmentId())
                .departmentName(userAccountInfo.getDepartmentName())
                .departmentNamePath(userAccountInfo.getDepartmentNamePath())
                .departmentNamePathId(userAccountInfo.getDepartmentNamePathId())
                .endReason("员工已离职，账号处理完成")
                .endStatus(AccountStatusEnum.CLOSE.getDescription())
                .userId(userAccountInfo.getUserId())
                .build();
    }


}

