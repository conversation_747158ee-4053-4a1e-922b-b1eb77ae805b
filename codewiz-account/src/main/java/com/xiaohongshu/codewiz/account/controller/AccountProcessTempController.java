package com.xiaohongshu.codewiz.account.controller;

import com.xiaohongshu.codewiz.account.po.AccountApplyProcessTempInfo;
import com.xiaohongshu.codewiz.account.service.impl.AccountApplyProcessTempInfoServiceImpl;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * @ClassName AccountProcessTempController
 * @Description
 * @Date 2025/5/26 17:42
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/codexaccount/api/account/processing")
public class AccountProcessTempController {

    @Resource
    private AccountApplyProcessTempInfoServiceImpl accountApplyProcessTempInfoService;

    /**
     * 获取所有流程中的账号申请信息
     *
     * @param serviceName 服务名称
     * @return 账号申请日志信息列表
     */
    @Operation(summary = "获取所有流程中的账号申请信息")
    @GetMapping("/all")
    public ResponseEntity<List<AccountApplyProcessTempInfo>> getAllAccountApplyLogInfo(@RequestParam("serviceName") String serviceName) {
        if (StringUtils.isEmpty(serviceName)) {
            log.warn("[获取所有在流程中的数据] 获取账号信息失败：serviceName为空");
            return ResponseEntity.badRequest().build();
        }
        List<AccountApplyProcessTempInfo> result = accountApplyProcessTempInfoService.getAllByService(serviceName);
        if (result.isEmpty()) {
            log.info("[获取所有在流程中的数据] 未找到匹配的账号信息: serviceName={}", serviceName);
            return ResponseEntity.ok(Collections.emptyList());  // 返回空列表而非 404
        }
        log.info("[获取所有在流程中的数据] 成功获取账号信息: serviceName={}, 数量={}", serviceName, result.size());
        return ResponseEntity.ok(result);
    }


    /**
     * 根据用户邮箱、服务名称 查询流程中的用户账号信息
     *
     * @param serviceName 服务名称
     * @param userEmail   用户邮箱
     * @return 用户账号信息
     */
    @Operation(summary = "根据服务名称和邮箱查询流程中的账号信息")
    @GetMapping("/getByServiceAndEmail")
    public ResponseEntity<List<AccountApplyProcessTempInfo>> getByServiceAndEmail(
            @RequestParam("serviceName") String serviceName,
            @RequestParam("userEmail") String userEmail) {
        if (StringUtils.isEmpty(serviceName) || StringUtils.isEmpty(userEmail)) {
            return ResponseEntity.badRequest().body(Collections.emptyList());
        }
        List<AccountApplyProcessTempInfo> result = accountApplyProcessTempInfoService.getByServiceAndEmail(serviceName, userEmail);
        log.info("查询结果: serviceName={}, userEmail={}, 数量={}", serviceName, userEmail, result.size());
        return ResponseEntity.ok(result);
    }

    /**
     * 根据单据号查询账号申请日志信息
     *
     * @param requestId 单据号
     * @return 账号申请日志信息
     */
    @Operation(summary = "根据单据号查询流程中的账号信息")
    @GetMapping("/getByRequestId")
    public ResponseEntity<List<AccountApplyProcessTempInfo>> getByRequestId(
            @RequestParam("requestId") String requestId) {
        if (StringUtils.isEmpty(requestId)) {
            return ResponseEntity.badRequest().body(Collections.emptyList());
        }
        List<AccountApplyProcessTempInfo> result = accountApplyProcessTempInfoService.getByRequestId(requestId);
        log.info("[获取流程中账号信息], 查询结果: requestId={}, 数量={}", requestId, result.size());
        return ResponseEntity.ok(result);
    }


    /**
     * 根据单据号查询账号申请日志信息
     *
     * @param serviceName 服务名称
     * @param userEmail   用户邮箱
     * @return 账号申请日志信息
     */
    @Operation(summary = "删除流程中的账号信息")
    @DeleteMapping("/del/{serviceName}/{userEmail}")
    public ResponseEntity<Void> deleteByServiceAndEmail(@PathVariable String serviceName, @PathVariable String userEmail) {
        if (StringUtils.isEmpty(serviceName) || StringUtils.isEmpty(userEmail)) {
            log.warn("[删除流程中账号信息]，删除账号信息失败：serviceName或userEmail为空");
            return ResponseEntity.badRequest().build();
        }
        try {
            // 执行删除操作
            boolean deleted = accountApplyProcessTempInfoService.delByServiceNameAndEmail(serviceName, userEmail);
            if (deleted) {
                log.info("[删除流程中账号信息] 成功删除账号信息: serviceName={}, userEmail={}", serviceName, userEmail);
                return ResponseEntity.noContent().build();
            } else {
                log.info("[删除流程中账号信息] 未找到匹配的账号信息: serviceName={}, userEmail={}", serviceName, userEmail);
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            log.error("[删除流程中账号信息] 删除账号信息失败: serviceName={}, userEmail={}", serviceName, userEmail, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }


}
