package com.xiaohongshu.codewiz.account.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @ClassName DepartureMessageBodyBO
 * @Description
 * @Date 2025/5/20 19:37
 * <AUTHOR>
 */
@Data
public class DepartureMessageBodyBO {

    /**
     * {
     *     "event":"2023-11-30 17:30:26",
     *     "eventType":"CREATE",
     *     "accountMail":"<EMAIL>",
     *     "accountName":"test",
     *     "accountType":1, //1-员工
     *     "channelUserId":"W15505", //员工账号才有，人事userID，注意：正编&实习 和 bpo不一样
     *     "accountSourceChannel":"EHR"
     * }
     */

    @JsonProperty("accountName")
    private String accountName;

    @JsonProperty("accountMail")
    private String accountMail;

    @JsonProperty("accountType")
    private Integer accountType;

    @JsonProperty("accountSourceChannel")
    private String accountSourceChannel;

    /**
     * CREATE 创建 / ENABLE 启用 / UPDATE 修改 / PWD_UPDATE 密码更新 / DISABLE 禁用账号 / CLOSE 关闭账号
     */
    @JsonProperty("eventType")
    private String eventType; // 例如: "ENABLE"

    @JsonProperty("channelUserId")
    private String channelUserId;

    @JsonProperty("event")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime eventTime;

    @JsonProperty("dataIsolationType")
    private Integer dataIsolationType;


}
