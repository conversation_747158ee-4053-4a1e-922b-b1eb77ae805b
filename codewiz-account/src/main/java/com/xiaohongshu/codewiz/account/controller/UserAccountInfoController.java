package com.xiaohongshu.codewiz.account.controller;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xiaohongshu.codewiz.account.bo.BatchOperationResult;
import com.xiaohongshu.codewiz.account.dto.CommonResponse;
import com.xiaohongshu.codewiz.account.dto.UserAccountQueryDTO;
import com.xiaohongshu.codewiz.account.enums.AccountStatusEnum;
import com.xiaohongshu.codewiz.account.outer.stopone.AccountAssignedService;
import com.xiaohongshu.codewiz.account.po.UserAccountInfo;
import com.xiaohongshu.codewiz.account.service.impl.AccountApplyProcessTempInfoServiceImpl;
import com.xiaohongshu.codewiz.account.service.impl.UserAccountInfoServiceImpl;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

/**
 * @ClassName UserAccountInfoController
 * @Description
 * @Date 2025/5/16 20:43
 * <AUTHOR>
 */
@RestController
@RequestMapping("/codexaccount/api/account/user")
@Slf4j
@Tag(name = "用户账号信息接口")
public class UserAccountInfoController {

    @Resource
    private UserAccountInfoServiceImpl userAccountInfoService;

    @Resource
    private AccountAssignedService accountAssignedService;

    @Resource
    private AccountApplyProcessTempInfoServiceImpl accountApplyProcessTempInfoService;

    @Operation(summary = "获取所有用户账号信息")
    @GetMapping("/all")
    public CommonResponse<?> getAllUserInfo() {
        List<UserAccountInfo> userAccountInfoList = userAccountInfoService.list();
        if (CollectionUtils.isEmpty(userAccountInfoList)) {
            log.warn("[查询全部用户数据] 数据为空，请核对DB");
            return CommonResponse.notFound();
        }
        log.info("[查询全部用户数据] 查询成功. result = {}", JSON.toJSONString(userAccountInfoList));
        return CommonResponse.success(userAccountInfoList);
    }

    /**
     * 分页查询用户账号信息
     *
     * @return 分页结果
     */
    @PostMapping("/condition/search")
    public CommonResponse<?> doConditionSearchUsers(@RequestBody UserAccountQueryDTO queryDTO) {
        try {
            log.info("[分页查询用户账号信息] 查询条件: {}", JSON.toJSONString(queryDTO));
            Page<UserAccountInfo> resultPage = userAccountInfoService.queryUserAccounts(queryDTO);
            if (resultPage == null || CollectionUtils.isEmpty(resultPage.getRecords())) {
                log.warn("[分页查询用户账号信息] 查询结果为空，请核验DB. queryDTO = {}", JSON.toJSONString(queryDTO));
                return CommonResponse.notFound();
            }
            for (UserAccountInfo user : resultPage.getRecords()) {
                log.info("用户: {}, 邮箱: {}", user.getUsername(), user.getUserEmail());
                log.info("[分页查询用户账号信息] 查询结果: 用户: {}", JSON.toJSONString(user));
            }
            log.info("结果= {}", JSON.toJSONString(ResponseEntity.ok(resultPage)));
            return CommonResponse.success(resultPage);
        } catch (Exception e) {
            log.error("[分页查询用户账号信息] 查询失败，出现异常", e);
            return CommonResponse.builder().code(500).message("查询失败，出现异常，请联系管理员处理").build();
        }
    }

    @Operation(summary = "批量取消账号关联")
    @PostMapping("/batchCloseAccount")
    public CommonResponse<?> batchCloseAccount(@RequestBody List<UserAccountQueryDTO> queryDTOS) {
        try {
            BatchOperationResult result = userAccountInfoService.batchCloseAccounts(queryDTOS);

            if (result.hasFailures()) {
                log.warn("[批量取消账号关联][部分失败] 成功数量: {}, 失败数量: {}", result.getSuccessCount(), result.getFailedCount());
                // 构建详细的失败信息
                StringBuilder failedInfo = new StringBuilder();
                for (int i = 0; i < result.getFailedItems().size(); i++) {
                    BatchOperationResult.FailedItem failedItem = result.getFailedItems().get(i);
                    failedInfo.append(failedItem.getEmail()).append("(").append(failedItem.getReason()).append("),");
                    if (i == result.getFailedItems().size() - 1) {
                        failedInfo.append("等").append(result.getFailedCount()).append("个账号取消关联失败");
                    }
                }
                String failMessage = "批量取消账号关联部分失败: 成功" + result.getSuccessCount() + "个，失败" + result.getFailedCount() + "个。失败详情: " + failedInfo;
                return CommonResponse.builder().code(207).message(failMessage).build();
            }
            return CommonResponse.success("批量取消账号关联成功，共处理" + result.getSuccessCount() + "个账号");
        } catch (Exception e) {
            log.error("[批量取消账号关联] 批量取消账号关联失败，出现异常", e);
            return CommonResponse.builder().code(500).message("批量取消账号关联失败，出现异常，请联系管理员处理").build();
        }
    }

    /**
     * 系统自核验账号与AD域分配情况
     *
     * @param serviceName 服务名称
     * @return 分配情况
     */
    @Operation(summary = "系统自核验账号与AD域分配情况")
    @GetMapping("/check/assigned")
    public CommonResponse<?> checkAssigned(@RequestParam(value = "serviceName") String serviceName) {
        try {
            List<String> checkUserAssigned = userAccountInfoService.systemCheckUserAssigned(serviceName);
            if (CollectionUtils.isEmpty(checkUserAssigned)) {
                log.info("[检查账号分配] 所有账号均已分配");
                return CommonResponse.success("所有账号均已在AD域分配");
            } else {
                String message = "以下账号在AD域未分配，已更新至关闭状态: " + String.join(", ", checkUserAssigned);
                log.warn("[检查账号分配] {}", message);
                return CommonResponse.builder().code(207).message(message).build();
            }
        } catch (Exception e) {
            log.error("[检查账号分配] 检查失败，出现异常", e);
            return CommonResponse.builder().code(500).message("检查失败，出现异常，请联系管理员处理").build();
        }
    }


    /**
     * 根据服务名称、用户邮箱 查询用户账号信息
     *
     * @param serviceName 开通服务名称，Cursor
     * @param userEmail   用户邮箱
     * @param status      状态
     * @return 用户账号信息
     */
    @Operation(summary = "根据服务名称和邮箱和状态查询用户账号信息")
    @GetMapping("/getByServiceAndEmailAndStatus")
    public CommonResponse<?> getUserInfoByServiceAndEmailAndStatus(@RequestParam("serviceName") String serviceName,
                                                                   @RequestParam("userEmail") String userEmail,
                                                                   @RequestParam("status") Integer status) {
        if (StringUtils.isEmpty(serviceName) || StringUtils.isEmpty(userEmail) || status == null) {
            log.warn("[查询用户数据] 重要参数缺失，查询失败. serviceName = {}, userEmail = {}, status = {}", serviceName, userEmail, status);
            return CommonResponse.badRequest("[查询用户数据] 重要参数缺失，查询失败.");
        }
        UserAccountInfo userAccountInfo = userAccountInfoService.getByEmailAndServiceAndStatus(serviceName, userEmail, status);
        if (userAccountInfo == null) {
            log.warn("[查询用户数据] 查询结果为空，请核验DB. service: {},  email: {}, status: {}", serviceName, userEmail, status);
            return CommonResponse.notFound();
        }
        log.info("[查询用户数据] 查询成功. service: {}, email: {}, status: {}, result = {}", serviceName, userEmail, status, JSON.toJSONString(userAccountInfo));
        return CommonResponse.success(userAccountInfo);
    }

    /**
     * 根据服务名称、用户邮箱 查询用户账号信息
     *
     * @param userEmail 用户邮箱
     * @return 用户账号信息
     */
    @Operation(summary = "根据邮箱和已开通状态查询用户账号信息")
    @GetMapping("/getByEmailAndOpenStatus")
    public CommonResponse<?> getUserInfoByEmailAndOpenStatus(@RequestParam("userEmail") String userEmail) {
        try {
            if (StringUtils.isEmpty(userEmail)) {
                log.warn("[查询用户已开通数据] 重要参数缺失，查询失败.  userEmail = {}, status = {}", userEmail, 2);
                return CommonResponse.badRequest("[查询用户已开通数据] 重要参数缺失，查询失败.");
            }
            List<UserAccountInfo> userAccountInfos = userAccountInfoService.getByEmailAndStatus(userEmail, 2);
            if (CollectionUtils.isEmpty(userAccountInfos)) {
                log.warn("[查询用户已开通数据] 查询结果为空，请核验DB. email: {}, status: {}", userEmail, 2);
                return CommonResponse.notFound();
            }
            log.info("[查询用户数据] 查询成功. email: {}, status: {}, result = {}", userEmail, 2, JSON.toJSONString(userAccountInfos));
            return CommonResponse.success(userAccountInfos);
        } catch (Exception e) {
            log.error("[查询用户已开通数据] userEmail = {}, 查询失败，出现异常", userEmail, e);
            return CommonResponse.builder().code(500).message("查询失败，出现异常，请联系管理员处理").build();
        }
    }

    /**
     * 根据服务名称、用户邮箱 查询用户账号信息
     *
     * @param serviceName 开通服务名称，Cursor
     * @param userEmail   用户邮箱
     * @return 用户账号信息
     */
    @Operation(summary = "根据服务名称和邮箱查询用户账号信息")
    @GetMapping("/getByServiceAndEmail")
    public CommonResponse<?> getUserInfoByServiceAndEmail(@RequestParam("serviceName") String serviceName,
                                                          @RequestParam("userEmail") String userEmail) {
        try {
            UserAccountInfo userAccountInfo = userAccountInfoService.getByServiceAndEmail(serviceName, userEmail);
            if (userAccountInfo == null) {
                log.warn("[查询用户数据] 查询结果为空，请核验DB. serviceName = {}, userEmail = {}", serviceName, userEmail);
                return CommonResponse.notFound();
            }
            log.info("[查询用户数据] 查询成功.  service: {}, email: {}, result = {}", serviceName, userEmail, JSON.toJSONString(userAccountInfo));
            return CommonResponse.success(userAccountInfo);
        } catch (Exception e) {
            log.error("[查询用户数据] service = {}, userEmail = {}, 查询失败，出现异常", serviceName, userEmail, e);
            return CommonResponse.builder().code(500).message("查询失败，出现异常，请联系管理员处理").build();
        }
    }

    /**
     * 根据用户邮箱、服务名称和状态查询用户账号信息
     *
     * @param serviceName 开通服务名称，Cursor
     * @param username    用户名
     * @return 用户账号信息
     */
    @Operation(summary = "根据服务名称和用户名查询用户账号信息")
    @GetMapping("/getByServiceAndName")
    public CommonResponse<?> getUserInfoByServiceAndName(@RequestParam("serviceName") String serviceName,
                                                         @RequestParam("username") String username) {
        try {
            UserAccountInfo userAccountInfo = userAccountInfoService.getByServiceAndName(serviceName, username);
            if (userAccountInfo == null) {
                log.warn("[查询用户数据] 查询结果为空，请核验DB. service: {},  username: {}", serviceName, username);
                return CommonResponse.notFound();
            }
            log.info("[查询用户数据] 查询成功.  service: {}, username: {}, result = {}", serviceName, username, JSON.toJSONString(userAccountInfo));
            return CommonResponse.success(userAccountInfo);
        } catch (Exception e) {
            log.error("[查询用户数据] service = {}, username = {}, 查询失败，出现异常", serviceName, username, e);
            return CommonResponse.builder().code(500).message("查询失败，出现异常，请联系管理员处理").build();
        }
    }

    /**
     * 根据服务名称和状态查询用户账号信息
     *
     * @param serviceName 开通服务名称，Cursor
     * @return 用户账号信息
     */
    @Operation(summary = "根据服务名称查询用户账号信息")
    @GetMapping("/getByService")
    public CommonResponse<?> getUserInfoByService(@RequestParam("serviceName") String serviceName) {
        try {
            List<UserAccountInfo> userAccountInfoList = userAccountInfoService.getAllByService(serviceName);
            if (CollectionUtils.isEmpty(userAccountInfoList)) {
                log.warn("[查询用户数据] 查询结果为空，请核验DB. service: {}, result = {}", serviceName, JSON.toJSONString(userAccountInfoList));
                return CommonResponse.notFound();
            }
            log.info("[查询用户数据] 查询成功.  service: {}, result = {}", serviceName, JSON.toJSONString(userAccountInfoList));
            return CommonResponse.success(userAccountInfoList);
        } catch (Exception e) {
            log.error("[查询用户数据] service = {}, 查询失败，出现异常", serviceName, e);
            return CommonResponse.builder().code(500).message("查询失败，出现异常，请联系管理员处理").build();
        }
    }

    /**
     * 根据服务名称和状态查询用户账号信息
     *
     * @param departmentId 部门id
     * @return 用户账号信息
     */
    @Operation(summary = "根据部门id查询用户账号信息")
    @GetMapping("/getByDepartmentId")
    public CommonResponse<?> getUserInfoByDepartmentId(@RequestParam("departmentId") String departmentId) {
        try {
            List<UserAccountInfo> userAccountInfoList = userAccountInfoService.getAllByDepartmentId(departmentId);
            if (CollectionUtils.isEmpty(userAccountInfoList)) {
                log.warn("[查询用户数据] 查询结果为空，请核验DB. departmentId: {}, result = {}", departmentId, JSON.toJSONString(userAccountInfoList));
                return CommonResponse.notFound();
            }
            log.info("[查询用户数据] 查询成功.  departmentId: {}, result = {}", departmentId, JSON.toJSONString(userAccountInfoList));
            return CommonResponse.success(userAccountInfoList);
        } catch (Exception e) {
            log.error("[查询用户数据] departmentId = {}, 查询失败，出现异常", departmentId, e);
            return CommonResponse.builder().code(500).message("查询失败，出现异常，请联系管理员处理").build();
        }
    }

    /**
     * 新增用户信息
     *
     * @param userAccountInfo 用户信息
     * @return 用户账号信息
     */
    @Operation(summary = "新增用户账号信息")
    @PostMapping("/add")
    public CommonResponse<?> addUserAccountInfo(@RequestBody UserAccountInfo userAccountInfo) {
        try {
            if (userAccountInfo == null || userAccountInfo.isNotValid()) {
                log.warn("[新增用户数据] 重要参数缺失, 请核验. userAccountInfo = {}", JSON.toJSONString(userAccountInfo));
                return CommonResponse.badRequest("[新增用户数据] 重要参数缺失, 请核验.");
            }
            userAccountInfoService.addUserAccountInfo(userAccountInfo);
            log.info("U[新增用户数据] 成功: {}", userAccountInfo);
            return CommonResponse.success(userAccountInfo);
        } catch (Exception e) {
            log.error("[新增用户数据] 失败，出现异常, userAccountInfo = {}", JSON.toJSONString(userAccountInfo), e);
            return CommonResponse.builder().code(500).message("新增用户数据失败，出现异常，请联系管理员处理").build();
        }
    }

    /**
     * 删除用户信息
     *
     * @return 用户账号信息
     */
    @Operation(summary = "删除用户账号信息")
    @PostMapping("/delete")
    public CommonResponse<?> deleteUserAccountInfo(@RequestBody List<UserAccountQueryDTO> queryDTOS) {
        try {
            for (UserAccountQueryDTO queryDTO : queryDTOS) {
                if (queryDTO == null || StringUtils.isEmpty(queryDTO.getServiceName()) || StringUtils.isEmpty(queryDTO.getUserEmail())) {
                    log.warn("[删除用户数据] 重要参数缺失, 请核验. queryDTO = {}", JSON.toJSONString(queryDTO));
                    return CommonResponse.badRequest("[删除用户数据] 重要参数缺失, 请核验.");
                }
                userAccountInfoService.delUserInfo(queryDTO.getServiceName(), queryDTO.getUserEmail());
            }
            return CommonResponse.success("删除完毕");
        } catch (Exception e) {
            log.error("[删除用户数据] 失败，出现异常, queryDTOS = {}", JSON.toJSONString(queryDTOS), e);
            return CommonResponse.builder().code(500).message("删除用户数据失败，出现异常，请联系管理员处理").build();
        }
    }

    /**
     * 删除用户信息
     *
     * @return 用户账号信息
     */
    @Operation(summary = "删除用户账号信息")
    @PostMapping("/deleteIds")
    public CommonResponse<?> deleteUserAccountInfoByIds(@RequestBody List<Long> ids) {
        try {
            Boolean delUserInfoByIds = userAccountInfoService.delUserInfoByIds(ids);
            return CommonResponse.success(delUserInfoByIds ? "数据删除成功" : "数据删除失败");
        } catch (Exception e) {
            log.error("[批量删除用户数据] 失败，出现异常, ids = {}", JSON.toJSONString(ids), e);
            return CommonResponse.builder().code(500).message("批量删除用户数据，出现异常，请联系管理员处理").build();
        }
    }

    /**
     * 删除用户信息
     *
     * @return 用户账号信息
     */
    @Operation(summary = "删除重复用户")
    @PostMapping("/delRepeat")
    public CommonResponse<?> deleteRepeat(@RequestBody List<UserAccountQueryDTO> queryDTOS) {
        try {
            for (UserAccountQueryDTO user: queryDTOS){
                if(StringUtils.isEmpty(user.getServiceName()) && StringUtils.isEmpty(user.getUserEmail())){
                    delRepeatUser(user.getServiceName(), user.getUserEmail());
                }
            }
            return CommonResponse.success("删除重复用户成功，保留Open的一条，非Open的一条数据");
        } catch (Exception e) {
            log.error("[删除重复用户成功] 失败，出现异常, queryDTOS = {} ", JSON.toJSONString(queryDTOS), e);
            return CommonResponse.builder().code(500).message("删除重复用户成功，出现异常，请联系管理员处理：" + e.getMessage()).build();
        }
    }

    /**
     * 新增用户信息
     *
     * @param userAccountInfo 管理员信息
     * @return 用户账号信息
     */
    @Operation(summary = "更新用户账号信息")
    @PostMapping("/update")
    public CommonResponse<?> updateUserAccountInfo(UserAccountInfo userAccountInfo) {
        try {
            if (userAccountInfo == null || userAccountInfo.isNotValid()) {
                log.warn("[更新用户数据] 重要参数缺失, 请核验. userAccountInfo = {}", JSON.toJSONString(userAccountInfo));
                return CommonResponse.badRequest("[更新用户数据] 重要参数缺失, 请核验.");
            }
            return CommonResponse.success(userAccountInfoService.updateUserAccountInfo(userAccountInfo));
        } catch (Exception e) {
            log.error("[更新用户数据] 失败，出现异常, userAccountInfo = {}", JSON.toJSONString(userAccountInfo), e);
            return CommonResponse.builder().code(500).message("更新用户数据失败，出现异常，请联系管理员处理").build();
        }
    }

    /**
     * 删除重复数据
     * @param serviceName 服务名称
     * @param userEmail 用户邮箱
     */
    private void delRepeatUser(String serviceName, String userEmail) {
        List<UserAccountInfo> list = userAccountInfoService.getListByServiceAndEmail(serviceName, userEmail);
        if (CollectionUtils.isNotEmpty(list) && list.size() > 1) {
            // 优先保留一条OPEN状态
            Optional<UserAccountInfo> openOpt = list.stream()
                    .filter(u -> AccountStatusEnum.OPEN.getAccountStatus().equals(u.getStatus()))
                    .findFirst();
            UserAccountInfo toKeep;
            // 没有OPEN，保留第一条
            toKeep = openOpt.orElseGet(() -> list.get(0));
            for (UserAccountInfo u : list) {
                if (!u.equals(toKeep)) {
                    userAccountInfoService.delUserAccountInfo(u);
                }
            }
        }
    }


}
