package com.xiaohongshu.codewiz.account.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xiaohongshu.codewiz.account.enums.AccountStatusEnum;
import com.xiaohongshu.codewiz.account.mapper.AccountApplyLogMapper;
import com.xiaohongshu.codewiz.account.mapper.AdminAccountInfoMapper;
import com.xiaohongshu.codewiz.account.po.AccountApplyLogInfo;
import com.xiaohongshu.codewiz.account.po.AdminAccountInfo;
import com.xiaohongshu.codewiz.account.po.UserAccountInfo;
import com.xiaohongshu.codewiz.account.service.IAccountApplyLogInfoService;
import com.xiaohongshu.codewiz.account.service.IAdminAccountInfoService;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * @ClassName AdminAccountInfoServiceImpl
 * @Description
 * @Date 2025/5/16 20:34
 * <AUTHOR>
 */
@Service
@Slf4j
public class AccountApplyLogInfoServiceImpl extends ServiceImpl<AccountApplyLogMapper, AccountApplyLogInfo> implements IAccountApplyLogInfoService, ApplicationContextAware {

    private ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(@NotNull ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }


    /**
     * 根据用户邮箱、服务名称 查询用户账号信息
     *
     * @param serviceName 服务名称
     * @param userEmail   用户邮箱
     * @return 用户账号信息
     */
    public List<AccountApplyLogInfo> getByServiceAndEmail(String serviceName, String userEmail) {
        return baseMapper.findByServiceAndEmail(serviceName, userEmail);
    }

    /**
     * 根据单据号 查询 日志信息
     *
     * @param requestId 单据号
     * @return 用户账号信息
     */
    public List<AccountApplyLogInfo> getByRequestId(String requestId) {
        return baseMapper.findByRequestId(requestId);
    }

    /**
     * 根据用户邮箱、服务名称 查询用户账号信息
     *
     * @param serviceName 服务名称
     * @param userEmail   用户邮箱
     * @param requestId   单据号
     * @return 用户账号信息
     */
    public AccountApplyLogInfo getByServiceAndEmailAndRequestId(String serviceName, String userEmail, String requestId) {
        return baseMapper.findByServiceAndEmailAndRequestId(serviceName, userEmail, requestId);
    }

    /**
     * 根据用户邮箱、服务名称 查询用户账号信息
     *
     * @param serviceName 服务名称
     * @param username    用户名
     * @return 用户账号信息
     */
    public AccountApplyLogInfo getByServiceAndName(String serviceName, String username) {
        return baseMapper.findByServiceAndName(serviceName, username);
    }

    /**
     * 根据服务查询账户申请日志账号信息
     *
     * @param serviceName 服务 Cursor
     * @return 账户申请日志账号信息
     */
    public List<AccountApplyLogInfo> getAllByService(String serviceName) {
        return baseMapper.findAllByService(serviceName);
    }

    /**
     * 新增账户申请日志信息
     *
     * @param accountApplyLogInfo 账户申请日志信息
     */
    public void addAccountApplyLogInfo(AccountApplyLogInfo accountApplyLogInfo) {
        // 通过 ApplicationContext 获取代理对象
        AccountApplyLogInfoServiceImpl proxy = applicationContext.getBean(AccountApplyLogInfoServiceImpl.class);
        proxy.saveOrUpdate(accountApplyLogInfo);
    }

    /**
     * 删除账户申请日志信息
     *
     * @param accountApplyLogInfo 账户申请日志信息
     */
    public Boolean delAccountApplyLogInfo(AccountApplyLogInfo accountApplyLogInfo) {
        return baseMapper.delAccountApplyLogInfo(accountApplyLogInfo);
    }

    /**
     * 更改账户申请日志信息
     *
     * @param accountApplyLogInfo 账户申请日志信息
     */
    public Boolean updateAccountApplyLogInfo(AccountApplyLogInfo accountApplyLogInfo) {
        return baseMapper.updateAccountApplyLogInfo(accountApplyLogInfo);
    }

    /**
     * 保存日志，当用户账号信息变更时调用:
     *
     * @param userAccountInfo 用户账号信息
     * @param closeReason     关闭原因
     */
    public void saveLogWhenUserAccountChanged(UserAccountInfo userAccountInfo, String closeReason) {
        try {
            // 日志记录
            AccountApplyLogInfo accountApplyLogInfo = AccountApplyLogInfo.builder()
                    .userId(userAccountInfo.getUserId())
                    .status(AccountStatusEnum.CLOSE.getAccountStatus())
                    .endStatus(AccountStatusEnum.CLOSE.getDescription())
                    .endReason(closeReason)
                    .departmentNamePathId(userAccountInfo.getDepartmentNamePathId())
                    .departmentNamePath(userAccountInfo.getDepartmentNamePath())
                    .departmentName(userAccountInfo.getDepartmentName())
                    .departmentId(userAccountInfo.getDepartmentId())
                    .serviceName(userAccountInfo.getServiceName())
                    .userEmail(userAccountInfo.getUserEmail())
                    .username(userAccountInfo.getUsername())
                    .build();
            addAccountApplyLogInfo(accountApplyLogInfo);
        } catch (Exception e) {
            log.error("[保存日志，当用户账号信息变更时调用], 日志落库失败, 出现异常，userAccountInfo: {}, closeReason: {}", JSON.toJSONString(userAccountInfo), closeReason, e);
        }
    }


    /**
     * RedFlow 日志落库处理
     *
     * @param accountApplyLogInfo 账户申请日志信息
     * @param accountStatusEnum   账户状态枚举
     */
    public void saveLog(AccountApplyLogInfo accountApplyLogInfo, AccountStatusEnum accountStatusEnum) {
        if (accountApplyLogInfo == null || accountStatusEnum == null) {
            log.error("[日志落库], 日志信息或账户状态枚举为空，accountApplyLogInfo: {}, accountStatusEnum: {}", JSON.toJSONString(accountApplyLogInfo), JSON.toJSONString(accountStatusEnum));
            return;
        }
        try {
            accountApplyLogInfo.setStatus(accountStatusEnum.getAccountStatus());
            accountApplyLogInfo.setEndStatus(accountStatusEnum.getDescription());
            addAccountApplyLogInfo(accountApplyLogInfo);
        } catch (Exception e) {
            log.error("[日志落库], 日志落库失败, 出现异常，accountApplyLogInfo: {}, accountStatusEnum: {}", JSON.toJSONString(accountApplyLogInfo), JSON.toJSONString(accountStatusEnum), e);
        }
    }

}
