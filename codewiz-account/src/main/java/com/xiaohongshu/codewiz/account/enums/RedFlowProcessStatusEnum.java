package com.xiaohongshu.codewiz.account.enums;

import lombok.Getter;

/**
 * @ClassName RedFlowProcessStatus
 * @Description
 * @Date 2025/5/20 20:06
 * <AUTHOR>
 */
@Getter
public enum RedFlowProcessStatusEnum {

    /**
     * 提交
     */
    SUBMIT("submit", "提交"),
    /**
     * 驳回
     */
    REJECT("reject", "驳回"),
    /**
     * 撤回
     */
    WITHDRAW("withdraw", "撤回"),
    /**
     * 终止
     */
    TERMINATE("terminate", "终止"),
    /**
     * 加签
     */
    ADD_SIGN("addSign", "加签"),
    /**
     * 删除流程
     */
    DELETE_PROCESS("deleteProcess", "删除流程"),
    /**
     * 转审委托
     */
    TRANS_APPROVE("transApprove", "转审委托"),
    /**
     * 流程结束
     */
    PROCESS_END("processEnd", "流程结束");

    private final String processType;       // 英文标识（用于接口/存储）
    private final String description; // 中文描述（用于展示）

    /**
     * 私有构造函数
     *
     * @param processType 英文标识
     * @param description 中文描述
     */
    RedFlowProcessStatusEnum(String processType, String description) {
        this.processType = processType;
        this.description = description;
    }

    /**
     * 判断当前状态是否与给定的值匹配
     * @param value 要匹配的值
     * @return 如果当前状态与给定值匹配，则返回 true，否则返回 false
     */
    public boolean matches(String value) {
        return this.processType.equals(value);
    }

    /**
     * 根据英文标识获取枚举值
     *
     * @param processType 英文标识
     * @return 对应的枚举值，若不存在则抛出异常
     */
    public static RedFlowProcessStatusEnum fromValue(String processType) {
        for (RedFlowProcessStatusEnum status : RedFlowProcessStatusEnum.values()) {
            if (status.processType.equals(processType)) {
                return status;
            }
        }
        throw new IllegalArgumentException("无效的流程状态值: " + processType);
    }

    /**
     * 判断是否为结束状态（终止/流程结束）
     *
     * @return 是否为结束状态
     */
    public boolean isEndStatus() {
        return this == TERMINATE || this == PROCESS_END;
    }
}
