package com.xiaohongshu.codewiz.account.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xiaohongshu.codewiz.account.config.db.CustomIdGenerator;
import com.xiaohongshu.codewiz.account.enums.AccountStatusEnum;
import com.xiaohongshu.codewiz.account.mapper.AccountApplyProcessTempMapper;
import com.xiaohongshu.codewiz.account.po.AccountApplyProcessTempInfo;
import com.xiaohongshu.codewiz.account.service.IAccountApplyProcessTempInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * @ClassName AccountApplyProcessTempInfoServiceImpl
 * @Description 用户申请流程中的数据，临时存储
 * @Date 2025/5/16 20:34
 * <AUTHOR>
 */
@Service
@Slf4j
public class AccountApplyProcessTempInfoServiceImpl extends ServiceImpl<AccountApplyProcessTempMapper, AccountApplyProcessTempInfo> implements IAccountApplyProcessTempInfoService {


    @Resource
    private CustomIdGenerator customIdGenerator;

    /**
     * 根据用户邮箱、服务名称 查询用户账号信息
     *
     * @param serviceName 服务名称
     * @param userEmail   用户邮箱
     * @return 用户账号信息
     */
    public List<AccountApplyProcessTempInfo> getByServiceAndEmail(String serviceName, String userEmail) {
        return baseMapper.findByServiceAndEmail(serviceName, userEmail);
    }

    /**
     * 根据 单据号 查询流程中的用户账号信息
     *
     * @param requestId 单据号
     * @return 用户账号信息
     */
    public List<AccountApplyProcessTempInfo> getByRequestId(String requestId) {
        return baseMapper.findByRequestId(requestId);
    }

    /**
     * 根据用户邮箱、服务名称 查询用户账号信息
     *
     * @param serviceName 服务名称
     * @param username    用户名
     * @return 用户账号信息
     */
    public AccountApplyProcessTempInfo getByServiceAndName(String serviceName, String username) {
        return baseMapper.findByServiceAndName(serviceName, username);
    }

    /**
     * 根据服务查询账户申请日志账号信息
     *
     * @param serviceName 服务 Cursor
     * @return 账户申请日志账号信息
     */
    public List<AccountApplyProcessTempInfo> getAllByService(String serviceName) {
        return baseMapper.findAllByService(serviceName);
    }

    /**
     * 新增 用户申请流程中的 信息
     *
     * @param accountApplyProcessTempInfo 账户申请流程中的信息
     */
    public void saveAccountApplyProcessTempInfo(AccountApplyProcessTempInfo accountApplyProcessTempInfo) {
        Long nextId = customIdGenerator.nextId(accountApplyProcessTempInfo);
        accountApplyProcessTempInfo.setId(nextId);
        this.save(accountApplyProcessTempInfo);
    }

    /**
     * 新增 用户申请流程中的 信息
     *
     * @param accountApplyProcessTempInfo 账户申请日志信息
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean checkCurNumThenAdd(AccountApplyProcessTempInfo accountApplyProcessTempInfo, int maxCount) {
        int maxRetries = 2;  // 最大重试次数
        long baseDelay = 100;  // 基础延迟毫秒

        for (int attempt = 0; attempt < maxRetries; attempt++) {
            // 1. 查询当前数量
            LambdaQueryWrapper<AccountApplyProcessTempInfo> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(AccountApplyProcessTempInfo::getServiceName, accountApplyProcessTempInfo.getServiceName())
                    .eq(AccountApplyProcessTempInfo::getStatus, AccountStatusEnum.PROCESSING.getAccountStatus()); // 仅统计待处理状态
            int count = this.count(queryWrapper);
            log.info("[流程中临时表数据] 当前数量: {}, 最大数量: {}", count, maxCount);

            // 2. 数量校验
            if (count >= maxCount) {
                return false;
            }

            // 3. 关键：原子性插入（利用唯一索引防止重复）
            try {
                return this.save(accountApplyProcessTempInfo);
            } catch (DuplicateKeyException e) {
                // 唯一索引冲突，说明并发插入已存在记录
                log.error("[流程中临时表数据][原子性插入]唯一索引冲突, 可能存在并发申请，尝试重试中: serviceName={}, userEmail={}", accountApplyProcessTempInfo.getServiceName(), accountApplyProcessTempInfo.getUserEmail(), e);
            }

            // 4. 指数退避重试
            if (attempt < maxRetries - 1) {
                long delay = baseDelay * (1 << attempt);
                try {
                    Thread.sleep(delay);
                } catch (InterruptedException ex) {
                    Thread.currentThread().interrupt();
                    return false;
                }
            }
        }
        return false;
    }

    /**
     * 根据服务名称查询账户申请流程中信息
     *
     * @param serviceName 服务名称
     * @return 账户申请流程中信息数量
     */
    public Integer getProcessCountByServiceName(String serviceName) {
        List<AccountApplyProcessTempInfo> allByService = baseMapper.findAllByService(serviceName);
        return CollectionUtils.isEmpty(allByService) ? 0 : allByService.size();
    }

    /**
     * 删除用户申请流程中信息
     *
     * @param accountApplyProcessTempInfo 账户申请流程中信息
     */
    public void delAccountApplyProcessTempInfo(AccountApplyProcessTempInfo accountApplyProcessTempInfo) {
        baseMapper.delAccountApplyProcessTempInfo(accountApplyProcessTempInfo);
    }

    /**
     * 删除用户申请流程中信息
     *
     * @param serviceName 服务名称
     * @param userEmail   用户邮箱
     */
    public Boolean delByServiceNameAndEmail(String serviceName, String userEmail) {
        return baseMapper.delByServiceNameAndEmail(serviceName, userEmail);
    }

    /**
     * 更改用户申请流程中信息
     *
     * @param accountApplyProcessTempInfo 用户申请流程中信息
     */
    public Boolean updateAccountApplyProcessTempInfo(AccountApplyProcessTempInfo accountApplyProcessTempInfo) {
        return baseMapper.updateAccountApplyProcessTempInfo(accountApplyProcessTempInfo);
    }


}
