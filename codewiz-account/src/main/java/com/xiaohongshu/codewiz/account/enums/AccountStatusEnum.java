package com.xiaohongshu.codewiz.account.enums;

import lombok.Getter;

/**
 * @ClassName AccountStatusEnum
 * @Description
 * @Date 2025/5/22 22:29
 * <AUTHOR>
 */
@Getter
public enum AccountStatusEnum {

    CLOSE(0, "账号关闭中"),
    PROCESSING(1, "申请流程中"),
    OPEN(2, "账号已开通"),
    FAIL(3, "账号开通失败");

    private final Integer accountStatus;

    private final String description;

    AccountStatusEnum(Integer accountStatus, String description) {
        this.accountStatus = accountStatus;
        this.description = description;
    }

    @Override
    public String toString() {
        return this.name() + ":" + this.accountStatus + ":" + this.description;
    }
}
