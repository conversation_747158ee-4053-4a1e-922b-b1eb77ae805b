package com.xiaohongshu.codewiz.account.mq;

import com.alibaba.fastjson.JSON;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xiaohongshu.codewiz.account.bo.RedFlowProcessMessageBodyBO;
import com.xiaohongshu.codewiz.account.config.CodexAccountGeneralConfig;
import com.xiaohongshu.codewiz.account.constants.RedFlowProcessConstant;
import com.xiaohongshu.codewiz.account.outer.redflow.RedFlowStatusHandler;
import com.xiaohongshu.codewiz.account.outer.redflow.handler.RedFlowStatusHandlerFactory;
import com.xiaohongshu.codewiz.account.outer.redflow.service.ApplyCloseStatusExecuteService;
import com.xiaohongshu.events.client.MessageExt;
import com.xiaohongshu.events.client.api.MessageProcessor;
import com.xiaohongshu.events.client.consumer.ConsumeContext;
import com.xiaohongshu.events.client.consumer.ConsumeStatus;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;

/**
 * @ClassName RedFlowMessageProcessor
 * @Description
 * @Date 2025/5/20 17:40
 * <AUTHOR>
 */
@Service
@Slf4j
public class RedFlowMessageProcessor implements MessageProcessor {

    @ApolloJsonValue("${codex.account.general.config:}")
    private CodexAccountGeneralConfig codexAccountGeneralConfig;

    @Resource
    private ApplyCloseStatusExecuteService applyCloseStatusExecuteService;

    private final ObjectMapper objectMapper = new ObjectMapper().findAndRegisterModules();

    private final RedFlowStatusHandlerFactory handlerFactory;

    public RedFlowMessageProcessor(RedFlowStatusHandlerFactory handlerFactory) {
        this.handlerFactory = handlerFactory;
    }


    @Override
    public ConsumeStatus process(MessageExt messageExt, ConsumeContext consumeContext) {
        long startTime = System.currentTimeMillis();
        try {
            // oasis_form-notice-codex-account-apply-process-consumer / oasis_form-notice-codex-account-approval-process-consumer
            String jsonStr = new String(messageExt.getBody(), StandardCharsets.UTF_8);
            log.info("[RedFlow消息订阅]收到RedFlow原始消息: {}", jsonStr);
            RedFlowProcessMessageBodyBO msgBodyBO = objectMapper.readValue(jsonStr, RedFlowProcessMessageBodyBO.class);
            log.info("[RedFlow消息订阅]原始消息转换为对象数据: {}", JSON.toJSONString(msgBodyBO));
            String auditStatus = msgBodyBO.getAuditStatus();
            String processOperate = msgBodyBO.getProcessOperate();
            Boolean processEnd = msgBodyBO.getProcessEnd();
            if (CollectionUtils.isEmpty(codexAccountGeneralConfig.getServiceList()) || !codexAccountGeneralConfig.getServiceList().contains(msgBodyBO.getServiceName())) {
                log.info("[不消费][RedFlow消息订阅]服务列表 = {} , 不包含当前消息的formType: {}, 消息ID: {}, 消息体: {}", JSON.toJSONString(codexAccountGeneralConfig.getServiceList()), msgBodyBO.getFormType(), messageExt.getMsgId(), JSON.toJSONString(messageExt));
                return ConsumeStatus.SUCCESS;
            }
            if (codexAccountGeneralConfig.getApplyCloseProcessTag().equals(msgBodyBO.getFormType())) {
                // 申请流程关闭
                log.info("[RedFlow消息订阅]处理申请流程关闭事件, 消息ID: {}, 消息体: {}", messageExt.getMsgId(), JSON.toJSONString(messageExt));
                Boolean applyCloseExecute = applyCloseStatusExecuteService.applyCloseExecute(msgBodyBO);
                return applyCloseExecute ? ConsumeStatus.SUCCESS : ConsumeStatus.FAIL;
            } else if (codexAccountGeneralConfig.getApplyOpenProcessTag().equals(msgBodyBO.getFormType())) {
                log.info("[RedFlow消息订阅]处理申请流程开启事件, 消息ID: {}, 消息体: {}", messageExt.getMsgId(), JSON.toJSONString(messageExt));
                RedFlowStatusHandler handler = handlerFactory.getHandler(auditStatus, processOperate, processEnd, msgBodyBO.getAuditPhaseKey());
                if (handler != null) {
                    // 执行处理逻辑
                    return handler.statusExecute(msgBodyBO);
                } else {
                    log.warn("[RedFlow消息订阅]未找到匹配的状态处理器，状态参数：auditStatus={}, processOperate={}, processEnd={}", auditStatus, processOperate, processEnd);
                    return ConsumeStatus.SUCCESS;
                }
            } else{
                log.warn("[RedFlow消息订阅]不处理的formType: {}, 消息ID: {}, 消息体: {}", msgBodyBO.getFormType(), messageExt.getMsgId(), JSON.toJSONString(messageExt));
            }
            return ConsumeStatus.SUCCESS;
        } catch (Exception e) {
            log.error("[RedFlow消息订阅]处理消息出现异常", e);
            return ConsumeStatus.FAIL;
        } finally {
            long endTime = System.currentTimeMillis();
            log.info("[RedFlow消息订阅] 处理完成, costMsTime: {}ms, 消息ID: {}, 消息体: {}", (endTime - startTime), messageExt.getMsgId(), JSON.toJSONString(messageExt));
        }
    }

}
