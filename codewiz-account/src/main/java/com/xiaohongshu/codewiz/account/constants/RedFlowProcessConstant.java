package com.xiaohongshu.codewiz.account.constants;

/**
 * @ClassName RedFlowProcessConstant
 * @Description
 * @Date 2025/5/21 21:20
 * <AUTHOR>
 */
public class RedFlowProcessConstant {

    public static final String FORM_TYPE_CURSOR = "cursor";
    // 一下两个与消费组 tag进行绑定的，不能更改
    // CodexAccountApplyOpenProcess  == CAAOP
    public static final String FORM_TYPE_APPLY_OPEN = "CAAOP";
    // CodexAccountApplyCloseProcess  == CAACP
    public static final String FORM_TYPE_APPLY_CLOSE = "CAACP";


}
