package com.xiaohongshu.codewiz.account.bo;

import lombok.Builder;
import lombok.Data;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName BatchOperationResult
 * @Description
 * @Date 2025/5/30 14:32
 * <AUTHOR>
 */
@Data
@Builder
public class BatchOperationResult {

    private final Integer totalCount;
    private final List<String> successEmails = new ArrayList<>();
    private final List<FailedItem> failedItems = new ArrayList<>();

    public void addSuccessEmail(String email) {
        successEmails.add(email);
    }

    public void addFailedEmail(String email, String reason) {
        failedItems.add(new FailedItem(email, reason));
    }

    public boolean hasFailures() {
        return !failedItems.isEmpty();
    }

    public int getTotalCount() {
        return totalCount;
    }

    public int getSuccessCount() {
        return successEmails.size();
    }

    public int getFailedCount() {
        return failedItems.size();
    }


    @Getter
    public static class FailedItem {
        private final String email;
        private final String reason;

        public FailedItem(String email, String reason) {
            this.email = email;
            this.reason = reason;
        }

    }

}
