package com.xiaohongshu.codewiz.account.controller;

import com.alibaba.fastjson.JSON;
import com.xiaohongshu.codewiz.account.po.AccountApplyLogInfo;
import com.xiaohongshu.codewiz.account.service.impl.AccountApplyLogInfoServiceImpl;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @ClassName AccountLogController
 * @Description
 * @Date 2025/5/26 17:42
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/codexaccount/api/account/log")
public class AccountLogController {

    @Resource
    private AccountApplyLogInfoServiceImpl accountApplyLogInfoService;

    /**
     * 获取所有账号申请日志信息
     *
     * @param serviceName 服务名称
     * @return 账号申请日志信息列表
     */
    @Operation(summary = "获取所有账号申请日志信息")
    @GetMapping("/all")
    private ResponseEntity<?> getAllAccountApplyLogInfo(@RequestParam("serviceName") String serviceName) {
        List<AccountApplyLogInfo> accountApplyLogInfos = accountApplyLogInfoService.getAllByService(serviceName);
        if (CollectionUtils.isEmpty(accountApplyLogInfos)) {
            log.warn("[获取所有账号申请日志信息] 无数据  serviceName: {}, result: {}", serviceName, JSON.toJSONString(accountApplyLogInfos));
            return ResponseEntity.notFound().build();
        }
        log.info("获取所有账号申请日志信息，serviceName = {}, result = {}", serviceName, JSON.toJSONString(accountApplyLogInfos));
        return ResponseEntity.ok(accountApplyLogInfos);
    }

    /**
     * 根据用户邮箱、服务名称 查询用户账号信息
     *
     * @param serviceName 服务名称
     * @param userEmail   用户邮箱
     * @return 用户账号信息
     */
    @Operation(summary = "根据服务名称和邮箱查询账号申请日志信息")
    @GetMapping("/getByServiceAndEmail")
    public ResponseEntity<?> getByServiceAndEmail(@RequestParam("serviceName") String serviceName,
                                                  @RequestParam("userEmail") String userEmail) {
        List<AccountApplyLogInfo> accountApplyLogInfos = accountApplyLogInfoService.getByServiceAndEmail(serviceName, userEmail);
        if (CollectionUtils.isEmpty(accountApplyLogInfos)) {
            log.warn("[获取账号申请日志信息] No account apply log info found for serviceName: {}, userEmail: {}, result = {}", serviceName, userEmail, JSON.toJSONString(accountApplyLogInfos));
            return ResponseEntity.notFound().build();
        }
        log.warn("[获取账号申请日志信息] No account apply log info found for serviceName: {}, userEmail: {}, result = {}", serviceName, userEmail, JSON.toJSONString(accountApplyLogInfos));
        return ResponseEntity.ok(accountApplyLogInfos);
    }

    /**
     * 根据单据号查询账号申请日志信息
     *
     * @param requestId 单据号
     * @return 账号申请日志信息
     */
    @Operation(summary = "根据单据号查询账号申请日志信息")
    @GetMapping("/getByRequestId")
    public ResponseEntity<?> getByFormNo(@RequestParam("requestId") String requestId) {
        List<AccountApplyLogInfo> accountApplyLogInfos = accountApplyLogInfoService.getByRequestId(requestId);
        if (CollectionUtils.isEmpty(accountApplyLogInfos)) {
            log.warn("[获取账号申请日志信息] No account apply log info found for requestId: {}, result = {}", requestId, JSON.toJSONString(accountApplyLogInfos));
            return ResponseEntity.notFound().build();
        }
        log.info("[获取账号申请日志信息] Account apply log info retrieved successfully for requestId: {}, result = {}", requestId, JSON.toJSONString(accountApplyLogInfos));
        return ResponseEntity.ok(accountApplyLogInfos);
    }


}
