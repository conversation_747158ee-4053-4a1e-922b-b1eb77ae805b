package com.xiaohongshu.codewiz.account.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xiaohongshu.codewiz.account.po.AdminAccountInfo;
import com.xiaohongshu.codewiz.account.po.UserAccountInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * @ClassName UserAccountInfoMapper
 * @Description 用户账号信息管理
 * @Date 2025/5/12 15:41
 * <AUTHOR>
 */
@Mapper
public interface UserAccountInfoMapper extends BaseMapper<UserAccountInfo> {

    /**
     * 根据用户邮箱、服务名称和状态查询用户账号信息
     */
    default UserAccountInfo selectByEmailAndServiceAndStatus(@Param("userEmail") String userEmail,
                                                             @Param("serviceName") String serviceName,
                                                             @Param("status") Integer status) {
        LambdaQueryWrapper<UserAccountInfo> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(UserAccountInfo::getUserEmail, userEmail)
                .eq(UserAccountInfo::getServiceName, serviceName)
                .eq(UserAccountInfo::getStatus, status)
                .last("LIMIT 1");

        return this.selectOne(wrapper);
    }

    /**
     * 根据服务名称和账号状态查询已开通席位
     */
    default List<UserAccountInfo> findByServiceAndStatus(@Param("serviceName") String serviceName,
                                                         @Param("status") Integer status) {
        LambdaQueryWrapper<UserAccountInfo> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(UserAccountInfo::getServiceName, serviceName)
                .eq(UserAccountInfo::getStatus, status);
        return this.selectList(wrapper);
    }

    /**
     * 根据用户邮箱 和状态查询用户账号信息
     */
    default List<UserAccountInfo> selectByEmailAndStatus(@Param("userEmail") String userEmail,
                                                         @Param("status") Integer status) {
        LambdaQueryWrapper<UserAccountInfo> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(UserAccountInfo::getUserEmail, userEmail)
                .eq(UserAccountInfo::getStatus, status);

        return this.selectList(wrapper);
    }

    /**
     * 根据用户邮箱、服务名称查询账号信息
     */
    default UserAccountInfo findByServiceAndEmail(@Param("serviceName") String serviceName,
                                                  @Param("userEmail") String userEmail) {
        LambdaQueryWrapper<UserAccountInfo> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(UserAccountInfo::getUserEmail, userEmail)
                .eq(UserAccountInfo::getServiceName, serviceName)
                .last("LIMIT 1");
        return this.selectOne(wrapper);
    }

    /**
     * 根据用户邮箱、服务名称查询账号信息
     */
    default List<UserAccountInfo> findListByServiceAndEmail(@Param("serviceName") String serviceName,
                                                  @Param("userEmail") String userEmail) {
        LambdaQueryWrapper<UserAccountInfo> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(UserAccountInfo::getUserEmail, userEmail)
                .eq(UserAccountInfo::getServiceName, serviceName);
        return this.selectList(wrapper);
    }

    /**
     * 根据服务名称/用户名查询账号信息
     */
    default UserAccountInfo findByServiceAndName(@Param("serviceName") String serviceName,
                                                 @Param("username") String username) {
        LambdaQueryWrapper<UserAccountInfo> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(UserAccountInfo::getServiceName, serviceName)
                .eq(UserAccountInfo::getUsername, username)
                .last("LIMIT 1");
        return this.selectOne(wrapper);
    }


    /**
     * 根据服务名称查询账号信息
     */
    default List<UserAccountInfo> findAllByService(@Param("serviceName") String serviceName) {
        LambdaQueryWrapper<UserAccountInfo> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(UserAccountInfo::getServiceName, serviceName);
        return this.selectList(wrapper);
    }

    /**
     * 根据服务名称查询账号信息
     */
    default List<UserAccountInfo> findAllByDepartmentId(@Param("departmentId") String departmentId) {
        LambdaQueryWrapper<UserAccountInfo> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(UserAccountInfo::getDepartmentId, departmentId);
        return this.selectList(wrapper);
    }

    /**
     * 删除用户信息
     */
    default Boolean delUserAccountInfo(UserAccountInfo userAccountInfo) {
        LambdaQueryWrapper<UserAccountInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(userAccountInfo.getId() != null, UserAccountInfo::getId, userAccountInfo.getId());
        queryWrapper.eq(UserAccountInfo::getServiceName, userAccountInfo.getServiceName());
        queryWrapper.eq(UserAccountInfo::getUserEmail, userAccountInfo.getUserEmail());
        return this.delete(queryWrapper) >= 1;
    }

    /**
     * 删除用户信息
     */
    default Boolean delUserInfo(String serviceName, String userEmail) {
        LambdaQueryWrapper<UserAccountInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserAccountInfo::getServiceName, serviceName);
        queryWrapper.eq(UserAccountInfo::getUserEmail, userEmail);
        return this.delete(queryWrapper) >= 1;
    }

    /**
     * 更新用户信息
     */
    default Boolean updateUserAccountInfo(UserAccountInfo userAccountInfo) {
        LambdaQueryWrapper<UserAccountInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserAccountInfo::getServiceName, userAccountInfo.getServiceName());
        queryWrapper.eq(UserAccountInfo::getUserEmail, userAccountInfo.getUserEmail());
        return this.update(userAccountInfo, queryWrapper) >= 1;
    }

}
