package com.xiaohongshu.codewiz.account.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xiaohongshu.codewiz.account.config.db.IdGeneratorTable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * @ClassName AdminAccountInfo
 * @Description
 * @Date 2025/5/19 12:11
 * <AUTHOR>
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@TableName("t_codex_account_manager_admin")
@IdGeneratorTable("t_codex_account_manager_admin")
@Builder
public class AdminAccountInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * "channelUserId":"W15505", //员工账号才有，人事userID，注意：正编&实习 和 bpo不一样
     */
    private String userId;
    /**
     * 用户名称
     */
    private String username;

    /**
     * 用户邮箱
     */
    private String userEmail;

    /**
     * 部门名称
     */
    private String departmentName;

    /**
     * 部门ID
     */
    private String departmentId;

    /**
     * 部门名称路径
     */
    private String departmentNamePath;

    /**
     * 部门id路径
     */
    private String departmentNamePathId;

    /**
     * 服务名称：Cursor
     */
    private String serviceName;

    /**
     * 角色：admin
     */
    private String role;

    /**
     * 状态
     */
    private Map<String, Object> custom;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建时间
     */
    private LocalDateTime updateTime;


    public Boolean isNotValid() {
        return StringUtils.isEmpty(this.serviceName) || StringUtils.isEmpty(this.userEmail);
    }
}
