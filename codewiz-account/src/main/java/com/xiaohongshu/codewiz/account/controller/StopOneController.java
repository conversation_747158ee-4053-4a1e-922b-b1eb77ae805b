package com.xiaohongshu.codewiz.account.controller;

import com.xiaohongshu.codewiz.account.dto.UserAccountApplyDto;
import com.xiaohongshu.codewiz.account.outer.stopone.AccountAssignedService;
import com.xiaohongshu.codewiz.account.outer.stopone.AccountStandDetailSearchService;
import com.xiaohongshu.force.lobot.thrift.dto.RuleVerificationResult;
import com.xiaohongshu.force.lobot.thrift.dto.res.Response;
import com.xiaohongshu.force.paploo.thrift.dto.AccountDetailBean;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * @ClassName StopOneController
 * @Description
 * @Date 2025/5/19 20:19
 * <AUTHOR>
 */
@Slf4j
@RestController
@Tag(name = "StopOneController", description = "下游IT服务")
@RequestMapping("/codexaccount/api/account/stopOne")
public class StopOneController {

    @Resource
    AccountAssignedService accountAssignedService;

    @Resource
    AccountStandDetailSearchService accountStandDetailSearchService;

    @Operation(summary = "获取所有AD域关联账号信息")
    @GetMapping("/all/live")
    public ResponseEntity<?> getStopOneInfo(@RequestParam("serviceName") String serviceName) {
        List<String> allPageAccountInfos = accountAssignedService.getAllPageAccountInfos(serviceName);
        if (CollectionUtils.isEmpty(allPageAccountInfos)) {
            log.warn("[获取所有AD域关联账号信息] 查询结果为空，请核对DB. serviceName = {}", serviceName);
            return ResponseEntity.noContent().build();
        }
        log.info("[获取所有AD域关联账号信息] 查询成功. serviceName = {}, result = {}", serviceName, allPageAccountInfos);
        return ResponseEntity.ok(allPageAccountInfos);
    }

    @Operation(summary = "增加AD域关联账号信息")
    @PostMapping("/accountAssign")
    public ResponseEntity<?> addStopOneAccount(@RequestBody UserAccountApplyDto userAccountApplyDto) {
        if (userAccountApplyDto == null || StringUtils.isEmpty(userAccountApplyDto.getServiceName()) || StringUtils.isEmpty(userAccountApplyDto.getUserEmail())) {
            log.warn("[获取所有AD域关联账号信息] 添加账号失败，参数不能为空. userAccountApplyDto = {}", userAccountApplyDto);
            return ResponseEntity.badRequest().build();
        }
        String serviceName = userAccountApplyDto.getServiceName();
        String userEmail = userAccountApplyDto.getUserEmail();
        Response response = accountAssignedService.addAccount(serviceName, userEmail);
        if (response == null) {
            log.warn("[获取所有AD域关联账号信息] 添加账号失败，请核对DB. serviceName = {}, userEmail = {}", serviceName, userEmail);
            return ResponseEntity.noContent().build();
        }
        log.info("[获取所有AD域关联账号信息] 添加账号成功. serviceName = {}, userEmail = {}", serviceName, userEmail);
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "获取所有AD域关联账号信息")
    @GetMapping("/removeAssigned")
    public ResponseEntity<?> removeStopOneAccount(@RequestParam("serviceName") String serviceName, @RequestParam("userEmail") String userEmail) {
        Response response = accountAssignedService.removeAccount(serviceName, userEmail);
        if (response == null) {
            log.warn("[获取所有AD域关联账号信息] 移除账号失败，请核对DB. serviceName = {}, userEmail = {}", serviceName, userEmail);
            return ResponseEntity.noContent().build();
        }
        log.info("[获取所有AD域关联账号信息] 移除账号成功. serviceName = {}, userEmail = {}", serviceName, userEmail);
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "核验账号是否已经在AD域关联")
    @GetMapping("/checkAccountHasAssigned")
    public ResponseEntity<?> checkAccountHasAssigned(@RequestParam("serviceName") String serviceName, @RequestParam("userEmail") String userEmail) {
        RuleVerificationResult ruleVerificationResult = accountAssignedService.checkAccountHasAssigned(serviceName, userEmail);
        if (ruleVerificationResult == null) {
            log.warn("[核验账号是否已经在AD域关联] 核验失败，请核对DB. serviceName = {}, userEmail = {}", serviceName, userEmail);
            return ResponseEntity.badRequest().build();
        }
        log.info("[核验账号是否已经在AD域关联] 核验成功. serviceName = {}, userEmail = {}, result = {}", serviceName, userEmail, ruleVerificationResult);
        return ResponseEntity.ok(ruleVerificationResult);
    }

    @Operation(summary = "员工账号详情查询")
    @GetMapping("/single/standDetail")
    public ResponseEntity<?> searchAccountStandInfo(@RequestParam("userEmail") String userEmail) {
        AccountDetailBean accountDetailInfo = accountStandDetailSearchService.getSingleAccountDetail(userEmail);
        if (accountDetailInfo == null) {
            log.warn("[员工账号详情查询] 查询结果为空，请核对DB. userEmail = {}", userEmail);
            return ResponseEntity.noContent().build();
        }
        log.info("[员工账号详情查询] 查询成功. userEmail = {}, result = {}", userEmail, accountDetailInfo);
        return ResponseEntity.ok(accountDetailInfo);
    }


}
