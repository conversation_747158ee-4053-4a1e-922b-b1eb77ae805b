package com.xiaohongshu.codewiz.account.outer.redflow;

import com.xiaohongshu.codewiz.account.bo.RedFlowProcessMessageBodyBO;
import com.xiaohongshu.events.client.consumer.ConsumeStatus;

/**
 * @ClassName RedFlowStatusHandler
 * @Description
 * @Date 2025/5/21 21:59
 * <AUTHOR>
 */
public interface RedFlowStatusHandler {

    ConsumeStatus statusExecute(RedFlowProcessMessageBodyBO message);

    Boolean judgeStatusProcess(String auditStatus, String processOperate, Boolean processEnd, String flowKey);

}
