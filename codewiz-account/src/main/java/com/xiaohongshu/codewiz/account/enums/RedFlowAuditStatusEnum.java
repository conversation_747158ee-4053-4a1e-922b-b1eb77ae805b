package com.xiaohongshu.codewiz.account.enums;

import lombok.Getter;

/**
 * @ClassName RedFlowAuditStatusEnum
 * @Description
 * @Date 2025/5/21 20:13
 * <AUTHOR>
 */
@Getter
public enum RedFlowAuditStatusEnum {

    IN_REVIEW("IN_REVIEW", "审批中"),

    AUDIT_PASS("AUDIT_PASS", "审核通过"),

    AUDIT_REFUSE("AUDIT_REFUSE", "审核拒绝"),

    AUDIT_TERMINATE("AUDIT_TERMINATE", "已终止"),

    WITHDRAWAL("WITHDRAWAL", "撤回待发起"),

    DELETE("DELETE", "已删除");

    private final String auditStatus;       // 状态码
    private final String description; // 状态描述

    /**
     * 私有构造函数
     *
     * @param auditStatus 状态码
     * @param description 状态描述
     */
    RedFlowAuditStatusEnum(String auditStatus, String description) {
        this.auditStatus = auditStatus;
        this.description = description;
    }

    /**
     * 判断当前状态是否与给定的值匹配
     * @param value 要匹配的值
     * @return 如果当前状态与给定值匹配，则返回 true，否则返回 false
     */
    public boolean matches(String value) {
        return this.auditStatus.equals(value);
    }

    /**
     * 根据状态码获取枚举值
     *
     * @param auditStatus 状态码
     * @return 对应的枚举值，若不存在则返回 null
     */
    public static RedFlowAuditStatusEnum fromCode(String auditStatus) {
        if (auditStatus == null) {
            return null;
        }
        for (RedFlowAuditStatusEnum status : RedFlowAuditStatusEnum.values()) {
            if (status.getAuditStatus().equals(auditStatus)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 根据状态描述获取枚举值
     *
     * @param description 状态描述
     * @return 对应的枚举值，若不存在则返回 null
     */
    public static RedFlowAuditStatusEnum fromDescription(String description) {
        if (description == null) {
            return null;
        }
        for (RedFlowAuditStatusEnum status : RedFlowAuditStatusEnum.values()) {
            if (status.getDescription().equals(description)) {
                return status;
            }
        }
        return null;
    }

}
