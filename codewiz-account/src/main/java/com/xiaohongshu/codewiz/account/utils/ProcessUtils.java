package com.xiaohongshu.codewiz.account.utils;

import com.xiaohongshu.codewiz.account.bo.RedFlowProcessMessageBodyBO;
import com.xiaohongshu.codewiz.account.enums.AccountStatusEnum;
import com.xiaohongshu.codewiz.account.outer.stopone.AccountStandDetailSearchService;
import com.xiaohongshu.codewiz.account.po.AccountApplyLogInfo;
import com.xiaohongshu.codewiz.account.po.AccountApplyProcessTempInfo;
import com.xiaohongshu.codewiz.account.po.UserAccountInfo;
import com.xiaohongshu.codewiz.account.service.impl.AccountApplyLogInfoServiceImpl;
import com.xiaohongshu.force.paploo.thrift.dto.AccountDetailBean;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Optional;

/**
 * @ClassName BuildObjUtils
 * @Description
 * @Date 2025/5/26 11:58
 * <AUTHOR>
 */
@Component
@Slf4j
public class ProcessUtils {


    @Resource
    private AccountApplyLogInfoServiceImpl accountApplyLogInfoService;
    @Resource
    private AccountStandDetailSearchService accountStandDetailSearchService;

    /**
     * 构建账户申请流程中信息
     *
     * @param message RedFlow流程消息体
     * @return 账户申请流程中信息
     */
    public UserAccountInfo buildUserAccountInfo(RedFlowProcessMessageBodyBO message, AccountDetailBean singleAccountDetail) {
        UserAccountInfo userAccountInfo = UserAccountInfo.builder()
                .serviceName(message.getServiceName())
                .status(AccountStatusEnum.PROCESSING.getAccountStatus())
                .userEmail(message.getStartUserEmail())
                .username(message.getStartUserName())
                .description(message.getRequestReason())
                .openTime(LocalDateTime.now())
                .build();
        if (singleAccountDetail != null) {
            userAccountInfo.setUserId(singleAccountDetail.getChannelUserId());
            userAccountInfo.setDepartmentId(String.valueOf(singleAccountDetail.getDepartmentId()));
            userAccountInfo.setDepartmentName(Optional.ofNullable(singleAccountDetail.getDepartmentNamePath())
                    .map(path -> path.split(","))
                    .filter(parts -> parts.length > 0)
                    .map(parts -> parts[0])
                    .orElse(null));
            userAccountInfo.setDepartmentNamePathId(singleAccountDetail.getDepartmentIdPath());
            userAccountInfo.setDepartmentNamePath(singleAccountDetail.getDepartmentNamePath());
            userAccountInfo.setUsername(singleAccountDetail.getDisplayName());
        }
        return userAccountInfo;
    }

    /**
     * 构建账户申请流程中信息
     *
     * @param message RedFlow流程消息体
     * @return 账户申请流程中信息
     */
    public AccountApplyProcessTempInfo buildAccountApplyProcessTempInfo(RedFlowProcessMessageBodyBO message, AccountDetailBean singleAccountDetail) {
        AccountApplyProcessTempInfo accountApplyProcessTempInfo = AccountApplyProcessTempInfo.builder()
                .serviceName(message.getServiceName())
                .status(AccountStatusEnum.PROCESSING.getAccountStatus())
                .userEmail(message.getStartUserEmail())
                .requestId(message.getFormNo())
                .requestType(message.getFormType())
                .username(message.getStartUserName())
                .requestReason(message.getRequestReason())
                .build();
        if (singleAccountDetail != null) {
            accountApplyProcessTempInfo.setUserId(singleAccountDetail.getChannelUserId());
            accountApplyProcessTempInfo.setDepartmentId(String.valueOf(singleAccountDetail.getDepartmentId()));
            accountApplyProcessTempInfo.setDepartmentName(Optional.ofNullable(singleAccountDetail.getDepartmentNamePath())
                    .map(path -> path.split(","))
                    .filter(parts -> parts.length > 0)
                    .map(parts -> parts[0])
                    .orElse(null));
            accountApplyProcessTempInfo.setDepartmentNamePathId(singleAccountDetail.getDepartmentIdPath());
            accountApplyProcessTempInfo.setDepartmentNamePath(singleAccountDetail.getDepartmentNamePath());
            accountApplyProcessTempInfo.setUsername(singleAccountDetail.getDisplayName());

        }
        return accountApplyProcessTempInfo;
    }


    /**
     * 构建账户申请日志信息
     *
     * @param message             RedFlow流程消息体
     * @param singleAccountDetail 账户详情
     * @return 账户申请日志信息
     */
    public AccountApplyLogInfo buildAccountApplyLogInfo(RedFlowProcessMessageBodyBO message, AccountDetailBean singleAccountDetail) {
        AccountApplyLogInfo accountApplyLogInfo = AccountApplyLogInfo.builder()
                .requestId(message.getFormNo())
                .userEmail(message.getStartUserEmail())
                .serviceName(message.getServiceName())
                .requestType(message.getFormType())
                .username(message.getStartUserName())
                .requestReason(message.getComment())
                .build();
        if (singleAccountDetail != null) {
            accountApplyLogInfo.setUserId(singleAccountDetail.getChannelUserId());
            accountApplyLogInfo.setDepartmentId(String.valueOf(singleAccountDetail.getDepartmentId()));
            accountApplyLogInfo.setDepartmentName(Optional.ofNullable(singleAccountDetail.getDepartmentNamePath())
                    .map(path -> path.split(","))
                    .filter(parts -> parts.length > 0)
                    .map(parts -> parts[0])
                    .orElse(null));
            accountApplyLogInfo.setDepartmentNamePathId(singleAccountDetail.getDepartmentIdPath());
            accountApplyLogInfo.setDepartmentNamePath(singleAccountDetail.getDepartmentNamePath());
            accountApplyLogInfo.setUsername(singleAccountDetail.getDisplayName());

        }
        return accountApplyLogInfo;

    }

    public AccountApplyLogInfo getLogProcessInfo(RedFlowProcessMessageBodyBO message, String processName) {
        try {
            String formNo = message.getFormNo();
            String startUserEmail = message.getStartUserEmail();
            String serviceName = StringUtils.isEmpty(message.getServiceName()) ? "cursor" : message.getServiceName();

            // 1. 获取相同单据的log日志
            AccountApplyLogInfo accountApplyLogInfo = accountApplyLogInfoService.getByServiceAndEmailAndRequestId(serviceName, startUserEmail, formNo);
            if (accountApplyLogInfo == null) {
                // 1.1 查询用户详情
                AccountDetailBean singleAccountDetail = accountStandDetailSearchService.getSingleAccountDetail(message.getStartUserEmail());
                accountApplyLogInfo = buildAccountApplyLogInfo(message, singleAccountDetail);
                if (singleAccountDetail == null) {
                    log.warn("[核查点][用户详情][RedFlow流程流程成功结束事件]获取用户账号详情失败，用户邮箱：{}", message.getStartUserEmail());
                }
            }
            return accountApplyLogInfo;
        } catch (Exception e) {
            log.error("[RedFlow{}事件]获取日志信息失败，出现异常", processName, e);
        }
        return AccountApplyLogInfo.builder().build();
    }


}
