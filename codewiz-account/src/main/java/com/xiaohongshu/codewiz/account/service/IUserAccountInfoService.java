package com.xiaohongshu.codewiz.account.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xiaohongshu.codewiz.account.bo.BatchOperationResult;
import com.xiaohongshu.codewiz.account.dto.UserAccountQueryDTO;
import com.xiaohongshu.codewiz.account.po.UserAccountInfo;

import java.util.List;

/**
 * @ClassName IUserAccountInfoService
 * @Description
 * @Date 2025/5/16 20:23
 * <AUTHOR>
 */
public interface IUserAccountInfoService extends IService<UserAccountInfo> {

    Page<UserAccountInfo> queryUserAccounts(UserAccountQueryDTO queryDTO);

    BatchOperationResult batchCloseAccounts(List<UserAccountQueryDTO> queryDTOS);
}
