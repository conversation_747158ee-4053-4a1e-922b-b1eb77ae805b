package com.xiaohongshu.codewiz.account.outer.redflow.service;

import com.xiaohongshu.codewiz.account.bo.RedFlowProcessMessageBodyBO;
import com.xiaohongshu.codewiz.account.enums.AccountStatusEnum;
import com.xiaohongshu.codewiz.account.outer.redflow.RedFlowProcessService;
import com.xiaohongshu.codewiz.account.outer.stopone.AccountAssignedService;
import com.xiaohongshu.codewiz.account.outer.stopone.AccountStandDetailSearchService;
import com.xiaohongshu.codewiz.account.po.AccountApplyLogInfo;
import com.xiaohongshu.codewiz.account.po.AccountApplyProcessTempInfo;
import com.xiaohongshu.codewiz.account.po.UserAccountInfo;
import com.xiaohongshu.codewiz.account.service.impl.AccountApplyLogInfoServiceImpl;
import com.xiaohongshu.codewiz.account.service.impl.AccountApplyProcessTempInfoServiceImpl;
import com.xiaohongshu.codewiz.account.service.impl.UserAccountInfoServiceImpl;
import com.xiaohongshu.codewiz.account.utils.ProcessUtils;
import com.xiaohongshu.force.lobot.thrift.dto.RuleVerificationResult;
import com.xiaohongshu.force.lobot.thrift.dto.res.Response;
import com.xiaohongshu.force.paploo.thrift.dto.AccountDetailBean;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * @ClassName SuccessEndStatusExecuteService
 * @Description
 * @Date 2025/5/26 16:18
 * <AUTHOR>
 */
@Slf4j
@Service
public class SuccessEndStatusExecuteService {


    @Resource
    private AccountApplyProcessTempInfoServiceImpl accountApplyProcessTempInfoService;

    @Resource
    private AccountStandDetailSearchService accountStandDetailSearchService;

    @Resource
    private AccountApplyLogInfoServiceImpl accountApplyLogInfoService;

    @Resource
    private ProcessUtils processUtils;

    @Resource
    private AccountAssignedService accountAssignedService;

    @Resource
    private RedFlowProcessService redFlowProcessService;

    @Resource
    private UserAccountInfoServiceImpl userAccountInfoServiceImpl;

    /**
     * 流程成功结束事件处理
     *
     * @param message MQ 流程消息
     * @return true: 处理成功，false: 处理失败
     */
    public Boolean successEndStatusExecute(RedFlowProcessMessageBodyBO message) {
        AccountApplyLogInfo accountApplyLogInfo = AccountApplyLogInfo.builder().build();
        try {
            String formNo = message.getFormNo();
            String startUserEmail = message.getStartUserEmail();
            String serviceName = StringUtils.isEmpty(message.getServiceName()) ? "cursor" : message.getServiceName();
            // 1. 获取相同单据的log日志
            accountApplyLogInfo = processUtils.getLogProcessInfo(message, "[流程成功结束事件]");
            // 2. 流程成功结束: 更新申请日志
            accountApplyLogInfo.setEndStatus(AccountStatusEnum.OPEN.getDescription());
            accountApplyLogInfo.setStatus(AccountStatusEnum.OPEN.getAccountStatus());
            accountApplyLogInfo.setEndReason("流程成功结束");
            // 3. 删除流程中临时表数据: 直接全删
            AccountApplyProcessTempInfo withDrawProcess = AccountApplyProcessTempInfo.builder()
//                    .requestId(formNo)
                    .userEmail(startUserEmail)
                    .serviceName(serviceName)
                    .build();
            accountApplyProcessTempInfoService.delAccountApplyProcessTempInfo(withDrawProcess);
            // 4. 流程结束: 判断是否已经关联了账号
            boolean accountHasAssigned = accountAssignedService.accountHasAssigned(serviceName, startUserEmail);
            if (accountHasAssigned) {
                log.info("[RedFlow流程成功结束事件]账号已被分配，无需再分配，serviceName={}, userEmail={}", serviceName, startUserEmail);
                accountApplyLogInfo.setEndReason("账号已被分配，申请失败");
                return false; // 账号已分配，无需再处理
            } else {
                // 关联账号
                Response response = accountAssignedService.addAccount(serviceName, startUserEmail);
                if (response != null && response.result != null && response.result.success) {
                    log.info("[RedFlow流程成功结束事件]账号分配成功，serviceName={}, userEmail={}", serviceName, startUserEmail);
                } else {
                    log.error("[RedFlow流程成功结束事件]账号分配失败，serviceName={}, userEmail={}, response={}", serviceName, startUserEmail, response);
                    accountApplyLogInfo.setEndReason("账号分配失败，请联系AD域和研效组处理");
                    return false;
                }
            }
            // 完成审批
            redFlowProcessService.completeTask(message.getFlowId(), message.getStartUserId(), "流程成功结束", message.getFormNo());
            // 5. 流程成功结束: 用户表落库
            UserAccountInfo byServiceAndEmail = userAccountInfoServiceImpl.getByServiceAndEmail(serviceName, startUserEmail);
            if (byServiceAndEmail == null) {
                AccountDetailBean singleAccountDetail = accountStandDetailSearchService.getSingleAccountDetail(message.getStartUserEmail());
                byServiceAndEmail = processUtils.buildUserAccountInfo(message, singleAccountDetail);
                byServiceAndEmail.setStatus(AccountStatusEnum.OPEN.getAccountStatus());
                byServiceAndEmail.setOpenTime(LocalDateTime.now());
                userAccountInfoServiceImpl.addUserAccountInfo(byServiceAndEmail);
            } else {
                byServiceAndEmail.setStatus(AccountStatusEnum.OPEN.getAccountStatus());
                byServiceAndEmail.setOpenTime(LocalDateTime.now());
                userAccountInfoServiceImpl.updateUserAccountInfo(byServiceAndEmail);
            }
        } catch (Exception e) {
            log.error("[RedFlow流程成功结束事件]处理失败，出现异常", e);
            if (accountApplyLogInfo != null) {
                accountApplyLogInfo.setEndReason("处理失败，出现异常，请联系AD域和研效组处理");
                accountApplyLogInfo.setDescription(e.getMessage());
            }
        } finally {
            accountApplyLogInfoService.saveLog(accountApplyLogInfo, AccountStatusEnum.OPEN);
        }
        return true;
    }

}
