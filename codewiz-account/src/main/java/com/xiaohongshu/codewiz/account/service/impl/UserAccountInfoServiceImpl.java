package com.xiaohongshu.codewiz.account.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xiaohongshu.codewiz.account.bo.BatchOperationResult;
import com.xiaohongshu.codewiz.account.dto.UserAccountQueryDTO;
import com.xiaohongshu.codewiz.account.enums.AccountStatusEnum;
import com.xiaohongshu.codewiz.account.mapper.UserAccountInfoMapper;
import com.xiaohongshu.codewiz.account.outer.stopone.AccountAssignedService;
import com.xiaohongshu.codewiz.account.po.AccountApplyLogInfo;
import com.xiaohongshu.codewiz.account.po.UserAccountInfo;
import com.xiaohongshu.codewiz.account.service.IUserAccountInfoService;
import com.xiaohongshu.force.lobot.thrift.dto.res.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName UserAccountInfoServiceImpl
 * @Description
 * @Date 2025/5/19 20:34
 * <AUTHOR>
 */
@Slf4j
@Service
public class UserAccountInfoServiceImpl extends ServiceImpl<UserAccountInfoMapper, UserAccountInfo> implements IUserAccountInfoService {


    @Resource
    private AccountAssignedService accountAssignedService;

    @Resource
    private AccountApplyLogInfoServiceImpl accountApplyLogInfoService;

    /**
     * 根据用户邮箱、服务名称和状态查询用户账号信息
     *
     * @param userEmail   用户邮箱
     * @param serviceName 服务名称
     * @param status      状态
     * @return 用户账号信息
     */
    public UserAccountInfo getByEmailAndServiceAndStatus(String userEmail, String serviceName, Integer status) {
        return baseMapper.selectByEmailAndServiceAndStatus(userEmail, serviceName, status);
    }

    /**
     * 根据服务名称和状态查询用户账号信息
     *
     * @param serviceName 服务
     * @param status      状态
     * @return 用户账号信息
     */
    public List<UserAccountInfo> getByServiceAndStatus(String serviceName, Integer status) {
        return baseMapper.findByServiceAndStatus(serviceName, status);

    }

    /**
     * 根据用户邮箱、服务名称和状态查询用户账号信息
     *
     * @param userEmail 用户邮箱
     * @param status    状态
     * @return 用户账号信息
     */
    public List<UserAccountInfo> getByEmailAndStatus(String userEmail, Integer status) {
        return baseMapper.selectByEmailAndStatus(userEmail, status);
    }

    /**
     * 根据用服务名称和账号开通状态 查询总数量
     *
     * @param serviceName 服务名称
     * @param status      状态
     * @return 用户账号信息
     */
    public Integer getByServiceAndStatusSumCount(String serviceName, Integer status) {
        List<UserAccountInfo> byServiceAndStatus = baseMapper.findByServiceAndStatus(serviceName, status);
        if (CollectionUtils.isNotEmpty(byServiceAndStatus)) {
            return byServiceAndStatus.size();
        }
        return 0;
    }

    /**
     * 根据用户邮箱、服务名称 查询用户账号信息
     *
     * @param serviceName 服务名称
     * @param userEmail   用户邮箱
     * @return 用户账号信息
     */
    public UserAccountInfo getByServiceAndEmail(String serviceName, String userEmail) {
        return baseMapper.findByServiceAndEmail(serviceName, userEmail);
    }

    /**
     * 根据用户邮箱、服务名称 查询用户账号信息
     *
     * @param serviceName 服务名称
     * @param userEmail   用户邮箱
     * @return 用户账号信息
     */
    public List<UserAccountInfo> getListByServiceAndEmail(String serviceName, String userEmail) {
        return baseMapper.findListByServiceAndEmail(serviceName, userEmail);
    }

    /**
     * 根据用户邮箱、服务名称 查询用户账号信息
     *
     * @param serviceName 服务名称
     * @param username    用户名
     * @return 用户账号信息
     */
    public UserAccountInfo getByServiceAndName(String serviceName, String username) {
        return baseMapper.findByServiceAndName(serviceName, username);
    }

    /**
     * 根据服务查询用户账号信息
     *
     * @param serviceName 服务 Cursor
     * @return 用户账号信息
     */
    public List<UserAccountInfo> getAllByService(String serviceName) {
        return baseMapper.findAllByService(serviceName);
    }

    /**
     * 根据服务查询用户账号信息
     *
     * @param departmentId 部门id
     * @return 用户账号信息
     */
    public List<UserAccountInfo> getAllByDepartmentId(String departmentId) {
        return baseMapper.findAllByDepartmentId(departmentId);
    }

    /**
     * 新增用户信息
     *
     * @param userAccountInfo 用户信息
     */
    public void addUserAccountInfo(UserAccountInfo userAccountInfo) {
        this.save(userAccountInfo);
    }

    /**
     * 删除用户信息
     *
     * @param userAccountInfo 用户信息
     */
    public Boolean delUserAccountInfo(UserAccountInfo userAccountInfo) {
        return baseMapper.delUserAccountInfo(userAccountInfo);
    }

    /**
     * 删除用户信息
     */
    public Boolean delUserInfo(String serviceName, String userEmail) {
        return baseMapper.delUserInfo(serviceName, userEmail);
    }


    /**
     * 删除用户信息
     */
    public Boolean delUserInfoByIds(List<Long> ids) {
        return baseMapper.deleteBatchIds(ids) >= 1;
    }


    /**
     * 更新用户信息
     *
     * @param userAccountInfo 用户信息
     */
    public Boolean updateUserAccountInfo(UserAccountInfo userAccountInfo) {
        return baseMapper.updateUserAccountInfo(userAccountInfo);
    }

    /**
     * 分页查询用户账号信息
     *
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    @Override
    public Page<UserAccountInfo> queryUserAccounts(UserAccountQueryDTO queryDTO) {
        long startTime = System.currentTimeMillis();
        Page<UserAccountInfo> result = null;
        try {
            // 创建分页对象
            Page<UserAccountInfo> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());

            // 构建查询条件
            LambdaQueryWrapper<UserAccountInfo> wrapper = new LambdaQueryWrapper<>();

            // 1. 关键词模糊匹配（姓名或邮箱）
            if (!StringUtils.isEmpty(queryDTO.getKeyword())) {
                wrapper.and(w -> w.like(UserAccountInfo::getUsername, queryDTO.getKeyword())
                        .or()
                        .like(UserAccountInfo::getUserEmail, queryDTO.getKeyword()));
            }
            // 2. 服务名称过滤
            if (!StringUtils.isEmpty(queryDTO.getServiceName())) {
                wrapper.eq(UserAccountInfo::getServiceName, queryDTO.getServiceName());
            }

            // 3. 开通日期区间查询
            if (!StringUtils.isEmpty(queryDTO.getDateFrom())) {
                LocalDate fromDate = LocalDate.parse(queryDTO.getDateFrom(), DateTimeFormatter.ISO_LOCAL_DATE);
                wrapper.ge(UserAccountInfo::getOpenTime, fromDate.atStartOfDay());
            }

            if (!StringUtils.isEmpty(queryDTO.getDateTo())) {
                LocalDate toDate = LocalDate.parse(queryDTO.getDateTo(), DateTimeFormatter.ISO_LOCAL_DATE);
                wrapper.le(UserAccountInfo::getOpenTime, toDate.atTime(23, 59, 59));
            }

            // 4. 状态筛选
            if (queryDTO.getStatus() != null) {
                wrapper.eq(UserAccountInfo::getStatus, queryDTO.getStatus());
            }

            // 5. 排序处理
            if (!StringUtils.isEmpty(queryDTO.getSortField()) && !StringUtils.isEmpty(queryDTO.getSortDirection())) {
                boolean isAsc = "asc".equalsIgnoreCase(queryDTO.getSortDirection());

                // 根据不同的排序字段设置排序条件
                switch (queryDTO.getSortField().toLowerCase()) {
                    case "opentime":
                        wrapper.orderBy(true, isAsc, UserAccountInfo::getOpenTime);
                        break;
                    case "createtime":
                        wrapper.orderBy(true, isAsc, UserAccountInfo::getCreateTime);
                        break;
                    case "updatetime":
                        wrapper.orderBy(true, isAsc, UserAccountInfo::getUpdateTime);
                        break;
                    // 可以添加更多排序字段支持
                    default:
                        // 默认按开通时间排序
                        wrapper.orderByDesc(UserAccountInfo::getOpenTime);
                }
            } else {
                // 默认按开通时间降序排列
                wrapper.orderByDesc(UserAccountInfo::getOpenTime);
            }

            // 执行分页查询
            result = this.page(page, wrapper);
            return result;
        } catch (Exception e) {
            log.error("[用户表条件查询]，查询异常: {}", e.getMessage(), e);
            return null;
        } finally {
            log.info("[用户表条件查询]，查询耗时: {} ms, req：{}, resp：{}", System.currentTimeMillis() - startTime, JSON.toJSONString(queryDTO), JSON.toJSONString(result));
        }
    }

    /**
     * 批量取消账号关联
     *
     * @param queryDTOS 查询条件列表
     * @return 批量操作结果
     */
    @Override
    public BatchOperationResult batchCloseAccounts(List<UserAccountQueryDTO> queryDTOS) {
        try {
            BatchOperationResult result = BatchOperationResult.builder().totalCount(queryDTOS.size()).build();
            if (CollectionUtils.isEmpty(queryDTOS)) {
                log.warn("[批量取消账号关联] 请求参数为空");
                return result;
            }
            for (UserAccountQueryDTO queryDTO : queryDTOS) {
                processSingleAccount(queryDTO, result);
            }
            return result;
        } catch (Exception e) {
            log.error("[批量取消账号关联] 处理异常: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 处理单个账号的取消关联逻辑
     *
     * @param queryDTO 查询条件
     * @param result   批量操作结果
     */
    private void processSingleAccount(UserAccountQueryDTO queryDTO, BatchOperationResult result) {
        // 参数校验
        if (StringUtils.isEmpty(queryDTO.getUserEmail()) || StringUtils.isEmpty(queryDTO.getServiceName())) {
            log.warn("[批量取消账号关联] 重要参数缺失: {}", JSON.toJSONString(queryDTO));
            result.addFailedEmail(queryDTO.getUserEmail(), "参数缺失");
            return;
        }

        try {
            // 检查账号是否已关联
            Boolean hasAssigned = accountAssignedService.accountHasAssigned(queryDTO.getServiceName(), queryDTO.getUserEmail());

            if (!hasAssigned) {
                log.warn("[批量取消账号关联] 账号未关联: {}", queryDTO.getUserEmail());
                result.addFailedEmail(queryDTO.getUserEmail(), "账号未关联");
                return;
            }
            // 执行解除关联操作
            Response response = accountAssignedService.removeAccount(queryDTO.getServiceName(), queryDTO.getUserEmail());

            if (response != null && response.result != null && response.result.success) {
                // 更新账号状态
                UserAccountInfo userAccountInfo = getByServiceAndEmail(queryDTO.getServiceName(), queryDTO.getUserEmail());
                if (userAccountInfo != null) {
                    userAccountInfo.setCloseReason("管理员批量取消账号关联");
                    userAccountInfo.setCloseTime(LocalDateTime.now());
                    userAccountInfo.setStatus(AccountStatusEnum.CLOSE.getAccountStatus());
                    updateUserAccountInfo(userAccountInfo);
                    result.addSuccessEmail(queryDTO.getUserEmail());
                    log.info("[批量取消账号关联] 成功: {}", queryDTO.getUserEmail());
                    // 日志记录
                    accountApplyLogInfoService.saveLogWhenUserAccountChanged(userAccountInfo, "管理员批量取消账号关联");
                } else {
                    result.addFailedEmail(queryDTO.getUserEmail(), "未找到用户信息");
                    log.warn("[批量取消账号关联] 未找到用户信息: {}", queryDTO.getUserEmail());
                }
            } else {
                result.addFailedEmail(queryDTO.getUserEmail(), "解除关联失败");
                log.warn("[批量取消账号关联] 解除关联失败: {}", queryDTO.getUserEmail());
            }
        } catch (Exception e) {
            result.addFailedEmail(queryDTO.getUserEmail(), "系统异常: " + e.getMessage());
            log.error("[批量取消账号关联] 处理单个账号时异常: {}", queryDTO.getUserEmail(), e);
        }
    }


    /**
     * 系统检查用户与AD域的账号分配是否匹配
     *
     * @param serviceName 服务名称
     * @return 未分配的账号列表
     */
    public List<String> systemCheckUserAssigned(String serviceName) {
        log.info("[系统检查用户与AD域的账号分配是否匹配] 开始检查账号分配状态: time = {}", LocalDateTime.now());
        List<String> unassignedAccounts = new ArrayList<>();
        try {
            List<UserAccountInfo> byServiceAndStatus = getByServiceAndStatus(serviceName, 2);
            if (CollectionUtils.isEmpty(byServiceAndStatus)) {
                log.info("[系统检查用户与AD域的账号分配是否匹配] 没有发现该服务下已经分配的账号信息");
                return unassignedAccounts;
            }
            for (UserAccountInfo user : byServiceAndStatus) {
                boolean hasAssigned = accountAssignedService.accountHasAssigned(serviceName, user.getUserEmail());
                if (!hasAssigned) {
                    unassignedAccounts.add(user.getUserEmail());
                    user.setCloseReason("系统核验发现账号在AD域未分配，自动关闭");
                    user.setCloseTime(LocalDateTime.now());
                    updateById(user);
                    // 日志记录
                    accountApplyLogInfoService.saveLogWhenUserAccountChanged(user, "系统核验发现账号在AD域未分配，自动关闭");
                }
            }
            return unassignedAccounts;
        } catch (Exception e) {
            log.error("[系统检查用户与AD域的账号分配是否匹配] 执行异常: {}", e.getMessage(), e);
            return unassignedAccounts;
        }
    }

}
