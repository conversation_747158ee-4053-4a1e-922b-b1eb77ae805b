package com.xiaohongshu.codewiz.account.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @ClassName RedFlowProcessMessageBodyBO
 * @Description
 * @Date 2025/5/21 20:06
 * <AUTHOR>
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class RedFlowProcessMessageBodyBO {

    private String applyType;
    private List<String> owner_emails;

    private String serviceName;

    /**
     * 流程是否结束
     */
    private Boolean processEnd;
    private String businessId;
    private String auditManRedName;
    /**
     * 审批节点key
     */
    private String auditPhaseId;
    private String jumpUrl;

    @JsonProperty("wf_startTimeDisplay")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime workflowStartTimeDisplay;

    @JsonProperty("wf_meDisplay")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime workflowMeDisplay;

    @JsonProperty("wf_formDataUpdateTimestamp")
    private Long workflowFormDataUpdateTimestamp;
    /**
     * 审批人名字
     */
    private String auditMan;
    /**
     * 发起人
     */
    private String startUserId;
    private String resourceUrl;
    private String startUserName;
    private String flowId;
    /**
     * 下一待审批节点
     */
    private List<TaskInfo> taskInfoList;
    /**
     * 单据类型 ： RedFlowProcessConstant.FORM_TYPE_APPLY_CLOSE
     */
    private String formType;

    // 申请理由
    private String requestReason;

    private List<String> remove_member_emails;

    private List<String> member_emails;
    /**
     * 审批人key
     */
    private String auditPhaseKey;
    private String address;
    private String startUserEmail;
    private List<RemoveMember> remove_members;
    /**
     * 审批人邮箱
     */
    private String auditManEmail;
    private Integer email_group_id;
    /**
     * 审批节点名字
     */
    private String auditPhase;
    private String startUserRedName;
    /**
     * 当前操作人ID
     */
    private String currentAuditUser;
    private Integer flowLevel;
    private String wf_comment;
    /**
     * 操作类型: RedFlowProcessStatusEnum对齐
     */
    private String processOperate;
    private String applyUrl;

    @JsonProperty("auditTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime auditTime;

    /**
     * 下一待审批节点 偶尔会有纯字符串格式，格式不稳定，不再解析
     */
    private List<CurrentTaskInfo> currentTaskInfo;

    @JsonProperty("wf_completeTimeDisplay")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime workflowCompleteTimeDisplay;

    private String myResource;
    /**
     * 单据状态
     */
    private String auditStatus;
    private String applyTypeDesc;
    /**
     * 单据号
     */
    private String formNo;
    /**
     * 审批意见
     */
    private String comment;

    @JsonProperty("smtp_email_item_details")
    private List<SmtpEmailItemDetails> smtpEmailItemDetails;

    @JsonProperty("owner_uids")
    private List<String> ownerUIds;
    @JsonProperty("smtp_email_passwords")
    private String smtpEmailPasswords;
    @JsonProperty("pre_apply_id")
    private String preApplyId;

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class TaskInfo {
        private String currentNodeId;
        private String currentNodeName;
        private List<String> userIdList;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class RemoveMember {
        @JsonProperty("type_display")
        private String typeDisplay;
        private String address;
        private String name;
        private Integer type;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class CurrentTaskInfo {
        private String taskId;
        @JsonProperty("taskNodeKey")
        private String taskNodeKey;
        @JsonProperty("taskNodeName")
        private String taskNodeName;
        private List<String> userIdList;
    }


    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class SmtpEmailItemDetails {
        private String address;
        @JsonProperty("display_name")
        private String displayName;
    }

}
