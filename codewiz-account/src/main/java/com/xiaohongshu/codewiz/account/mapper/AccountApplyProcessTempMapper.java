package com.xiaohongshu.codewiz.account.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xiaohongshu.codewiz.account.po.AccountApplyProcessTempInfo;
import com.xiaohongshu.codewiz.account.po.AccountApplyProcessTempInfo;
import com.xiaohongshu.codewiz.account.po.AdminAccountInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @ClassName AccountApplyProcessTempMapper
 * @Description
 * @Date 2025/5/22 11:11
 * <AUTHOR>
 */
@Mapper
public interface AccountApplyProcessTempMapper extends BaseMapper<AccountApplyProcessTempInfo> {

    /**
     * 根据用户邮箱、服务名称查询管理员账号信息
     */
    default List<AccountApplyProcessTempInfo> findByServiceAndEmail(@Param("serviceName") String serviceName,
                                                              @Param("userEmail") String userEmail) {
        LambdaQueryWrapper<AccountApplyProcessTempInfo> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(AccountApplyProcessTempInfo::getUserEmail, userEmail)
                .eq(AccountApplyProcessTempInfo::getServiceName, serviceName);
        return this.selectList(wrapper);
    }

    /**
     * 根据服务名和用户名查询信息
     */
    default AccountApplyProcessTempInfo findByServiceAndName(@Param("serviceName") String serviceName,
                                                             @Param("username") String username) {
        LambdaQueryWrapper<AccountApplyProcessTempInfo> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(AccountApplyProcessTempInfo::getServiceName, serviceName)
                .eq(AccountApplyProcessTempInfo::getUsername, username)
                .last("LIMIT 1");
        return this.selectOne(wrapper);
    }

    /**
     * 根据 单据号 查询流程中的数据
     */
    default List<AccountApplyProcessTempInfo> findByRequestId(@Param("requestId") String requestId) {
        LambdaQueryWrapper<AccountApplyProcessTempInfo> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(AccountApplyProcessTempInfo::getRequestId, requestId);
        return this.selectList(wrapper);
    }


    /**
     * 根据服务名称查询账号信息
     */
    default List<AccountApplyProcessTempInfo> findAllByService(@Param("serviceName") String serviceName) {
        LambdaQueryWrapper<AccountApplyProcessTempInfo> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(AccountApplyProcessTempInfo::getServiceName, serviceName);
        return this.selectList(wrapper);
    }

    /**
     * 根据部门id查询账号信息
     */
    default List<AccountApplyProcessTempInfo> findAllByDepartmentId(@Param("departmentId") String departmentId) {
        LambdaQueryWrapper<AccountApplyProcessTempInfo> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(AccountApplyProcessTempInfo::getDepartmentId, departmentId);
        return this.selectList(wrapper);
    }


    /**
     * 删除员工流程中临时表数据
     *
     * @param accountApplyProcessTempInfo 员工流程中临时表
     * @return 是否删除成功
     */
    default Boolean delAccountApplyProcessTempInfo(AccountApplyProcessTempInfo accountApplyProcessTempInfo) {
        LambdaQueryWrapper<AccountApplyProcessTempInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AccountApplyProcessTempInfo::getServiceName, accountApplyProcessTempInfo.getServiceName());
        queryWrapper.eq(AccountApplyProcessTempInfo::getUserEmail, accountApplyProcessTempInfo.getUserEmail());
//        queryWrapper.eq(AccountApplyProcessTempInfo::getRequestId, accountApplyProcessTempInfo.getRequestId());
        return this.delete(queryWrapper) >= 1;
    }

    /**
     * 删除员工流程中临时表数据
     * @param serviceName 服务名称
     * @param userEmail  用户邮箱
     * @return 是否删除成功
     */
    default Boolean delByServiceNameAndEmail(String serviceName, String userEmail) {
        LambdaQueryWrapper<AccountApplyProcessTempInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AccountApplyProcessTempInfo::getServiceName, serviceName);
        queryWrapper.eq(AccountApplyProcessTempInfo::getUserEmail, userEmail);
        return this.delete(queryWrapper) >= 1;
    }

    /**
     * 更新临时表数据
     *
     * @param accountApplyProcessTempInfo 员工流程中临时表
     */
    default Boolean updateAccountApplyProcessTempInfo(AccountApplyProcessTempInfo accountApplyProcessTempInfo) {
        LambdaQueryWrapper<AccountApplyProcessTempInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AccountApplyProcessTempInfo::getServiceName, accountApplyProcessTempInfo.getServiceName());
        queryWrapper.eq(AccountApplyProcessTempInfo::getUserEmail, accountApplyProcessTempInfo.getUserEmail());
        return this.update(accountApplyProcessTempInfo, queryWrapper) >= 1;
    }

}
