package com.xiaohongshu.codewiz.account.config;

import javax.annotation.Resource;
import javax.sql.DataSource;

import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration;
import com.xiaohongshu.redsql.group.jdbc.GroupDataSource;

/**
 * @ClassName DataSourceConfiguration
 * @Description
 * @Date 2025/5/16 20:43
 * <AUTHOR>
 */
@Configuration
@MapperScan(basePackages = {"com.xiaohongshu.codewiz.account.mapper"}, sqlSessionFactoryRef = "sqlSessionFactory")
public class DataSourceConfiguration {
    @Resource
    private MybatisPlusAutoConfiguration mybatisPlusAutoConfiguration;

    // redsql 数据源创建
    @Bean(name = "codewizAccountDataSource", initMethod = "init")
    @ConfigurationProperties(prefix = "spring.datasource.codewiz")
    public GroupDataSource codewizDataSource() {
        return DataSourceBuilder.create().type(GroupDataSource.class).build();
    }

    /**
     * 如果不配置此方法默认由 {@link MybatisPlusAutoConfiguration#sqlSessionFactory} 创建
     * 为兼容多数据源，手动创建 sqlSessionFactory 对象
     */
    @Bean
    @Primary
    public SqlSessionFactory sqlSessionFactory(@Qualifier("codewizAccountDataSource") DataSource datasource)
            throws Exception {
        return mybatisPlusAutoConfiguration.sqlSessionFactory(datasource);
    }
}
