package com.xiaohongshu.codewiz.account.controller;

import com.xiaohongshu.codewiz.account.dto.CommonResponse;
import com.xiaohongshu.codewiz.account.enums.AccountStatusEnum;
import com.xiaohongshu.codewiz.account.outer.stopone.AccountAssignedService;
import com.xiaohongshu.codewiz.account.outer.stopone.AccountStandDetailSearchService;
import com.xiaohongshu.codewiz.account.po.UserAccountInfo;
import com.xiaohongshu.codewiz.account.service.impl.UserAccountInfoServiceImpl;
import com.xiaohongshu.force.paploo.thrift.dto.AccountDetailBean;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;

/**
 * @ClassName AccountSyncController
 * @Description
 * @Date 2025/6/17 12:53
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/codexaccount/api/account/sync")
public class AccountSyncController {

    @Resource
    AccountAssignedService accountAssignedService;

    @Resource
    AccountStandDetailSearchService accountStandDetailSearchService;

    @Resource
    private UserAccountInfoServiceImpl userAccountInfoServiceImpl;

    @Operation(summary = "同步账号绑定信息")
    @GetMapping("/serviceName")
    public CommonResponse<?> syncAccount(@RequestParam("serviceName") String serviceName) {
        try {
            List<String> allPageAccountEmails = accountAssignedService.getAllPageAccountInfos(serviceName);
            if (CollectionUtils.isEmpty(allPageAccountEmails)) {
                return CommonResponse.notFound("未查询到it侧账号关联信息");
            }

            Map<String, List<String>> resultMap = new HashMap<>();
            // 根据邮箱查询所有用户详情信息
            for (String userEmail : allPageAccountEmails) {
                // 删除重复数据
                delRepeatUser(serviceName, userEmail);
                UserAccountInfo userExisted = userAccountInfoServiceImpl.getByServiceAndEmail(serviceName, userEmail);
                if (userExisted == null) {
                    storeUserInfoByDetailInfo(serviceName, userEmail);
                    resultMap.computeIfAbsent("add", k -> new ArrayList<>()).add(userEmail);
                } else {
                    Integer status = userExisted.getStatus();
                    if (Objects.equals(AccountStatusEnum.OPEN.getAccountStatus(), status)) {
                        resultMap.computeIfAbsent("added", k -> new ArrayList<>()).add(userEmail);
                        continue;
                    }
                    userExisted.setStatus(AccountStatusEnum.OPEN.getAccountStatus());
                    userExisted.setOpenTime(LocalDateTime.now());
                    userExisted.setDescription("数据自动迁移");
                    userAccountInfoServiceImpl.updateUserAccountInfo(userExisted);
                    resultMap.computeIfAbsent("update", k -> new ArrayList<>()).add(userEmail);
                }
            }
            return CommonResponse.success(resultMap);
        } catch (Exception e) {
            log.error("[IT数据自动迁移至User表中] 出现异常", e);
            return CommonResponse.builder().code(500).message("IT数据自动迁移至User表中" + e.getMessage()).build();
        }
    }

    /**
     * 删除重复数据
     * @param serviceName 服务名称
     * @param userEmail 用户邮箱
     */
    private void delRepeatUser(String serviceName, String userEmail) {
        List<UserAccountInfo> list = userAccountInfoServiceImpl.getListByServiceAndEmail(serviceName, userEmail);
        if (CollectionUtils.isNotEmpty(list) && list.size() > 1) {
            // 优先保留一条OPEN状态
            Optional<UserAccountInfo> openOpt = list.stream()
                    .filter(u -> AccountStatusEnum.OPEN.getAccountStatus().equals(u.getStatus()))
                    .findFirst();
            UserAccountInfo toKeep;
            // 没有OPEN，保留第一条
            toKeep = openOpt.orElseGet(() -> list.get(0));
            for (UserAccountInfo u : list) {
                if (!u.equals(toKeep)) {
                    userAccountInfoServiceImpl.delUserAccountInfo(u);
                }
            }
        }
    }

    /**
     * 获取用户详情，填充userInfo并落库
     *
     * @param serviceName 服务名称
     * @param userEmail   用户邮箱
     */
    private void storeUserInfoByDetailInfo(String serviceName, String userEmail) {
        AccountDetailBean singleAccountDetail = accountStandDetailSearchService.getSingleAccountDetail(userEmail);
        if(singleAccountDetail == null){
            return;
        }
        UserAccountInfo userAccountInfo = UserAccountInfo.builder()
                .openTime(LocalDateTime.now())
                .description("系统自动迁移")
                .serviceName(serviceName)
                .userEmail(userEmail)
                .userId(singleAccountDetail.getChannelUserId())
                .departmentId(String.valueOf(singleAccountDetail.getDepartmentId()))
                .departmentName(Optional.ofNullable(singleAccountDetail.getDepartmentNamePath())
                        .map(path -> path.split(","))
                        .filter(parts -> parts.length > 0)
                        .map(parts -> parts[0])
                        .orElse(null))
                .departmentNamePathId(singleAccountDetail.getDepartmentIdPath())
                .departmentNamePath(singleAccountDetail.getDepartmentNamePath())
                .username(singleAccountDetail.getDisplayName())
                .status(AccountStatusEnum.OPEN.getAccountStatus())
                .build();
        userAccountInfoServiceImpl.save(userAccountInfo);
    }

}
