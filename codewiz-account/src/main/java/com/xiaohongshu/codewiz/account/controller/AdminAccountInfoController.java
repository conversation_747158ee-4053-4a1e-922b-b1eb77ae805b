package com.xiaohongshu.codewiz.account.controller;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xiaohongshu.codewiz.account.dto.AdminAccountQueryDTO;
import com.xiaohongshu.codewiz.account.dto.CommonResponse;
import com.xiaohongshu.codewiz.account.po.AdminAccountInfo;
import com.xiaohongshu.codewiz.account.po.UserAccountInfo;
import com.xiaohongshu.codewiz.account.service.impl.AdminAccountInfoServiceImpl;
import com.xiaohongshu.codewiz.account.service.impl.UserAccountInfoServiceImpl;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * @ClassName AdminAccountInfoController
 * @Description
 * @Date 2025/5/16 20:43
 * <AUTHOR>
 */
@RestController
@RequestMapping("/codexaccount/api/account/admin")
@Slf4j
@Tag(name = "管理员账号信息接口")
public class AdminAccountInfoController {

    @Resource
    private AdminAccountInfoServiceImpl adminAccountInfoService;

    @Operation(summary = "获取所有管理员账号信息")
    @GetMapping("/all")
    public CommonResponse<?> getAllAdminInfo() {
        try {
            List<AdminAccountInfo> adminAccountInfos = adminAccountInfoService.list();
            if (adminAccountInfos.isEmpty()) {
                log.warn("[管理员账号查询]查询全部，数据为空，请核对DB");
                return CommonResponse.notFound();
            }
            log.info("[管理员账号查询] 查询全部成功. result = {}", JSON.toJSONString(adminAccountInfos));
            return CommonResponse.success(adminAccountInfos);
        } catch (Exception e) {
            log.error("[管理员账号查询] 查询全部，出现异常", e);
            return CommonResponse.builder().code(500).message("Error retrieving admin account info").build();
        }
    }

    /**
     * 新增用户信息
     *
     * @param adminAccountQueryDTO 查询信息
     * @return 用户账号信息
     */
    @Operation(summary = "更新管理员账号信息")
    @PostMapping("/condition/search")
    public CommonResponse<?> doConditionAdminAccountInfo(@RequestBody AdminAccountQueryDTO adminAccountQueryDTO) {
        try {
            log.info("[分页查询管理员账号信息] 查询条件: {}", JSON.toJSONString(adminAccountQueryDTO));
            Page<AdminAccountInfo> resultPage = adminAccountInfoService.queryAdminAccounts(adminAccountQueryDTO);
            if (resultPage == null || CollectionUtils.isEmpty(resultPage.getRecords())) {
                log.warn("[分页查询管理员账号信息] 查询结果为空，请核验DB. queryDTO = {}", JSON.toJSONString(adminAccountQueryDTO));
                return CommonResponse.notFound();
            }
            for (AdminAccountInfo adminAccountInfo : resultPage.getRecords()) {
                log.info("管理员: {}, 邮箱: {}", adminAccountInfo.getUsername(), adminAccountInfo.getUserEmail());
                log.info("[分页查询管理员账号信息] 查询结果: 管理员: {}", JSON.toJSONString(adminAccountInfo));
            }
            log.info("结果= {}", JSON.toJSONString(ResponseEntity.ok(resultPage)));
            return CommonResponse.success(resultPage);
        } catch (Exception e) {
            log.error("[分页查询管理员账号信息] 查询失败，出现异常", e);
            return CommonResponse.builder().code(500).message("查询失败，出现异常，请联系管理员处理").build();
        }
    }


    /**
     * 根据服务名称、用户邮箱 查询用户账号信息
     *
     * @param serviceName 开通服务名称，Cursor
     * @param userEmail   用户邮箱
     * @return 用户账号信息
     */
    @Operation(summary = "根据服务名称和邮箱查询管理员账号信息")
    @GetMapping("/getByServiceAndEmail")
    public CommonResponse<?> getAdminInfoByServiceAndEmail(@RequestParam("serviceName") String serviceName,
                                                           @RequestParam("userEmail") String userEmail) {
        try {
            AdminAccountInfo adminAccountInfo = adminAccountInfoService.getByServiceAndEmail(serviceName, userEmail);
            if (adminAccountInfo == null) {
                log.warn("[管理员账号查询]查询数据为空, 请核对DB  service: {},  email: {}", serviceName, userEmail);
                return CommonResponse.notFound();
            }
            log.info("[管理员账号查询]查询成功, service: {}, email: {}, result = {}", serviceName, userEmail, JSON.toJSONString(adminAccountInfo));
            return CommonResponse.success(adminAccountInfo);
        } catch (Exception e) {
            log.error("[管理员账号查询]查询失败，serviceName = {}, userEmail = {}, 出现异常", serviceName, userEmail, e);
            return CommonResponse.builder().code(500).message("查询失败，出现异常，请联系管理员处理").build();
        }
    }

    /**
     * 根据用户邮箱、服务名称和状态查询用户账号信息
     *
     * @param serviceName 开通服务名称，Cursor
     * @param username    用户名
     * @return 用户账号信息
     */
    @Operation(summary = "根据服务名称和用户名查询管理员账号信息")
    @GetMapping("/getByServiceAndName")
    public CommonResponse<?> getAdminInfoByServiceAndName(@RequestParam("serviceName") String serviceName,
                                                          @RequestParam("username") String username) {
        try {
            AdminAccountInfo adminAccountInfo = adminAccountInfoService.getByServiceAndName(serviceName, username);
            if (adminAccountInfo == null) {
                log.warn("[管理员账号查询]查询数据为空，请核对DB, service: {},  username: {}", serviceName, username);
                return CommonResponse.notFound();
            }
            log.info("[管理员账号查询]查询成功, service: {}, username: {}, result = {}", serviceName, username, JSON.toJSONString(adminAccountInfo));
            return CommonResponse.success(adminAccountInfo);
        } catch (Exception e) {
            log.error("[管理员账号查询]查询失败，serviceName = {}, username = {}, 出现异常", serviceName, username, e);
            return CommonResponse.builder().code(500).message("查询失败，出现异常，请联系管理员处理").build();
        }
    }

    /**
     * 根据服务名称和状态查询用户账号信息
     *
     * @param serviceName 开通服务名称，Cursor
     * @return 用户账号信息
     */
    @Operation(summary = "根据服务名称查询管理员账号信息")
    @GetMapping("/getByService")
    public CommonResponse<?> getAdminInfoByService(@RequestParam("serviceName") String serviceName) {
        try {
            List<AdminAccountInfo> adminAccountInfos = adminAccountInfoService.getAllByService(serviceName);
            if (CollectionUtils.isEmpty(adminAccountInfos)) {
                log.warn("[管理员账号查询]查询数据为空，请核对DB  service: {}, result = {}", serviceName, JSON.toJSONString(adminAccountInfos));
                return CommonResponse.notFound();
            }
            log.info("[管理员账号查询]查询数据成功, service: {}, result = {}", serviceName, JSON.toJSONString(adminAccountInfos));
            return CommonResponse.success(adminAccountInfos);
        } catch (Exception e) {
            log.error("[管理员账号查询]查询失败，serviceName = {}, 出现异常", serviceName, e);
            return CommonResponse.builder().code(500).message("查询失败，出现异常，请联系管理员处理").build();
        }
    }

    /**
     * 根据服务名称和状态查询用户账号信息
     *
     * @param departmentId 部门id
     * @return 用户账号信息
     */
    @Operation(summary = "根据部门id查询管理员账号信息")
    @GetMapping("/getByDepartmentId")
    public CommonResponse<?> getAdminInfoByDepartmentId(@RequestParam("departmentId") String departmentId) {
        try {
            List<AdminAccountInfo> adminAccountInfos = adminAccountInfoService.getAllByDepartmentId(departmentId);
            if (CollectionUtils.isEmpty(adminAccountInfos)) {
                log.warn("[管理员账号查询]查询数据为空, 请核对DB, departmentId: {}, result = {}", departmentId, JSON.toJSONString(adminAccountInfos));
                return CommonResponse.notFound();
            }
            log.info("[管理员账号查询]查询数据成功, departmentId: {}, result = {}", departmentId, JSON.toJSONString(adminAccountInfos));
            return CommonResponse.success(adminAccountInfos);
        } catch (Exception e) {
            log.error("[管理员账号查询]查询失败，departmentId = {}, 出现异常", departmentId, e);
            return CommonResponse.builder().code(500).message("查询失败，出现异常，请联系管理员处理").build();
        }
    }

    /**
     * 新增用户信息
     *
     * @param adminAccountInfo 管理员信息
     * @return 用户账号信息
     */
    @Operation(summary = "新增管理员账号信息")
    @PostMapping("/add")
    public CommonResponse<?> addAdminAccountInfo(@RequestBody AdminAccountInfo adminAccountInfo) {
        try {
            if (adminAccountInfo == null || adminAccountInfo.isNotValid()) {
                log.warn("[管理员账号新增], 重要参数缺失，新增失败. adminAccountInfo = {}", JSON.toJSONString(adminAccountInfo));
                return CommonResponse.badRequest("[管理员账号新增], 重要参数缺失，新增失败. ");
            }
            adminAccountInfoService.addAdminAccountInfo(adminAccountInfo);
            log.info("[管理员账号新增] 成功: {}", adminAccountInfo);
            return CommonResponse.success(adminAccountInfo);
        } catch (Exception e) {
            log.error("[管理员账号新增] 失败，出现异常, adminAccountInfo = {}", JSON.toJSONString(adminAccountInfo), e);
            return CommonResponse.builder().code(500).message("新增管理员账号信息失败，出现异常，请联系管理员处理").build();
        }
    }

    /**
     * 新增用户信息
     *
     * @param adminAccountInfo 管理员信息
     * @return 用户账号信息
     */
    @Operation(summary = "删除管理员账号信息")
    @PostMapping("/delete")
    public CommonResponse<?> deleteAdminAccountInfo(@RequestBody AdminAccountInfo adminAccountInfo) {
        try {
            if (adminAccountInfo == null || adminAccountInfo.isNotValid()) {
                log.warn("[管理员账号删除], 重要参数缺失，删除失败. adminAccountInfo = {}", JSON.toJSONString(adminAccountInfo));
                return CommonResponse.badRequest("[管理员账号删除], 重要参数缺失，新增失败.");
            }
            return CommonResponse.success(adminAccountInfoService.delAdminAccountInfo(adminAccountInfo));
        } catch (Exception e) {
            log.error("[管理员账号删除] 失败，出现异常, adminAccountInfo = {}", JSON.toJSONString(adminAccountInfo), e);
            return CommonResponse.builder().code(500).message("删除管理员账号信息失败，出现异常，请联系管理员处理").build();
        }
    }

    /**
     * 新增用户信息
     *
     * @param adminAccountInfo 管理员信息
     * @return 用户账号信息
     */
    @Operation(summary = "更新管理员账号信息")
    @PostMapping("/update")
    public CommonResponse<?> updateAdminAccountInfo(@RequestBody AdminAccountInfo adminAccountInfo) {
        try {
            if (adminAccountInfo == null || adminAccountInfo.isNotValid()) {
                log.warn("[管理员账号更新], 重要参数缺失，更新失败. adminAccountInfo = {}", JSON.toJSONString(adminAccountInfo));
                return CommonResponse.badRequest("[管理员账号更新], 重要参数缺失，更新失败.");
            }
            return CommonResponse.success(adminAccountInfoService.updateAdminAccountInfo(adminAccountInfo));
        } catch (Exception e) {
            log.error("[管理员账号更新] 失败，出现异常, adminAccountInfo = {}", JSON.toJSONString(adminAccountInfo), e);
            return CommonResponse.builder().code(500).message("更新管理员账号信息失败，出现异常，请联系管理员处理").build();
        }
    }

}
