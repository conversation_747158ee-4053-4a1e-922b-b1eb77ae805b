package com.xiaohongshu.codewiz.account.outer.redflow;

import com.xiaohongshu.fls.finance.rpc.workflow.process.operator.OaFlowRuntimeProcessRpc;
import com.xiaohongshu.fls.finance.rpc.workflow.task.operator.OaFlowRuntimeTaskRpc;
import com.xiaohongshu.fls.rpc.finance.workflow.process.req.OaRpcAbandonReq;
import com.xiaohongshu.fls.rpc.finance.workflow.process.response.OaRpcProcessCurrentInfoResp;
import com.xiaohongshu.fls.rpc.finance.workflow.task.req.OaRpcTaskCompleteReq;
import com.xiaohongshu.infra.rpc.base.Context;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @ClassName RedFlowProcessService
 * @Description
 * @Date 2025/5/21 09:28
 * <AUTHOR>
 */
@Service
@Slf4j
public class RedFlowProcessService {


    @Resource
    private OaFlowRuntimeProcessRpc.Iface oaFlowRuntimeProcessRpc;

    @Resource
    private OaFlowRuntimeTaskRpc.Iface oaFlowRuntimeTaskRpc;

    /**
     * 完成任务 接口调用
     *
     * @param taskId    任务id
     * @param userId    用户ID
     * @param opinion   意见
     * @param formNum   单据号
     */
    public void completeTask(String taskId, String userId, String opinion, String formNum) {
        OaRpcTaskCompleteReq oaRpcTaskCompleteReq = buildOaRpcTaskCompleteReq(taskId, userId, opinion, formNum);
        long startTime = System.currentTimeMillis();
        com.xiaohongshu.fls.rpc.finance.workflow.task.response.OaRpcProcessCurrentInfoResp oaRpcProcessCurrentInfoResp = null;
        try {
            oaRpcProcessCurrentInfoResp = oaFlowRuntimeTaskRpc.completeTask(new Context(), oaRpcTaskCompleteReq);
        } catch (TException e) {
            log.error("[redFlow审批通过流程][审核通过completeTask接口], 出现异常, req={}; ", oaRpcTaskCompleteReq, e);
        } finally {
            log.info("[redFlow审批通过流程][审核通过completeTask接口], costTime={}, req={} , abandonResult={}", System.currentTimeMillis() - startTime, oaRpcTaskCompleteReq, oaRpcProcessCurrentInfoResp);
        }
    }


    /**
     * 终止流程 接口调用
     *
     * @param formNum   单据号
     * @param userId    用户ID
     */
    public void abandonProcess(String formNum, String userId, String opinion) {
        OaRpcAbandonReq oaRpcAbandonReq = buildOaRpcAbandonReq(formNum, userId, opinion);
        long startTime = System.currentTimeMillis();
        OaRpcProcessCurrentInfoResp abandonResult = null;
        boolean abandonProcess = false;
        try {
            abandonResult = oaFlowRuntimeProcessRpc.abandon(new Context(), oaRpcAbandonReq);
            if (abandonResult != null && abandonResult.response != null) {
                abandonProcess = abandonResult.response.success;
            }
            if (abandonProcess) {
                log.info("[RedFlow流程提交事件][终止流程abandon接口]流程已被放弃，formNo: {}, userId: {}", formNum, userId);
            } else {
                log.warn("[RedFlow流程提交事件][终止流程abandon接口]流程放弃/终止 接口返参为false，请核验，formNo: {}, userId: {}", formNum, userId);
            }
        } catch (TException e) {
            log.info("[redFlow终止流程][终止流程abandon接口], 出现异常, costTime={}, req={} , abandonResult={}; ", System.currentTimeMillis() - startTime, oaRpcAbandonReq, abandonResult, e);
        } finally {
            log.info("[redFlow终止流程][终止流程abandon接口], costTime={}, req={} , abandonResult={}", System.currentTimeMillis() - startTime, oaRpcAbandonReq, abandonResult);
        }
    }

    /**
     * 完成任务 接口调用
     *
     * @param taskId    任务id
     * @param userId    用户ID
     * @param opinion   意见
     * @param formNum   单据号
     * @return 构建请求体
     */
    private OaRpcTaskCompleteReq buildOaRpcTaskCompleteReq(String taskId, String userId, String opinion, String formNum) {
        OaRpcTaskCompleteReq req = new OaRpcTaskCompleteReq();
        req.setUserId(userId);
        req.setTaskId(taskId);
        req.setOpinion(opinion);
        req.setFormNum(formNum);
        return req;
    }

    /**
     * 构建终止流程请求
     *
     * @param formNum   单据号
     * @param userId    用户id
     * @return OaRpcAbandonReq
     */
    private OaRpcAbandonReq buildOaRpcAbandonReq(String formNum, String userId, String opinion) {
        OaRpcAbandonReq req = new OaRpcAbandonReq();
        req.setFormNum(formNum);
        req.setUserId(userId);
        req.setOpinion(opinion);
        return req;
    }

}
