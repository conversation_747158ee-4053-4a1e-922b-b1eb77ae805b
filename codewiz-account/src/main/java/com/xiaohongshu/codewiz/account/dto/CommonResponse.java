package com.xiaohongshu.codewiz.account.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName ApiResponse
 * @Description
 * @Date 2025/6/3 15:57
 * <AUTHOR>
 */
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CommonResponse<T> {
    private int code;          // 状态码
    private String message;    // 状态描述
    private T data;            // 响应数据

    // 请求参数错误
    public static <T> CommonResponse<T> badRequest(String message) {
        return new CommonResponse<>(400, message, null);
    }

    // 成功响应（带数据）
    public static <T> CommonResponse<T> success(T data) {
        return new CommonResponse<>(200, "success", data);
    }

    // 成功响应（无数据）
    public static <T> CommonResponse<T> success() {
        return new CommonResponse<>(200, "success", null);
    }

    // 检查无结果
    public static <T> CommonResponse<T> notFound() {
        return new CommonResponse<>(204, "no content", null);
    }

    // 检查无结果
    public static <T> CommonResponse<T> notFound(String message) {
        return new CommonResponse<>(204, message, null);
    }

    // 失败响应
    public static <T> CommonResponse<T> error(int code, String message) {
        return new CommonResponse<>(code, message, null);
    }
}
