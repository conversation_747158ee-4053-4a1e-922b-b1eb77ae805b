package com.xiaohongshu.codewiz.account.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xiaohongshu.codewiz.account.po.AdminAccountInfo;
import com.xiaohongshu.codewiz.account.po.UserAccountInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * @ClassName PromptContentMapper
 * @Description prompt模板内容
 * @Date 2025/5/19 15:41
 * <AUTHOR>
 */
@Mapper
public interface AdminAccountInfoMapper extends BaseMapper<AdminAccountInfo> {

    /**
     * 根据用户邮箱、服务名称查询管理员账号信息
     */
    default AdminAccountInfo findByServiceAndEmail(@Param("serviceName") String serviceName,
                                                   @Param("userEmail") String userEmail) {
        LambdaQueryWrapper<AdminAccountInfo> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(AdminAccountInfo::getUserEmail, userEmail)
                .eq(AdminAccountInfo::getServiceName, serviceName)
                .last("LIMIT 1");
        return this.selectOne(wrapper);
    }

    /**
     * 根据服务名称/用户名查询管理员账号信息
     */
    default AdminAccountInfo findByServiceAndName(@Param("serviceName") String serviceName,
                                                  @Param("username") String username) {
        LambdaQueryWrapper<AdminAccountInfo> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(AdminAccountInfo::getServiceName, serviceName)
                .eq(AdminAccountInfo::getUsername, username)
                .last("LIMIT 1");
        return this.selectOne(wrapper);
    }


    /**
     * 根据服务名称查询管理员账号信息
     */
    default List<AdminAccountInfo> findAllByService(@Param("serviceName") String serviceName) {
        LambdaQueryWrapper<AdminAccountInfo> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(AdminAccountInfo::getServiceName, serviceName);
        return this.selectList(wrapper);
    }

    /**
     * 根据服务名称查询管理员账号信息
     */
    default List<AdminAccountInfo> findAllByDepartmentId(@Param("departmentId") String departmentId) {
        LambdaQueryWrapper<AdminAccountInfo> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(AdminAccountInfo::getDepartmentId, departmentId);
        return this.selectList(wrapper);
    }

    default Boolean delAdminAccountInfo(AdminAccountInfo adminAccountInfo) {
        LambdaQueryWrapper<AdminAccountInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AdminAccountInfo::getServiceName, adminAccountInfo.getServiceName());
        queryWrapper.eq(AdminAccountInfo::getUserEmail, adminAccountInfo.getUserEmail());
        return this.delete(queryWrapper) >= 1;
    }

    /**
     * 新增用户信息
     *
     * @param adminAccountInfo 管理员信息
     */
    default Boolean updateAdminAccountInfo(AdminAccountInfo adminAccountInfo) {
        LambdaQueryWrapper<AdminAccountInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AdminAccountInfo::getServiceName, adminAccountInfo.getServiceName());
        queryWrapper.eq(AdminAccountInfo::getUserEmail, adminAccountInfo.getUserEmail());
        return this.update(adminAccountInfo, queryWrapper) >= 1;
    }

}
