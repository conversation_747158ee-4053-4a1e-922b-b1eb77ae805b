package com.xiaohongshu.codewiz.account.dto;

import lombok.Data;

/**
 * @ClassName AdminAccountQueryDTO
 * @Description
 * @Date 2025/5/29 20:19
 * <AUTHOR>
 */
@Data
public class AdminAccountQueryDTO {

    // 搜索关键词（姓名或邮箱）
    private String keyword;

    private String serviceName;  // 服务名称，用于过滤

    // 日期区间
    private String dateFrom;  // YYYY-MM-DD
    private String dateTo;    // YYYY-MM-DD

    // 分页信息
    private Integer pageSize = 10;  // 默认每页10条

    private Integer pageNum = 1;    // 默认第1页

    // 排序字段和方向
    private String sortField = "createTime";  // 默认按开通时间排序

    private String sortDirection = "desc";  // 默认降序


}
