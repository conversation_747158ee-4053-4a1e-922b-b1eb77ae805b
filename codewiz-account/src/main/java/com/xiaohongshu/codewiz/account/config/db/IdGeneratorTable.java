package com.xiaohongshu.codewiz.account.config.db;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * @ClassName IdGeneratorTable
 * @Description
 * @Date 2025/5/22 20:11
 * <AUTHOR>
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface IdGeneratorTable {

    String value(); // 表名

}