package com.xiaohongshu.codewiz.account.outer.stopone;

import com.alibaba.fastjson.JSON;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.xiaohongshu.codewiz.account.config.StopOneRequestConfig;
import com.xiaohongshu.force.lobot.thrift.api.LobotThriftService;
import com.xiaohongshu.force.lobot.thrift.dto.PageParam;
import com.xiaohongshu.force.lobot.thrift.dto.RuleVerificationResult;
import com.xiaohongshu.force.lobot.thrift.dto.req.AddAccountRequest;
import com.xiaohongshu.force.lobot.thrift.dto.req.PageAccountRequest;
import com.xiaohongshu.force.lobot.thrift.dto.req.RemoveAccountRequest;
import com.xiaohongshu.force.lobot.thrift.dto.req.RulesVerificationRequest;
import com.xiaohongshu.force.lobot.thrift.dto.res.PageAccountResponse;
import com.xiaohongshu.force.lobot.thrift.dto.res.Response;
import com.xiaohongshu.force.lobot.thrift.dto.res.RulesVerificationResponse;
import com.xiaohongshu.infra.rpc.base.Context;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * @ClassName StopOneService
 * @Description
 * @Date 2025/5/19 16:26
 * <AUTHOR>
 */
@Service
@Slf4j
public class AccountAssignedService {

    @ApolloJsonValue("${codex.account.stopOne.request}")
    private Map<String, StopOneRequestConfig> stopOneRequestConfigMap;

    @Resource
    private LobotThriftService.Iface lobotPubService;

    /**
     * 增加AD域账号关联
     *
     * @param userEmail 用户邮箱
     */
    public Response addAccount(String serviceName, String userEmail) {
        long startTime = System.currentTimeMillis();
        AddAccountRequest addAccountRequest = null;
        Response response = null;
        try {
            addAccountRequest = buildAddAccountRequest(serviceName, userEmail);
            response = lobotPubService.addAccount(new Context(), addAccountRequest);
            return response;
        } catch (TException e) {
            log.error("[增加AD域账号关联]调用StopOne服务失败，costMsTime: {}, userEmail: {}, 出现异常：", System.currentTimeMillis() - startTime, userEmail, e);
            return null;
        } finally {
            log.info("[增加AD域账号关联]执行完毕, costMsTime: {}, req: {}, resp：{}", System.currentTimeMillis() - startTime, JSON.toJSONString(addAccountRequest), JSON.toJSONString(response));
        }
    }

    /**
     * 解除AD域账号关联
     *
     * @param userEmail 用户邮箱
     */
    public Response removeAccount(String serviceName, String userEmail) {
        long startTime = System.currentTimeMillis();
        RemoveAccountRequest removeAccountRequest = null;
        Response response = null;
        try {
            removeAccountRequest = buildRemoveAccountRequest(serviceName, userEmail);
            response = lobotPubService.removeAccount(new Context(), removeAccountRequest);
            return response;
        } catch (TException e) {
            log.error("[解除AD域账号关联]调用StopOne服务失败，出现异常：", e);
            return null;
        } finally {
            log.info("[解除AD域账号关联]执行完毕, costMsTime: {}, req: {}, resp：{}", System.currentTimeMillis() - startTime, JSON.toJSONString(removeAccountRequest), JSON.toJSONString(response));
        }
    }

    /**
     * 查询账号是否已分配当前许可
     *
     * @param userEmail 用户邮箱
     * @return 是否已分配
     */
    public Boolean accountHasAssigned(String serviceName, String userEmail) {
        try {
            RuleVerificationResult ruleVerificationResult = checkAccountHasAssigned(serviceName, userEmail);
            if (ruleVerificationResult != null && ruleVerificationResult.isRuleParseResult()) {
                // 规则校验结果不为null，说明账号已分配
                log.info("[查询账号是否已分配] 账号已分配 userEmail: {}, serviceName: {}", userEmail, serviceName);
                return true;
            } else {
                // 规则校验结果为null，说明账号未分配
                log.info("[查询账号是否已分配] 账号未分配 userEmail: {}, serviceName: {}", userEmail, serviceName);
                return false;
            }
        } catch (Exception e) {
            log.error("[查询账号是否已分配]调用StopOne服务失败 userEmail : {}, 出现异常：", userEmail, e);
            return false;
        }
    }

    /**
     * 查询账号是否已分配当前许可
     *
     * @param userEmail 用户邮箱
     * @return 是否已分配
     */
    public RuleVerificationResult checkAccountHasAssigned(String serviceName, String userEmail) {
        long startTime = System.currentTimeMillis();
        RulesVerificationRequest rulesVerificationRequest = null;
        RulesVerificationResponse rulesVerificationResult = null;
        try {
            rulesVerificationRequest = buildRulesVerificationRequest(serviceName, userEmail);
            rulesVerificationResult = lobotPubService.getRulesVerificationResult(new Context(), rulesVerificationRequest);
            log.info("[查询账号是否已分配] req: {}, resp：{}", JSON.toJSONString(rulesVerificationRequest), JSON.toJSONString(rulesVerificationResult));
            return rulesVerificationResult == null ? null : rulesVerificationResult.getRuleVerificationResult();
        } catch (TException e) {
            log.error("[查询账号是否已分配]调用StopOne服务失败 userEmail : {}, 出现异常：", userEmail, e);
            return null;
        } finally {
            log.info("[查询账号是否已分配]执行完毕, costMsTime: {}, req: {}, resp：{}", System.currentTimeMillis() - startTime, JSON.toJSONString(rulesVerificationRequest), JSON.toJSONString(rulesVerificationResult));
        }
    }

    /**
     * 全量AD域账号关联查询
     *
     * @return 账号列表
     */
    public List<String> getAllPageAccountInfos(String serviceName) {
        long startTime = System.currentTimeMillis();
        PageAccountRequest pageAccountRequest = null;
        PageParam pageParam = null;
        PageAccountResponse pageAccountResponse = null;
        try {
            pageAccountRequest = buildPageAccountRequest(serviceName);
            pageParam = buildDefaultPageParam(serviceName);
            pageAccountResponse = lobotPubService.pageAccount(new Context(), pageAccountRequest, pageParam);
            log.info("[全量AD域账号关联查询] req: {}, {}; resp：{}", JSON.toJSONString(pageAccountRequest), JSON.toJSONString(pageParam), JSON.toJSONString(pageAccountResponse));
            return pageAccountResponse == null ? null : pageAccountResponse.getAccountList();
        } catch (TException e) {
            log.error("[全量AD域账号关联查询]调用StopOne服务失败，出现异常：", e);
            return null;
        } finally {
            log.info("[全量AD域账号关联查询]执行完毕, costMsTime: {}, req: {}, param: {}, resp：{}", System.currentTimeMillis() - startTime, JSON.toJSONString(pageAccountRequest), JSON.toJSONString(pageParam), JSON.toJSONString(pageAccountResponse));
        }
    }

    /**
     * 构建添加账号请求
     *
     * @param userEmail 用户邮箱
     * @return 添加账号请求对象
     */
    private AddAccountRequest buildAddAccountRequest(String serviceName, String userEmail) {
        StopOneRequestConfig stopOneRequestConfig = stopOneRequestConfigMap.get(serviceName);
        AddAccountRequest addAccountRequest = new AddAccountRequest();
        addAccountRequest.setAccountMail(userEmail);
        addAccountRequest.setServiceName(stopOneRequestConfig.getServiceName());
        addAccountRequest.setGroupId(stopOneRequestConfig.getGroupId());
        // 有效期开始时间，可以不传，开始结束时间不传则是永久有效
        return addAccountRequest;
    }

    /**
     * 构建删除账号请求
     *
     * @param userEmail 用户邮箱
     * @return 删除账号请求对象
     */
    public RemoveAccountRequest buildRemoveAccountRequest(String serviceName, String userEmail) {
        StopOneRequestConfig stopOneRequestConfig = stopOneRequestConfigMap.get(serviceName);
        RemoveAccountRequest removeAccountRequest = new RemoveAccountRequest();
        removeAccountRequest.setAccountMail(userEmail);
        removeAccountRequest.setServiceName(stopOneRequestConfig.getServiceName());
        removeAccountRequest.setGroupId(stopOneRequestConfig.getGroupId());
        return removeAccountRequest;
    }

    /**
     * 构建分页查询请求
     *
     * @return 分页查询请求对象
     */
    public PageAccountRequest buildPageAccountRequest(String serviceName) {
        StopOneRequestConfig stopOneRequestConfig = stopOneRequestConfigMap.get(serviceName);
        PageAccountRequest pageAccountRequest = new PageAccountRequest();
        pageAccountRequest.setServiceName(stopOneRequestConfig.getServiceName());
        pageAccountRequest.setGroupId(stopOneRequestConfig.getGroupId());
        return pageAccountRequest;
    }

    /**
     * 分页对象初始化，后期配置化
     *
     * @return 分页对象
     */
    public PageParam buildDefaultPageParam(String serviceName) {
        StopOneRequestConfig stopOneRequestConfig = stopOneRequestConfigMap.get(serviceName);
        PageParam pageParam = new PageParam();
        // 默认值最大量取
        pageParam.setPageNo(stopOneRequestConfig.getPageNo());
        pageParam.setPageSize(stopOneRequestConfig.getPageSize());
        return pageParam;
    }

    /**
     * 构建规则校验请求
     *
     * @param userEmail 用户邮箱
     * @return 规则校验请求对象
     */
    private RulesVerificationRequest buildRulesVerificationRequest(String serviceName, String userEmail) {
        StopOneRequestConfig stopOneRequestConfig = stopOneRequestConfigMap.get(serviceName);
        RulesVerificationRequest rulesVerificationRequest = new RulesVerificationRequest();
        rulesVerificationRequest.setAccountMail(userEmail);
        rulesVerificationRequest.setId(stopOneRequestConfig.getGroupId());
        return rulesVerificationRequest;
    }

}
