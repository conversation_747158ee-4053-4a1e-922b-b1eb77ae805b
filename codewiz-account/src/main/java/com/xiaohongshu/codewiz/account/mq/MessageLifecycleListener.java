package com.xiaohongshu.codewiz.account.mq;

import com.xiaohongshu.events.client.consumer.AbstractConsumer;
import com.xiaohongshu.events.client.producer.AbstractProducer;
import com.xiaohongshu.events.common.life.LifeCycleException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.SmartLifecycle;

import java.util.Collection;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * @ClassName MessageLifecycleListener
 * @Description
 * @Date 2025/5/20 12:00
 * <AUTHOR>
 */
@Slf4j
public class MessageLifecycleListener implements SmartLifecycle, ApplicationContextAware {

    private final AtomicBoolean started = new AtomicBoolean(false);

    private ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    @Override
    public void start() {
        if (started.compareAndSet(false, true)) {
            startup();
        }
    }

    @Override
    public void stop() {
        if (started.compareAndSet(true, false)) {
            shutdown();
        }
    }

    @Override
    public boolean isRunning() {
        return started.get();
    }

    private void startup() {
        log.info("开始启动消息生产者和消费者");
        // 启动消费者
        Optional.of(applicationContext.getBeansOfType(AbstractConsumer.class))
                .map(Map::values)
                .stream()
                .flatMap(Collection::stream)
                .forEach(this::startConsumer);

        // 启动生产者
        Optional.of(applicationContext.getBeansOfType(AbstractProducer.class))
                .map(Map::values)
                .stream()
                .flatMap(Collection::stream)
                .forEach(this::startProducer);
    }

    private void shutdown() {
        log.info("开始关闭消息生产者和消费者");
        if (Objects.isNull(applicationContext)) {
            return;
        }

        // 关闭生产者
        Optional.of(applicationContext.getBeansOfType(AbstractProducer.class))
                .map(Map::values)
                .stream()
                .flatMap(Collection::stream)
                .forEach(this::shutdownProducer);

        // 关闭消费者
        Optional.of(applicationContext.getBeansOfType(AbstractConsumer.class))
                .map(Map::values)
                .stream()
                .flatMap(Collection::stream)
                .forEach(this::shutdownConsumer);
    }

    private void startConsumer(AbstractConsumer consumer) {
        try {
            if (!consumer.isStarted()) {
                consumer.start();
            }
        } catch (LifeCycleException e) {
            log.error("启动消费者 topic: {}, group: {} 失败: {}", consumer.getTopic(), consumer.getGroup(), e.getMessage(), e);
        }
    }

    private void startProducer(AbstractProducer producer) {
        try {
            if (!producer.isStarted()) {
                producer.start();
            }
        } catch (LifeCycleException e) {
            log.error("启动生产者: [{}] 失败: {}", producer.getTopics(), e.getMessage(), e);
        }
    }

    private void shutdownConsumer(AbstractConsumer consumer) {
        try {
            if (consumer.isStarted()) {
                consumer.shutdown();
            }
        } catch (LifeCycleException e) {
            log.error("关闭消费者 topic: {}, group: {} 失败: {}", consumer.getTopic(), consumer.getGroup(), e.getMessage(), e);
        }
    }

    private void shutdownProducer(AbstractProducer producer) {
        try {
            if (producer.isStarted()) {
                producer.shutdown();
            }
        } catch (LifeCycleException e) {
            log.error("关闭生产者: [{}] 失败: {}", producer.getTopics(), e.getMessage(), e);
        }
    }
}
