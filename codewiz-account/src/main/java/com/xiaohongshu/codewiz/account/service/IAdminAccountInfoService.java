package com.xiaohongshu.codewiz.account.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xiaohongshu.codewiz.account.dto.AdminAccountQueryDTO;
import com.xiaohongshu.codewiz.account.dto.UserAccountQueryDTO;
import com.xiaohongshu.codewiz.account.po.AdminAccountInfo;
import com.xiaohongshu.codewiz.account.po.UserAccountInfo;

/**
 * @ClassName IAdminAccountInfoService
 * @Description
 * @Date 2025/5/16 20:23
 * <AUTHOR>
 */
public interface IAdminAccountInfoService extends IService<AdminAccountInfo> {

    Page<AdminAccountInfo> queryAdminAccounts(AdminAccountQueryDTO queryDTO);


}
