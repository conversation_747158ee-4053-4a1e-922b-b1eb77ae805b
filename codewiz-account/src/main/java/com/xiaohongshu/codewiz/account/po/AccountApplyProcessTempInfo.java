package com.xiaohongshu.codewiz.account.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.xiaohongshu.codewiz.account.config.db.IdGeneratorTable;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * @ClassName AccountApplyProcessTempInfo
 * @Description
 * @Date 2025/5/22 20:09
 * <AUTHOR>
 */
@Data
@TableName("t_codex_account_apply_process_temp")
@IdGeneratorTable("t_codex_account_apply_process_temp")
@Builder
public class AccountApplyProcessTempInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * username
     */
    private String username;

    /**
     * "channelUserId":"W15505", //员工账号才有，人事userID，注意：正编&实习 和 bpo不一样
     */
    private String userId;

    /**
     * 用户邮箱
     */
    private String userEmail;

    /**
     * 部门名称
     */
    private String departmentName;

    /**
     * 部门ID
     */
    private String departmentId;

    /**
     * 部门名称路径
     */
    private String departmentNamePath;

    /**
     * 部门名称路径ID
     */
    private String departmentNamePathId;

    /**
     * 服务名称
     */
    private String serviceName;

    /**
     * 申请单id = formNo
     */
    private String requestId;

    /**
     * 申请单类型
     */
    private String requestType;

    /**
     * 申请原因
     */
    private String requestReason;

    /**
     * 状态:1开通，0关闭
     */
    private Integer status;

    /**
     * 自定义
     */
    private Map<String, Object> custom;

    /**
     * 描述
     */
    private String description;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;


    // 关键非空字段判断
    public boolean isNotValid() {
        return StringUtils.isEmpty(this.serviceName) || StringUtils.isEmpty(this.userEmail);
    }
}
