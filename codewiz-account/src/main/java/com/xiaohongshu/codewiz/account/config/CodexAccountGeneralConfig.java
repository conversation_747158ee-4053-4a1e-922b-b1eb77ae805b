package com.xiaohongshu.codewiz.account.config;

import lombok.Data;

import java.util.List;

/**
 * @ClassName CodexAccountGeneralConfig
 * @Description
 * @Date 2025/5/26 17:23
 * <AUTHOR>
 */
@Data
public class CodexAccountGeneralConfig {

    /**
     * 服务列表
     * 例如：["cursor", "tongyi"]
     */
    private List<String> serviceList;

    /**
     * 最大席位数量
     */
    private Integer maxCount;

    /**
     * 申请开通流程的tag
     */
    private String applyOpenProcessTag;

    /**
     * 申请关闭流程的tag
     */
    private String applyCloseProcessTag;

    private Boolean openSwitch = false;

}
