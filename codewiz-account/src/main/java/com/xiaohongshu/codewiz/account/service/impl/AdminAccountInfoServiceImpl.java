package com.xiaohongshu.codewiz.account.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xiaohongshu.codewiz.account.config.db.CustomIdGenerator;
import com.xiaohongshu.codewiz.account.dto.AdminAccountQueryDTO;
import com.xiaohongshu.codewiz.account.dto.UserAccountQueryDTO;
import com.xiaohongshu.codewiz.account.mapper.AdminAccountInfoMapper;
import com.xiaohongshu.codewiz.account.po.AdminAccountInfo;
import com.xiaohongshu.codewiz.account.po.UserAccountInfo;
import com.xiaohongshu.codewiz.account.service.IAdminAccountInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * @ClassName AdminAccountInfoServiceImpl
 * @Description
 * @Date 2025/5/16 20:34
 * <AUTHOR>
 */
@Slf4j
@Service
public class AdminAccountInfoServiceImpl extends ServiceImpl<AdminAccountInfoMapper, AdminAccountInfo> implements IAdminAccountInfoService {


    @Resource
    private CustomIdGenerator customIdGenerator;

    /**
     * 根据用户邮箱、服务名称 查询用户账号信息
     *
     * @param serviceName 服务名称
     * @param userEmail   用户邮箱
     * @return 用户账号信息
     */
    public AdminAccountInfo getByServiceAndEmail(String serviceName, String userEmail) {
        return baseMapper.findByServiceAndEmail(serviceName, userEmail);
    }

    /**
     * 根据用户邮箱、服务名称 查询用户账号信息
     *
     * @param serviceName 服务名称
     * @param username    用户名
     * @return 用户账号信息
     */
    public AdminAccountInfo getByServiceAndName(String serviceName, String username) {
        return baseMapper.findByServiceAndName(serviceName, username);
    }

    /**
     * 根据服务查询管理员账号信息
     *
     * @param serviceName 服务 Cursor
     * @return 管理员账号信息
     */
    public List<AdminAccountInfo> getAllByService(String serviceName) {
        return baseMapper.findAllByService(serviceName);
    }

    /**
     * 根据服务查询管理员账号信息
     *
     * @param departmentId 部门id
     * @return 管理员账号信息
     */
    public List<AdminAccountInfo> getAllByDepartmentId(String departmentId) {
        return baseMapper.findAllByDepartmentId(departmentId);
    }

    /**
     * 新增管理员用户信息
     *
     * @param adminAccountInfo 管理员信息
     */
    public void addAdminAccountInfo(AdminAccountInfo adminAccountInfo) {
        Long number = customIdGenerator.nextId(adminAccountInfo);
        if (number != null) {
            adminAccountInfo.setId(number);
        }
        this.save(adminAccountInfo);
    }

    /**
     * 删除管理员信息
     *
     * @param adminAccountInfo 管理员信息
     */
    public Boolean delAdminAccountInfo(AdminAccountInfo adminAccountInfo) {
        return baseMapper.delAdminAccountInfo(adminAccountInfo);
    }

    /**
     * 更改管理员信息
     *
     * @param adminAccountInfo 管理员信息
     */
    public Boolean updateAdminAccountInfo(AdminAccountInfo adminAccountInfo) {
        return baseMapper.updateAdminAccountInfo(adminAccountInfo);
    }

    /**
     * 分页查询管理员账号信息
     *
     * @param adminAccountQueryDTO 查询条件
     * @return 分页结果
     */
    @Override
    public Page<AdminAccountInfo> queryAdminAccounts(AdminAccountQueryDTO adminAccountQueryDTO) {
        long startTime = System.currentTimeMillis();
        Page<AdminAccountInfo> result = null;
        try {
            // 创建分页对象
            Page<AdminAccountInfo> page = new Page<>(adminAccountQueryDTO.getPageNum(), adminAccountQueryDTO.getPageSize());

            // 构建查询条件
            LambdaQueryWrapper<AdminAccountInfo> wrapper = new LambdaQueryWrapper<>();

            // 1. 关键词模糊匹配（姓名或邮箱）
            if (!StringUtils.isEmpty(adminAccountQueryDTO.getKeyword())) {
                wrapper.and(w -> w.like(AdminAccountInfo::getUsername, adminAccountQueryDTO.getKeyword())
                        .or()
                        .like(AdminAccountInfo::getUserEmail, adminAccountQueryDTO.getKeyword()));
            }
            // 2. 服务名称过滤
            if (!StringUtils.isEmpty(adminAccountQueryDTO.getServiceName())) {
                wrapper.eq(AdminAccountInfo::getServiceName, adminAccountQueryDTO.getServiceName());
            }

            // 5. 排序处理
            if (!StringUtils.isEmpty(adminAccountQueryDTO.getSortField()) && !StringUtils.isEmpty(adminAccountQueryDTO.getSortDirection())) {
                boolean isAsc = "asc".equalsIgnoreCase(adminAccountQueryDTO.getSortDirection());

                // 根据不同的排序字段设置排序条件
                switch (adminAccountQueryDTO.getSortField().toLowerCase()) {
                    case "createtime":
                        wrapper.orderBy(true, isAsc, AdminAccountInfo::getCreateTime);
                        break;
                    case "updatetime":
                        wrapper.orderBy(true, isAsc, AdminAccountInfo::getUpdateTime);
                        break;
                    // 可以添加更多排序字段支持
                    default:
                        // 默认按开通时间排序
                        wrapper.orderByDesc(AdminAccountInfo::getCreateTime);
                }
            } else {
                // 默认按开通时间降序排列
                wrapper.orderByDesc(AdminAccountInfo::getCreateTime);
            }
            // 执行分页查询
            result = this.page(page, wrapper);
            return result;
        } catch (Exception e) {
            log.error("[用户表条件查询]，查询异常: {}", e.getMessage(), e);
            return null;
        } finally {
            log.info("[用户表条件查询]，查询耗时: {} ms, req：{}, resp：{}", System.currentTimeMillis() - startTime, JSON.toJSONString(adminAccountQueryDTO), JSON.toJSONString(result));
        }
    }


}
