{"applyType": "SMTP_EMAIL_CREATE", "owner_emails": ["wangshu.com"], "processEnd": false, "businessId": "wangx3841002", "smtp_email_item_details": [{"address": "tangzhshu.com", "display_name": "唐"}], "auditManRedName": "铸", "auditPhaseId": "1085148805", "jumpUrl": "", "wf_startTimeDisplay": "2025-05-21 20:18:38", "auditMan": "唐通)", "startUserId": "50026", "resourceUrl": "https://cadirectTool.html?pc=https%3ndow=true", "startUserName": "唐通)", "owner_uids": ["500226"], "smtp_email_passwords": "", "flowId": "WF8400", "taskInfoList": [{"currentNodeId": "Activitwd9s", "currentNodeName": "三责人", "userIdList": ["15385"]}], "formType": "XTYXCJ", "auditPhaseKey": "Activity_submit", "startUserEmail": "wan.com", "auditManEmail": "wangxiehu.com", "auditPhase": "发起人提交", "startUserRedName": "铸", "pre_apply_id": "2160ddee63583", "currentAuditUser": "5026", "flowLevel": 0, "wf_comment": "", "processOperate": "submit", "applyUrl": "https://epage%3Fcode%3DSMTP_EMAIL_CREATE&isCloseWindow=true", "auditTime": "2025-05-21 20:18:39", "currentTaskInfo": [{"taskId": "1085193409", "taskNodeKey": "Activityd9s", "taskNodeName": "三责人", "userIdList": ["154535385"]}], "wf_completeTimeDisplay": "2025-05-21 20:18:38", "myResource": "我的系统邮箱", "auditStatus": "IN_REVIEW", "applyTypeDesc": "系统邮箱创建", "formNo": "XTY210256327899", "comment": ""}