14:47:36  INFO -- [s-consumer-Poll-Message-3] c.x.c.a.mq.RedflowMessageProcessor                 : 收到redflow消息: {"applyType":"SMTP_EMAIL_CREATE","owner_emails":["<EMAIL>"],"processEnd":false,"businessId":"wangxietong2025052114413041005","smtp_email_item_details":[{"address":"<EMAIL>","display_name":"唐铸测试系统邮箱"}],"auditManRedName":"王贰","auditPhaseId":"1085058396560375809","jumpUrl":"","wf_startTimeDisplay":"2025-05-21 14:41:30","auditMan":"王贰(王浩)","startUserId":"500030226","resourceUrl":"https://calendar.xiaohongshu.com/redirectTool.html?pc=https%3A%2F%2Fits.devops.sit.xiaohongshu.com%2FmyResouce%3FtabKey%3Demail%26type%3Dsystem_mail&mobile=https%3A%2F%2Fits-m.sit.xiaohongshu.com%2Fmy-email%3FtabKey%3Dsystem_mail&isCloseWindow=true","startUserName":"唐铸(王勰通)","owner_uids":["500030226"],"smtp_email_passwords":"","flowId":"WF128382","taskInfoList":[{"currentNodeId":"Activity_1qtvpor","currentNodeName":"邮箱运维管理员","userIdList":["500003089","500013046"]}],"formType":"XTYXCJ","auditPhaseKey":"Activity_1l7wd9s","startUserEmail":"<EMAIL>","auditManEmail":"<EMAIL>","auditPhase":"三级部门负责人","startUserRedName":"唐铸","pre_apply_id":"830f915f-10e4-4fa7-ba39-51d3450bb758","currentAuditUser":"*********","flowLevel":0,"wf_comment":"","processOperate":"submit","applyUrl":"https://calendar.xiaohongshu.com/redirectTool.html?pc=https%3A%2F%2Fits.devops.sit.xiaohongshu.com%2Fhomepage%3FapplyType%3DSMTP_EMAIL_CREATE&mobile=https%3A%2F%2Fits-m.sit.xiaohongshu.com%2Fhomepage%3Fcode%3DSMTP_EMAIL_CREATE&isCloseWindow=true","auditTime":"2025-05-21 14:41:31","currentTaskInfo":[{"taskId":"1085058462595497985","taskNodeKey":"Activity_1qtvpor","taskNodeName":"邮箱运维管理员","userIdList":["500003089","500013046"]}],"wf_completeTimeDisplay":"2025-05-21 14:41:30","myResource":"我的系统邮箱","auditStatus":"IN_REVIEW","applyTypeDesc":"系统邮箱创建","formNo":"XTYXCJ202505210256321878","comment":"管理员【唐铸】操作【管理员运维通过】：管理员【唐铸(王勰通)】后台运维操作"}
14:47:36  INFO -- [s-consumer-Poll-Message-4] c.x.c.a.mq.RedflowMessageProcessor                 : 收到redflow消息: {"applyType":"SMTP_EMAIL_CREATE","owner_emails":["<EMAIL>"],"processEnd":false,"businessId":"wangxietong2025052114413041005","smtp_email_item_details":[{"address":"<EMAIL>","display_name":"唐铸测试系统邮箱"}],"auditManRedName":"小坡","auditPhaseId":"1085058462595497985","jumpUrl":"","wf_startTimeDisplay":"2025-05-21 14:41:30","auditMan":"小坡(徐连杰)","startUserId":"500030226","resourceUrl":"https://calendar.xiaohongshu.com/redirectTool.html?pc=https%3A%2F%2Fits.devops.sit.xiaohongshu.com%2FmyResouce%3FtabKey%3Demail%26type%3Dsystem_mail&mobile=https%3A%2F%2Fits-m.sit.xiaohongshu.com%2Fmy-email%3FtabKey%3Dsystem_mail&isCloseWindow=true","startUserName":"唐铸(王勰通)","owner_uids":["500030226"],"smtp_email_passwords":"","flowId":"WF128382","taskInfoList":[{"currentNodeId":"EMAILXTYXCJ_SYSTEM_PROCESS","currentNodeName":"系统处理","userIdList":["WORKFLOW_SYSTEM_USER"]}],"formType":"XTYXCJ","auditPhaseKey":"Activity_1qtvpor","startUserEmail":"<EMAIL>","auditManEmail":"<EMAIL>","auditPhase":"邮箱运维管理员","startUserRedName":"唐铸","pre_apply_id":"830f915f-10e4-4fa7-ba39-51d3450bb758","currentAuditUser":"500013046","flowLevel":0,"wf_comment":"","processOperate":"submit","applyUrl":"https://calendar.xiaohongshu.com/redirectTool.html?pc=https%3A%2F%2Fits.devops.sit.xiaohongshu.com%2Fhomepage%3FapplyType%3DSMTP_EMAIL_CREATE&mobile=https%3A%2F%2Fits-m.sit.xiaohongshu.com%2Fhomepage%3Fcode%3DSMTP_EMAIL_CREATE&isCloseWindow=true","auditTime":"2025-05-21 14:41:31","currentTaskInfo":[{"taskId":"1085058925351038977","taskNodeKey":"EMAILXTYXCJ_SYSTEM_PROCESS","taskNodeName":"系统处理","userIdList":["WORKFLOW_SYSTEM_USER"]}],"wf_completeTimeDisplay":"2025-05-21 14:41:30","myResource":"我的系统邮箱","auditStatus":"IN_REVIEW","applyTypeDesc":"系统邮箱创建","formNo":"XTYXCJ202505210256321878","comment":"管理员【唐铸】操作【管理员运维通过】：管理员【唐铸(王勰通)】后台运维操作"}
14:47:36  INFO -- [s-consumer-Poll-Message-2] c.x.c.a.mq.RedflowMessageProcessor                 : 收到redflow消息: {"applyType":"SMTP_EMAIL_CREATE","owner_emails":["<EMAIL>"],"processEnd":true,"businessId":"wangxietong2025052114413041005","smtp_email_item_details":[{"address":"<EMAIL>","display_name":"唐铸测试系统邮箱"}],"auditPhaseId":"1085058925351038977","jumpUrl":"","wf_startTimeDisplay":"2025-05-21 14:41:30","auditMan":"系统审批","startUserId":"500030226","resourceUrl":"https://calendar.xiaohongshu.com/redirectTool.html?pc=https%3A%2F%2Fits.devops.sit.xiaohongshu.com%2FmyResouce%3FtabKey%3Demail%26type%3Dsystem_mail&mobile=https%3A%2F%2Fits-m.sit.xiaohongshu.com%2Fmy-email%3FtabKey%3Dsystem_mail&isCloseWindow=true","startUserName":"唐铸(王勰通)","owner_uids":["500030226"],"smtp_email_passwords":"{\"<EMAIL>\":\"clrcVFro9ZVhlDT3Q4Y4Kw==\"}","flowId":"WF128382","formType":"XTYXCJ","auditPhaseKey":"EMAILXTYXCJ_SYSTEM_PROCESS","startUserEmail":"<EMAIL>","auditPhase":"系统处理","startUserRedName":"唐铸","pre_apply_id":"830f915f-10e4-4fa7-ba39-51d3450bb758","currentAuditUser":"WORKFLOW_SYSTEM_USER","flowLevel":0,"wf_comment":"","processOperate":"processEnd","applyUrl":"https://calendar.xiaohongshu.com/redirectTool.html?pc=https%3A%2F%2Fits.devops.sit.xiaohongshu.com%2Fhomepage%3FapplyType%3DSMTP_EMAIL_CREATE&mobile=https%3A%2F%2Fits-m.sit.xiaohongshu.com%2Fhomepage%3Fcode%3DSMTP_EMAIL_CREATE&isCloseWindow=true","auditTime":"2025-05-21 14:41:31","wf_formDataUpdateTimestamp":1747809819962,"wf_completeTimeDisplay":"2025-05-21 14:41:30","myResource":"我的系统邮箱","auditStatus":"AUDIT_PASS","applyTypeDesc":"系统邮箱创建","formNo":"XTYXCJ202505210256321878","comment":""}
14:47:36  INFO -- [s-consumer-Poll-Message-5] c.x.c.a.mq.RedflowMessageProcessor                 : 收到redflow消息: {"applyType":"SMTP_EMAIL_CREATE","owner_emails":["<EMAIL>"],"processEnd":false,"businessId":"wangxietong2025052114413041005","smtp_email_item_details":[{"address":"<EMAIL>","display_name":"唐铸测试系统邮箱"}],"auditManRedName":"唐铸","auditPhaseId":"1085058395507605509","jumpUrl":"","wf_startTimeDisplay":"2025-05-21 14:41:30","auditMan":"唐铸(王勰通)","startUserId":"500030226","resourceUrl":"https://calendar.xiaohongshu.com/redirectTool.html?pc=https%3A%2F%2Fits.devops.sit.xiaohongshu.com%2FmyResouce%3FtabKey%3Demail%26type%3Dsystem_mail&mobile=https%3A%2F%2Fits-m.sit.xiaohongshu.com%2Fmy-email%3FtabKey%3Dsystem_mail&isCloseWindow=true","startUserName":"唐铸(王勰通)","owner_uids":["500030226"],"smtp_email_passwords":"","flowId":"WF128382","taskInfoList":[{"currentNodeId":"Activity_1l7wd9s","currentNodeName":"三级部门负责人","userIdList":["*********"]}],"formType":"XTYXCJ","auditPhaseKey":"Activity_submit","startUserEmail":"<EMAIL>","auditManEmail":"<EMAIL>","auditPhase":"发起人提交","startUserRedName":"唐铸","pre_apply_id":"830f915f-10e4-4fa7-ba39-51d3450bb758","currentAuditUser":"500030226","flowLevel":0,"wf_comment":"","processOperate":"submit","applyUrl":"https://calendar.xiaohongshu.com/redirectTool.html?pc=https%3A%2F%2Fits.devops.sit.xiaohongshu.com%2Fhomepage%3FapplyType%3DSMTP_EMAIL_CREATE&mobile=https%3A%2F%2Fits-m.sit.xiaohongshu.com%2Fhomepage%3Fcode%3DSMTP_EMAIL_CREATE&isCloseWindow=true","auditTime":"2025-05-21 14:41:31","currentTaskInfo":[{"taskId":"1085058396560375809","taskNodeKey":"Activity_1l7wd9s","taskNodeName":"三级部门负责人","userIdList":["*********"]}],"wf_completeTimeDisplay":"2025-05-21 14:41:30","myResource":"我的系统邮箱","auditStatus":"IN_REVIEW","applyTypeDesc":"系统邮箱创建","formNo":"XTYXCJ202505210256321878","comment":""}

14:47:35  WARN -- [e-consumer-Poll-Message-2] events                                             : pullMessage exception, consumer:plo-account-codex-account-departure-consumer topic:plo-account node:Node{ip='***********', port=9713}, err count 1

org.apache.thrift.transport.TTransportException: null
	at org.apache.thrift.transport.TIOStreamTransport.read(TIOStreamTransport.java:132)
	at org.apache.thrift.transport.TTransport.readAll(TTransport.java:86)
	at org.apache.thrift.transport.TFramedTransport.readFrame(TFramedTransport.java:132)
	at org.apache.thrift.transport.TFramedTransport.read(TFramedTransport.java:100)
	at org.apache.thrift.transport.TTransport.readAll(TTransport.java:86)
	at org.apache.thrift.protocol.TCompactProtocol.readByte(TCompactProtocol.java:637)
	at org.apache.thrift.protocol.TCompactProtocol.readMessageBegin(TCompactProtocol.java:505)
	at org.apache.thrift.TServiceClient.receiveBase(TServiceClient.java:77)
	at com.xiaohongshu.events.common.thrift.consumer.ConsumerService$Client.recv_pull(ConsumerService.java:81)
	at com.xiaohongshu.events.common.thrift.consumer.ConsumerService$Client.pull(ConsumerService.java:68)
	at com.xiaohongshu.events.client.consumer.EventsConsumerImpl.doPullMessage(EventsConsumerImpl.java:133)
	at com.xiaohongshu.events.client.consumer.EventsConsumerImpl.doPullMessage(EventsConsumerImpl.java:41)
	at com.xiaohongshu.events.client.consumer.BaseEventsConsumer.pullMessage(BaseEventsConsumer.java:168)
	at com.xiaohongshu.events.client.consumer.BaseEventsConsumer.start(BaseEventsConsumer.java:133)
	at com.xiaohongshu.events.client.consumer.AbstractConsumer.lambda$createConsumer$2(AbstractConsumer.java:135)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:834)

14:47:35  WARN -- [s-consumer-Poll-Message-3] events                                             : pullMessage exception, consumer:oasis_form-notice-codex-account-approval-process-consumer topic:oasis_form-notice node:Node{ip='***********', port=9713}, err count 0

org.apache.thrift.transport.TTransportException: null
	at org.apache.thrift.transport.TIOStreamTransport.read(TIOStreamTransport.java:132)
	at org.apache.thrift.transport.TTransport.readAll(TTransport.java:86)
	at org.apache.thrift.transport.TFramedTransport.readFrame(TFramedTransport.java:132)
	at org.apache.thrift.transport.TFramedTransport.read(TFramedTransport.java:100)
	at org.apache.thrift.transport.TTransport.readAll(TTransport.java:86)
	at org.apache.thrift.protocol.TCompactProtocol.readByte(TCompactProtocol.java:637)
	at org.apache.thrift.protocol.TCompactProtocol.readMessageBegin(TCompactProtocol.java:505)
	at org.apache.thrift.TServiceClient.receiveBase(TServiceClient.java:77)
	at com.xiaohongshu.events.common.thrift.consumer.ConsumerService$Client.recv_pull(ConsumerService.java:81)
	at com.xiaohongshu.events.common.thrift.consumer.ConsumerService$Client.pull(ConsumerService.java:68)
	at com.xiaohongshu.events.client.consumer.EventsConsumerImpl.doPullMessage(EventsConsumerImpl.java:133)
	at com.xiaohongshu.events.client.consumer.EventsConsumerImpl.doPullMessage(EventsConsumerImpl.java:41)
	at com.xiaohongshu.events.client.consumer.BaseEventsConsumer.pullMessage(BaseEventsConsumer.java:168)
	at com.xiaohongshu.events.client.consumer.BaseEventsConsumer.start(BaseEventsConsumer.java:133)
	at com.xiaohongshu.events.client.consumer.AbstractConsumer.lambda$createConsumer$2(AbstractConsumer.java:135)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:834)

14:47:35  WARN -- [e-consumer-Poll-Message-4] events                                             : pullMessage exception, consumer:plo-account-codex-account-departure-consumer topic:plo-account node:Node{ip='***********', port=9713}, err count 0


