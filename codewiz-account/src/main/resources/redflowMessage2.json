21:03:53  INFO -- [CDS-Subscriber-Executor-3] c.x.infra.xds.cds.CDSSharedStubWrapper             : CDS receive [inbound] :
{"versionInfo":"2025-05-21T21:03:50+08:00/21294","typeUrl":"type.googleapis.com/envoy.config.cluster.v3.Cluster","nonce":"uN8suIMvioY\u003db1a28b6c-a18f-4bfb-8694-d4102fc7aa20"}
21:03:54  INFO -- [CDS-Subscriber-Executor-3] c.x.infra.xds.cds.CDSSharedStubWrapper             : CDS receive [outbound] :
{"versionInfo":"2025-05-21T21:03:50+08:00/21294","typeUrl":"type.googleapis.com/envoy.config.cluster.v3.Cluster","nonce":"uN8suIMvioY\u003de866c564-ff22-447e-be36-6f3c69600089"}
21:05:35  INFO -- [s-consumer-Poll-Message-3] c.x.c.a.mq.RedflowMessageProcessor                 : [RedFlow消息订阅]收到RedFlow原始消息: {"applyType":"SMTP_EMAIL_CREATE","owner_emails":["<EMAIL>"],"processEnd":false,"businessId":"wangxietong2025052121053441001","smtp_email_item_details":[{"address":"<EMAIL>","display_name":"测试系统邮箱2"}],"auditManRedName":"唐铸","auditPhaseId":"1085155046444318725","jumpUrl":"","wf_startTimeDisplay":"2025-05-21 21:05:34","auditMan":"唐铸(王勰通)","startUserId":"*********","resourceUrl":"https://calendar.xiaohongshu.com/redirectTool.html?pc=https%3A%2F%2Fits.devops.sit.xiaohongshu.com%2FmyResouce%3FtabKey%3Demail%26type%3Dsystem_mail&mobile=https%3A%2F%2Fits-m.sit.xiaohongshu.com%2Fmy-email%3FtabKey%3Dsystem_mail&isCloseWindow=true","startUserName":"唐铸(王勰通)","owner_uids":["*********"],"smtp_email_passwords":"","flowId":"WF128403","taskInfoList":[{"currentNodeId":"Activity_1l7wd9s","currentNodeName":"三级部门负责人","userIdList":["154535385"]}],"formType":"XTYXCJ","auditPhaseKey":"Activity_submit","startUserEmail":"<EMAIL>","auditManEmail":"<EMAIL>","auditPhase":"发起人提交","startUserRedName":"唐铸","pre_apply_id":"7a7f043e-0b2a-4df2-ad34-9a6391a3d01c","currentAuditUser":"*********","flowLevel":0,"wf_comment":"","processOperate":"submit","applyUrl":"https://calendar.xiaohongshu.com/redirectTool.html?pc=https%3A%2F%2Fits.devops.sit.xiaohongshu.com%2Fhomepage%3FapplyType%3DSMTP_EMAIL_CREATE&mobile=https%3A%2F%2Fits-m.sit.xiaohongshu.com%2Fhomepage%3Fcode%3DSMTP_EMAIL_CREATE&isCloseWindow=true","auditTime":"2025-05-21 21:05:35","currentTaskInfo":[{"taskId":"1085155047505477633","taskNodeKey":"Activity_1l7wd9s","taskNodeName":"三级部门负责人","userIdList":["154535385"]}],"wf_completeTimeDisplay":"2025-05-21 21:05:34","myResource":"我的系统邮箱","auditStatus":"IN_REVIEW","applyTypeDesc":"系统邮箱创建","formNo":"XTYXCJ202505210256328277","comment":""}
21:05:35  INFO -- [s-consumer-Poll-Message-3] c.x.c.a.mq.RedflowMessageProcessor                 : [RedFlow消息订阅]原始消息转换为对象数据: {"applyType":"SMTP_EMAIL_CREATE","applyTypeDesc":"系统邮箱创建","applyUrl":"https://calendar.xiaohongshu.com/redirectTool.html?pc=https%3A%2F%2Fits.devops.sit.xiaohongshu.com%2Fhomepage%3FapplyType%3DSMTP_EMAIL_CREATE&mobile=https%3A%2F%2Fits-m.sit.xiaohongshu.com%2Fhomepage%3Fcode%3DSMTP_EMAIL_CREATE&isCloseWindow=true","auditMan":"唐铸(王勰通)","auditManEmail":"<EMAIL>","auditManRedName":"唐铸","auditPhase":"发起人提交","auditPhaseId":"1085155046444318725","auditPhaseKey":"Activity_submit","auditStatus":"IN_REVIEW","auditTime":"2025-05-21T21:05:35","businessId":"wangxietong2025052121053441001","comment":"","currentAuditUser":"*********","currentTaskInfo":[{"taskId":"1085155047505477633","taskNodeKey":"Activity_1l7wd9s","taskNodeName":"三级部门负责人","userIdList":["154535385"]}],"flowId":"WF128403","flowLevel":0,"formNo":"XTYXCJ202505210256328277","formType":"XTYXCJ","jumpUrl":"","myResource":"我的系统邮箱","owner_emails":["<EMAIL>"],"owner_uids":["*********"],"preApplyId":"7a7f043e-0b2a-4df2-ad34-9a6391a3d01c","processEnd":false,"processOperate":"submit","resourceUrl":"https://calendar.xiaohongshu.com/redirectTool.html?pc=https%3A%2F%2Fits.devops.sit.xiaohongshu.com%2FmyResouce%3FtabKey%3Demail%26type%3Dsystem_mail&mobile=https%3A%2F%2Fits-m.sit.xiaohongshu.com%2Fmy-email%3FtabKey%3Dsystem_mail&isCloseWindow=true","smtpEmailItemDetails":[{"address":"<EMAIL>","displayName":"测试系统邮箱2"}],"smtpEmailPasswords":"","startUserEmail":"<EMAIL>","startUserId":"*********","startUserName":"唐铸(王勰通)","startUserRedName":"唐铸","taskInfoList":[{"currentNodeId":"Activity_1l7wd9s","currentNodeName":"三级部门负责人","userIdList":["154535385"]}],"wf_comment":"","workflowCompleteTimeDisplay":"2025-05-21T21:05:34","workflowStartTimeDisplay":"2025-05-21T21:05:34"}
21:05:45  INFO -- [s-consumer-Poll-Message-3] c.x.c.a.mq.RedflowMessageProcessor                 : [RedFlow消息订阅]收到RedFlow原始消息: {"applyType":"SMTP_EMAIL_CREATE","owner_emails":["<EMAIL>"],"processEnd":false,"businessId":"wangxietong2025052121053441001","smtp_email_item_details":[{"address":"<EMAIL>","display_name":"测试系统邮箱2"}],"auditManRedName":"王贰","auditPhaseId":"1085155047505477633","jumpUrl":"","wf_startTimeDisplay":"2025-05-21 21:05:34","auditMan":"王贰(王浩)","startUserId":"*********","resourceUrl":"https://calendar.xiaohongshu.com/redirectTool.html?pc=https%3A%2F%2Fits.devops.sit.xiaohongshu.com%2FmyResouce%3FtabKey%3Demail%26type%3Dsystem_mail&mobile=https%3A%2F%2Fits-m.sit.xiaohongshu.com%2Fmy-email%3FtabKey%3Dsystem_mail&isCloseWindow=true","startUserName":"唐铸(王勰通)","owner_uids":["*********"],"smtp_email_passwords":"","flowId":"WF128403","taskInfoList":[{"currentNodeId":"Activity_1qtvpor","currentNodeName":"邮箱运维管理员","userIdList":["500003089","500013046"]}],"formType":"XTYXCJ","auditPhaseKey":"Activity_1l7wd9s","startUserEmail":"<EMAIL>","auditManEmail":"<EMAIL>","auditPhase":"三级部门负责人","startUserRedName":"唐铸","pre_apply_id":"7a7f043e-0b2a-4df2-ad34-9a6391a3d01c","currentAuditUser":"154535385","flowLevel":0,"wf_comment":"","processOperate":"submit","applyUrl":"https://calendar.xiaohongshu.com/redirectTool.html?pc=https%3A%2F%2Fits.devops.sit.xiaohongshu.com%2Fhomepage%3FapplyType%3DSMTP_EMAIL_CREATE&mobile=https%3A%2F%2Fits-m.sit.xiaohongshu.com%2Fhomepage%3Fcode%3DSMTP_EMAIL_CREATE&isCloseWindow=true","auditTime":"2025-05-21 21:05:35","currentTaskInfo":[{"taskId":"1085155091440812033","taskNodeKey":"Activity_1qtvpor","taskNodeName":"邮箱运维管理员","userIdList":["500003089","500013046"]}],"wf_completeTimeDisplay":"2025-05-21 21:05:34","myResource":"我的系统邮箱","auditStatus":"IN_REVIEW","applyTypeDesc":"系统邮箱创建","formNo":"XTYXCJ202505210256328277","comment":"管理员【唐铸】操作【管理员运维通过】：管理员【唐铸(王勰通)】后台运维操作"}
21:05:45  INFO -- [s-consumer-Poll-Message-3] c.x.c.a.mq.RedflowMessageProcessor                 : [RedFlow消息订阅]原始消息转换为对象数据: {"applyType":"SMTP_EMAIL_CREATE","applyTypeDesc":"系统邮箱创建","applyUrl":"https://calendar.xiaohongshu.com/redirectTool.html?pc=https%3A%2F%2Fits.devops.sit.xiaohongshu.com%2Fhomepage%3FapplyType%3DSMTP_EMAIL_CREATE&mobile=https%3A%2F%2Fits-m.sit.xiaohongshu.com%2Fhomepage%3Fcode%3DSMTP_EMAIL_CREATE&isCloseWindow=true","auditMan":"王贰(王浩)","auditManEmail":"<EMAIL>","auditManRedName":"王贰","auditPhase":"三级部门负责人","auditPhaseId":"1085155047505477633","auditPhaseKey":"Activity_1l7wd9s","auditStatus":"IN_REVIEW","auditTime":"2025-05-21T21:05:35","businessId":"wangxietong2025052121053441001","comment":"管理员【唐铸】操作【管理员运维通过】：管理员【唐铸(王勰通)】后台运维操作","currentAuditUser":"154535385","currentTaskInfo":[{"taskId":"1085155091440812033","taskNodeKey":"Activity_1qtvpor","taskNodeName":"邮箱运维管理员","userIdList":["500003089","500013046"]}],"flowId":"WF128403","flowLevel":0,"formNo":"XTYXCJ202505210256328277","formType":"XTYXCJ","jumpUrl":"","myResource":"我的系统邮箱","owner_emails":["<EMAIL>"],"owner_uids":["*********"],"preApplyId":"7a7f043e-0b2a-4df2-ad34-9a6391a3d01c","processEnd":false,"processOperate":"submit","resourceUrl":"https://calendar.xiaohongshu.com/redirectTool.html?pc=https%3A%2F%2Fits.devops.sit.xiaohongshu.com%2FmyResouce%3FtabKey%3Demail%26type%3Dsystem_mail&mobile=https%3A%2F%2Fits-m.sit.xiaohongshu.com%2Fmy-email%3FtabKey%3Dsystem_mail&isCloseWindow=true","smtpEmailItemDetails":[{"address":"<EMAIL>","displayName":"测试系统邮箱2"}],"smtpEmailPasswords":"","startUserEmail":"<EMAIL>","startUserId":"*********","startUserName":"唐铸(王勰通)","startUserRedName":"唐铸","taskInfoList":[{"currentNodeId":"Activity_1qtvpor","currentNodeName":"邮箱运维管理员","userIdList":["500003089","500013046"]}],"wf_comment":"","workflowCompleteTimeDisplay":"2025-05-21T21:05:34","workflowStartTimeDisplay":"2025-05-21T21:05:34"}
21:05:53  INFO -- [s-consumer-Poll-Message-6] c.x.c.a.mq.RedflowMessageProcessor                 : [RedFlow消息订阅]收到RedFlow原始消息: {"applyType":"SMTP_EMAIL_CREATE","owner_emails":["<EMAIL>"],"processEnd":false,"businessId":"wangxietong2025052121053441001","smtp_email_item_details":[{"address":"<EMAIL>","display_name":"测试系统邮箱2"}],"auditManRedName":"小坡","auditPhaseId":"1085155091440812033","jumpUrl":"","wf_startTimeDisplay":"2025-05-21 21:05:34","auditMan":"小坡(徐连杰)","startUserId":"*********","resourceUrl":"https://calendar.xiaohongshu.com/redirectTool.html?pc=https%3A%2F%2Fits.devops.sit.xiaohongshu.com%2FmyResouce%3FtabKey%3Demail%26type%3Dsystem_mail&mobile=https%3A%2F%2Fits-m.sit.xiaohongshu.com%2Fmy-email%3FtabKey%3Dsystem_mail&isCloseWindow=true","startUserName":"唐铸(王勰通)","owner_uids":["*********"],"smtp_email_passwords":"","flowId":"WF128403","taskInfoList":[{"currentNodeId":"EMAILXTYXCJ_SYSTEM_PROCESS","currentNodeName":"系统处理","userIdList":["WORKFLOW_SYSTEM_USER"]}],"formType":"XTYXCJ","auditPhaseKey":"Activity_1qtvpor","startUserEmail":"<EMAIL>","auditManEmail":"<EMAIL>","auditPhase":"邮箱运维管理员","startUserRedName":"唐铸","pre_apply_id":"7a7f043e-0b2a-4df2-ad34-9a6391a3d01c","currentAuditUser":"500013046","flowLevel":0,"wf_comment":"","processOperate":"submit","applyUrl":"https://calendar.xiaohongshu.com/redirectTool.html?pc=https%3A%2F%2Fits.devops.sit.xiaohongshu.com%2Fhomepage%3FapplyType%3DSMTP_EMAIL_CREATE&mobile=https%3A%2F%2Fits-m.sit.xiaohongshu.com%2Fhomepage%3Fcode%3DSMTP_EMAIL_CREATE&isCloseWindow=true","auditTime":"2025-05-21 21:05:35","currentTaskInfo":[{"taskId":"1085155124309553153","taskNodeKey":"EMAILXTYXCJ_SYSTEM_PROCESS","taskNodeName":"系统处理","userIdList":["WORKFLOW_SYSTEM_USER"]}],"wf_completeTimeDisplay":"2025-05-21 21:05:34","myResource":"我的系统邮箱","auditStatus":"IN_REVIEW","applyTypeDesc":"系统邮箱创建","formNo":"XTYXCJ202505210256328277","comment":"管理员【唐铸】操作【管理员运维通过】：管理员【唐铸(王勰通)】后台运维操作"}
21:05:53  INFO -- [s-consumer-Poll-Message-6] c.x.c.a.mq.RedflowMessageProcessor                 : [RedFlow消息订阅]原始消息转换为对象数据: {"applyType":"SMTP_EMAIL_CREATE","applyTypeDesc":"系统邮箱创建","applyUrl":"https://calendar.xiaohongshu.com/redirectTool.html?pc=https%3A%2F%2Fits.devops.sit.xiaohongshu.com%2Fhomepage%3FapplyType%3DSMTP_EMAIL_CREATE&mobile=https%3A%2F%2Fits-m.sit.xiaohongshu.com%2Fhomepage%3Fcode%3DSMTP_EMAIL_CREATE&isCloseWindow=true","auditMan":"小坡(徐连杰)","auditManEmail":"<EMAIL>","auditManRedName":"小坡","auditPhase":"邮箱运维管理员","auditPhaseId":"1085155091440812033","auditPhaseKey":"Activity_1qtvpor","auditStatus":"IN_REVIEW","auditTime":"2025-05-21T21:05:35","businessId":"wangxietong2025052121053441001","comment":"管理员【唐铸】操作【管理员运维通过】：管理员【唐铸(王勰通)】后台运维操作","currentAuditUser":"500013046","currentTaskInfo":[{"taskId":"1085155124309553153","taskNodeKey":"EMAILXTYXCJ_SYSTEM_PROCESS","taskNodeName":"系统处理","userIdList":["WORKFLOW_SYSTEM_USER"]}],"flowId":"WF128403","flowLevel":0,"formNo":"XTYXCJ202505210256328277","formType":"XTYXCJ","jumpUrl":"","myResource":"我的系统邮箱","owner_emails":["<EMAIL>"],"owner_uids":["*********"],"preApplyId":"7a7f043e-0b2a-4df2-ad34-9a6391a3d01c","processEnd":false,"processOperate":"submit","resourceUrl":"https://calendar.xiaohongshu.com/redirectTool.html?pc=https%3A%2F%2Fits.devops.sit.xiaohongshu.com%2FmyResouce%3FtabKey%3Demail%26type%3Dsystem_mail&mobile=https%3A%2F%2Fits-m.sit.xiaohongshu.com%2Fmy-email%3FtabKey%3Dsystem_mail&isCloseWindow=true","smtpEmailItemDetails":[{"address":"<EMAIL>","displayName":"测试系统邮箱2"}],"smtpEmailPasswords":"","startUserEmail":"<EMAIL>","startUserId":"*********","startUserName":"唐铸(王勰通)","startUserRedName":"唐铸","taskInfoList":[{"currentNodeId":"EMAILXTYXCJ_SYSTEM_PROCESS","currentNodeName":"系统处理","userIdList":["WORKFLOW_SYSTEM_USER"]}],"wf_comment":"","workflowCompleteTimeDisplay":"2025-05-21T21:05:34","workflowStartTimeDisplay":"2025-05-21T21:05:34"}
21:05:58  INFO -- [s-consumer-Poll-Message-7] c.x.c.a.mq.RedflowMessageProcessor                 : [RedFlow消息订阅]收到RedFlow原始消息: {"applyType":"SMTP_EMAIL_CREATE","owner_emails":["<EMAIL>"],"processEnd":true,"businessId":"wangxietong2025052121053441001","smtp_email_item_details":[{"address":"<EMAIL>","display_name":"测试系统邮箱2"}],"auditPhaseId":"1085155124309553153","jumpUrl":"","wf_startTimeDisplay":"2025-05-21 21:05:34","auditMan":"系统审批","startUserId":"*********","resourceUrl":"https://calendar.xiaohongshu.com/redirectTool.html?pc=https%3A%2F%2Fits.devops.sit.xiaohongshu.com%2FmyResouce%3FtabKey%3Demail%26type%3Dsystem_mail&mobile=https%3A%2F%2Fits-m.sit.xiaohongshu.com%2Fmy-email%3FtabKey%3Dsystem_mail&isCloseWindow=true","startUserName":"唐铸(王勰通)","owner_uids":["*********"],"smtp_email_passwords":"{\"<EMAIL>\":\"41kmBHPY8cXCfzSo6hLXfg==\"}","flowId":"WF128403","formType":"XTYXCJ","auditPhaseKey":"EMAILXTYXCJ_SYSTEM_PROCESS","startUserEmail":"<EMAIL>","auditPhase":"系统处理","startUserRedName":"唐铸","pre_apply_id":"7a7f043e-0b2a-4df2-ad34-9a6391a3d01c","currentAuditUser":"WORKFLOW_SYSTEM_USER","flowLevel":0,"wf_comment":"","processOperate":"processEnd","applyUrl":"https://calendar.xiaohongshu.com/redirectTool.html?pc=https%3A%2F%2Fits.devops.sit.xiaohongshu.com%2Fhomepage%3FapplyType%3DSMTP_EMAIL_CREATE&mobile=https%3A%2F%2Fits-m.sit.xiaohongshu.com%2Fhomepage%3Fcode%3DSMTP_EMAIL_CREATE&isCloseWindow=true","auditTime":"2025-05-21 21:05:35","wf_formDataUpdateTimestamp":*************,"wf_completeTimeDisplay":"2025-05-21 21:05:34","myResource":"我的系统邮箱","auditStatus":"AUDIT_PASS","applyTypeDesc":"系统邮箱创建","formNo":"XTYXCJ202505210256328277","comment":""}
21:05:58 ERROR -- [s-consumer-Poll-Message-7] c.x.c.a.mq.RedflowMessageProcessor                 : [RedFlow消息订阅]处理消息出现异常

com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException: Unrecognized field "wf_formDataUpdateTimestamp" (class com.xiaohongshu.codewiz.account.bo.RedFlowProcessMessageBodyBO), not marked as ignorable (43 known properties: "resourceUrl", "applyType", "wf_completeTimeDisplay", "currentAuditUser", "startUserEmail", "taskInfoList", "processOperate", "myResource", "auditPhaseId", "member_emails", "auditManEmail", "formType", "auditPhaseKey", "auditPhase", "jumpUrl", "smtp_email_item_details", "owner_emails", "formNo", "pre_apply_id", "flowId", "processEnd", "auditStatus", "startUserRedName", "address", "flowLevel", "startUserName", "applyTypeDesc", "businessId", "auditManRedName", "auditMan", "startUserId", "wf_meDisplay", "remove_members", "remove_member_emails", "wf_startTimeDisplay", "currentTaskInfo", "wf_comment", "email_group_id", "applyUrl", "comment", "owner_uids", "auditTime", "smtp_email_passwords"])
at [Source: (String)"{"applyType":"SMTP_EMAIL_CREATE","owner_emails":["<EMAIL>"],"processEnd":true,"businessId":"wangxietong2025052121053441001","smtp_email_item_details":[{"address":"<EMAIL>","display_name":"测试系统邮箱2"}],"auditPhaseId":"1085155124309553153","jumpUrl":"","wf_startTimeDisplay":"2025-05-21 21:05:34","auditMan":"系统审批","startUserId":"*********","resourceUrl":"https://calendar.xiaohongshu.com/redirectTool.html?pc=https%3A%2F%2Fits.devops.sit.xiaohong"[truncated 1155 chars]; line: 1, column: 1485] (through reference chain: com.xiaohongshu.codewiz.account.bo.RedFlowProcessMessageBodyBO["wf_formDataUpdateTimestamp"])
at com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException.from(UnrecognizedPropertyException.java:61)
at com.fasterxml.jackson.databind.DeserializationContext.handleUnknownProperty(DeserializationContext.java:823)
at com.fasterxml.jackson.databind.deser.std.StdDeserializer.handleUnknownProperty(StdDeserializer.java:1153)
at com.fasterxml.jackson.databind.deser.BeanDeserializerBase.handleUnknownProperty(BeanDeserializerBase.java:1589)
at com.fasterxml.jackson.databind.deser.BeanDeserializerBase.handleUnknownVanilla(BeanDeserializerBase.java:1567)
at com.fasterxml.jackson.databind.deser.BeanDeserializer.vanillaDeserialize(BeanDeserializer.java:294)
at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserialize(BeanDeserializer.java:151)
at com.fasterxml.jackson.databind.ObjectMapper._readMapAndClose(ObjectMapper.java:4013)
at com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3004)
at com.xiaohongshu.codewiz.account.mq.RedflowMessageProcessor.process(RedflowMessageProcessor.java:36)
at com.xiaohongshu.events.client.consumer.EventsPushConsumer.lambda$wrapProcessor$0(EventsPushConsumer.java:35)
at com.xiaohongshu.events.client.consumer.EventsConsumerImpl.doProcessMessage(EventsConsumerImpl.java:270)
at com.xiaohongshu.events.client.consumer.EventsConsumerImpl.doProcessResponse(EventsConsumerImpl.java:210)
at com.xiaohongshu.events.client.consumer.EventsConsumerImpl.processResponse(EventsConsumerImpl.java:204)
at com.xiaohongshu.events.client.consumer.EventsConsumerImpl.processResponse(EventsConsumerImpl.java:41)
at com.xiaohongshu.events.client.consumer.BaseEventsConsumer.start(BaseEventsConsumer.java:140)
at com.xiaohongshu.events.client.consumer.AbstractConsumer.lambda$createConsumer$2(AbstractConsumer.java:135)
at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
at java.base/java.lang.Thread.run(Thread.java:834)

21:05:59  INFO -- [s-consumer-Poll-Message-2] c.x.c.a.mq.RedflowMessageProcessor                 : [RedFlow消息订阅]收到RedFlow原始消息: {"applyType":"SMTP_EMAIL_CREATE","owner_emails":["<EMAIL>"],"processEnd":true,"businessId":"wangxietong2025052121053441001","smtp_email_item_details":[{"address":"<EMAIL>","display_name":"测试系统邮箱2"}],"auditPhaseId":"1085155124309553153","jumpUrl":"","wf_startTimeDisplay":"2025-05-21 21:05:34","auditMan":"系统审批","startUserId":"*********","resourceUrl":"https://calendar.xiaohongshu.com/redirectTool.html?pc=https%3A%2F%2Fits.devops.sit.xiaohongshu.com%2FmyResouce%3FtabKey%3Demail%26type%3Dsystem_mail&mobile=https%3A%2F%2Fits-m.sit.xiaohongshu.com%2Fmy-email%3FtabKey%3Dsystem_mail&isCloseWindow=true","startUserName":"唐铸(王勰通)","owner_uids":["*********"],"smtp_email_passwords":"{\"<EMAIL>\":\"41kmBHPY8cXCfzSo6hLXfg==\"}","flowId":"WF128403","formType":"XTYXCJ","auditPhaseKey":"EMAILXTYXCJ_SYSTEM_PROCESS","startUserEmail":"<EMAIL>","auditPhase":"系统处理","startUserRedName":"唐铸","pre_apply_id":"7a7f043e-0b2a-4df2-ad34-9a6391a3d01c","currentAuditUser":"WORKFLOW_SYSTEM_USER","flowLevel":0,"wf_comment":"","processOperate":"processEnd","applyUrl":"https://calendar.xiaohongshu.com/redirectTool.html?pc=https%3A%2F%2Fits.devops.sit.xiaohongshu.com%2Fhomepage%3FapplyType%3DSMTP_EMAIL_CREATE&mobile=https%3A%2F%2Fits-m.sit.xiaohongshu.com%2Fhomepage%3Fcode%3DSMTP_EMAIL_CREATE&isCloseWindow=true","auditTime":"2025-05-21 21:05:35","wf_formDataUpdateTimestamp":*************,"wf_completeTimeDisplay":"2025-05-21 21:05:34","myResource":"我的系统邮箱","auditStatus":"AUDIT_PASS","applyTypeDesc":"系统邮箱创建","formNo":"XTYXCJ202505210256328277","comment":""}
21:05:59 ERROR -- [s-consumer-Poll-Message-2] c.x.c.a.mq.RedflowMessageProcessor                 : [RedFlow消息订阅]处理消息出现异常

com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException: Unrecognized field "wf_formDataUpdateTimestamp" (class com.xiaohongshu.codewiz.account.bo.RedFlowProcessMessageBodyBO), not marked as ignorable (43 known properties: "resourceUrl", "applyType", "wf_completeTimeDisplay", "currentAuditUser", "startUserEmail", "taskInfoList", "processOperate", "myResource", "auditPhaseId", "member_emails", "auditManEmail", "formType", "auditPhaseKey", "auditPhase", "jumpUrl", "smtp_email_item_details", "owner_emails", "formNo", "pre_apply_id", "flowId", "processEnd", "auditStatus", "startUserRedName", "address", "flowLevel", "startUserName", "applyTypeDesc", "businessId", "auditManRedName", "auditMan", "startUserId", "wf_meDisplay", "remove_members", "remove_member_emails", "wf_startTimeDisplay", "currentTaskInfo", "wf_comment", "email_group_id", "applyUrl", "comment", "owner_uids", "auditTime", "smtp_email_passwords"])
at [Source: (String)"{"applyType":"SMTP_EMAIL_CREATE","owner_emails":["<EMAIL>"],"processEnd":true,"businessId":"wangxietong2025052121053441001","smtp_email_item_details":[{"address":"<EMAIL>","display_name":"测试系统邮箱2"}],"auditPhaseId":"1085155124309553153","jumpUrl":"","wf_startTimeDisplay":"2025-05-21 21:05:34","auditMan":"系统审批","startUserId":"*********","resourceUrl":"https://calendar.xiaohongshu.com/redirectTool.html?pc=https%3A%2F%2Fits.devops.sit.xiaohong"[truncated 1155 chars]; line: 1, column: 1485] (through reference chain: com.xiaohongshu.codewiz.account.bo.RedFlowProcessMessageBodyBO["wf_formDataUpdateTimestamp"])
at com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException.from(UnrecognizedPropertyException.java:61)
at com.fasterxml.jackson.databind.DeserializationContext.handleUnknownProperty(DeserializationContext.java:823)
at com.fasterxml.jackson.databind.deser.std.StdDeserializer.handleUnknownProperty(StdDeserializer.java:1153)
at com.fasterxml.jackson.databind.deser.BeanDeserializerBase.handleUnknownProperty(BeanDeserializerBase.java:1589)
at com.fasterxml.jackson.databind.deser.BeanDeserializerBase.handleUnknownVanilla(BeanDeserializerBase.java:1567)
at com.fasterxml.jackson.databind.deser.BeanDeserializer.vanillaDeserialize(BeanDeserializer.java:294)
at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserialize(BeanDeserializer.java:151)
at com.fasterxml.jackson.databind.ObjectMapper._readMapAndClose(ObjectMapper.java:4013)
at com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3004)
at com.xiaohongshu.codewiz.account.mq.RedflowMessageProcessor.process(RedflowMessageProcessor.java:36)
at com.xiaohongshu.events.client.consumer.EventsPushConsumer.lambda$wrapProcessor$0(EventsPushConsumer.java:35)
at com.xiaohongshu.events.client.consumer.EventsConsumerImpl.doProcessMessage(EventsConsumerImpl.java:270)
at com.xiaohongshu.events.client.consumer.EventsConsumerImpl.doProcessResponse(EventsConsumerImpl.java:210)
at com.xiaohongshu.events.client.consumer.EventsConsumerImpl.processResponse(EventsConsumerImpl.java:204)
at com.xiaohongshu.events.client.consumer.EventsConsumerImpl.processResponse(EventsConsumerImpl.java:41)
at com.xiaohongshu.events.client.consumer.BaseEventsConsumer.start(BaseEventsConsumer.java:140)
at com.xiaohongshu.events.client.consumer.AbstractConsumer.lambda$createConsumer$2(AbstractConsumer.java:135)
at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
at java.base/java.lang.Thread.run(Thread.java:834)

21:06:00  INFO -- [s-consumer-Poll-Message-2] c.x.c.a.mq.RedflowMessageProcessor                 : [RedFlow消息订阅]收到RedFlow原始消息: {"applyType":"SMTP_EMAIL_CREATE","owner_emails":["<EMAIL>"],"processEnd":true,"businessId":"wangxietong2025052121053441001","smtp_email_item_details":[{"address":"<EMAIL>","display_name":"测试系统邮箱2"}],"auditPhaseId":"1085155124309553153","jumpUrl":"","wf_startTimeDisplay":"2025-05-21 21:05:34","auditMan":"系统审批","startUserId":"*********","resourceUrl":"https://calendar.xiaohongshu.com/redirectTool.html?pc=https%3A%2F%2Fits.devops.sit.xiaohongshu.com%2FmyResouce%3FtabKey%3Demail%26type%3Dsystem_mail&mobile=https%3A%2F%2Fits-m.sit.xiaohongshu.com%2Fmy-email%3FtabKey%3Dsystem_mail&isCloseWindow=true","startUserName":"唐铸(王勰通)","owner_uids":["*********"],"smtp_email_passwords":"{\"<EMAIL>\":\"41kmBHPY8cXCfzSo6hLXfg==\"}","flowId":"WF128403","formType":"XTYXCJ","auditPhaseKey":"EMAILXTYXCJ_SYSTEM_PROCESS","startUserEmail":"<EMAIL>","auditPhase":"系统处理","startUserRedName":"唐铸","pre_apply_id":"7a7f043e-0b2a-4df2-ad34-9a6391a3d01c","currentAuditUser":"WORKFLOW_SYSTEM_USER","flowLevel":0,"wf_comment":"","processOperate":"processEnd","applyUrl":"https://calendar.xiaohongshu.com/redirectTool.html?pc=https%3A%2F%2Fits.devops.sit.xiaohongshu.com%2Fhomepage%3FapplyType%3DSMTP_EMAIL_CREATE&mobile=https%3A%2F%2Fits-m.sit.xiaohongshu.com%2Fhomepage%3Fcode%3DSMTP_EMAIL_CREATE&isCloseWindow=true","auditTime":"2025-05-21 21:05:35","wf_formDataUpdateTimestamp":*************,"wf_completeTimeDisplay":"2025-05-21 21:05:34","myResource":"我的系统邮箱","auditStatus":"AUDIT_PASS","applyTypeDesc":"系统邮箱创建","formNo":"XTYXCJ202505210256328277","comment":""}
21:06:00 ERROR -- [s-consumer-Poll-Message-2] c.x.c.a.mq.RedflowMessageProcessor                 : [RedFlow消息订阅]处理消息出现异常

com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException: Unrecognized field "wf_formDataUpdateTimestamp" (class com.xiaohongshu.codewiz.account.bo.RedFlowProcessMessageBodyBO), not marked as ignorable (43 known properties: "resourceUrl", "applyType", "wf_completeTimeDisplay", "currentAuditUser", "startUserEmail", "taskInfoList", "processOperate", "myResource", "auditPhaseId", "member_emails", "auditManEmail", "formType", "auditPhaseKey", "auditPhase", "jumpUrl", "smtp_email_item_details", "owner_emails", "formNo", "pre_apply_id", "flowId", "processEnd", "auditStatus", "startUserRedName", "address", "flowLevel", "startUserName", "applyTypeDesc", "businessId", "auditManRedName", "auditMan", "startUserId", "wf_meDisplay", "remove_members", "remove_member_emails", "wf_startTimeDisplay", "currentTaskInfo", "wf_comment", "email_group_id", "applyUrl", "comment", "owner_uids", "auditTime", "smtp_email_passwords"])
at [Source: (String)"{"applyType":"SMTP_EMAIL_CREATE","owner_emails":["<EMAIL>"],"processEnd":true,"businessId":"wangxietong2025052121053441001","smtp_email_item_details":[{"address":"<EMAIL>","display_name":"测试系统邮箱2"}],"auditPhaseId":"1085155124309553153","jumpUrl":"","wf_startTimeDisplay":"2025-05-21 21:05:34","auditMan":"系统审批","startUserId":"*********","resourceUrl":"https://calendar.xiaohongshu.com/redirectTool.html?pc=https%3A%2F%2Fits.devops.sit.xiaohong"[truncated 1155 chars]; line: 1, column: 1485] (through reference chain: com.xiaohongshu.codewiz.account.bo.RedFlowProcessMessageBodyBO["wf_formDataUpdateTimestamp"])
at com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException.from(UnrecognizedPropertyException.java:61)
at com.fasterxml.jackson.databind.DeserializationContext.handleUnknownProperty(DeserializationContext.java:823)
at com.fasterxml.jackson.databind.deser.std.StdDeserializer.handleUnknownProperty(StdDeserializer.java:1153)
at com.fasterxml.jackson.databind.deser.BeanDeserializerBase.handleUnknownProperty(BeanDeserializerBase.java:1589)
at com.fasterxml.jackson.databind.deser.BeanDeserializerBase.handleUnknownVanilla(BeanDeserializerBase.java:1567)
at com.fasterxml.jackson.databind.deser.BeanDeserializer.vanillaDeserialize(BeanDeserializer.java:294)
at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserialize(BeanDeserializer.java:151)
at com.fasterxml.jackson.databind.ObjectMapper._readMapAndClose(ObjectMapper.java:4013)
at com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3004)
at com.xiaohongshu.codewiz.account.mq.RedflowMessageProcessor.process(RedflowMessageProcessor.java:36)
at com.xiaohongshu.events.client.consumer.EventsPushConsumer.lambda$wrapProcessor$0(EventsPushConsumer.java:35)
at com.xiaohongshu.events.client.consumer.EventsConsumerImpl.doProcessMessage(EventsConsumerImpl.java:270)
at com.xiaohongshu.events.client.consumer.EventsConsumerImpl.doProcessResponse(EventsConsumerImpl.java:210)
at com.xiaohongshu.events.client.consumer.EventsConsumerImpl.processResponse(EventsConsumerImpl.java:204)
at com.xiaohongshu.events.client.consumer.EventsConsumerImpl.processResponse(EventsConsumerImpl.java:41)
at com.xiaohongshu.events.client.consumer.BaseEventsConsumer.start(BaseEventsConsumer.java:140)
at com.xiaohongshu.events.client.consumer.AbstractConsumer.lambda$createConsumer$2(AbstractConsumer.java:135)
at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
at java.base/java.lang.Thread.run(Thread.java:834)

21:06:01  INFO -- [s-consumer-Poll-Message-3] c.x.c.a.mq.RedflowMessageProcessor                 : [RedFlow消息订阅]收到RedFlow原始消息: {"applyType":"SMTP_EMAIL_CREATE","owner_emails":["<EMAIL>"],"processEnd":true,"businessId":"wangxietong2025052121053441001","smtp_email_item_details":[{"address":"<EMAIL>","display_name":"测试系统邮箱2"}],"auditPhaseId":"1085155124309553153","jumpUrl":"","wf_startTimeDisplay":"2025-05-21 21:05:34","auditMan":"系统审批","startUserId":"*********","resourceUrl":"https://calendar.xiaohongshu.com/redirectTool.html?pc=https%3A%2F%2Fits.devops.sit.xiaohongshu.com%2FmyResouce%3FtabKey%3Demail%26type%3Dsystem_mail&mobile=https%3A%2F%2Fits-m.sit.xiaohongshu.com%2Fmy-email%3FtabKey%3Dsystem_mail&isCloseWindow=true","startUserName":"唐铸(王勰通)","owner_uids":["*********"],"smtp_email_passwords":"{\"<EMAIL>\":\"41kmBHPY8cXCfzSo6hLXfg==\"}","flowId":"WF128403","formType":"XTYXCJ","auditPhaseKey":"EMAILXTYXCJ_SYSTEM_PROCESS","startUserEmail":"<EMAIL>","auditPhase":"系统处理","startUserRedName":"唐铸","pre_apply_id":"7a7f043e-0b2a-4df2-ad34-9a6391a3d01c","currentAuditUser":"WORKFLOW_SYSTEM_USER","flowLevel":0,"wf_comment":"","processOperate":"processEnd","applyUrl":"https://calendar.xiaohongshu.com/redirectTool.html?pc=https%3A%2F%2Fits.devops.sit.xiaohongshu.com%2Fhomepage%3FapplyType%3DSMTP_EMAIL_CREATE&mobile=https%3A%2F%2Fits-m.sit.xiaohongshu.com%2Fhomepage%3Fcode%3DSMTP_EMAIL_CREATE&isCloseWindow=true","auditTime":"2025-05-21 21:05:35","wf_formDataUpdateTimestamp":*************,"wf_completeTimeDisplay":"2025-05-21 21:05:34","myResource":"我的系统邮箱","auditStatus":"AUDIT_PASS","applyTypeDesc":"系统邮箱创建","formNo":"XTYXCJ202505210256328277","comment":""}
21:06:01 ERROR -- [s-consumer-Poll-Message-3] c.x.c.a.mq.RedflowMessageProcessor                 : [RedFlow消息订阅]处理消息出现异常

com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException: Unrecognized field "wf_formDataUpdateTimestamp" (class com.xiaohongshu.codewiz.account.bo.RedFlowProcessMessageBodyBO), not marked as ignorable (43 known properties: "resourceUrl", "applyType", "wf_completeTimeDisplay", "currentAuditUser", "startUserEmail", "taskInfoList", "processOperate", "myResource", "auditPhaseId", "member_emails", "auditManEmail", "formType", "auditPhaseKey", "auditPhase", "jumpUrl", "smtp_email_item_details", "owner_emails", "formNo", "pre_apply_id", "flowId", "processEnd", "auditStatus", "startUserRedName", "address", "flowLevel", "startUserName", "applyTypeDesc", "businessId", "auditManRedName", "auditMan", "startUserId", "wf_meDisplay", "remove_members", "remove_member_emails", "wf_startTimeDisplay", "currentTaskInfo", "wf_comment", "email_group_id", "applyUrl", "comment", "owner_uids", "auditTime", "smtp_email_passwords"])
at [Source: (String)"{"applyType":"SMTP_EMAIL_CREATE","owner_emails":["<EMAIL>"],"processEnd":true,"businessId":"wangxietong2025052121053441001","smtp_email_item_details":[{"address":"<EMAIL>","display_name":"测试系统邮箱2"}],"auditPhaseId":"1085155124309553153","jumpUrl":"","wf_startTimeDisplay":"2025-05-21 21:05:34","auditMan":"系统审批","startUserId":"*********","resourceUrl":"https://calendar.xiaohongshu.com/redirectTool.html?pc=https%3A%2F%2Fits.devops.sit.xiaohong"[truncated 1155 chars]; line: 1, column: 1485] (through reference chain: com.xiaohongshu.codewiz.account.bo.RedFlowProcessMessageBodyBO["wf_formDataUpdateTimestamp"])
at com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException.from(UnrecognizedPropertyException.java:61)
at com.fasterxml.jackson.databind.DeserializationContext.handleUnknownProperty(DeserializationContext.java:823)
at com.fasterxml.jackson.databind.deser.std.StdDeserializer.handleUnknownProperty(StdDeserializer.java:1153)
at com.fasterxml.jackson.databind.deser.BeanDeserializerBase.handleUnknownProperty(BeanDeserializerBase.java:1589)
at com.fasterxml.jackson.databind.deser.BeanDeserializerBase.handleUnknownVanilla(BeanDeserializerBase.java:1567)
at com.fasterxml.jackson.databind.deser.BeanDeserializer.vanillaDeserialize(BeanDeserializer.java:294)
at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserialize(BeanDeserializer.java:151)
at com.fasterxml.jackson.databind.ObjectMapper._readMapAndClose(ObjectMapper.java:4013)
at com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3004)
at com.xiaohongshu.codewiz.account.mq.RedflowMessageProcessor.process(RedflowMessageProcessor.java:36)
at com.xiaohongshu.events.client.consumer.EventsPushConsumer.lambda$wrapProcessor$0(EventsPushConsumer.java:35)
at com.xiaohongshu.events.client.consumer.EventsConsumerImpl.doProcessMessage(EventsConsumerImpl.java:270)
at com.xiaohongshu.events.client.consumer.EventsConsumerImpl.doProcessResponse(EventsConsumerImpl.java:210)
at com.xiaohongshu.events.client.consumer.EventsConsumerImpl.processResponse(EventsConsumerImpl.java:204)
at com.xiaohongshu.events.client.consumer.EventsConsumerImpl.processResponse(EventsConsumerImpl.java:41)
at com.xiaohongshu.events.client.consumer.BaseEventsConsumer.start(BaseEventsConsumer.java:140)
at com.xiaohongshu.events.client.consumer.AbstractConsumer.lambda$createConsumer$2(AbstractConsumer.java:135)
at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
at java.base/java.lang.Thread.run(Thread.java:834)

21:06:02  INFO -- [e-consumer-Poll-Message-8] c.x.c.a.mq.DepartureMessageProcessor               : [人事][消息消费]收到消息: messageExt = {"accountName":"system_mail_test_2","accountMail":"<EMAIL>","accountType":8,"accountSourceChannel":"INNER","eventType":"ENABLE","channelUserId":null,"event":"2025-05-21 21:06:02","dataIsolationType":1}; consumeContext = {"group":"plo-account-codex-account-departure-consumer","qid":"sit-rocketmq-cluster_broker-02_0","topic":"plo-account"}
21:06:02  INFO -- [e-consumer-Poll-Message-8] c.x.c.a.mq.DepartureMessageProcessor               : [人事][离职消息消费]收到离职事件消息: message = {"accountMail":"<EMAIL>","accountName":"system_mail_test_2","accountSourceChannel":"INNER","accountType":8,"dataIsolationType":1,"eventTime":"2025-05-21T21:06:02","eventType":"ENABLE"}
21:06:02  INFO -- [e-consumer-Poll-Message-8] c.x.c.a.mq.DepartureMessageProcessor               : [离职消息消费]消息处理完成: costMsTime = 23, messageExt = Message{topic='plo-account', key='GkBtlckGnECYA0bL', tags='null', hashId=0, partitionId=-2, priority=-1, properties={_events_message_partition_id=-2, events_max_reconsume_times=3, DELIVERY_TIME=*************, _events_message_hash_id=0, events_reconsume_times=0}}; consumeContext = {"group":"plo-account-codex-account-departure-consumer","qid":"sit-rocketmq-cluster_broker-02_0","topic":"plo-account"}
21:06:03  INFO -- [e-consumer-Poll-Message-4] c.x.c.a.mq.DepartureMessageProcessor               : [人事][消息消费]收到消息: messageExt = {"accountName":"system_mail_test_2","accountMail":"<EMAIL>","accountType":8,"accountSourceChannel":"INNER","eventType":"CREATE","channelUserId":null,"event":"2025-05-21 21:06:02","dataIsolationType":1}; consumeContext = {"group":"plo-account-codex-account-departure-consumer","qid":"sit-rocketmq-cluster_broker-01_0","topic":"plo-account"}
21:06:03  INFO -- [e-consumer-Poll-Message-4] c.x.c.a.mq.DepartureMessageProcessor               : [人事][离职消息消费]收到离职事件消息: message = {"accountMail":"<EMAIL>","accountName":"system_mail_test_2","accountSourceChannel":"INNER","accountType":8,"dataIsolationType":1,"eventTime":"2025-05-21T21:06:02","eventType":"CREATE"}
21:06:03  INFO -- [e-consumer-Poll-Message-4] c.x.c.a.mq.DepartureMessageProcessor               : [离职消息消费]消息处理完成: costMsTime = 1, messageExt = Message{topic='plo-account', key='VbLrLC8ZOSVhUNlU', tags='null', hashId=0, partitionId=-2, priority=-1, properties={_events_message_partition_id=-2, events_max_reconsume_times=3, DELIVERY_TIME=*************, _events_message_hash_id=0, events_reconsume_times=0}}; consumeContext = {"group":"plo-account-codex-account-departure-consumer","qid":"sit-rocketmq-cluster_broker-01_0","topic":"plo-account"}
