<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.xiaohongshu</groupId>
		<artifactId>codewiz-server</artifactId>
		<version>${revision}</version>
	</parent>

	<packaging>jar</packaging>
	<artifactId>codewiz-account</artifactId>

	<dependencies>
		<dependency>
			<groupId>com.xiaohongshu</groupId>
			<artifactId>codewiz-core</artifactId>
			<version>${revision}</version>
		</dependency>

		<!--stopOne对接start RPC-->
		<dependency>
			<groupId>com.xiaohongshu.force.thrift</groupId>
			<artifactId>lobot-client</artifactId>
			<version>master-********.070844-35</version>
		</dependency>

		<dependency>
			<groupId>com.xiaohongshu</groupId>
			<artifactId>thrift-springboot</artifactId>
		</dependency>
		<dependency>
			<groupId>com.xiaohongshu</groupId>
			<artifactId>infra-framework-rpc-core</artifactId>
		</dependency>
		<!-- 非 Spring 环境比如 Flink 不需要引入此 infra-framework-rpc-spring 依赖 -->
		<dependency>
			<groupId>com.xiaohongshu</groupId>
			<artifactId>infra-framework-rpc-spring</artifactId>
		</dependency>

		<!--StopOne 标准账号查询接口： 横向的服务通常关联存储账号唯一标识 账号邮箱/账号名(账号邮箱前缀)，需要通过账号服务获取账号状态、账号显示名、账号归属部门组织等信息来进行呈现。-->
		<dependency>
			<groupId>com.xiaohongshu.force.thrift</groupId>
			<artifactId>plo-paploo-client</artifactId>
			<version>1.11.3</version>
		</dependency>

		<!--StopOne对接 end-->


		<!--消息队列-->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-actuator</artifactId>
		</dependency>
		<dependency>
			<groupId>com.xiaohongshu</groupId>
			<artifactId>events-client</artifactId>
		</dependency>
		<dependency>
			<groupId>com.xiaohongshu</groupId>
			<artifactId>gateway-starter</artifactId>
		</dependency>
		<dependency>
			<groupId>com.xiaohongshu.xray</groupId>
			<artifactId>xray-logging</artifactId>
		</dependency>
		<!--消息队列-->

		<!-- RedFlow 集成 RPC-->
		<!-- Thrift OA 公共库 -->
		<dependency>
			<groupId>com.xiaohongshu.fls.thrift</groupId>
			<artifactId>lib-thrift-oa-public</artifactId>
			<version>sit-SNAPSHOT</version>
		</dependency>
		<!-- RedFlow 集成 RPC-->


		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>

	</dependencies>

	<build>
		<finalName>${project.artifactId}</finalName>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
			</plugin>
		</plugins>
	</build>

</project>
