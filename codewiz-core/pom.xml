<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.xiaohongshu</groupId>
        <artifactId>codewiz-server</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>codewiz-core</artifactId>

    <dependencies>
        <!-- 日志组件 -->
        <dependency>
            <groupId>com.xiaohongshu.xray</groupId>
            <artifactId>xray-logging</artifactId>
        </dependency>
        <!-- fix:xray日志缺少类异常 -->
        <dependency>
            <groupId>javax.xml.bind</groupId>
            <artifactId>jaxb-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

        <!-- 埋点 -->
        <dependency>
            <groupId>com.dianping.cat</groupId>
            <artifactId>cat-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xiaohongshu</groupId>
            <artifactId>infra-framework-common</artifactId>
        </dependency>
        <!-- 配置中心 -->
        <dependency>
            <groupId>com.xiaohongshu</groupId>
            <artifactId>infra-redconf-client-all</artifactId>
            <version>2.0.0</version>
        </dependency>
        <!-- 定时任务 -->
        <dependency>
            <groupId>com.xiaohongshu.infra.midware</groupId>
            <artifactId>redschedule-spring-boot-starter</artifactId>
        </dependency>
        <!-- MySQL接入 -->
        <dependency>
            <groupId>com.xiaohongshu.redsql</groupId>
            <artifactId>redsql-spring-boot-starter</artifactId>
            <exclusions>
                <!-- 移除redsql中的日志依赖 -->
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis-spring</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis</artifactId>
        </dependency>
        <!-- Feign -->
        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-jackson</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
        <!-- WebFlux -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-webflux</artifactId>
        </dependency>
        <!-- Redis接入 -->
        <dependency>
            <groupId>com.xiaohongshu.infra.midware</groupId>
            <artifactId>redis-spring</artifactId>
        </dependency>
        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
        </dependency>
        <!-- GitLab SDK -->
        <dependency>
            <groupId>org.gitlab4j</groupId>
            <artifactId>gitlab4j-api</artifactId>
        </dependency>
        <!-- S3 -->
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-s3</artifactId>
        </dependency>
        <!-- Swagger -->
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-ui</artifactId>
        </dependency>

        <!-- Kafka -->
        <dependency>
            <groupId>org.apache.kafka</groupId>
            <artifactId>kafka-clients</artifactId>
        </dependency>

        <!-- Elasticsearch -->
        <dependency>
            <groupId>co.elastic.clients</groupId>
            <artifactId>elasticsearch-java</artifactId>
            <version>8.13.3</version>
        </dependency>
        <dependency>
            <groupId>org.elasticsearch.client</groupId>
            <artifactId>elasticsearch-rest-client</artifactId>
            <version>8.13.3</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>javax.validation</groupId>
            <artifactId>validation-api</artifactId>
        </dependency>
        <dependency>
            <groupId>io.milvus</groupId>
            <artifactId>milvus-sdk-java</artifactId>
            <version>2.5.2</version>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-core</artifactId>
            <version>5.8.25</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>
        <dependency>
            <groupId>org.eclipse.lsp4j</groupId>
            <artifactId>org.eclipse.lsp4j</artifactId>
            <version>0.12.0</version>
        </dependency>
        <dependency>
            <groupId>org.freemarker</groupId>
            <artifactId>freemarker</artifactId>
        </dependency>
        <!-- https://docs.xiaohongshu.com/doc/37d659fe598e488ff628269939ebe7c8 -->
        <!-- HTTP服务端打点 -->
        <dependency>
            <groupId>com.xiaohongshu</groupId>
            <artifactId>gateway-starter</artifactId>
        </dependency>
        <!-- JVM打点 -->
        <dependency>
            <groupId>com.xiaohongshu</groupId>
            <artifactId>monitor-starter</artifactId>
        </dependency>
        <!-- ElasticSearch -->
        <dependency>
            <groupId>co.elastic.clients</groupId>
            <artifactId>elasticsearch-java</artifactId>
        </dependency>

        <!-- 查询用户信息 -->
        <dependency>
            <groupId>com.xiaohongshu.fls.thrift</groupId>
            <artifactId>lib-thrift-rcoppublicidl</artifactId>
            <version>0.0.10</version>
        </dependency>
        <dependency>
            <groupId>com.xiaohongshu</groupId>
            <artifactId>thrift-rpc</artifactId>
        </dependency>
    </dependencies>
</project>
