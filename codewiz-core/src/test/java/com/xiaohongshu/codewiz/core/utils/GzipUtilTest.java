package com.xiaohongshu.codewiz.core.utils;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;

import org.junit.Test;

/**
 * Gzip工具类测试
 *
 * <AUTHOR>
 * @since 2024-05-24
 */
public class GzipUtilTest {

    @Test
    public void testCompressAndDecompress() throws Exception {
        String originalText = "这是一个测试文本\n包含换行符和特殊字符：!@#$%^&*()_+\n用于测试gzip压缩和解压功能";
        
        // 测试压缩
        String compressed = GzipUtil.compress(originalText);
        assertNotNull(compressed);
        System.out.println("Original length: " + originalText.length());
        System.out.println("Compressed length: " + compressed.length());
        
        // 测试解压
        String decompressed = GzipUtil.decompress(compressed);
        assertNotNull(decompressed);
        assertEquals(originalText, decompressed);
        System.out.println("Compression and decompression test passed!");
    }

    @Test
    public void testCompressNull() throws Exception {
        String result = GzipUtil.compress(null);
        assertNull(result);
    }

    @Test
    public void testCompressEmpty() throws Exception {
        String result = GzipUtil.compress("");
        assertNull(result);
    }

    @Test
    public void testDecompressNull() throws Exception {
        String result = GzipUtil.decompress(null);
        assertNull(result);
    }

    @Test
    public void testDecompressEmpty() throws Exception {
        String result = GzipUtil.decompress("");
        assertNull(result);
    }

    @Test
    public void testLargeText() throws Exception {
        // 构建一个较大的文本
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < 1000; i++) {
            sb.append("这是第").append(i).append("行测试数据，包含中文和英文字符 Line ").append(i).append(" with some data\n");
        }
        String originalText = sb.toString();
        
        // 测试压缩
        String compressed = GzipUtil.compress(originalText);
        assertNotNull(compressed);
        
        // 测试解压
        String decompressed = GzipUtil.decompress(compressed);
        assertNotNull(decompressed);
        assertEquals(originalText, decompressed);
        
        System.out.println("Large text test - Original: " + originalText.length() + 
                          " bytes, Compressed: " + compressed.length() + " bytes");
        System.out.println("Compression ratio: " + String.format("%.2f%%", 
                          (1.0 - (double)compressed.length() / originalText.length()) * 100));
    }

    @Test
    public void testPluginLogExample() throws Exception {
        // 模拟真实的插件日志内容
        String pluginLog = "2024-05-24 17:00:00 [INFO] Plugin started\n" +
                          "2024-05-24 17:00:01 [DEBUG] Loading configuration\n" +
                          "2024-05-24 17:00:02 [INFO] User action: code completion requested\n" +
                          "2024-05-24 17:00:03 [DEBUG] Analyzing context: function calculateTotal() {\n" +
                          "2024-05-24 17:00:04 [DEBUG] Sending request to LLM service\n" +
                          "2024-05-24 17:00:05 [INFO] Received response from LLM\n" +
                          "2024-05-24 17:00:06 [ERROR] Network timeout occurred\n" +
                          "2024-05-24 17:00:07 [INFO] Retrying request\n" +
                          "2024-05-24 17:00:08 [INFO] Request successful\n" +
                          "2024-05-24 17:00:09 [DEBUG] Processing completion: const result = items.reduce(...)\n" +
                          "2024-05-24 17:00:10 [INFO] Code completion provided to user\n";

        // 压缩
        String compressed = GzipUtil.compress(pluginLog);
        assertNotNull(compressed);
        
        // 解压
        String decompressed = GzipUtil.decompress(compressed);
        assertNotNull(decompressed);
        assertEquals(pluginLog, decompressed);
        
        System.out.println("Plugin log test passed!");
        System.out.println("Original log size: " + pluginLog.length() + " bytes");
        System.out.println("Compressed size: " + compressed.length() + " bytes");
        System.out.println("Compression ratio: " + String.format("%.2f%%", 
                          (1.0 - (double)compressed.length() / pluginLog.length()) * 100));
    }
} 