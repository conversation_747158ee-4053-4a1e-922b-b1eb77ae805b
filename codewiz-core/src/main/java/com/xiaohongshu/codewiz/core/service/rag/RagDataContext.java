package com.xiaohongshu.codewiz.core.service.rag;

import java.util.List;
import java.util.Map;

import com.xiaohongshu.codewiz.core.constant.enums.RagEmbeddingModelEnum;
import com.xiaohongshu.codewiz.core.constant.enums.RagRecallStrategyEnum;
import com.xiaohongshu.codewiz.core.entity.rag.RagDataAddRequest;
import com.xiaohongshu.codewiz.core.entity.rag.RagDataQueryRequest;
import com.xiaohongshu.codewiz.core.entity.rag.RagDataQueryResponse;
import com.xiaohongshu.codewiz.core.entity.rag.RagDocument;

import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/2/27 21:04
 */
@Data
@Builder
public class RagDataContext<T extends RagDocument> {
    private RagDataAddRequest addRequest;

    private RagDataQueryRequest queryRequest;

    private RagDataQueryResponse<T> queryResponse;

    private String collectionName;

    private List<T> documents;

    private Map<String, String> analysisQuery;

    private RagAddResult<T> addResult;

    private TopK topK;

    private RagEmbeddingModelEnum embeddingModel;

    private RagRecallStrategyEnum recallStrategy;

    @Data
    public static class TopK {
        private int recallMilvusTopK;
        private int recallEsTopK;
        private int rerankTopK;
    }

    @Data
    @Builder
    public static class RagAddResult<T extends RagDocument> {
        // 用于存储上下文数据，如操作结果等
        private int successCount;
        private int failedCount;
        private List<T> failedDocuments;
    }
}
