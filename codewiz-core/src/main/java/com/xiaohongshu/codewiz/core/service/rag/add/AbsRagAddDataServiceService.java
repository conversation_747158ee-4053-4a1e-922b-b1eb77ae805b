package com.xiaohongshu.codewiz.core.service.rag.add;

import org.apache.commons.collections4.CollectionUtils;

import com.xiaohongshu.codewiz.core.constant.ErrorCodeConstant;
import com.xiaohongshu.codewiz.core.entity.rag.RagDataAddRequest;
import com.xiaohongshu.codewiz.core.entity.rag.RagDocument;
import com.xiaohongshu.codewiz.core.exception.BizException;
import com.xiaohongshu.codewiz.core.service.rag.RagDataContext;

/**
 * <AUTHOR>
 * @date 2025/2/27 19:36
 */
public abstract class AbsRagAddDataServiceService<T extends RagDocument> implements RagAddDataService {
    @Override
    public RagDataContext.RagAddResult addData(RagDataAddRequest request) {
        // 1. check
        check(request);

        // 构造唯一性id
        RagDataContext<T> context = buildContext(request);

        // 语义丰富，关联相关上下文
        extendDocument(context);

        // 存入mysql db
        saveToMysql(context);

        // 存入milvus db
        saveToMilvus(context);

        return context.getAddResult();
    }

    protected abstract void check(RagDataAddRequest request);

    protected abstract String getCollectionName(RagDataAddRequest request);

    protected abstract void extendDocument(RagDataContext<T> context);

    protected abstract void saveToMysql(RagDataContext<T> context);

    protected abstract void saveToMilvus(RagDataContext<T> context);


    protected RagDataContext<T> buildContext(RagDataAddRequest request) {
        String collectionName = getCollectionName(request);
        return RagDataContext.<T>builder()
                .addRequest(request)
                .collectionName(collectionName).build();
    }

    protected void baseCheck(RagDataAddRequest request) {
        // 1. check
        if (request == null) {
            throw new RuntimeException("request is null");
        }

        if (request.getScene() == null || request.getScene() == 0) {
            throw new BizException(ErrorCodeConstant.RAG_DATA_SCENE_ERROR);
        }

        if (CollectionUtils.isEmpty(request.getDocuments())) {
            throw new BizException(ErrorCodeConstant.RAG_DATA_ADD_DOCUMENTS_NOT_FOUND);
        }
    }
}
