package com.xiaohongshu.codewiz.core.service.config;

import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.xiaohongshu.codewiz.core.constant.enums.plugin.ConfigTypeEnum;
import com.xiaohongshu.codewiz.core.entity.config.dto.PluginCanaryWhiteDTO;
import com.xiaohongshu.codewiz.core.entity.config.dto.PluginCanaryWhiteDTO.CanaryWhiteDepartment;
import com.xiaohongshu.codewiz.core.entity.config.dto.PluginVersionConfigDTO;
import com.xiaohongshu.codewiz.core.service.user.RedUserBO;
import com.xiaohongshu.codewiz.core.service.user.RedUserService;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.xiaohongshu.codewiz.core.dao.config.ConfigSnapshotDao;
import com.xiaohongshu.codewiz.core.dao.config.PersonConfigDao;
import com.xiaohongshu.codewiz.core.entity.common.SingleResponse;
import com.xiaohongshu.codewiz.core.entity.config.ConfigSnapshotDO;
import com.xiaohongshu.codewiz.core.entity.config.PersonConfigDO;
import com.xiaohongshu.codewiz.core.entity.config.dto.ConfigDTO;
import com.xiaohongshu.codewiz.core.entity.config.dto.ConfigQueryRequest;
import com.xiaohongshu.codewiz.core.entity.config.dto.ConfigQueryResponse;
import com.xiaohongshu.codewiz.core.entity.config.dto.ConfigUpdateRequest;
import com.xiaohongshu.codewiz.core.entity.config.dto.ConfigUpgradeRequest;
import com.xiaohongshu.codewiz.core.utils.JsonMapperUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * IDE配置服务
 *
 * <AUTHOR>
 * @since 2025-04-21
 */
@Slf4j
@Service
public class IDEConfigService {

    @Autowired
    private ConfigSnapshotDao configSnapshotDao;

    @Autowired
    private PersonConfigDao personConfigDao;

    @Resource
    private RedUserService redUserService;

    @ApolloJsonValue("${plugin.version.config: {}}")
    private Map<String, List<PluginVersionConfigDTO>> pluginVersionConfig;

    @ApolloJsonValue("${plugin.canary.whiteList: {}}")
    private Map<String, PluginCanaryWhiteDTO> pluginCanaryWhite;

    @Value("${mcp.install.command}")
    private String mcpInstallCommand;

    /**
     * 查询配置
     *
     * @param request 查询请求
     * @return 配置信息
     */
    public SingleResponse<ConfigQueryResponse> queryConfig(ConfigQueryRequest request) {
        try {
            // 1. 根据插件版本和type去查询配置快照内容
            List<ConfigSnapshotDO> configSnapshots = configSnapshotDao.queryConfigSnapshot(
                    request.getPluginVersion(), request.getType());
            log.info("queryConfig, configSnapshots: {}", configSnapshots);

            if (CollectionUtils.isEmpty(configSnapshots)) {
                // 查询所有最新的配置
                log.info("No config snapshots found for specified version, trying to get latest configs");
                configSnapshots = configSnapshotDao.queryLatestByType(request.getType());
            }

            if (CollectionUtils.isEmpty(configSnapshots)) {
                // 没有找到配置快照
                log.warn("No config snapshots found for version: {}, type: {}",
                        request.getPluginVersion(), request.getType());
                return SingleResponse.buildFailure("404", "No config found");
            }

            // 转换为DTO
            List<ConfigDTO> configDTOs = configSnapshots.stream().map(this::convertToDTO)
                    .collect(Collectors.toList());

            // 2. 如果用户邮箱不为空，查询个人配置并覆盖
            if (StringUtils.isNotBlank(request.getUserEmail())) {
                applyPersonalConfigs(request.getUserEmail(), configDTOs, request.getIdePlatform());
            }

            // 3. 组装响应
            ConfigQueryResponse response = new ConfigQueryResponse();
            response.setUserEmail(request.getUserEmail());
            response.setPluginVersion(request.getPluginVersion());
            response.setConfigs(configDTOs);

            log.info("queryConfig, response: {}", response);
            return SingleResponse.of(response);
        } catch (Exception e) {
            log.error("Query config error", e);
            return SingleResponse.buildFailure("500", "Query config error: " + e.getMessage());
        }
    }

    /**
     * 更新配置
     *
     * @param request 更新请求
     * @return 更新结果
     */
    public SingleResponse<Void> updateConfig(ConfigUpdateRequest request) {
        try {
            // 1. 检查用户邮箱是否为空
            if (StringUtils.isBlank(request.getUserEmail())) {
                return SingleResponse.buildFailure("400", "User email cannot be empty");
            }

            // 2. 检查配置列表是否为空
            if (CollectionUtils.isEmpty(request.getConfigs())) {
                return SingleResponse.buildFailure("400", "Config list cannot be empty");
            }

            // 3. 根据插件版本和配置类型查询配置快照
            List<ConfigSnapshotDO> allSnapshots = new ArrayList<>();
            for (ConfigDTO configDTO : request.getConfigs()) {
                List<ConfigSnapshotDO> snapshots = configSnapshotDao.queryConfigSnapshot(
                        request.getPluginVersion(), configDTO.getConfigType(), configDTO.getConfigName());
                if (CollectionUtils.size(snapshots) > 1) {
                    log.error("config snapshots found for version: {}, type: {}, name: {}",
                            request.getPluginVersion(), configDTO.getConfigType(), configDTO.getConfigName());
                }
                allSnapshots.addAll(snapshots);
            }

            if (CollectionUtils.isEmpty(allSnapshots)) {
                log.warn("allSnapshots, No config snapshots found for pluginVersion: {}, configs: {}",
                        request.getPluginVersion(), request.getConfigs());
                return SingleResponse.buildFailure("404", "No config snapshots found");
            }

            log.info("updateConfig, all config size == {}, allSnapshots: {}", allSnapshots.size(), allSnapshots);

            // 4. 构建配置名称和类型到快照ID的映射
            Map<String, ConfigSnapshotDO> nameTypeToIdMap = new HashMap<>();
            for (ConfigSnapshotDO snapshot : allSnapshots) {
                String key = buildConfigKey(request.getPluginVersion(), snapshot.getConfigName(),
                        snapshot.getConfigType());
                nameTypeToIdMap.put(key, snapshot);
            }

            // 5. 更新个人配置
            for (ConfigDTO configDTO : request.getConfigs()) {
                String key = buildConfigKey(request.getPluginVersion(), configDTO.getConfigName(),
                        configDTO.getConfigType());
                ConfigSnapshotDO snapshot = nameTypeToIdMap.get(key);
                if (snapshot == null) {
                    log.warn("No snapshot found for pluginVersion: {}, configName: {}, configType: {}",
                            request.getPluginVersion(), configDTO.getConfigName(), configDTO.getConfigType());
                    continue;
                }

                // 创建个人配置
                PersonConfigDO personConfig = new PersonConfigDO();
                personConfig.setConfigSnapshotId(snapshot.getId());
                personConfig.setUserEmail(request.getUserEmail());
                personConfig.setConfigValue(configDTO.getConfigValue());
                personConfig.setConfigName(configDTO.getConfigName());

                // 保存或更新
                personConfigDao.saveOrUpdatePersonConfig(personConfig);
            }
            log.info("updateConfig, update success");
            return SingleResponse.ok();
        } catch (Exception e) {
            log.error("Update config error", e);
            return SingleResponse.buildFailure("500", "Update config error: " + e.getMessage());
        }
    }

    /**
     * 升级配置
     */
    @Transactional(rollbackFor = Exception.class)
    public SingleResponse<Void> upgradeConfig(ConfigUpgradeRequest request) {
        try {
            // 1. 检查插件版本是否为空
            if (StringUtils.isBlank(request.getPluginVersion())) {
                return SingleResponse.buildFailure("400", "Plugin version cannot be empty");
            }

            // 2. 查询所有标记为最新的配置
            List<ConfigSnapshotDO> latestConfigs = configSnapshotDao.queryLatestByType(null);
            if (CollectionUtils.isEmpty(latestConfigs)) {
                log.error("No latest configs found!!");
                return SingleResponse.buildFailure("404", "No latest configs found!!");
            }

            // 3. 如果没有配置传入，使用最新配置
            if (CollectionUtils.isEmpty(request.getConfigs())) {
                request.setConfigs(latestConfigs.stream().map(this::convertToDTO).collect(Collectors.toList()));

                // 如果传入版本和最新版本一致，无需更新
                if (Objects.equals(latestConfigs.get(0).getConfigVersion(), request.getPluginVersion())) {
                    log.info("upgradeConfig, plugin version is already latest, no need to upgrade");
                    return SingleResponse.ok();
                }
            }

            log.info("upgradeConfig, found {} latest configs, processing {} configs",
                    latestConfigs.size(), request.getConfigs().size());

            // 4. 将所有最新配置标记为非最新
            for (ConfigSnapshotDO config : latestConfigs) {
                config.setIsLasted(false);
                configSnapshotDao.updateById(config);
            }
            log.info("upgradeConfig, marked all latest configs as non-latest");

            // 5. 处理配置升级
            String latestVersion = latestConfigs.get(0).getConfigVersion();
            boolean isSameVersion = Objects.equals(latestVersion, request.getPluginVersion());

            // 遍历请求中的配置
            for (ConfigDTO configDTO : request.getConfigs()) {
                // 查找是否有同名同类型的最新配置
                ConfigSnapshotDO matchingConfig = null;
                for (ConfigSnapshotDO config : latestConfigs) {
                    if (Objects.equals(config.getConfigName(), configDTO.getConfigName())
                            && Objects.equals(config.getConfigType(), configDTO.getConfigType())) {
                        matchingConfig = config;
                        break;
                    }
                }

                if (isSameVersion && matchingConfig != null) {
                    // 5.1 如果版本相同，更新已有配置
                    log.info("upgradeConfig, updating existing config: {}", configDTO.getConfigName());
                    matchingConfig.setConfigValue(configDTO.getConfigValue());
                    matchingConfig.setConfigDesc(configDTO.getConfigDesc());
                    matchingConfig.setIsLasted(true);
                    matchingConfig.setUpdateAt(System.currentTimeMillis());
                    configSnapshotDao.updateById(matchingConfig);
                } else {
                    // 5.2 如果版本不同或没有匹配配置，插入新配置
                    log.info("upgradeConfig, inserting new config: {}", configDTO.getConfigName());
                    ConfigSnapshotDO newConfig = new ConfigSnapshotDO();
                    BeanUtils.copyProperties(configDTO, newConfig);
                    newConfig.setId(null); // 确保是新记录
                    newConfig.setConfigVersion(request.getPluginVersion());
                    newConfig.setIsLasted(true);
                    newConfig.setCreateAt(System.currentTimeMillis());
                    newConfig.setUpdateAt(System.currentTimeMillis());
                    configSnapshotDao.save(newConfig);
                }
            }

            log.info("upgradeConfig, upgrade config success");
            return SingleResponse.ok();
        } catch (Exception e) {
            log.error("Upgrade config error", e);
            return SingleResponse.buildFailure("500", "Upgrade config error: " + e.getMessage());
        }
    }

    private String buildConfigKey(String pluginVersion, String configName, Integer configType) {
        return pluginVersion + "#" + configName + "#" + configType;
    }

    /**
     * 将DO对象转换为DTO对象
     *
     * @param snapshotDO 配置快照DO
     * @return 配置DTO
     */
    private ConfigDTO convertToDTO(ConfigSnapshotDO snapshotDO) {
        ConfigDTO configDTO = new ConfigDTO();
        BeanUtils.copyProperties(snapshotDO, configDTO);
        // 当 configType = 2 时，configValue 是 json 字符串，需要转换为 map
        if (ConfigTypeEnum.LSP.getCode().equals(snapshotDO.getConfigType())) {
            log.info("convertToDTO, configType = 2, configValue: {}", snapshotDO.getConfigValue());
            Map<String, Object> mapValue = JsonMapperUtils.fromJson(snapshotDO.getConfigValue());
            mapValue.put("markdown", Map.of("mcp_install_markdown", mcpInstallCommand));
            configDTO.setConfigValue(JsonMapperUtils.toJson(mapValue));
        }
        configDTO.setConfigSnapshotId(snapshotDO.getId());
        return configDTO;
    }

    /**
     * 应用个人配置到配置列表
     *
     * @param userEmail  用户邮箱
     * @param configDTOs 配置列表
     */
    private void applyPersonalConfigs(String userEmail, List<ConfigDTO> configDTOs, String idePlatform) {
        for (ConfigDTO configDTO : configDTOs) {
            log.info("应用个人配置, userEmail: {}", userEmail);
            Map<String, Object> configJsonValueMap = JsonMapperUtils.fromJson(configDTO.getConfigValue());

            PersonConfigDO personConfig = personConfigDao.queryLatestByUserEmail(userEmail, configDTO.getConfigName());
            if (personConfig != null) {
                log.info("查询到最新个人配置: {}, config name: {}", personConfig, configDTO.getConfigName());
                Map<String, Object> personJsonValueMap = JsonMapperUtils.fromJson(personConfig.getConfigValue());
                configJsonValueMap.putAll(personJsonValueMap);
            }
            if (ConfigTypeEnum.PLUGIN.getCode().equals(configDTO.getConfigType())) {
                //插件配置的话，需要额外添加插件可用版本及灰度名单
                applyPluginCanaryConfigs(userEmail, configJsonValueMap, idePlatform);
            }
            configDTO.setConfigValue(JsonMapperUtils.toJson(configJsonValueMap));
        }
    }
    private void applyPluginCanaryConfigs(String userEmail, Map<String, Object> configJsonValueMap, String idePlatform) {
        if (null == pluginVersionConfig
            || pluginVersionConfig.isEmpty()
            || StringUtils.isEmpty(idePlatform)
            || !pluginVersionConfig.containsKey(idePlatform)) {
            return;
        }
        // 遍历当前的配置版本
        List<PluginVersionConfigDTO> pluginVersions = pluginVersionConfig.get(idePlatform);
        if (CollectionUtils.isEmpty(pluginVersions)) {
            return;
        }
        List<PluginVersionConfigDTO> copyVersions = pluginVersions.stream().map(dto -> {
            PluginVersionConfigDTO copy = new PluginVersionConfigDTO();
            BeanUtils.copyProperties(dto, copy);
            return copy;
        }).collect(Collectors.toList());
        for (PluginVersionConfigDTO pluginVersionConfigDTO : copyVersions) {
            if (!pluginVersionConfigDTO.isEnabled()) {
                //已经被禁用的配置，跳过
                continue;
            }
            //灰度版本需要额外判断
            if (pluginVersionConfigDTO.isCanary()) {
                if (null == pluginCanaryWhite || pluginCanaryWhite.isEmpty()) {
                    //没有设置相关灰度信息，直接禁用
                    pluginVersionConfigDTO.setEnabled(false);
                    log.info("灰度版本被禁用，原因：未设置白名单");
                    continue;
                }
                //设置了白名单，需要看白名单（未单独设置时走默认）
                PluginCanaryWhiteDTO whiteDTO = pluginCanaryWhite.getOrDefault(
                    pluginVersionConfigDTO.getVersion(), pluginCanaryWhite.get("default"));
                //当前用户处于用户级别白名单时，灰度版本可用
                if (CollectionUtils.isNotEmpty(whiteDTO.getUser()) && whiteDTO.getUser().contains(userEmail)) {
                    continue;
                }
                //在个人不在白名单，且不存在部门级白名单时，灰度版本不可用
                if (CollectionUtils.isEmpty(whiteDTO.getDepartment())) {
                    pluginVersionConfigDTO.setEnabled(false);
                    continue;
                }
                //查看部门级白名单
                Optional<RedUserBO> redUserOptional = redUserService.searchRedUser(userEmail);
                RedUserBO userBO = redUserOptional.orElse(null);
                //查询用户信息为空，灰度版本不可用
                if (null == userBO) {
                    pluginVersionConfigDTO.setEnabled(false);
                    continue;
                }
                //或者用户部门不在白名单中，灰度版本不可用
                List<String> whiteDepartmentIds = whiteDTO.getDepartment().stream().map(CanaryWhiteDepartment::getId).collect(Collectors.toList());
                if (!whiteDepartmentIds.contains(userBO.getDepartmentId())) {
                    pluginVersionConfigDTO.setEnabled(false);
                }
                log.info("用户: {}, 部门: {}, 白名单: {}", userBO.getUserEmail(), userBO.getDepartmentId(), whiteDepartmentIds);
            }
        }
        //版本二次可用校验完毕(基于灰度白名单)
        configJsonValueMap.put("versionHistoryList", copyVersions);
    }
}
