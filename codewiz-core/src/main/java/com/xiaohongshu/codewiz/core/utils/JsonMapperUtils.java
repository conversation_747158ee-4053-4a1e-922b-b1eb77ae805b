package com.xiaohongshu.codewiz.core.utils;


import static com.fasterxml.jackson.core.JsonFactory.Feature.INTERN_FIELD_NAMES;
import static com.fasterxml.jackson.core.JsonParser.Feature.ALLOW_COMMENTS;
import static com.fasterxml.jackson.core.JsonParser.Feature.STRICT_DUPLICATE_DETECTION;
import static com.fasterxml.jackson.databind.DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES;
import static com.fasterxml.jackson.databind.type.TypeFactory.defaultInstance;

import java.io.IOException;
import java.io.UncheckedIOException;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.commons.lang3.StringUtils;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonFactory;
import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.core.util.DefaultIndenter;
import com.fasterxml.jackson.core.util.DefaultPrettyPrinter;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/2/25 19:11
 */
@SuppressWarnings("rawtypes")
public class JsonMapperUtils {

    private JsonMapperUtils() {
        throw new UnsupportedOperationException();
    }

    private static final ObjectMapper MAPPER = new ObjectMapper(new JsonFactory().disable(INTERN_FIELD_NAMES));

    private static final DefaultPrettyPrinter PRETTY_PRINTER = new DefaultPrettyPrinter();

    private static final ObjectMapper MAPPER_2 = new ObjectMapper(new JsonFactory().disable(INTERN_FIELD_NAMES));

    static {
        MAPPER.enable(ALLOW_COMMENTS);
        MAPPER.enable(STRICT_DUPLICATE_DETECTION);
        MAPPER.disable(FAIL_ON_UNKNOWN_PROPERTIES);
        DefaultIndenter indenter = new DefaultIndenter("  ", DefaultIndenter.SYS_LF);
        PRETTY_PRINTER.indentObjectsWith(indenter);
        PRETTY_PRINTER.indentArraysWith(indenter);
    }

    static {
        MAPPER_2.enable(ALLOW_COMMENTS);
        MAPPER_2.enable(STRICT_DUPLICATE_DETECTION);
        MAPPER_2.disable(FAIL_ON_UNKNOWN_PROPERTIES);
        MAPPER_2.setSerializationInclusion(JsonInclude.Include.NON_NULL);
    }

    public static String toJson(Object obj) {
        try {
            return MAPPER.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            throw new UncheckedIOException(e);
        }
    }

    /**
     * 输出格式化好的json
     * 请不要在输出log时使用
     * <p>
     * 一般只用于写结构化数据到ZooKeeper时使用（为了更好的可读性）
     */
    public static String toPrettyJson(Object obj) {
        if (obj == null) {
            return null;
        }
        try {
            return MAPPER.writer(PRETTY_PRINTER).writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            throw new UncheckedIOException(e);
        }
    }

    /**
     * 输出格式化好的json
     * 请不要在输出log时使用
     * <p>
     * 一般只用于写结构化数据到ZooKeeper时使用（为了更好的可读性）
     */
    public static String toPrettyJsonNoNull(Object obj) {
        if (obj == null) {
            return null;
        }
        try {
            return MAPPER_2.writer(PRETTY_PRINTER).writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            throw new UncheckedIOException(e);
        }
    }

    public static <T> T fromJson(String json, Class<T> valueType) {
        try {
            return MAPPER.readValue(json, valueType);
        } catch (IOException e) {
            throw new UncheckedIOException(e);
        }
    }

    public static <T> T fromJson(String json, JavaType javaType) {
        try {
            return MAPPER.readValue(json, javaType);
        } catch (IOException e) {
            throw new UncheckedIOException(e);
        }
    }


    public static <T> T fromJson(String json, TypeReference<T> typeReference) {
        try {
            return MAPPER.readValue(json, typeReference);
        } catch (IOException e) {
            throw new UncheckedIOException(e);
        }
    }

    public static <T> T fromJson(byte[] json, Class<T> valueType) {
        try {
            return MAPPER.readValue(json, valueType);
        } catch (IOException e) {
            throw new UncheckedIOException(e);
        }
    }

    public static <T> T convertMapToClass(Map<?, ?> map, Class<T> clazz) {
        return MAPPER.convertValue(map, clazz);
    }

    public static <T> T convertMapToClass(Map<?, ?> map, TypeReference<T> typeReference) {
        return MAPPER.convertValue(map, typeReference);
    }

    public static <T> T convertMapToClass(Map<?, ?> map, JavaType javaType) {
        return MAPPER.convertValue(map, javaType);
    }

    public static <E, T extends Collection<E>> T ofJsonCollection(String json, Class<? extends Collection> collectionType,
                                                                  Class<E> valueType) {
        try {
            return MAPPER.readValue(json, defaultInstance().constructCollectionType(collectionType, valueType));
        } catch (IOException e) {
            throw new UncheckedIOException(e);
        }
    }

    public static <K, V, T extends Map<K, V>> T ofJsonMap(String json, Class<? extends Map> mapType, Class<K> keyType, Class<V> valueType) {
        try {
            return MAPPER.readValue(json, defaultInstance().constructMapType(mapType, keyType, valueType));
        } catch (IOException e) {
            throw new UncheckedIOException(e);
        }
    }

    public static <K, V, T extends Map<K, Set<V>>> T ofSetMap(String json, Class<? extends Map> mapType, Class<K> keyClass,
                                                              Class<V> subValueClass) {
        JavaType valueType = MAPPER.getTypeFactory().constructCollectionType(Set.class, subValueClass);
        return ofCollectionMap(json, mapType, keyClass, valueType);
    }

    public static <K, V, T extends Map<K, List<V>>> T ofListMap(String json, Class<? extends Map> mapType, Class<K> keyClass,
                                                                Class<V> subValueClass) {
        JavaType valueType = MAPPER.getTypeFactory().constructCollectionType(List.class, subValueClass);
        return ofCollectionMap(json, mapType, keyClass, valueType);
    }

    public static <K, K1, V1, T extends Map<K, Map<K1, V1>>> T ofMapMap(String json, Class<? extends Map> mapType, Class<K> keyClass,
                                                                        Class<K1> subKeyClass, Class<V1> subValueClass) {
        JavaType valueType = MAPPER.getTypeFactory().constructMapType(Map.class, subKeyClass, subValueClass);
        return ofCollectionMap(json, mapType, keyClass, valueType);
    }

    public static <T> T ofCollectionMap(String json, Class<? extends Map> mapType, Class<?> keyClass, JavaType valueType) {
        try {
            JavaType keyType = MAPPER.getTypeFactory().constructType(keyClass);
            return MAPPER.readValue(json, defaultInstance().constructMapType(mapType, keyType, valueType));
        } catch (IOException e) {
            throw new UncheckedIOException(e);
        }
    }

    public static <E, T extends Collection<E>> T ofJsonCollection(byte[] bytes, Class<? extends Collection> collectionType,
                                                                  Class<E> valueType) {
        try {
            return MAPPER.readValue(bytes, defaultInstance().constructCollectionType(collectionType, valueType));
        } catch (IOException e) {
            throw new UncheckedIOException(e);
        }
    }

    public static <K, V, T extends Map<K, V>> T ofJsonMap(byte[] bytes, Class<? extends Map> mapType, Class<K> keyType,
                                                          Class<V> valueType) {
        try {
            return MAPPER.readValue(bytes, defaultInstance().constructMapType(mapType, keyType, valueType));
        } catch (IOException e) {
            throw new UncheckedIOException(e);
        }
    }

    public static boolean isValidJson(String jsonStr) {
        if (StringUtils.isBlank(jsonStr)) {
            return false;
        }
        try (JsonParser parser = MAPPER.getFactory().createParser(jsonStr)) {
            // noinspection StatementWithEmptyBody
            while (parser.nextToken() != null) {
                // do nothing
            }
            return true;
        } catch (IOException ioe) {
            return false;
        }
    }

    public static void tryParseJson(String jsonStr) throws IOException {
        if (StringUtils.isBlank(jsonStr)) {
            throw new IOException("json data is empty.");
        }
        try (JsonParser parser = MAPPER.getFactory().createParser(jsonStr)) {
            // noinspection StatementWithEmptyBody
            while (parser.nextToken() != null) {
                // do nothing
            }
        }
    }

    public static Map<String, Object> fromJson(String string) {
        return ofJsonMap(string, Map.class, String.class, Object.class);
    }

    public static Map<String, Object> fromJson(byte[] bytes) {
        return ofJsonMap(bytes, Map.class, String.class, Object.class);
    }

    public static String fromMapToArrayNode(final Map<?, ?> map) throws JsonProcessingException {
        final ArrayNode arrayNode = MAPPER.createArrayNode();
        map.forEach((key, value) -> {
            final ObjectNode childNode = MAPPER.createObjectNode();
            childNode.put("key", key.toString());
            childNode.put("value", value.toString());
            arrayNode.add(childNode);
        });
        return MAPPER.writeValueAsString(arrayNode);
    }

    /**
     * @return the MAPPER
     */
    public static ObjectMapper mapper() {
        return MAPPER;
    }

    enum State {
        OUTSIDE_COMMENT, INSIDE_LINECOMMENT, INSIDE_BLOCKCOMMENT, INSIDE_STRING,
    }

    public static String removeComments(String code) {
        State state = State.OUTSIDE_COMMENT;
        StringBuilder result = new StringBuilder();
        int length = code.length();
        int i = 0;
        while (i < length) {
            char c = code.charAt(i);
            char nc = i < length - 1 ? code.charAt(i + 1) : 0;
            i += 1;
            switch (state) {
                case OUTSIDE_COMMENT:
                    if (c == '/' && nc != 0) {
                        i += 1;
                        if (nc == '/') {
                            state = State.INSIDE_LINECOMMENT;
                        } else if (nc == '*') {
                            state = State.INSIDE_BLOCKCOMMENT;
                        } else {
                            result.append(c).append(nc);
                        }
                    } else {
                        result.append(c);
                        if (c == '"') {
                            state = State.INSIDE_STRING;
                        }
                    }
                    break;
                case INSIDE_STRING:
                    result.append(c);
                    if (c == '"') {
                        state = State.OUTSIDE_COMMENT;
                    } else if (c == '\\' && nc != 0) {
                        i += 1;
                        result.append(nc);
                    }
                    break;
                case INSIDE_LINECOMMENT:
                    if (c == '\n' || c == '\r') {
                        state = State.OUTSIDE_COMMENT;
                        result.append(c);
                    }
                    break;
                case INSIDE_BLOCKCOMMENT:
                    if (c == '*' && nc == '/') {
                        i += 1;
                        state = State.OUTSIDE_COMMENT;
                    }
                    break;
                default:
                    throw new IllegalStateException("Unexpected value: " + state);
            }
        }
        String noCommentsJson = result.toString();
        if (code.length() != noCommentsJson.length()) {
            JsonMapperUtils.checkRemoveComments(noCommentsJson, code);
        }
        return noCommentsJson;
    }

    public static void checkRemoveComments(String noCmtJson, String originJson) {
        try {
            String json1 = mapper().writeValueAsString(mapper().readTree(originJson));
            ObjectMapper disableCmtMapper = mapper().copy().disable(ALLOW_COMMENTS);
            String json2 = disableCmtMapper.writeValueAsString(disableCmtMapper.readTree(noCmtJson));
            if (!json1.equals(json2)) {
                throw new IllegalStateException("remove json comments failed");
            }
        } catch (IOException e) {
            throw new UncheckedIOException(e);
        }
    }

    /**
     * 判断字符串是否是json结构
     */
    public static boolean isJson(String content) {
        try {
            MAPPER.readTree(content);
            return true;
        } catch (IOException e) {
            return false;
        }
    }


    public static <T> JsonParserErrorInfo parseJSON(String jsonString, Class<T> clazz) {
        ObjectMapper mapper = new ObjectMapper();
        try {
            mapper.readValue(jsonString, clazz);
        } catch (JsonProcessingException e) {
            return parserErrorInfo(jsonString, e);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null; // 当解析失败时返回null
    }


    public static JsonParserErrorInfo parserErrorInfo(String jsonString, Exception exception) {

        while (exception != null && !(exception instanceof JsonProcessingException)) {
            exception = (Exception) exception.getCause();
        }
        if (exception instanceof JsonParseException) {
            return handleJsonParseException(jsonString, (JsonParseException) exception);
        } else if (exception instanceof JsonMappingException) {
            return handleJsonMappingException(jsonString, (JsonMappingException) exception);
        } else {
            return JsonParserErrorInfo.of(exception.getMessage(), "");
        }
    }


    private static JsonParserErrorInfo handleJsonParseException(String jsonString, JsonParseException e) {
        // 处理不完整 JSON 响应逻辑
        String message = e.getOriginalMessage();
        long location = e.getLocation().getCharOffset();
        System.err.println("JSON Parse Exception: " + message + " at offset " + location);

        // 判断是否是不完整的 JSON 响应
        if (message.startsWith("Unexpected end-of-input")) {
            return JsonParserErrorInfo.of(e.getMessage(), jsonString);
        } else if (message.startsWith("Invalid") || message.startsWith("Unrecognized token")) {
            Pattern pattern = Pattern.compile("[},\n]");
            Matcher matcher = pattern.matcher(jsonString.substring((int) location));
            String errStr;
            if (matcher.find()) {
                errStr = jsonString.substring((int) location, (int) (location + matcher.start()));
            } else {
                errStr = jsonString.substring((int) location);
            }
            errStr = "Invalid value: `" + errStr + "`";
            return JsonParserErrorInfo.of(errStr, "");
        } else {
            return JsonParserErrorInfo.of(message, "");
        }
    }


    private static JsonParserErrorInfo handleJsonMappingException(String jsonString, JsonMappingException e) {
        // 处理无效 JSON 映射响应逻辑
        String message = e.getOriginalMessage();
        if (message.startsWith("Unexpected end-of-input: expected close marker for Object")) {
            return JsonParserErrorInfo.of(message, jsonString);
        }
        return JsonParserErrorInfo.of(message, "");
    }

    @Data
    public static class JsonParserErrorInfo {

        private String errStr;

        private String buffer;

        public static JsonParserErrorInfo of(String errStr, String buffer) {
            JsonParserErrorInfo info = new JsonParserErrorInfo();
            info.errStr = errStr;
            info.buffer = buffer;
            return info;
        }
    }

}
