package com.xiaohongshu.codewiz.core.dao.feedback;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xiaohongshu.codewiz.core.entity.feedback.FeedbackDO;
import com.xiaohongshu.codewiz.core.mapper.feedback.FeedbackMapper;

import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 反馈表 DAO层
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-24
 */
@Slf4j
@Service
public class FeedbackDao extends ServiceImpl<FeedbackMapper, FeedbackDO> {

    /**
     * 根据反馈ID查询反馈信息
     *
     * @param feedbackId 反馈ID
     * @return 反馈信息
     */
    public FeedbackDO queryByFeedbackId(String feedbackId) {
        LambdaQueryWrapper<FeedbackDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FeedbackDO::getFeedbackId, feedbackId);
        return this.getOne(queryWrapper);
    }

    /**
     * 保存反馈信息
     *
     * @param feedbackDO 反馈信息
     * @return 是否成功
     */
    public boolean saveFeedback(FeedbackDO feedbackDO) {
        feedbackDO.setCreateAt(System.currentTimeMillis());
        log.info("saveFeedback, feedbackDO: {}", feedbackDO);
        return this.save(feedbackDO);
    }
} 