package com.xiaohongshu.codewiz.core.service.rag.recall;

import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.xiaohongshu.codewiz.core.constant.enums.RagCaseKnowledgeEnum;
import com.xiaohongshu.codewiz.core.entity.elasticsearch.EsSearchResultDto;
import com.xiaohongshu.codewiz.core.entity.rag.FewShotCase;
import com.xiaohongshu.codewiz.core.service.elasticsearch.ElasticsearchService;
import com.xiaohongshu.codewiz.core.service.rag.RagDataContext;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2025/4/13 10:38
 */
@Slf4j
@Component
public class CommentsEsRecallStrategy extends AbstractEsRecallStrategy<FewShotCase> {

    public CommentsEsRecallStrategy(ElasticsearchService elasticsearchService) {
        super(elasticsearchService);
    }

    @Override
    protected List<FewShotCase> doRecall(RagDataContext<FewShotCase> context) {
        log.info("执行评论 ES 召回策略");
        String commentsFileName = RagCaseKnowledgeEnum.COMMENTS.getFileName();
        String cmts = context.getAnalysisQuery().getOrDefault(commentsFileName, StringUtils.EMPTY);
        int recallEsTopK = context.getTopK().getRecallEsTopK();

        List<EsSearchResultDto> esSearchResultDtoList = elasticsearchService.searchNegativeComments(cmts, recallEsTopK);
        List<FewShotCase> fewShotCases = esSearchResultDtoList.stream()
                .map(FewShotCase::convertFromEsSearchResultDto)
                .collect(Collectors.toList());
        log.info("评论 ES 召回结果数量: {}", fewShotCases.size());
        return fewShotCases;
    }
}
