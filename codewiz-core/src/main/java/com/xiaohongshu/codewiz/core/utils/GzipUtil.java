package com.xiaohongshu.codewiz.core.utils;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Base64;
import java.util.zip.GZIPInputStream;
import java.util.zip.GZIPOutputStream;

/**
 * Gzip压缩解压工具类
 *
 * <AUTHOR>
 * @since 2024-05-24
 */
public class GzipUtil {

    public static String compress(String str) throws IOException {
        return Base64.getEncoder().encodeToString(compress(str, "UTF-8"));
    }

    public static String decompress(String str) throws IOException {
        if (str == null || str.isEmpty()) {
            return null;
        }
        return decompress(Base64.getDecoder().decode(str), "UTF-8");
    }

    // 压缩为字节数组
    public static byte[] compress(String str, String charset) throws IOException {
        if (str == null || str.isEmpty()) {
            return null;
        }
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        try (GZIPOutputStream gzip = new GZIPOutputStream(out)) {
            gzip.write(str.getBytes(charset));
        }
        return out.toByteArray();
    }

    // 解压缩字节数组为字符串
    public static String decompress(byte[] bytes, String charset) throws IOException {
        if (bytes == null || bytes.length == 0) {
            return null;
        }
        ByteArrayInputStream in = new ByteArrayInputStream(bytes);
        try (GZIPInputStream gzip = new GZIPInputStream(in)) {
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            int len;
            while ((len = gzip.read(buffer)) != -1) {
                out.write(buffer, 0, len);
            }
            return out.toString(charset);
        }
    }
}