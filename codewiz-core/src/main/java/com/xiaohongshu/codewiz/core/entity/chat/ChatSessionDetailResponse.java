package com.xiaohongshu.codewiz.core.entity.chat;

import java.util.List;

import lombok.Data;

/**
 * Author: liukunpeng Date: 2025-03-10 Description:
 */
@Data
public class ChatSessionDetailResponse {
    private String sessionId;
    private List<DialogDTO> dialogs;


    @Data
    public class DialogDTO {
        private Long dialogId;
        private String userContent;
        private String assistantContent;
        private ChatDialogRequest.Extra extra;
        private Integer type;
        private Long createTime;
        private Integer feedbackType;
    }
}
