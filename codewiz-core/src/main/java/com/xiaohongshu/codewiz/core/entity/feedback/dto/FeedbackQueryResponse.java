package com.xiaohongshu.codewiz.core.entity.feedback.dto;

import lombok.Data;

/**
 * 反馈查询响应DTO
 *
 * <AUTHOR>
 * @since 2024-05-24
 */
@Data
public class FeedbackQueryResponse {

    /**
     * 反馈ID
     */
    private String feedbackId;

    /**
     * 反馈描述
     */
    private String feedbackDesc;

    /**
     * 用户邮箱
     */
    private String feedbackUserEmail;

    /**
     * 编辑器信息
     */
    private String feedbackEditor;

    /**
     * 反馈扩展信息
     */
    private String feedbackExtra;

    /**
     * 日志文件原始内容（已解压）
     */
    private String pluginLogFileContent;

    /**
     * 创建时间
     */
    private Long createAt;

    /**
     * s3 key
     */
    private String logFileKey;
}