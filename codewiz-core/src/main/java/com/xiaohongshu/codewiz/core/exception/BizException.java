package com.xiaohongshu.codewiz.core.exception;


import com.xiaohongshu.codewiz.core.constant.ErrorCodeConstant;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2023/11/10 17:25
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class BizException extends RuntimeException {
    private String message;
    private int status;

    public BizException(String message) {
        this.message = message;
        this.status = ErrorCodeConstant.SERVER_ERROR.getStatus();
    }

    public BizException(String message, Throwable cause, int status) {
        super(message, cause);
        this.message = message;
        this.status = status;
    }

    public BizException(int status, String message) {
        this.status = status;
        this.message = message;
    }

    public BizException(ErrorCodeConstant errorCodeConstant) {
        super(errorCodeConstant.getMessage());
        this.message = errorCodeConstant.getMessage();
        this.status = errorCodeConstant.getStatus();
    }
}
