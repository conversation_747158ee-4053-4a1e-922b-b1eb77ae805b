package com.xiaohongshu.codewiz.core.service.rag.recall;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.xiaohongshu.codewiz.core.client.EmbeddingClient;
import com.xiaohongshu.codewiz.core.constant.enums.RagCaseKnowledgeEnum;
import com.xiaohongshu.codewiz.core.entity.rag.FewShotCase;
import com.xiaohongshu.codewiz.core.service.milvus.CaseMilvusService;
import com.xiaohongshu.codewiz.core.service.rag.RagDataContext;

import lombok.extern.slf4j.Slf4j;

/**
 * 混合文本向量召回策略
 *
 * <AUTHOR>
 * @date 2025/4/10 11:20
 */
@Slf4j
@Component
public class HybridKnowledgeRecallStrategy extends AbstractMilvusRecallStrategy<FewShotCase> {

    public HybridKnowledgeRecallStrategy(CaseMilvusService caseMilvusService,
                                         EmbeddingClient embeddingClient) {
        super(caseMilvusService, embeddingClient);
    }

    /**
     * 实现混合文本向量召回策略
     *
     * @param context RAG数据上下文
     */
    @Override
    protected List<FewShotCase> doRecall(RagDataContext<FewShotCase> context) {
        log.info("执行混合文本向量召回策略");

        // 混合文本向量召回
        String hybridFileName = RagCaseKnowledgeEnum.HYBRID_KNOWLEDGE.getFileName();
        String hybridKnowledge = context.getAnalysisQuery().getOrDefault(hybridFileName, StringUtils.EMPTY);
        List<FewShotCase> hybridRecallResults =
                milvusVectorRecall(context, hybridKnowledge, hybridFileName);

        log.info("混合文本向量召回结果数量: {}", hybridRecallResults.size());
        return hybridRecallResults;
    }
}