package com.xiaohongshu.codewiz.core.entity.rag.scenecase;

import java.util.Map;

import com.xiaohongshu.codewiz.core.client.EmbeddingClient;
import com.xiaohongshu.codewiz.core.constant.enums.RagCaseKnowledgeEnum;
import com.xiaohongshu.codewiz.core.entity.rag.FewShotCase;

/**
 * <AUTHOR>
 * @date 2025/3/14 15:05
 */
public class CaseCommentsRequest extends CaseMilvusRequest {

    public CaseCommentsRequest(FewShotCase fewShotCase,
                               Map<String, Object> knowledge,
                               EmbeddingClient embeddingClient) {
        super(fewShotCase, knowledge, embeddingClient);
    }

    @Override
    public RagCaseKnowledgeEnum getField() {
        return RagCaseKnowledgeEnum.COMMENTS;
    }
}
