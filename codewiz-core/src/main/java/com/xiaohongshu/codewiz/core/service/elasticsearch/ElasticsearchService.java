package com.xiaohongshu.codewiz.core.service.elasticsearch;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.xiaohongshu.codewiz.core.entity.elasticsearch.EsSearchResultDto;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.SortOptions;
import co.elastic.clients.elasticsearch._types.SortOrder;
import co.elastic.clients.elasticsearch._types.mapping.KeywordProperty;
import co.elastic.clients.elasticsearch._types.mapping.LongNumberProperty;
import co.elastic.clients.elasticsearch._types.mapping.Property;
import co.elastic.clients.elasticsearch._types.mapping.TextProperty;
import co.elastic.clients.elasticsearch._types.query_dsl.MatchQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.Query;
import co.elastic.clients.elasticsearch.core.BulkRequest;
import co.elastic.clients.elasticsearch.core.BulkResponse;
import co.elastic.clients.elasticsearch.core.SearchRequest;
import co.elastic.clients.elasticsearch.core.SearchResponse;
import co.elastic.clients.elasticsearch.core.bulk.BulkResponseItem;
import co.elastic.clients.elasticsearch.core.search.Hit;
import co.elastic.clients.elasticsearch.indices.CreateIndexResponse;
import co.elastic.clients.elasticsearch.indices.ExistsRequest;
import lombok.extern.slf4j.Slf4j;

/**
 * Elasticsearch服务类
 *
 * <AUTHOR>
 * @date 2025/3/28 10:25
 */
@Slf4j
@Service
public class ElasticsearchService {

    @Autowired
    private ElasticsearchClient elasticsearchClient;

    @Value("${elasticsearch.negative-comments-index}")
    private String negativeCommentsIndex;

    /**
     * 创建负向评论索引
     *
     * @return 是否创建成功
     */
    public boolean createNegativeCommentsIndex() {
        try {
            // 检查索引是否存在
            boolean exists = elasticsearchClient.indices()
                    .exists(ExistsRequest.of(r -> r.index(negativeCommentsIndex)))
                    .value();

            if (exists) {
                log.info("索引已存在: {}", negativeCommentsIndex);
                return true;
            }

            // 构建映射
            Map<String, Property> properties = new HashMap<>();

            // 设置cmt_rule字段为text类型，用于全文检索
            properties.put("cmt_rule", Property.of(p -> p.text(TextProperty.of(t -> t
                    .analyzer("standard")))));

            // 设置create_at字段为long类型
            properties.put("create_at", Property.of(p -> p.long_(LongNumberProperty.of(l -> l))));

            // 设置language字段为keyword类型
            properties.put("language", Property.of(p -> p.keyword(KeywordProperty.of(k -> k))));

            // meta字段需要嵌套属性
            Map<String, Property> metaProperties = new HashMap<>();
            metaProperties.put("tag", Property.of(p -> p.keyword(KeywordProperty.of(k -> k))));
            metaProperties.put("cmt_id", Property.of(p -> p.keyword(KeywordProperty.of(k -> k))));
            metaProperties.put("mr_id", Property.of(p -> p.keyword(KeywordProperty.of(k -> k))));
            metaProperties.put("repo_id", Property.of(p -> p.keyword(KeywordProperty.of(k -> k))));

            // 添加meta字段
            properties.put("meta", Property.of(p -> p.object(o -> o.properties(metaProperties))));

            // 创建索引
            CreateIndexResponse response = elasticsearchClient.indices()
                    .create(c -> c
                            .index(negativeCommentsIndex)
                            .mappings(m -> m.properties(properties))
                    );

            boolean acknowledged = response.acknowledged();
            log.info("创建索引 {} {}", negativeCommentsIndex, acknowledged ? "成功" : "失败");
            return acknowledged;
        } catch (IOException e) {
            log.error("创建索引失败: {}", negativeCommentsIndex, e);
            return false;
        }
    }

    /**
     * 批量插入负向评论数据
     *
     * @param documents 文档列表
     * @return 是否插入成功
     */
    public boolean bulkInsertNegativeComments(List<Map<String, Object>> documents) {
        try {
            BulkRequest.Builder br = new BulkRequest.Builder();

            for (Map<String, Object> document : documents) {
                String id = document.containsKey("id") ? document.get("id").toString() : null;

                if (id != null) {
                    br.operations(op -> op
                            .index(idx -> idx
                                    .index(negativeCommentsIndex)
                                    .id(id)
                                    .document(document)
                            )
                    );
                } else {
                    br.operations(op -> op
                            .index(idx -> idx
                                    .index(negativeCommentsIndex)
                                    .document(document)
                            )
                    );
                }
            }

            BulkResponse response = elasticsearchClient.bulk(br.build());

            // 检查是否有错误
            if (response.errors()) {
                log.error("批量插入部分失败:");
                for (BulkResponseItem item : response.items()) {
                    if (item.error() != null) {
                        log.error("ID: {}, 错误类型: {}, 原因: {}",
                                item.id(), item.error().type(), item.error().reason());
                    }
                }
                return false;
            }

            log.info("批量插入成功，文档数量: {}", documents.size());
            return true;
        } catch (IOException e) {
            log.error("批量插入失败", e);
            return false;
        }
    }

    /**
     * 负向评论查询
     */
    public List<EsSearchResultDto> searchNegativeComments(String commentContent, Integer topNumber) {
        // 构建查询
        Query query = Query.of(q -> q
                .match(MatchQuery.of(m -> m
                        .field("cmt_rule")
                        .query(commentContent)
                ))
        );
        // 构建排序选项，按分值降序排序
        SortOptions sortOptions = SortOptions.of(s -> s
                .field(f -> f
                        .field("_score")
                        .order(SortOrder.Desc)
                )
        );
        // 构建搜索请求，设置 size 参数为 topNumber 并添加排序选项
        SearchRequest searchRequest = new SearchRequest.Builder()
                .index(getNegativeCommentsIndexName())
                .query(query)
                .size(topNumber)
                .sort(sortOptions)
                .build();
        // 返回结果封装
        List<EsSearchResultDto> results = Lists.newArrayList();
        try {
            SearchResponse<Map> response = elasticsearchClient.search(searchRequest, Map.class);
            List<Hit<Map>> hits = response.hits().hits();
            for (Hit<Map> hit : hits) {
                Map<String, Object> source = hit.source();
                if (source == null || hit.score() == null) {
                    continue;
                }
                String content = Optional.ofNullable(source.get("cmt_rule")).orElse(StringUtils.EMPTY).toString();
                String language = Optional.ofNullable(source.get("language")).orElse(StringUtils.EMPTY).toString();
                if (StringUtils.isEmpty(content) || StringUtils.isEmpty(language)) {
                    continue;
                }
                Map<String, Object> meta = (Map<String, Object>) source.getOrDefault("meta", new HashMap<>());
                EsSearchResultDto result = EsSearchResultDto.builder().score(hit.score())
                        .content(content)
                        .index(getNegativeCommentsIndexName())
                        .meta(meta)
                        .build();
                results.add(result);
            }
        } catch (Exception e) {
            log.error("es 查询失败 {}", e.getMessage());
        }
        return results;
    }

    /**
     * 获取负向评论索引名称
     *
     * @return 索引名称
     */
    public String getNegativeCommentsIndexName() {
        return negativeCommentsIndex;
    }

} 