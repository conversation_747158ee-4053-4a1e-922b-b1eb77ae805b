package com.xiaohongshu.codewiz.core.entity.rag;

import java.util.Map;

import lombok.Data;

/**
 * 负向评论实体类
 *
 * <AUTHOR>
 * @date 2025/3/28 10:15
 */
@Data
public class NegativeComment implements RagDocument {
    /**
     * ID
     */
    private String id;

    /**
     * 评论规则
     */
    private String cmtRule;

    /**
     * 创建时间
     */
    private Long createAt;

    /**
     * 语言
     */
    private String language;

    /**
     * 元数据
     */
    private Map<String, Object> meta;
} 