package com.xiaohongshu.codewiz.core.config.feign;

import java.io.IOException;

import com.dianping.cat.Cat;

import feign.Logger;
import feign.Request;
import feign.Response;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class FeignTimingLogger extends Logger {

    private static final ThreadLocal<Long> START_TIME_HOLDER = new ThreadLocal<>();

    @Override
    protected void log(String configKey, String format, Object... objects) {
        log.info(String.format(methodTag(configKey) + format, objects));
    }

    @Override
    protected void logRequest(String configKey, Level logLevel, Request request) {
        START_TIME_HOLDER.set(System.currentTimeMillis());
        super.logRequest(configKey, logLevel, request);
    }

    @Override
    protected Response logAndRebufferResponse(String configKey, Level logLevel, Response response, long elapsedTime) throws IOException {
        Long startTime = START_TIME_HOLDER.get();
        if (startTime != null) {
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            log.info("feign Request to {} took {} ms", response.request().url(), duration);
            Cat.newCompletedTransactionWithDuration("Feign", response.request().url(), duration);
        }

        return super.logAndRebufferResponse(configKey, logLevel, response, elapsedTime);
    }
}