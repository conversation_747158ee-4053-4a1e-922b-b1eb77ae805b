package com.xiaohongshu.codewiz.core.service.rag.query;

import static com.xiaohongshu.codewiz.core.service.rag.add.RagAddCrCaseService.HYBRID_KNOWLEDGE_FIELD_TEMPLATE;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xiaohongshu.codewiz.core.client.RerankClient;
import com.xiaohongshu.codewiz.core.constant.enums.BusinessTrackingLogRecordType;
import com.xiaohongshu.codewiz.core.constant.enums.RagCaseKnowledgeEnum;
import com.xiaohongshu.codewiz.core.constant.enums.RagDataTypeEnum;
import com.xiaohongshu.codewiz.core.constant.enums.RagRecallStrategyEnum;
import com.xiaohongshu.codewiz.core.dao.log.BusinessTrackingLogRecordDao;
import com.xiaohongshu.codewiz.core.entity.log.BusinessTrackingLogRecordDO;
import com.xiaohongshu.codewiz.core.entity.rag.FewShotCase;
import com.xiaohongshu.codewiz.core.entity.rag.RagDataQueryRequest;
import com.xiaohongshu.codewiz.core.entity.rag.RagDataQueryResponse;
import com.xiaohongshu.codewiz.core.entity.rerank.RerankRequestDto;
import com.xiaohongshu.codewiz.core.entity.rerank.RerankResultDto;
import com.xiaohongshu.codewiz.core.entity.rerank.RerankType;
import com.xiaohongshu.codewiz.core.service.milvus.CaseMilvusService;
import com.xiaohongshu.codewiz.core.service.rag.RagCodeExplainService;
import com.xiaohongshu.codewiz.core.service.rag.RagCollectionService;
import com.xiaohongshu.codewiz.core.service.rag.RagDataContext;
import com.xiaohongshu.codewiz.core.utils.JsonMapperUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2025/2/28 16:26
 */
@Slf4j
@Service
public class RagQueryCrCaseService extends AbsRagQueryDataService<FewShotCase> {

    @Resource
    private CaseMilvusService caseMilvusService;
    @Resource
    private RagCollectionService ragCollectionService;
    @Resource
    private RagCodeExplainService codeExplainService;
    @Resource
    private Executor ragAddDataExecutor;
    @Resource
    private Executor logRecordExecutor;
    @Resource
    private BusinessTrackingLogRecordDao businessTrackingLogRecordDao;
    @Resource
    private RerankClient rerankClient;

    @Override
    protected void check(RagDataQueryRequest request) {
        super.baseCheck(request);
        // score
        if (Objects.isNull(request.getExtension().getScoreThreshold())) {
            // 默认0.8, todo: 后续需要从apollo获取
            request.getExtension().setScoreThreshold(0.8);
        }
    }

    @Override
    protected void analyze(RagDataContext<FewShotCase> context) {

        context.setAnalysisQuery(constructAnalysisQuery(context));
        // 设置milvus topK
        context.getTopK().setRecallMilvusTopK(context.getQueryRequest().getExtension().getReturnNum() * 2);
        // 设置rerank topK
        context.getTopK().setRerankTopK(context.getQueryRequest().getExtension().getReturnNum());
        context.setCollectionName(getCollectionName(context.getQueryRequest()));
        // 设置默认召回策略
        setRecallStrategy(context, RagRecallStrategyEnum.CODE_SNIPPET_AND_EXPLAIN);
        // 设置embeddingModel
        setEmbeddingModel(context);
    }

    private Map<String, String> constructAnalysisQuery(RagDataContext<FewShotCase> context) {
        String code = context.getQueryRequest().getQuery();
        String explainCode = codeExplainService.explainCode(code);
        String codeExplainFileName = RagCaseKnowledgeEnum.CODE_EXPLAIN.getFileName();
        String codeSnippetFileName = RagCaseKnowledgeEnum.CODE_SNIPPET.getFileName();
        String hybridKnowledge = String.format(HYBRID_KNOWLEDGE_FIELD_TEMPLATE,
                codeSnippetFileName, code,
                codeExplainFileName, explainCode);
        Map<String, String> analysisQuery = Maps.newHashMap();
        analysisQuery.put(codeExplainFileName, explainCode);
        analysisQuery.put(codeSnippetFileName, code);
        analysisQuery.put(RagCaseKnowledgeEnum.HYBRID_KNOWLEDGE.getFileName(), hybridKnowledge);
        return analysisQuery;
    }

    @Override
    protected void recall(RagDataContext<FewShotCase> context) {
        super.recall(context);

        // 将各路召回的case，记录下来，完全异步执行，不需要等待执行结果，避免影响链路性能。即，尽最大可能记录即可。
        CompletableFuture.runAsync(() -> recordRecallTrackingLog(context), logRecordExecutor);
    }

    @Override
    protected void merge(RagDataContext<FewShotCase> context) {
        // 相同issue id，合并多轮召回的分数
        List<FewShotCase> documents = context.getDocuments();
        if (documents == null || documents.isEmpty()) {
            return;
        }

        Map<String, FewShotCase> mergedMap = new HashMap<>();

        for (FewShotCase fewShotCase : documents) {
            String issueId = fewShotCase.getIssueId();
            if (mergedMap.containsKey(issueId)) {
                // 如果已存在相同item_id的case，累加score
                FewShotCase existingCase = mergedMap.get(issueId);
                existingCase.setScore(existingCase.getScore() + fewShotCase.getScore());
            } else {
                // 如果是新的item_id，直接放入map
                mergedMap.put(issueId, fewShotCase);
            }
        }

        // 更新context中的documents
        context.setDocuments(new ArrayList<>(mergedMap.values()));
        log.info("after merge, case size == {}", context.getDocuments().size());
    }

    @Override
    protected void rerank(RagDataContext<FewShotCase> context) {
        int topKey = context.getTopK().getRerankTopK();
        if (context.getDocuments() == null || context.getDocuments().isEmpty()) {
            return;
        }

        // query 和case的fieldContent的相似度
        List<RerankRequestDto> requestDtos = context.getDocuments()
                .stream()
                .map(fewShotCase -> RerankRequestDto.convertFromFewShotCase(fewShotCase,
                        context.getQueryRequest().getQuery()))
                .collect(Collectors.toList());
        List<RerankResultDto> rerankResultDtoList = rerankClient.getRerankResult(requestDtos, RerankType.GTE_RERANK);
        if (CollectionUtils.isEmpty(rerankResultDtoList)) {
            log.info("rerankResultDtoList is empty");
            return;
        }
        // 设置分数
        Map<String, RerankResultDto> fieldContentToRerankResultDto = rerankResultDtoList.stream()
                .collect(Collectors.toMap(RerankResultDto::getQuery, Function.identity(), (k1, k2) -> k1));
        for (FewShotCase fewShotCase : context.getDocuments()) {
            RerankResultDto rerankResultDto = fieldContentToRerankResultDto.get(fewShotCase.getFieldContent());
            if (rerankResultDto == null) {
                continue;
            }
            fewShotCase.setScore(rerankResultDto.getScore().floatValue());
        }
        // 过滤分数，根据入参过滤
        Double scoreThreshold = context.getQueryRequest().getExtension().getScoreThreshold();
        if (scoreThreshold != null) {
            context.getDocuments().removeIf(caseData -> caseData.getScore() < scoreThreshold.floatValue());
        }
        context.getDocuments().sort(Comparator.comparingDouble(FewShotCase::getScore).reversed());
        context.setDocuments(context.getDocuments().subList(0, Math.min(topKey, context.getDocuments().size())));

        // 将各路召回的case，记录下来，完全异步执行，不需要等待执行结果，避免影响链路性能。即，尽最大可能记录即可。
        CompletableFuture.runAsync(() -> recordRerankTrackingLog(
                context.getQueryRequest().getBusinessTrackingId(),
                context.getQueryRequest().getXrayTrackingId(),
                context.getQueryRequest().getUuidTrackingId(),
                context.getDocuments()), logRecordExecutor);
    }

    @Override
    protected void filter(RagDataContext<FewShotCase> context) {
        // filter level
        filterLevel(context);

        // 移除issueDetails为空的文档
        context.getDocuments().removeIf(caseData -> CollectionUtils.isEmpty(caseData.getIssueDetails()));

        log.info("filter case size == {}", context.getDocuments().size());
    }

    @Override
    protected void format(RagDataContext<FewShotCase> context) {
        if (Objects.isNull(context.getQueryResponse())) {
            context.setQueryResponse(RagDataQueryResponse.<FewShotCase>builder()
                    .scene(context.getQueryRequest().getScene())
                    .documents(context.getDocuments())
                    .build());
        } else {
            context.getQueryResponse().setDocuments(context.getDocuments());
        }

        CompletableFuture.runAsync(() -> recordRagRequestTrackingLog(context), logRecordExecutor);
    }

    @Override
    protected String getCollectionName(RagDataQueryRequest request) {
        String collectionName = ragCollectionService.getCrCollection();
        if (StringUtils.isNotEmpty(request.getExtension().getCollectionName())) {
            collectionName = request.getExtension().getCollectionName();
        }
        return collectionName;
    }

    @Override
    public RagDataTypeEnum source() {
        return RagDataTypeEnum.CR_CASE;
    }

    private void recordRagRequestTrackingLog(RagDataContext<FewShotCase> context) {
        RagDataQueryRequest ragDataQueryRequest = context.getQueryRequest();
        RagDataQueryResponse<FewShotCase> ragDataQueryResponse = context.getQueryResponse();

        String businessTrackingId = ragDataQueryRequest.getBusinessTrackingId();
        String xrayTrackingId = ragDataQueryRequest.getXrayTrackingId();
        String uuidTrackingId = ragDataQueryRequest.getUuidTrackingId();

        log.info(
                "开始进行rag请求日志记录，用于链路追踪，businessTrackingId:{}, xrayTrackingId:{}, uuidTrackingId:{}, ragDataQueryRequest:{}",
                businessTrackingId, xrayTrackingId, uuidTrackingId, JsonMapperUtils.toJson(ragDataQueryRequest));

        BusinessTrackingLogRecordDO codeSnippetFewShotRequestRecord = buildLogRecord(businessTrackingId, xrayTrackingId,
                uuidTrackingId,
                BusinessTrackingLogRecordType.RAG_CASE_FEW_SHOT_CALL_CHAIN,
                JsonMapperUtils.toJson(ragDataQueryRequest), JsonMapperUtils.toJson(ragDataQueryResponse), new Date());

        businessTrackingLogRecordDao.saveBusinessTrackingLogRecord(codeSnippetFewShotRequestRecord);
    }

    private void recordRecallTrackingLog(RagDataContext<FewShotCase> context) {

        String businessTrackingId = context.getQueryRequest().getBusinessTrackingId();
        String xrayTrackingId = context.getQueryRequest().getXrayTrackingId();
        String uuidTrackingId = context.getQueryRequest().getUuidTrackingId();
        List<FewShotCase> fewShotCases = context.getDocuments();
        String code = context.getAnalysisQuery().getOrDefault(RagCaseKnowledgeEnum.CODE_SNIPPET.getFileName(),
                StringUtils.EMPTY);
        String explain = context.getAnalysisQuery().getOrDefault(RagCaseKnowledgeEnum.CODE_EXPLAIN.getFileName(),
                StringUtils.EMPTY);
        String hybridKnowledge = context.getAnalysisQuery()
                .getOrDefault(RagCaseKnowledgeEnum.HYBRID_KNOWLEDGE.getFileName(), StringUtils.EMPTY);

        log.info(
                "开始进行recall日志记录，用于链路追踪，businessTrackingId:{}, xrayTrackingId:{}, code:{}, explain:{}, hybridKnowledge:{}, fewShotCases:{}",
                businessTrackingId, xrayTrackingId, code, explain, hybridKnowledge,
                JsonMapperUtils.toJson(fewShotCases));

        if (CollectionUtils.isEmpty(fewShotCases)) {
            return;
        }

        Date actionDate = new Date();

        // 分别记录各路的请求&响应
        // 代码片段路召回日志
        List<FewShotCase> codeSnippetFewShots = fewShotCases.stream()
                .filter(fewShotCase -> fewShotCase.getFieldName()
                        .equals(RagCaseKnowledgeEnum.CODE_SNIPPET.getFileName()))
                .collect(Collectors.toList());
        BusinessTrackingLogRecordDO codeSnippetFewShotRequestRecord = buildLogRecord(businessTrackingId, xrayTrackingId,
                uuidTrackingId,
                BusinessTrackingLogRecordType.RAG_CODE_SNIPPET_FEW_SHOT_CALL_CHAIN, code,
                JsonMapperUtils.toJson(codeSnippetFewShots), actionDate);

        // 代码解释路召回日志
        List<FewShotCase> codeExplainFewShots = fewShotCases.stream()
                .filter(fewShotCase -> fewShotCase.getFieldName()
                        .equals(RagCaseKnowledgeEnum.CODE_EXPLAIN.getFileName()))
                .collect(Collectors.toList());
        BusinessTrackingLogRecordDO codeExplainFewShotRequestRecord = buildLogRecord(businessTrackingId, xrayTrackingId,
                uuidTrackingId,
                BusinessTrackingLogRecordType.RAG_CODE_EXPLAIN_FEW_SHOT_CALL_CHAIN, explain,
                JsonMapperUtils.toJson(codeExplainFewShots), actionDate);

        // 混合只是路召回日志
        List<FewShotCase> hybridKnowledgeFewShots = fewShotCases.stream()
                .filter(fewShotCase -> fewShotCase.getFieldName()
                        .equals(RagCaseKnowledgeEnum.HYBRID_KNOWLEDGE.getFileName()))
                .collect(Collectors.toList());
        BusinessTrackingLogRecordDO hybridKnowledgeFewShotRequestRecord = buildLogRecord(businessTrackingId,
                xrayTrackingId, uuidTrackingId,
                BusinessTrackingLogRecordType.RAG_HYBRID_KNOWLEDGE_FEW_SHOT_CALL_CHAIN, hybridKnowledge,
                JsonMapperUtils.toJson(hybridKnowledgeFewShots), actionDate);

        List<BusinessTrackingLogRecordDO> logRecords = Lists.newArrayList(
                codeSnippetFewShotRequestRecord,
                codeExplainFewShotRequestRecord,
                hybridKnowledgeFewShotRequestRecord);
        // 批量存储
        businessTrackingLogRecordDao.batchSaveBusinessTrackingLogRecord(logRecords);
    }

    private void recordRerankTrackingLog(String businessTrackingId, String xrayTrackingId, String uuidTrackingId,
                                         List<FewShotCase> fewShotCases) {

        log.info("开始进行rerank日志记录，用于链路追踪，businessTrackingId:{}, xrayTrackingId:{}, fewShotCases:{}",
                businessTrackingId, xrayTrackingId, JsonMapperUtils.toJson(fewShotCases));

        if (CollectionUtils.isEmpty(fewShotCases)) {
            return;
        }

        Date actionDate = new Date();

        // 再记录响应
        BusinessTrackingLogRecordDO rerankResponseRecord = buildLogRecord(businessTrackingId, xrayTrackingId,
                uuidTrackingId,
                BusinessTrackingLogRecordType.RAG_RERANK_FEW_SHOT_CALL_CHAIN, null,
                JsonMapperUtils.toJson(fewShotCases),
                actionDate);
        // 存储
        businessTrackingLogRecordDao.saveBusinessTrackingLogRecord(rerankResponseRecord);
    }

}
