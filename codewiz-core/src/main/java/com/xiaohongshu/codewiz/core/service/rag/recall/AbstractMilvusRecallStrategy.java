package com.xiaohongshu.codewiz.core.service.rag.recall;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xiaohongshu.codewiz.core.client.EmbeddingClient;
import com.xiaohongshu.codewiz.core.constant.enums.RagEmbeddingModelEnum;
import com.xiaohongshu.codewiz.core.entity.rag.FewShotCase;
import com.xiaohongshu.codewiz.core.entity.rag.RagDocument;
import com.xiaohongshu.codewiz.core.entity.rag.scenecase.CaseMilvusSearchResponse;
import com.xiaohongshu.codewiz.core.service.milvus.CaseMilvusService;
import com.xiaohongshu.codewiz.core.service.rag.RagDataContext;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2025/4/10 11:20
 */
@Slf4j
public abstract class AbstractMilvusRecallStrategy<T extends RagDocument> implements IRecallStrategy<T> {

    protected CaseMilvusService caseMilvusService;
    protected EmbeddingClient embeddingClient;

    public AbstractMilvusRecallStrategy(CaseMilvusService caseMilvusService,
                                        EmbeddingClient embeddingClient) {
        this.caseMilvusService = caseMilvusService;
        this.embeddingClient = embeddingClient;
    }

    /**
     * 执行召回策略
     *
     * @param context RAG数据上下文
     */
    @Override
    public List<T> recall(RagDataContext<T> context) {
        try {
            return doRecall(context);
        } catch (Exception e) {
            log.error("执行milvus召回策略失败，context: {}", context, e);
            return Lists.newArrayList();
        }
    }

    /**
     * 实际的召回策略实现
     *
     * @param context RAG数据上下文
     */
    protected abstract List<T> doRecall(RagDataContext<T> context);

    /**
     * 使用向量召回
     */
    protected List<FewShotCase> milvusVectorRecall(RagDataContext<FewShotCase> context,
                                                   String query,
                                                   String fieldNameFilter) {
        long start = System.currentTimeMillis();
        if (StringUtils.isBlank(query)) {
            return Lists.newArrayList();
        }

        String collectionName = context.getCollectionName();
        Integer topKey = context.getTopK().getRecallMilvusTopK();
        Map<String, Object> filterField = getMilvusFilterFields(context);
        Map<String, Object> localFilter = new HashMap<>(filterField);
        Double scoreThreshold = context.getQueryRequest().getExtension().getScoreThreshold();
        RagEmbeddingModelEnum embeddingModel = context.getEmbeddingModel();

        List<Float> embeddingVector = embeddingClient.getEmbeddingVector(query, embeddingModel);
        // 创建副本以避免并发修改
        localFilter.put("field_name", fieldNameFilter);
        List<CaseMilvusSearchResponse> searchResponses = caseMilvusService.search(
                collectionName, embeddingVector, topKey, localFilter, scoreThreshold, embeddingModel.getAnnField());
        log.info("field_name == {}, milvusVectorRecall searchResponses size == {}, cost == {}",
                fieldNameFilter, searchResponses.size(), System.currentTimeMillis() - start);
        return searchResponses
                .stream()
                .map(response -> convertCase(response, embeddingModel.getModel()))
                .collect(Collectors.toList());
    }

    protected Map<String, Object> getMilvusFilterFields(RagDataContext<FewShotCase> context) {
        Map<String, Object> filterFiled = Optional
                .ofNullable(context.getQueryRequest().getExtension().getFilterFields())
                .map(fields -> fields.entrySet().stream()
                        .filter(e -> getFilterFields().contains(e.getKey()))
                        .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue)))
                .orElseGet(Maps::newHashMap);
        log.info("recall filter filed == {}", filterFiled);

        return filterFiled;
    }

    protected List<String> getFilterFields() {
        return Lists.newArrayList("repo",
                "language",
                "review_type",
                "branch_name",
                "file_path");
    }

    /**
     * 转换为FewShotCase
     *
     * @param response Milvus响应
     * @return FewShotCase
     */
    protected FewShotCase convertCase(CaseMilvusSearchResponse response, String model) {
        FewShotCase fewShotCase = new FewShotCase();
        CaseMilvusSearchResponse.CaseKnowledgeSearchInfo entity = response.getEntity();
        CaseMilvusSearchResponse.ItemData itemData = entity.getItemData();

        fewShotCase.setCodeSnippet(itemData.getCodeSnippet());
        fewShotCase.setTargetFunction(itemData.getTargetFunction());
        fewShotCase.setReviewType(itemData.getReviewType());
        fewShotCase.setIssueDetails(itemData.getIssueDetail());
        fewShotCase.setRepo(itemData.getRepo());
        fewShotCase.setLanguage(itemData.getLanguage());
        fewShotCase.setExtra(itemData.getExtra() != null ? itemData.getExtra().toString() : null);
        fewShotCase.setScore(response.getScore());
        fewShotCase.setIssueId(entity.getIssueId());
        fewShotCase.setFieldName(entity.getFieldName());
        fewShotCase.setFieldContent(entity.getFieldContent());
        fewShotCase.setAnnField(response.getAnnField());
        fewShotCase.setCollectionName(response.getCollectionName());
        fewShotCase.setEmbeddingModel(model);
        return fewShotCase;
    }
}