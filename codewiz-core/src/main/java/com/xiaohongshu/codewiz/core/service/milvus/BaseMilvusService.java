package com.xiaohongshu.codewiz.core.service.milvus;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import com.google.common.collect.Lists;
import com.google.gson.JsonObject;
import com.xiaohongshu.codewiz.core.client.CaseMilvusClient;

import io.milvus.v2.common.ConsistencyLevel;
import io.milvus.v2.service.collection.request.GetLoadStateReq;
import io.milvus.v2.service.collection.request.LoadCollectionReq;
import io.milvus.v2.service.vector.request.AnnSearchReq;
import io.milvus.v2.service.vector.request.HybridSearchReq;
import io.milvus.v2.service.vector.request.InsertReq;
import io.milvus.v2.service.vector.request.SearchReq;
import io.milvus.v2.service.vector.request.UpsertReq;
import io.milvus.v2.service.vector.request.data.BaseVector;
import io.milvus.v2.service.vector.request.data.EmbeddedText;
import io.milvus.v2.service.vector.request.data.FloatVec;
import io.milvus.v2.service.vector.request.ranker.BaseRanker;
import io.milvus.v2.service.vector.request.ranker.RRFRanker;
import io.milvus.v2.service.vector.response.InsertResp;
import io.milvus.v2.service.vector.response.SearchResp;
import io.milvus.v2.service.vector.response.UpsertResp;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2025/2/25 19:11
 */
@Slf4j
@Getter
public abstract class BaseMilvusService {

    @Autowired
    private CaseMilvusClient caseMilvusClient;

    public Boolean hasCollection(String collectionName) {
        return caseMilvusClient.hasCollection(collectionName);
    }

    public <T extends MilvusEntity> InsertResp insertData(String collectionName, List<T> entities) {
        if (CollectionUtils.isEmpty(entities)) {
            throw new RuntimeException("entities is empty");
        }
        // 插入数据
        List<JsonObject> jsonObjects = Lists.newArrayList();
        for (MilvusEntity entity : entities) {
            jsonObjects.add(entity.covertJsonObject());
        }
        InsertReq request = InsertReq.builder()
                .collectionName(collectionName)
                .data(jsonObjects)
                .build();

        return caseMilvusClient.insert(request);
    }

    public <T extends MilvusEntity> UpsertResp upsertData(String collectionName, List<T> entities) {
        if (CollectionUtils.isEmpty(entities)) {
            throw new RuntimeException("entities is empty");
        }
        // 插入数据
        List<JsonObject> jsonObjects = Lists.newArrayList();
        for (MilvusEntity entity : entities) {
            jsonObjects.add(entity.covertJsonObject());
        }
        log.info("upsert data to collection {}, data size == {}", collectionName, jsonObjects.size());
        UpsertReq request = UpsertReq.builder()
                .collectionName(collectionName)
                .data(jsonObjects)
                .build();

        return caseMilvusClient.upsert(request);
    }


    public SearchResp search(String collectionName,
                             List<Float> vector,
                             Integer topK,
                             String annsField,
                             String filter) {
        FloatVec floatVec = new FloatVec(vector);
        SearchReq request = SearchReq.builder()
                .collectionName(collectionName)
                .topK(topK)
                .annsField(annsField)
                .filter(filter)
                .data(Lists.newArrayList(floatVec))
                .outputFields(getOutputFields())
                .build();
        return caseMilvusClient.search(request);
    }

    /**
     * 全文搜索+向量搜索
     */
    public SearchResp hybridSearch(String collectionName,
                                   String vectorField,
                                   List<Float> vector,
                                   String sparseField,
                                   String sparseContent,
                                   Integer topK) {
        List<BaseVector> queryDenseVectors = Collections.singletonList(new FloatVec(vector));
        List<BaseVector> querySparseVectors = Collections.singletonList(new EmbeddedText(sparseContent));
        List<AnnSearchReq> searchRequests = new ArrayList<>();

        // dense召回请求
        Map<String, Object> denseParams = new HashMap<>();
        denseParams.put("ef", 100);
        searchRequests.add(AnnSearchReq.builder()
                .vectorFieldName(vectorField)
                .vectors(queryDenseVectors)
                .params(denseParams.toString())
                .topK(topK)
                .build());

        // sparse召回请求
        Map<String, Object> sparseParams = new HashMap<>();
        sparseParams.put("drop_ratio_search", 0.0);
        sparseParams.put("min_score", 0.0);
        sparseParams.put("weight_threshold", 0.5);
        sparseParams.put("max_candidates", 100);
        sparseParams.put("min_term_frequency", 1);
        searchRequests.add(AnnSearchReq.builder()
                .vectorFieldName(sparseField)
                .vectors(querySparseVectors)
                .params(sparseParams.toString())
                .topK(topK)
                .build());

        BaseRanker reranker = new RRFRanker(100);

        HybridSearchReq hybridSearchReq = HybridSearchReq.builder()
                .collectionName(collectionName)
                .searchRequests(searchRequests)
                .ranker(reranker)
                .topK(topK)
                .consistencyLevel(ConsistencyLevel.BOUNDED)
                .build();

        return caseMilvusClient.hybridSearch(hybridSearchReq);
    }

    public Boolean getLoadState(String collectionName) {
        return getCaseMilvusClient().getLoadState(GetLoadStateReq.builder().collectionName(collectionName).build());
    }

    public void loadCollection(String collectionName) {
        getCaseMilvusClient().loadCollection(LoadCollectionReq.builder().collectionName(collectionName).build());
    }

    public abstract List<String> getOutputFields();
}
