package com.xiaohongshu.codewiz.core.client;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import io.milvus.v2.client.ConnectConfig;
import io.milvus.v2.client.MilvusClientV2;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2025/3/24 17:15
 */
@Slf4j
@Component
public class OpenMilvusClient extends MilvusClient {

    @Value("${milvus.url}")
    private String url;

    @Value("${milvus.username}")
    private String username;

    @Value("${milvus.password}")
    private String password;

    @Value("${milvus.openDbName}")
    private String openDbName;

    private volatile MilvusClientV2 client;

    @Override
    public MilvusClientV2 getClient() {
        if (client == null) {
            synchronized (this) {
                if (client == null) {
                    try {
                        ConnectConfig connectConfig = ConnectConfig.builder()
                                .uri(url)
                                .username(username)
                                .password(password)
                                .dbName(openDbName)
                                .build();

                        client = new MilvusClientV2(connectConfig);
                    } catch (Exception e) {
                        log.error("open milvus client error", e);
                        return null;
                    }
                }
            }
        }
        return client;
    }
}