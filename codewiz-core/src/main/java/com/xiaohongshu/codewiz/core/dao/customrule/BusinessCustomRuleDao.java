package com.xiaohongshu.codewiz.core.dao.customrule;

import java.util.ArrayList;
import java.util.List;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xiaohongshu.codewiz.core.constant.ChatCommonConstant;
import com.xiaohongshu.codewiz.core.entity.customrule.BusinessCustomRuleDO;
import com.xiaohongshu.codewiz.core.mapper.customrule.BusinessCustomRuleMapper;

import cn.hutool.core.collection.CollectionUtil;

/**
 * <p>
 * 会话表 mapper操作类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@Service
public class BusinessCustomRuleDao extends ServiceImpl<BusinessCustomRuleMapper, BusinessCustomRuleDO> {


    public List<BusinessCustomRuleDO> selectFirstSessionDialog(String projectId) {
        LambdaQueryWrapper<BusinessCustomRuleDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusinessCustomRuleDO::getProjectId, projectId)
                .eq(BusinessCustomRuleDO::getIsDeleted, ChatCommonConstant.NO);
        // queryWrapper.last(" limit 1");
        List<BusinessCustomRuleDO> dialogs = this.list(queryWrapper);
        return CollectionUtil.isEmpty(dialogs) ? new ArrayList<>() : dialogs;
    }
}
