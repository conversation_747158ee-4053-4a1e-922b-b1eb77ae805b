package com.xiaohongshu.codewiz.core.feign;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.xiaohongshu.codewiz.core.entity.pipeline.PipelineRunRequest;
import com.xiaohongshu.codewiz.core.entity.pipeline.PipelineRunResponse;

/**
 * <AUTHOR>
 * Created on 2025/3/17
 */
@FeignClient(name = "Pipeline", url = "${feign-client.pipeline.url}")
public interface PipelineFeignClient {
    @PostMapping("api/job/pipeline/run")
    ResponseEntity<PipelineRunResponse> pipelineRun(@RequestBody PipelineRunRequest request);
}
