package com.xiaohongshu.codewiz.core.service.milvus;

import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

import javax.annotation.Resource;

import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.xiaohongshu.codewiz.core.client.EmbeddingClient;
import com.xiaohongshu.codewiz.core.entity.rag.FewShotCase;
import com.xiaohongshu.codewiz.core.entity.rag.scenecase.CaseCodeExplainRequest;
import com.xiaohongshu.codewiz.core.entity.rag.scenecase.CaseCodeSnippetRequest;
import com.xiaohongshu.codewiz.core.entity.rag.scenecase.CaseCommentsRequest;
import com.xiaohongshu.codewiz.core.entity.rag.scenecase.CaseHybridKnowledgeRequest;
import com.xiaohongshu.codewiz.core.entity.rag.scenecase.CaseMilvusRequest;
import com.xiaohongshu.codewiz.core.entity.rag.scenecase.CaseMilvusSearchResponse;
import com.xiaohongshu.codewiz.core.service.rag.RagDataContext;
import com.xiaohongshu.codewiz.core.service.rag.RagDataContext.RagAddResult;
import com.xiaohongshu.codewiz.core.utils.JsonMapperUtils;

import io.milvus.v2.common.DataType;
import io.milvus.v2.common.IndexParam;
import io.milvus.v2.service.collection.request.AddFieldReq;
import io.milvus.v2.service.collection.request.CreateCollectionReq;
import io.milvus.v2.service.vector.response.SearchResp;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2025/2/25 19:11
 */
@Slf4j
@Service
public class CaseMilvusService extends BaseMilvusService {

    @Resource
    private EmbeddingClient embeddingClient;

    private static final int VARCHAR_FIELD_LENGTH = 256;
    private static final int VARCHAR_LONG_FIELD_LENGTH = 65535;
    private static final int MODEL_DIMENSION = 1024;

    /**
     * 删除collection
     */
    public void deleteCollection(String collectionName) {
        getCaseMilvusClient().dropCollection(collectionName);
    }

    public void insertCases(List<FewShotCase> fewShotCases,
                            String collectionName,
                            RagDataContext<FewShotCase> context) {

        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failedCount = new AtomicInteger(0);
        List<FewShotCase> failedDocuments = Lists.newArrayList();
        fewShotCases.forEach(fewShotCase -> {
            try {
                List<CaseMilvusRequest> caseMilvusRequests = List.of(
                        new CaseCodeSnippetRequest(fewShotCase, fewShotCase.getKnowledge(), embeddingClient),
                        new CaseCodeExplainRequest(fewShotCase, fewShotCase.getKnowledge(), embeddingClient),
                        new CaseHybridKnowledgeRequest(fewShotCase, fewShotCase.getKnowledge(), embeddingClient),
                        new CaseCommentsRequest(fewShotCase, fewShotCase.getKnowledge(), embeddingClient)
                );
                super.upsertData(collectionName, caseMilvusRequests);
                successCount.incrementAndGet();
            } catch (Exception e) {
                log.error("case insert failed! request == {}", JsonMapperUtils.toJson(fewShotCase), e);
                failedCount.incrementAndGet();
                failedDocuments.add(fewShotCase);
            }
        });

        context.setAddResult(RagAddResult.<FewShotCase>builder()
                .successCount(successCount.get())
                .failedCount(failedCount.get())
                .failedDocuments(failedDocuments)
                .build());

        log.info("insert data to collection {} success! successCount == {}, failedCount == {}", collectionName, successCount.get(),
                failedCount.get());
    }

    /**
     * 删除数据
     */
    public void deleteData(String collectionName, Map<String, Object> data) {

    }

    public List<CaseMilvusSearchResponse> search(String collectionName,
                                                 List<Float> vector,
                                                 Integer topK,
                                                 Map<String, Object> filterFiled,
                                                 Double scoreThreshold,
                                                 String annField) {
        if (!getLoadState(collectionName)) {
            log.info("collection {} is not loaded, load it first", collectionName);
            loadCollection(collectionName);
        }

        List<String> conditions = Lists.newArrayList();

        if (MapUtils.isNotEmpty(filterFiled)) {
            filterFiled.forEach((k, v) -> {
                conditions.add(String.format("%s==\"%s\"", k, v));
            });
        }
        String filterCondition = String.join(" and ", conditions);

        SearchResp searchResp = search(
                collectionName,
                vector,
                topK,
                annField,
                filterCondition);

        List<CaseMilvusSearchResponse> responses = Lists.newArrayList();
        for (SearchResp.SearchResult searchResult : searchResp.getSearchResults().get(0)) {
            if (scoreThreshold == null || searchResult.getScore() >= scoreThreshold) {
                responses.add(CaseMilvusSearchResponse.from(searchResult, annField, collectionName));
            }
        }

        return responses;
    }

    public void createCaseCollection(String collectionName) {

        if (getCaseMilvusClient().hasCollection(collectionName)) {
            return;
        }

        CreateCollectionReq request = CreateCollectionReq.builder()
                .collectionName(collectionName)
                .autoID(false)
                .build();

        // 定义schema
        CreateCollectionReq.CollectionSchema schema = CreateCollectionReq
                .CollectionSchema
                .builder()
                .build();

        // 定义字段
        schema.addField(AddFieldReq.builder()
                .fieldName("id")
                .dataType(DataType.VarChar)
                .isPrimaryKey(true)
                .autoID(false)
                .maxLength(VARCHAR_FIELD_LENGTH)
                .description("主键")
                .build());

        schema.addField(AddFieldReq.builder()
                .fieldName("knowledge_base")
                .dataType(DataType.VarChar)
                .maxLength(VARCHAR_FIELD_LENGTH)
                .description("知识库名称")
                .build());

        schema.addField(AddFieldReq.builder()
                .fieldName("field_name")
                .dataType(DataType.VarChar)
                .maxLength(VARCHAR_FIELD_LENGTH)
                .description("索引字段名称")
                .build());

        schema.addField(AddFieldReq.builder()
                .fieldName("field_content")
                .dataType(DataType.VarChar)
                .maxLength(VARCHAR_LONG_FIELD_LENGTH)
                .description("索引字段内容")
                // .enableAnalyzer(true)
                .build());

        schema.addField(AddFieldReq.builder()
                .fieldName("field_embedding")
                .dataType(DataType.FloatVector)
                .dimension(MODEL_DIMENSION)
                .description("索引字段向量")
                .build());

        // schema.addField(AddFieldReq.builder()
        //         .fieldName("field_sparse")
        //         .dataType(DataType.SparseFloatVector)
        //         .description("索引字段稀疏向量")
        //         .build());

        schema.addField(AddFieldReq.builder()
                .fieldName("item_id")
                .dataType(DataType.VarChar)
                .maxLength(VARCHAR_FIELD_LENGTH)
                .description("业务数据ID")
                .build());

        schema.addField(AddFieldReq.builder()
                .fieldName("item_data")
                .dataType(DataType.VarChar)
                .maxLength(VARCHAR_LONG_FIELD_LENGTH)
                .description("实际业务数据")
                .build());

        schema.addField(AddFieldReq.builder()
                .fieldName("repo")
                .dataType(DataType.VarChar)
                .maxLength(VARCHAR_FIELD_LENGTH)
                .description("代码库名称")
                .build());

        schema.addField(AddFieldReq.builder()
                .fieldName("language")
                .dataType(DataType.VarChar)
                .maxLength(VARCHAR_FIELD_LENGTH)
                .description("编程语言")
                .build());

        schema.addField(AddFieldReq.builder()
                .fieldName("review_type")
                .dataType(DataType.VarChar)
                .maxLength(VARCHAR_FIELD_LENGTH)
                .description("审查类型")
                .build());

        schema.addField(AddFieldReq.builder()
                .fieldName("case_tag")
                .dataType(DataType.VarChar)
                .maxLength(VARCHAR_FIELD_LENGTH)
                .description("case标签")
                .build());

        schema.addField(AddFieldReq.builder()
                .fieldName("branch_name")
                .dataType(DataType.VarChar)
                .maxLength(VARCHAR_FIELD_LENGTH)
                .description("仓库分支")
                .build());

        schema.addField(AddFieldReq.builder()
                .fieldName("file_path")
                .dataType(DataType.VarChar)
                .maxLength(VARCHAR_FIELD_LENGTH)
                .description("问题所在文件路径")
                .build());

        schema.addField(AddFieldReq.builder()
                .fieldName("issue_id")
                .dataType(DataType.VarChar)
                .maxLength(VARCHAR_FIELD_LENGTH)
                .description("case来源问题的唯一id")
                .build());

        schema.addField(AddFieldReq.builder()
                .fieldName("extra")
                .dataType(DataType.VarChar)
                .maxLength(VARCHAR_LONG_FIELD_LENGTH)
                .description("额外扩展字段")
                .build());

        // 稀疏向量生成函数
        // schema.addFunction(CreateCollectionReq.Function.builder()
        //         .functionType(FunctionType.BM25)
        //         .name("field_content_bm25_emb")
        //         .inputFieldNames(Collections.singletonList("field_content"))
        //         .outputFieldNames(Collections.singletonList("field_sparse"))
        //         .build());

        // 定义向量索引
        IndexParam indexParam = IndexParam.builder()
                .fieldName("field_embedding")
                .indexType(IndexParam.IndexType.HNSW)
                .metricType(IndexParam.MetricType.COSINE)
                .indexName("field_embedding_index")
                .extraParams(Map.of("M", "32", "efConstruction", "500"))
                .build();

        // 稀疏向量索引
        // IndexParam sparseIndexParam = IndexParam.builder()
        //         .fieldName("field_sparse")
        //         .indexType(IndexParam.IndexType.SPARSE_INVERTED_INDEX)
        //         .metricType(IndexParam.MetricType.BM25)
        //         .indexName("field_sparse_index")
        //         .extraParams(Map.of("inverted_index_algo", "DAAT_MAXSCORE", "k1", "1.3", "b", "0.3"))
        //         .build();

        request.setIndexParams(Lists.newArrayList(indexParam));
        request.setCollectionSchema(schema);
        getCaseMilvusClient().createCollection(request);
        log.info("create collection {} success!", collectionName);
    }

    @Override
    public List<String> getOutputFields() {
        return Lists.newArrayList(
                "item_data",
                "field_name",
                "field_content",
                "knowledge_base",
                "item_id",
                "issue_id"
        );
    }
}
