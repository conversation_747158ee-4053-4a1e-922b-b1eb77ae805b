package com.xiaohongshu.codewiz.core.entity.allin;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2025/3/3
 */
@Data
public class ChatCompletionResponseDTO {
    private String id;
    private long created;
    private List<Choice> choices;
    private Usage usage;
    private String model;
    private String baseUrl;

    @Data
    public static class Choice {
        private int index;
        @JsonProperty("finish_reason")
        private String finishReason;
        private ChatMessage message;
        @JsonProperty("matched_stop")
        private String matchedStop;
        private ChatMessage delta;
    }

    @Data
    public static class Usage {
        @JsonProperty("completion_tokens")
        private Integer completionTokens;
        @JsonProperty("prompt_tokens")
        private Integer promptTokens;
        @JsonProperty("total_tokens")
        private Integer totalTokens;
    }

    @Data
    public static class ChatMessage {
        private String role;
        private String content;
    }
}