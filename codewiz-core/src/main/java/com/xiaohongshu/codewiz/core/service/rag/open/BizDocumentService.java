package com.xiaohongshu.codewiz.core.service.rag.open;

import static com.xiaohongshu.codewiz.core.constant.BizDocumentConstant.CHUNK;
import static com.xiaohongshu.codewiz.core.constant.BizDocumentConstant.CHUNK_ID;
import static com.xiaohongshu.codewiz.core.constant.BizDocumentConstant.COLLECTION_TAG_PRIVATE;
import static com.xiaohongshu.codewiz.core.constant.BizDocumentConstant.CONTENT;
import static com.xiaohongshu.codewiz.core.constant.BizDocumentConstant.CREATED_AT;
import static com.xiaohongshu.codewiz.core.constant.BizDocumentConstant.DOC;
import static com.xiaohongshu.codewiz.core.constant.BizDocumentConstant.DOC_ID;
import static com.xiaohongshu.codewiz.core.constant.BizDocumentConstant.DOC_TYPE;
import static com.xiaohongshu.codewiz.core.constant.BizDocumentConstant.EMBEDDING;
import static com.xiaohongshu.codewiz.core.constant.BizDocumentConstant.EXTRA;
import static com.xiaohongshu.codewiz.core.constant.BizDocumentConstant.FILE_PATH;
import static com.xiaohongshu.codewiz.core.constant.BizDocumentConstant.FILE_TYPE;
import static com.xiaohongshu.codewiz.core.constant.BizDocumentConstant.ID_PREFIX_CHUNK;
import static com.xiaohongshu.codewiz.core.constant.BizDocumentConstant.ID_PREFIX_DOC;
import static com.xiaohongshu.codewiz.core.constant.BizDocumentConstant.INDEX;
import static com.xiaohongshu.codewiz.core.constant.BizDocumentConstant.KB_ID;
import static com.xiaohongshu.codewiz.core.constant.BizDocumentConstant.PROJECT_ID;
import static com.xiaohongshu.codewiz.core.constant.BizDocumentConstant.TAG;
import static com.xiaohongshu.codewiz.core.constant.BizDocumentConstant.UPDATED_AT;
import static com.xiaohongshu.codewiz.core.constant.BizDocumentConstant.USER_PROJECT_ID;

import java.text.Format;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.xiaohongshu.codewiz.core.client.EmbeddingClient;
import com.xiaohongshu.codewiz.core.constant.ErrorCodeConstant;
import com.xiaohongshu.codewiz.core.constant.enums.OpenKnowledgeTypeEnum;
import com.xiaohongshu.codewiz.core.entity.rag.open.BizDocumentChunkDeleteReq;
import com.xiaohongshu.codewiz.core.entity.rag.open.BizDocumentChunkUpdateReq;
import com.xiaohongshu.codewiz.core.entity.rag.open.BizDocumentChunkUpdateResp;
import com.xiaohongshu.codewiz.core.entity.rag.open.BizDocumentChunksResp;
import com.xiaohongshu.codewiz.core.entity.rag.open.BizDocumentCreateOrUpdateReq;
import com.xiaohongshu.codewiz.core.entity.rag.open.BizDocumentDeleteReq;
import com.xiaohongshu.codewiz.core.entity.rag.open.BizDocumentListResp;
import com.xiaohongshu.codewiz.core.entity.rag.open.BizDocumentUpdateResp;
import com.xiaohongshu.codewiz.core.entity.rag.open.BizDocumentUploadResp;
import com.xiaohongshu.codewiz.core.exception.BizException;
import com.xiaohongshu.codewiz.core.service.milvus.BizMilvusService;
import com.xiaohongshu.codewiz.core.service.rag.open.split.BizDocumentChunkSplitStrategy;
import com.xiaohongshu.infra.utils.ObjectMapperUtils;

import io.milvus.v2.service.vector.response.QueryResp;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2025/3/24 15:55
 */
@Service
@Slf4j
public class BizDocumentService {
    public static final int MAX_CONTENT_LENGTH = 65535;
    @Resource
    Map<String, BizDocumentChunkSplitStrategy> strategyMap;
    @Resource
    EmbeddingClient embeddingClient;
    @Resource
    BizMilvusService bizMilvusService;
    @Resource
    BizPermissionService bizPermissionService;

    public BizDocumentUploadResp uploadDocument(BizDocumentCreateOrUpdateReq req) {
        checkParams(req, true);
        bizPermissionService.checkPermission(req.getBizId(), true);
        return upload(req, ID_PREFIX_DOC + UUID.randomUUID());
    }

    private BizDocumentUploadResp upload(BizDocumentCreateOrUpdateReq req, String docId) {
        log.info("upload document: {}, {}", ObjectMapperUtils.toJSON(req), docId);
        // 根据文件类型选择合适的切分策略进行文档分块，使用策略设计模式，根据文件类型选择合适的切分策略
        // TODO 目前只实现了200行切分的基础策略
        BizDocumentChunkSplitStrategy bizDocumentChunkSplitStrategy = strategyMap.get(req.getFileType());
        if (bizDocumentChunkSplitStrategy == null) {
            bizDocumentChunkSplitStrategy = strategyMap.get("base");
        }
        List<String> chunks = bizDocumentChunkSplitStrategy.split(req.getContent());

        // 为每个分块生成向量嵌入，复用目前已有的获取向量的接口
        int index = 0;
        Gson gson = new Gson();
        List<JsonObject> list = new ArrayList<>();
        // chunk记录
        for (String chunk : chunks) {
            List<Float> embeddingVector = embeddingClient.getBgeEmbeddingVector(chunk);
            JsonObject jsonObject = new JsonObject();
            jsonObject.addProperty(CHUNK_ID, ID_PREFIX_CHUNK + UUID.randomUUID());
            jsonObject.addProperty(USER_PROJECT_ID, req.getUserId() + "_" + req.getProjectId());
            jsonObject.addProperty(PROJECT_ID, req.getProjectId());
            jsonObject.addProperty(KB_ID, req.getKbId());
            jsonObject.addProperty(DOC_ID, docId);
            jsonObject.addProperty(DOC_TYPE, CHUNK);
            jsonObject.addProperty(CONTENT, chunk);
            jsonObject.addProperty(FILE_PATH, req.getFilePath());
            jsonObject.addProperty(FILE_TYPE, req.getFileType());
            jsonObject.addProperty(INDEX, index++);
            jsonObject.addProperty(TAG, "");
            jsonObject.addProperty(EXTRA, "");
            long current = System.currentTimeMillis();
            jsonObject.addProperty(CREATED_AT, current);
            jsonObject.addProperty(UPDATED_AT, current);
            jsonObject.add(EMBEDDING, gson.toJsonTree(embeddingVector));
            list.add(jsonObject);
        }
        // 文件本身记录
        JsonObject jsonObject = new JsonObject();
        jsonObject.addProperty(CHUNK_ID, docId);
        jsonObject.addProperty(USER_PROJECT_ID, req.getUserId() + "_" + req.getProjectId());
        jsonObject.addProperty(PROJECT_ID, req.getProjectId());
        jsonObject.addProperty(KB_ID, req.getKbId());
        jsonObject.addProperty(DOC_ID, docId);
        jsonObject.addProperty(DOC_TYPE, DOC);
        jsonObject.addProperty(CONTENT, req.getContent());
        jsonObject.addProperty(FILE_PATH, req.getFilePath());
        jsonObject.addProperty(FILE_TYPE, req.getFileType());
        jsonObject.addProperty(INDEX, 0);
        jsonObject.addProperty(TAG, "");
        jsonObject.addProperty(EXTRA, "");
        long current = System.currentTimeMillis();
        jsonObject.addProperty(CREATED_AT, current);
        jsonObject.addProperty(UPDATED_AT, current);
        jsonObject.add(EMBEDDING, gson.toJsonTree(embeddingClient.getBgeEmbeddingVector(req.getContent())));
        list.add(jsonObject);

        // 将向量及元数据插入Milvus，相关的向量和标量根据collection的schema进行确定
        bizMilvusService.insert(req.getKbId(), list);

        BizDocumentUploadResp resp = new BizDocumentUploadResp();
        resp.setDocId(docId);
        List<String> chunkIds = list.stream().filter(l -> l.get(DOC_TYPE).getAsString().equals(CHUNK))
                .map(l -> l.get(CHUNK_ID).getAsString()).collect(Collectors.toList());
        resp.setChunkIds(chunkIds);
        return resp;
    }

    private static void checkParams(BizDocumentCreateOrUpdateReq req, boolean create) {
        if (StringUtils.isBlank(req.getBizId())) {
            throw new BizException(ErrorCodeConstant.OPEN_RAG_BIZ_ID_ERROR);
        }
        if (StringUtils.isBlank(req.getKbType())) {
            throw new BizException(ErrorCodeConstant.OPEN_RAG_KB_TYPE_ERROR);
        }
        if (StringUtils.isBlank(req.getKbId())) {
            throw new BizException(ErrorCodeConstant.OPEN_RAG_KB_ID_ERROR);
        }
        if (StringUtils.isBlank(req.getFilePath())) {
            throw new BizException(ErrorCodeConstant.OPEN_RAG_FILE_PATH_ERROR);
        }
        if (StringUtils.isBlank(req.getFileType())) {
            throw new BizException(ErrorCodeConstant.OPEN_RAG_FILE_TYPE_ERROR);
        }
        if (StringUtils.isBlank(req.getContent())) {
            throw new BizException(ErrorCodeConstant.OPEN_RAG_CONTENT_ERROR);
        }
        if (req.getContent().length() > BizMilvusService.VARCHAR_LONG_FIELD_LENGTH) {
            throw new BizException(ErrorCodeConstant.OPEN_RAG_CONTENT_LENGTH_LIMIT);
        }
        String kbId = req.getKbId();
        if (OpenKnowledgeTypeEnum.PRIVATE.getValue().equalsIgnoreCase(req.getKbType())) {
            if (StringUtils.isBlank(req.getUserId())) {
                throw new BizException(ErrorCodeConstant.OPEN_RAG_USER_ID_ERROR);
            }
            if (StringUtils.isBlank(req.getProjectId())) {
                throw new BizException(ErrorCodeConstant.OPEN_RAG_PROJECT_ID_ERROR);
            }
            if (!kbId.contains(COLLECTION_TAG_PRIVATE)) {
                log.warn("知识库ID格式错误: {}", kbId);
                throw new BizException(ErrorCodeConstant.OPEN_RAG_KB_ID_INVALID_ERROR);
            }
            // 验证知识库ID格式是否正确（格式应为：{bizId}_private_{hash}）
            String[] parts = kbId.split(COLLECTION_TAG_PRIVATE);
            if (parts.length != 2 || !parts[0].equals(req.getBizId())) {
                log.warn("知识库ID格式错误: {}", kbId);
                throw new BizException(ErrorCodeConstant.OPEN_RAG_KB_ID_INVALID_ERROR);
            }
        } else if (OpenKnowledgeTypeEnum.PUBLIC.getValue().equalsIgnoreCase(req.getKbType())) {
            if (!kbId.contains("_public")) {
                log.warn("知识库ID格式错误: {}", kbId);
                throw new BizException(ErrorCodeConstant.OPEN_RAG_KB_ID_INVALID_ERROR);
            }
        } else {
            log.warn("未知知识库类型: {}", kbId);
            throw new BizException(ErrorCodeConstant.OPEN_RAG_KB_ID_INVALID_ERROR);
        }

        if (!create) {
            if (StringUtils.isBlank(req.getDocId())) {
                throw new BizException(ErrorCodeConstant.OPEN_RAG_DOC_ID_ERROR);
            }
        }
    }

    public BizDocumentUpdateResp updateDocument(BizDocumentCreateOrUpdateReq req) {
        checkParams(req, false);
        bizPermissionService.checkPermission(req.getBizId(), true);

        log.info("update document: {}", ObjectMapperUtils.toJSON(req));
        String chunkId = req.getChunkId();
        String docId = req.getDocId();

        boolean hasDoc = bizMilvusService.hasDoc(req.getKbId(), docId);
        if (!hasDoc) {
            throw new BizException(ErrorCodeConstant.OPEN_RAG_DOC_ERROR);
        }

        // 如果chunk_id为空，则认为更新的是文件本身，否则认为更新的是指定chunk
        if (StringUtils.isBlank(chunkId)) {
            // 先把文件及其下面的chunk删除，然后重新插入文件本身和chunk，插入逻辑跟upload一致
            bizMilvusService.deleteDocAndChunk(req.getKbId(), docId, chunkId);

            BizDocumentUploadResp upload = upload(req, docId);
            BizDocumentUpdateResp resp = new BizDocumentUpdateResp();
            resp.setDocId(req.getDocId());
            resp.setChunkIds(upload.getChunkIds());
            return resp;
        }
        // 如果更新的是指定chunk，此时chunk id不为空，则更新指定chunk的记录

        Map<String, Object> info = bizMilvusService.queryByChunkId(req.getKbId(), chunkId);
        if (info.isEmpty()) {
            throw new BizException(ErrorCodeConstant.OPEN_RAG_CHUNK_ERROR);
        }

        Gson gson = new Gson();
        List<Float> embeddingVector = embeddingClient.getBgeEmbeddingVector(req.getContent());
        JsonObject jsonObject = new JsonObject();
        jsonObject.addProperty(CHUNK_ID, chunkId);
        jsonObject.addProperty(USER_PROJECT_ID, req.getUserId() + "_" + req.getProjectId());
        jsonObject.addProperty(PROJECT_ID, req.getProjectId());
        jsonObject.addProperty(KB_ID, req.getKbId());
        jsonObject.addProperty(DOC_ID, docId);
        jsonObject.addProperty(DOC_TYPE, CHUNK);
        jsonObject.addProperty(CONTENT, req.getContent());
        jsonObject.addProperty(FILE_PATH, req.getFilePath());
        jsonObject.addProperty(FILE_TYPE, req.getFileType());
        Optional.ofNullable(info.get(INDEX)).ifPresent(data -> jsonObject.addProperty(INDEX, (Long) data));
        jsonObject.addProperty(TAG, "");
        jsonObject.addProperty(EXTRA, "");
        long current = System.currentTimeMillis();
        Optional.ofNullable(info.get(CREATED_AT)).ifPresent(data -> jsonObject.addProperty(CREATED_AT, (Long) data));
        jsonObject.addProperty(UPDATED_AT, current);
        jsonObject.add(EMBEDDING, gson.toJsonTree(embeddingVector));
        bizMilvusService.updateChunk(req.getKbId(), jsonObject);
        BizDocumentUpdateResp resp = new BizDocumentUpdateResp();
        resp.setDocId(req.getDocId());
        resp.setChunkIds(List.of(chunkId));
        return resp;
    }

    public void deleteDocument(BizDocumentDeleteReq req) {
        if (StringUtils.isBlank(req.getDocId())) {
            throw new BizException(ErrorCodeConstant.OPEN_RAG_DOC_ID_ERROR);
        }
        if (StringUtils.isBlank(req.getKbId())) {
            throw new BizException(ErrorCodeConstant.OPEN_RAG_KB_ID_ERROR);
        }
        log.info("delete document: {}", ObjectMapperUtils.toJSON(req));
        bizPermissionService.checkPermission(req.getBizId(), true);

        String chunkId = req.getChunkId();
        String docId = req.getDocId();
        String kbId = req.getKbId();
        if (StringUtils.isBlank(chunkId)) {
            bizMilvusService.deleteDoc(kbId, docId);
        } else {
            bizMilvusService.deleteChunk(kbId, chunkId);
        }
    }

    public BizDocumentListResp listDocuments(String bizId, String userId, String kbId, Integer page, Integer pageSize) {
        if (page == null || page < 1) {
            page = 1;
        }
        if (pageSize == null || pageSize < 1) {
            pageSize = 10;
        }
        if (pageSize > 500) {
            pageSize = 500;
        }
        if (StringUtils.isBlank(kbId)) {
            throw new BizException(ErrorCodeConstant.OPEN_RAG_KB_ID_ERROR);
        }
        bizPermissionService.checkPermission(bizId, false);

        log.info("list documents: {}, {}, {}, {}, {}", bizId, userId, kbId, page, pageSize);
        List<QueryResp.QueryResult> queryResults = bizMilvusService.listDocuments(kbId, page, pageSize);
        ArrayList<BizDocumentListResp.Document> documents = new ArrayList<>();
        for (QueryResp.QueryResult queryResult : queryResults) {
            Map<String, Object> entity = queryResult.getEntity();
            BizDocumentListResp.Document document = new BizDocumentListResp.Document();
            document.setDocId((String) entity.get(DOC_ID));
            document.setFilePath((String) entity.get(FILE_PATH));
            document.setFileType((String) entity.get(FILE_TYPE));
            document.setCreatedAt(convertTime((Long) entity.get(CREATED_AT)));
            document.setUpdatedAt(convertTime((Long) entity.get(UPDATED_AT)));
            documents.add(document);

            List<QueryResp.QueryResult> documentChunks = bizMilvusService.getDocumentChunks(kbId, document.getDocId());
            if (CollectionUtils.isNotEmpty(documentChunks)) {
                document.setChunkIds(documentChunks.stream().map(q -> (String) q.getEntity().get(CHUNK_ID)).collect(Collectors.toList()));
            }
        }
        BizDocumentListResp resp = new BizDocumentListResp();
        resp.setTotal(bizMilvusService.countDocuments(kbId));
        resp.setDocuments(documents);
        return resp;
    }

    public String convertTime(long time) {
        Date date = new Date(time);
        Format format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
        return format.format(date);
    }

    public BizDocumentChunksResp getDocumentChunks(String bizId, String userId, String kbId, String docId) {
        if (StringUtils.isBlank(kbId)) {
            throw new BizException(ErrorCodeConstant.OPEN_RAG_KB_ID_ERROR);
        }
        if (StringUtils.isBlank(docId)) {
            throw new BizException(ErrorCodeConstant.OPEN_RAG_DOC_ID_ERROR);
        }
        log.info("get document chunks: {}, {}, {}, {}", bizId, userId, kbId, docId);
        bizPermissionService.checkPermission(bizId, false);

        ArrayList<BizDocumentChunksResp.Chunk> chunks = new ArrayList<>();
        List<QueryResp.QueryResult> documentChunks = bizMilvusService.getDocumentChunks(kbId, docId);
        for (QueryResp.QueryResult documentChunk : documentChunks) {
            Map<String, Object> entity = documentChunk.getEntity();
            BizDocumentChunksResp.Chunk chunk = new BizDocumentChunksResp.Chunk();
            chunk.setChunkId((String) entity.get(CHUNK_ID));
            chunk.setContent((String) entity.get(CONTENT));
            chunk.setIndex((Long) entity.get(INDEX));
            chunks.add(chunk);
        }

        BizDocumentChunksResp resp = new BizDocumentChunksResp();
        resp.setChunks(chunks);
        return resp;
    }

    public void deleteDocumentChunk(BizDocumentChunkDeleteReq req) {
        if (StringUtils.isBlank(req.getChunkId())) {
            throw new BizException(ErrorCodeConstant.OPEN_RAG_CHUNK_ID_ERROR);
        }
        if (StringUtils.isBlank(req.getKbId())) {
            throw new BizException(ErrorCodeConstant.OPEN_RAG_KB_ID_ERROR);
        }
        log.info("delete document chunk: {}", ObjectMapperUtils.toJSON(req));
        bizPermissionService.checkPermission(req.getBizId(), true);
        String chunkId = req.getChunkId();
        bizMilvusService.deleteChunk(req.getKbId(), chunkId);
    }

    public BizDocumentChunkUpdateResp updateDocumentChunk(BizDocumentChunkUpdateReq req) {
        if (StringUtils.isBlank(req.getChunkId())) {
            throw new BizException(ErrorCodeConstant.OPEN_RAG_CHUNK_ID_ERROR);
        }
        if (StringUtils.isBlank(req.getKbId())) {
            throw new BizException(ErrorCodeConstant.OPEN_RAG_KB_ID_ERROR);
        }

        log.info("update document chunk: {}", ObjectMapperUtils.toJSON(req));
        bizPermissionService.checkPermission(req.getBizId(), true);
        String chunkId = req.getChunkId();

        Map<String, Object> info = bizMilvusService.queryByChunkId(req.getKbId(), chunkId);
        if (info.isEmpty()) {
            throw new BizException(ErrorCodeConstant.OPEN_RAG_CHUNK_ERROR);
        }
        Gson gson = new Gson();
        List<Float> embeddingVector = embeddingClient.getBgeEmbeddingVector(req.getContent());
        JsonObject jsonObject = new JsonObject();
        jsonObject.addProperty(CHUNK_ID, chunkId);
        Optional.ofNullable(info.get(USER_PROJECT_ID)).ifPresent(data -> jsonObject.addProperty(USER_PROJECT_ID, (String) data));
        Optional.ofNullable(info.get(PROJECT_ID)).ifPresent(data -> jsonObject.addProperty(PROJECT_ID, (String) data));
        jsonObject.addProperty(KB_ID, req.getKbId());
        jsonObject.addProperty(DOC_ID, req.getDocId());
        jsonObject.addProperty(DOC_TYPE, CHUNK);
        jsonObject.addProperty(CONTENT, req.getContent());
        Optional.ofNullable(info.get(FILE_PATH)).ifPresent(data -> jsonObject.addProperty(FILE_PATH, (String) data));
        Optional.ofNullable(info.get(FILE_TYPE)).ifPresent(data -> jsonObject.addProperty(FILE_TYPE, (String) data));
        Optional.ofNullable(info.get(INDEX)).ifPresent(data -> jsonObject.addProperty(INDEX, (Long) data));
        jsonObject.addProperty(TAG, "");
        jsonObject.addProperty(EXTRA, "");
        long current = System.currentTimeMillis();
        Optional.ofNullable(info.get(CREATED_AT)).ifPresent(data -> jsonObject.addProperty(CREATED_AT, (Long) data));
        jsonObject.addProperty(UPDATED_AT, current);
        jsonObject.add(EMBEDDING, gson.toJsonTree(embeddingVector));
        bizMilvusService.updateChunk(req.getKbId(), jsonObject);

        BizDocumentChunkUpdateResp resp = new BizDocumentChunkUpdateResp();
        resp.setChunkId(chunkId);
        return resp;
    }
}