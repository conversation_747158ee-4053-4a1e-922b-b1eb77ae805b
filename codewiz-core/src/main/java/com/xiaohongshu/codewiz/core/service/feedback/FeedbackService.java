package com.xiaohongshu.codewiz.core.service.feedback;

import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.xiaohongshu.codewiz.core.dao.feedback.FeedbackDao;
import com.xiaohongshu.codewiz.core.entity.common.SingleResponse;
import com.xiaohongshu.codewiz.core.entity.feedback.FeedbackDO;
import com.xiaohongshu.codewiz.core.entity.feedback.dto.FeedbackInfo;
import com.xiaohongshu.codewiz.core.entity.feedback.dto.FeedbackReportRequest;
import com.xiaohongshu.codewiz.core.entity.feedback.dto.FeedbackQueryResponse;
import com.xiaohongshu.codewiz.core.utils.JsonMapperUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 反馈业务服务
 *
 * <AUTHOR>
 * @since 2024-05-24
 */
@Slf4j
@Service
public class FeedbackService {

    @Autowired
    private FeedbackDao feedbackDao;

    /**
     * 提交反馈
     *
     * @param request    反馈请求
     * @param logFileKey S3上传后的key
     * @return 提交结果
     */
    public SingleResponse<Void> reportFeedback(FeedbackReportRequest request, String logFileKey) {
        try {
            // 1. 参数校验
            if (StringUtils.isBlank(request.getFeedbackId())) {
                return SingleResponse.buildFailure("400", "Feedback ID cannot be empty");
            }

            if (request.getFeedback() == null) {
                return SingleResponse.buildFailure("400", "Feedback cannot be empty");
            }

            Map<String, Object> feedback = request.getFeedback();

            // 2. 检查反馈ID是否已存在
            FeedbackDO existingFeedback = feedbackDao.queryByFeedbackId(request.getFeedbackId());
            if (existingFeedback != null) {
                log.warn("Feedback ID already exists: {}", request.getFeedbackId());
                return SingleResponse.buildFailure("409", "Feedback ID already exists");
            }

            // 3. 构建额外信息JSON，替换pluginLogFile为S3 key
            if (feedback.containsKey("pluginLogFile")) {
                feedback.put("pluginLogFile", logFileKey);
            }
            // String extraJson = JsonMapperUtils.toJson(feedback);

            // 4. 保存到数据库
            FeedbackDO feedbackDO = new FeedbackDO();
            feedbackDO.setFeedbackId(request.getFeedbackId());
            feedbackDO.setFeedbackDesc(feedback.getOrDefault("description", "").toString());
            feedbackDO.setFeedbackLogFile(logFileKey);
            feedbackDO.setFeedbackUserEmail(feedback.getOrDefault("userEmail", "").toString());
            feedbackDO.setFeedbackEditor(feedback.getOrDefault("editor", "").toString());
            feedbackDO.setFeedbackExtra("");

            boolean saved = feedbackDao.saveFeedback(feedbackDO);
            if (!saved) {
                log.error("Failed to save feedback to database: {}", request.getFeedbackId());
                return SingleResponse.buildFailure("500", "Failed to save feedback");
            }

            log.info("reportFeedback success, feedbackId: {}, logFileKey: {}", request.getFeedbackId(), logFileKey);
            return SingleResponse.ok();

        } catch (Exception e) {
            log.error("Report feedback error, feedbackId: {}", request.getFeedbackId(), e);
            return SingleResponse.buildFailure("500", "Report feedback error: " + e.getMessage());
        }
    }

    /**
     * 根据反馈ID查询反馈信息
     *
     * @param feedbackId 反馈ID
     * @return 反馈信息
     */
    public SingleResponse<FeedbackQueryResponse> queryFeedback(String feedbackId) {
        try {
            // 1. 参数校验
            if (StringUtils.isBlank(feedbackId)) {
                return SingleResponse.buildFailure("400", "Feedback ID cannot be empty");
            }

            // 2. 从数据库查询反馈信息
            FeedbackDO feedbackDO = feedbackDao.queryByFeedbackId(feedbackId);
            if (feedbackDO == null) {
                log.warn("Feedback not found: {}", feedbackId);
                return SingleResponse.buildFailure("404", "Feedback not found");
            }

            // 3. 构建响应对象
            FeedbackQueryResponse response = new FeedbackQueryResponse();
            response.setFeedbackId(feedbackDO.getFeedbackId());
            response.setFeedbackDesc(feedbackDO.getFeedbackDesc());
            response.setFeedbackUserEmail(feedbackDO.getFeedbackUserEmail());
            response.setFeedbackEditor(feedbackDO.getFeedbackEditor());
            response.setFeedbackExtra(feedbackDO.getFeedbackExtra());
            response.setLogFileKey(feedbackDO.getFeedbackLogFile());
            response.setCreateAt(feedbackDO.getCreateAt());

            log.info("queryFeedback success, feedbackId: {}", feedbackId);
            return SingleResponse.of(response);

        } catch (Exception e) {
            log.error("Query feedback error, feedbackId: {}", feedbackId, e);
            return SingleResponse.buildFailure("500", "Query feedback error: " + e.getMessage());
        }
    }
}