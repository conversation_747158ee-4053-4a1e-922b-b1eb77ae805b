package com.xiaohongshu.codewiz.core.entity.rag.open;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/3/24 15:53
 */
@Data
public class BizSearchReq {
    @JsonProperty("biz_id")
    private String bizId;
    @JsonProperty("user_id")
    private String userId;
    @JsonProperty("project_id")
    private String projectId;
    @JsonProperty("project_ids")
    private List<String> projectIds;
    @JsonProperty("search_type")
    private Integer searchType;
    private String query;
    private List<String> kbIds;
    @JsonProperty("top_k")
    private Integer topK;
    private Map<String, Object> filter = new HashMap<>();
} 