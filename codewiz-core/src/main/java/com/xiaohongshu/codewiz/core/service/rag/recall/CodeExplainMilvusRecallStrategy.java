package com.xiaohongshu.codewiz.core.service.rag.recall;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;
import com.xiaohongshu.codewiz.core.client.EmbeddingClient;
import com.xiaohongshu.codewiz.core.constant.enums.RagCaseKnowledgeEnum;
import com.xiaohongshu.codewiz.core.entity.rag.FewShotCase;
import com.xiaohongshu.codewiz.core.service.milvus.CaseMilvusService;
import com.xiaohongshu.codewiz.core.service.rag.RagDataContext;

import lombok.extern.slf4j.Slf4j;

/**
 * 代码含义向量召回策略
 *
 * <AUTHOR>
 * @date 2025/4/10 11:20
 */
@Slf4j
@Component
public class CodeExplainMilvusRecallStrategy extends AbstractMilvusRecallStrategy<FewShotCase> {

    public CodeExplainMilvusRecallStrategy(CaseMilvusService caseMilvusService,
                                           EmbeddingClient embeddingClient) {
        super(caseMilvusService, embeddingClient);
    }

    /**
     * 实现代码含义向量召回策略
     *
     * @param context RAG数据上下文
     */
    @Override
    protected List<FewShotCase> doRecall(RagDataContext<FewShotCase> context) {
        log.info("执行代码含义向量召回策略");

        // 代码含义向量召回
        String codeExplainFileName = RagCaseKnowledgeEnum.CODE_EXPLAIN.getFileName();
        String explain = context.getAnalysisQuery().getOrDefault(codeExplainFileName, StringUtils.EMPTY);
        if (StringUtils.isBlank(explain)) {
            log.warn("代码含义为空，不进行召回");
            return Lists.newArrayList();
        }
        List<FewShotCase> explainRecallResults =
                milvusVectorRecall(context, explain, codeExplainFileName);

        log.info("代码含义向量召回结果数量: {}", explainRecallResults.size());
        return explainRecallResults;
    }
}