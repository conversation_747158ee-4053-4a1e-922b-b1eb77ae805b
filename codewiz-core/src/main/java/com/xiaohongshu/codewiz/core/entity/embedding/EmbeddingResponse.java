package com.xiaohongshu.codewiz.core.entity.embedding;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

@Data
public class EmbeddingResponse {

    // @JsonProperty("code")
    // private int code;
    //
    // @JsonProperty("msg")
    // private String msg;
    //
    // @JsonProperty("cost_time")
    // private float costTime;

    private List<List<Float>> embeddings;
    @JsonProperty("model_type")
    private String modelType;

    public static EmbeddingResponse empty() {
        return new EmbeddingResponse();
    }
}
