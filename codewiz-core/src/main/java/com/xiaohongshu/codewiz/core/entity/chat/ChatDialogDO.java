package com.xiaohongshu.codewiz.core.entity.chat;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xiaohongshu.codewiz.core.config.MysqlJsonHandler;

import lombok.Data;

/**
 * <p>
 * 对话表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@Data
@TableName("chat_dialog")
public class ChatDialogDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 会话id
     */
    private String sessionId;

    /**
     * 对话类型：0-会话对话，1-行间对话(行间对话为临时对话，仅记录，关闭后不在展示给用户)
     */
    private Integer dialogType;

    /**
     * 对话功能枚举：0-未知，1-代码CR，2-基于注释生成代码，3-生成注释，4-单测生成，5-通用知识问答，6-解释代码
     */
    private Integer dialogEnum;

    /**
     * message信息存在在oss平台的key
     */
    private String messageContentKey;

    /**
     * 本次对话使用的模型名称
     */
    private String modelName;

    /**
     * 是否删除，0-未删除，1-删除
     */
    private Integer isDelete;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 模型开始生成时间
     */
    private LocalDateTime startGenerateTime;

    /**
     * 模型结束生成时间
     */
    private LocalDateTime endGenerateTime;

    /**
     * 当前对话需要额外展示的内容(引用的文件路径/类型等)，json结构
     */
    @TableField(typeHandler = MysqlJsonHandler.class)
    private Extra extra;

    @Data
    public static class Extra {

    }
}
