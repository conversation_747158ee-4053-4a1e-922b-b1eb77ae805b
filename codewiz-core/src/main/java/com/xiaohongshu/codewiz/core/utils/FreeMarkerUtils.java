package com.xiaohongshu.codewiz.core.utils;

import java.io.IOException;
import java.io.StringWriter;
import java.nio.charset.StandardCharsets;

import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;

/**
 * <AUTHOR>
 * Created on 2025/3/27
 */
public class FreeMarkerUtils {
    private static final Configuration CFG = new Configuration(Configuration.VERSION_2_3_28);

    static {
        CFG.setDefaultEncoding(StandardCharsets.UTF_8.name());
    }

    public static String renderTemplate(String templateContent, Object model) {
        try {
            Template template = new Template(FreeMarkerUtils.class.getName(), templateContent, CFG);
            StringWriter stringWriter = new StringWriter();
            template.process(model, stringWriter);
            return stringWriter.toString();
        } catch (IOException | TemplateException e) {
            throw new RuntimeException(e);
        }
    }
}