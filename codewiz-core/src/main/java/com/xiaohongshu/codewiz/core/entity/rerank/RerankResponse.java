package com.xiaohongshu.codewiz.core.entity.rerank;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/3/28
 */
@Data
public class RerankResponse {
    private int code;
    private String msg;
    private float costTime;

    @JsonProperty("rerank_type")
    private String rerankTypeStr;

    private transient RerankType rerankType;

    public List<RerankResultDto> data;

    public RerankType getRerankType() {
        if (rerankType == null && rerankTypeStr != null) {
            try {
                rerankType = RerankType.valueOf(rerankTypeStr.toUpperCase());
            } catch (IllegalArgumentException e) {
                // 默认使用BGE_RERANK
                rerankType = RerankType.BGE_RERANK;
            }
        }
        return rerankType;
    }

    public static RerankResponse empty() {
        return new RerankResponse();
    }
}
