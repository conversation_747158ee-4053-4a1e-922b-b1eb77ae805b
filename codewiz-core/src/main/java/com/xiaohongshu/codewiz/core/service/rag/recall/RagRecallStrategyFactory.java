package com.xiaohongshu.codewiz.core.service.rag.recall;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.xiaohongshu.codewiz.core.constant.enums.RagRecallStrategyEnum;
import com.xiaohongshu.codewiz.core.entity.rag.RagDocument;

import lombok.extern.slf4j.Slf4j;

/**
 * RAG召回策略工厂
 *
 * <AUTHOR>
 * @date 2025/4/10 11:20
 */
@Slf4j
@Component
public class RagRecallStrategyFactory {

    @Resource
    private CodeSnippetMilvusRecallStrategy codeSnippetMilvusRecallStrategy;

    @Resource
    private CodeExplainMilvusRecallStrategy codeExplainMilvusRecallStrategy;

    @Resource
    private HybridKnowledgeRecallStrategy hybridKnowledgeRecallStrategy;

    @Resource
    private CommentsMilvusRecallStrategy commentsMilvusRecallStrategy;

    @Resource
    private CommentsEsRecallStrategy commentsEsRecallStrategy;

    @Resource
    private ExecutorService recallExecutor;

    /**
     * 根据策略类型创建召回策略
     *
     * @param strategyType 策略类型
     * @return 召回策略
     */
    public IRecallStrategy createStrategy(RagRecallStrategyEnum strategyType) {
        if (strategyType == null) {
            log.warn("策略类型为空，默认使用代码片段召回策略");
            return codeSnippetMilvusRecallStrategy;
        }

        switch (strategyType) {
            case CODE_SNIPPET:
                return codeSnippetMilvusRecallStrategy;

            case CODE_EXPLAIN:
                return codeExplainMilvusRecallStrategy;

            case HYBRID_KNOWLEDGE:
                return hybridKnowledgeRecallStrategy;

            case CODE_SNIPPET_AND_EXPLAIN:
                return createCompositeStrategy(
                        codeSnippetMilvusRecallStrategy,
                        codeExplainMilvusRecallStrategy
                );

            case COMMENTS:
                return commentsMilvusRecallStrategy;

            case COMMENTS_ES:
                return commentsEsRecallStrategy;

            case COMMENTS_AND_ES:
                return createCompositeStrategy(
                        commentsMilvusRecallStrategy,
                        commentsEsRecallStrategy
                );

            default:
                log.warn("不支持的策略类型: {}，默认使用代码片段召回策略", strategyType);
                return codeSnippetMilvusRecallStrategy;
        }
    }

    /**
     * 创建组合策略
     *
     * @param strategies 子策略
     * @return 组合策略
     */
    @SafeVarargs
    private <T extends RagDocument> CompositeRecallStrategy<T> createCompositeStrategy(IRecallStrategy<T>... strategies) {
        List<IRecallStrategy<T>> strategyList = new ArrayList<>();
        if (strategies != null && strategies.length > 0) {
            for (IRecallStrategy<T> strategy : strategies) {
                if (strategy != null) {
                    strategyList.add(strategy);
                }
            }
        }

        return new CompositeRecallStrategy<>(strategyList, recallExecutor);
    }
} 