package com.xiaohongshu.codewiz.core.feign;

import com.xiaohongshu.codewiz.core.entity.config.dto.CreateLingmaTokenRequest;
import com.xiaohongshu.codewiz.core.entity.config.dto.CreateLingmaTokenResponse;
import com.xiaohongshu.codewiz.core.entity.config.dto.QueryLingmaUserResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * <AUTHOR>
 * Created on 2025/3/17
 */
@FeignClient(name = "Lingma", url = "${lingma.domain}")
public interface LingmaRemoteClient {
    @GetMapping("/oapi/v1/platform/users/{userName}")
    ResponseEntity<QueryLingmaUserResponse> queryUser(@PathVariable("userName") String userName, @RequestHeader("x-yunxiao-token") String supperToken);
    @PostMapping("/oapi/v1/platform/users/admin/personalAccessTokens")
    ResponseEntity<CreateLingmaTokenResponse> createToken(@RequestBody CreateLingmaTokenRequest request, @RequestHeader("x-yunxiao-token") String supperToken);
}
