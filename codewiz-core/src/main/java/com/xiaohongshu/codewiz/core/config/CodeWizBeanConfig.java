package com.xiaohongshu.codewiz.core.config;

import java.util.concurrent.TimeUnit;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.web.client.ResponseErrorHandler;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.reactive.function.client.WebClient;

import com.amazonaws.ClientConfiguration;
import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.AWSCredentialsProvider;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.client.builder.AwsClientBuilder;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3Client;
import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;

import io.netty.channel.ChannelOption;
import io.netty.handler.timeout.ReadTimeoutHandler;
import io.netty.handler.timeout.WriteTimeoutHandler;
import reactor.netty.http.client.HttpClient;

/**
 * <AUTHOR>
 * @date 2023/10/31 18:48
 */
@EnableApolloConfig
@EnableFeignClients(value = "com.xiaohongshu.codewiz.core.feign")
@Configuration
public class CodeWizBeanConfig {
    public static final int DEFAULT_CONN_TIMEOUT = 2000;
    public static final int DEFAULT_READ_TIMEOUT = 10000;

    @Bean
    public RestTemplate modelRestTemplate(RestTemplateBuilder builder) {
        SimpleClientHttpRequestFactory clientHttpRequestFactory = new SimpleClientHttpRequestFactory();
        clientHttpRequestFactory.setConnectTimeout(DEFAULT_CONN_TIMEOUT);
        clientHttpRequestFactory.setReadTimeout(DEFAULT_READ_TIMEOUT); // 600s
        clientHttpRequestFactory.setBufferRequestBody(false);

        RestTemplate restTemplate = builder.build();
        restTemplate.setRequestFactory(clientHttpRequestFactory);
        restTemplate.setErrorHandler(new ResponseErrorHandler() {
            @Override
            public boolean hasError(org.springframework.http.client.ClientHttpResponse response) {
                return false;
            }

            @Override
            public void handleError(org.springframework.http.client.ClientHttpResponse response) {
            }
        });
        return restTemplate;
    }

    @Bean
    public WebClient llmAdapterWebClient(@Value("${feign-client.llm-adapter.url}") String url,
                                         @Value("${feign-client.llm-adapter.api-key}") String apiKey) {
        // 配置带超时的HttpClient
        HttpClient httpClient = HttpClient.create()
                .tcpConfiguration(tcpClient ->
                        tcpClient.option(ChannelOption.CONNECT_TIMEOUT_MILLIS, 10000)
                                .doOnConnected(conn ->
                                        conn.addHandlerLast(new ReadTimeoutHandler(30, TimeUnit.SECONDS)) // 读取超时
                                                .addHandlerLast(new WriteTimeoutHandler(30, TimeUnit.SECONDS)) // 写入超时
                                )
                );
        return WebClient.builder()
                .clientConnector(new ReactorClientHttpConnector(httpClient))
                .baseUrl(url)
                .defaultHeader("api-key", apiKey)
                .build();
    }

    @Bean
    public AmazonS3 rosS3Client(@Value("${ros.access-key}") String accessKey,
                                @Value("${ros.secret-key}") String secretKey,
                                @Value("${ros.endpoint}") String endpoint,
                                @Value("${ros.region}") String region) {
        ClientConfiguration config = new ClientConfiguration();
        AwsClientBuilder.EndpointConfiguration endpointConfig = new AwsClientBuilder.EndpointConfiguration(endpoint, region);
        AWSCredentials awsCredentials = new BasicAWSCredentials(accessKey, secretKey);
        AWSCredentialsProvider awsCredentialsProvider = new AWSStaticCredentialsProvider(awsCredentials);
        // 初始化 S3 Client
        return AmazonS3Client.builder()
                .withEndpointConfiguration(endpointConfig)
                .withClientConfiguration(config)
                .withCredentials(awsCredentialsProvider)
                .disableChunkedEncoding()
                .build();
    }
}
