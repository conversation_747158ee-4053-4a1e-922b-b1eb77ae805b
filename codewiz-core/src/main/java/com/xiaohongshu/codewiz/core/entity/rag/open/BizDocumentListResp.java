package com.xiaohongshu.codewiz.core.entity.rag.open;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/3/24 15:53
 */
@Data
public class BizDocumentListResp {
    private Integer total;
    private List<Document> documents;

    @Data
    public static class Document {
        @JsonProperty("doc_id")
        private String docId;
        @JsonProperty("chunk_ids")
        private List<String> chunkIds;
        @JsonProperty("file_path")
        private String filePath;
        @JsonProperty("file_type")
        private String fileType;
        @JsonProperty("created_at")
        private String createdAt;
        @JsonProperty("updated_at")
        private String updatedAt;
    }
} 