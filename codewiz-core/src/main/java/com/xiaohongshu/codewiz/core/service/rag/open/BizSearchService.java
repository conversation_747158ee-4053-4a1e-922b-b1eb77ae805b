package com.xiaohongshu.codewiz.core.service.rag.open;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.xiaohongshu.codewiz.core.client.EmbeddingClient;
import com.xiaohongshu.codewiz.core.constant.BizDocumentConstant;
import com.xiaohongshu.codewiz.core.constant.ErrorCodeConstant;
import com.xiaohongshu.codewiz.core.constant.enums.BizSearchTypeEnum;
import com.xiaohongshu.codewiz.core.entity.rag.open.BizSearchReq;
import com.xiaohongshu.codewiz.core.entity.rag.open.BizSearchResp;
import com.xiaohongshu.codewiz.core.exception.BizException;
import com.xiaohongshu.codewiz.core.service.milvus.BizMilvusService;
import com.xiaohongshu.codewiz.core.utils.CompletableFutureUtil;
import com.xiaohongshu.infra.utils.ObjectMapperUtils;

import cn.hutool.core.util.StrUtil;
import io.milvus.v2.service.vector.response.SearchResp;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2025/3/24 15:55
 */
@Service
@Slf4j
public class BizSearchService {
    @Resource
    BizMilvusService bizMilvusService;
    @Resource
    EmbeddingClient embeddingClient;
    @Resource
    ExecutorService bizSearchExecutor;
    @Resource
    BizPermissionService bizPermissionService;

    public BizSearchResp search(BizSearchReq req) {
        log.info("search req: {}", ObjectMapperUtils.toJSON(req));
        Integer searchType = req.getSearchType();
        searchParamCheck(req);

        bizPermissionService.checkPermission(req.getBizId(), false);

        List<SearchResp.SearchResult> searchResults = new ArrayList<>();
        if (BizSearchTypeEnum.PUBLIC.getScene().equals(searchType)) {
            searchResults.addAll(publicSearch(req));
        } else if (BizSearchTypeEnum.PRIVATE.getScene().equals(searchType)) {
            searchResults.addAll(privateSearch(req));
        } else if (BizSearchTypeEnum.ALL.getScene().equals(searchType)) {
            searchAllType(req, searchResults);
        } else {
            throw new BizException(ErrorCodeConstant.OPEN_RAG_SEARCH_TYPE_ERROR);
        }
        // 按score排序，取前TOP_K
        List<SearchResp.SearchResult> resultTopK =
                searchResults.stream().sorted((o1, o2) -> Float.compare(o2.getScore(), o1.getScore())).limit(req.getTopK())
                        .collect(Collectors.toList());
        List<BizSearchResp.Result> resultList = new ArrayList<>();
        for (SearchResp.SearchResult searchResult : resultTopK) {
            BizSearchResp.Result result = new BizSearchResp.Result();
            Map<String, Object> entity = searchResult.getEntity();
            result.setKbId((String) entity.get(BizDocumentConstant.KB_ID));
            result.setDocId((String) entity.get(BizDocumentConstant.DOC_ID));
            result.setChunkId((String) entity.get(BizDocumentConstant.CHUNK_ID));
            result.setContent((String) entity.get(BizDocumentConstant.CONTENT));
            result.setFilePath((String) entity.get(BizDocumentConstant.FILE_PATH));
            result.setFileType((String) entity.get(BizDocumentConstant.FILE_TYPE));
            result.setScore(searchResult.getScore());
            resultList.add(result);
        }
        BizSearchResp resp = new BizSearchResp();
        resp.setResults(resultList);
        return resp;
    }

    private void searchAllType(BizSearchReq req, List<SearchResp.SearchResult> searchResults) {
        CompletableFuture<List<SearchResp.SearchResult>> publicSearch =
                CompletableFuture.supplyAsync(() -> publicSearch(req), bizSearchExecutor);
        CompletableFuture<List<SearchResp.SearchResult>> privateSearch =
                CompletableFuture.supplyAsync(() -> privateSearch(req), bizSearchExecutor);
        List<List<SearchResp.SearchResult>> lists = CompletableFutureUtil.syncGetJoinResult(Arrays.asList(privateSearch, publicSearch));
        for (List<SearchResp.SearchResult> list : lists) {
            if (CollectionUtils.isEmpty(list)) {
                continue;
            }
            searchResults.addAll(list);
        }
    }

    private static void searchParamCheck(BizSearchReq req) {
        if (req.getTopK() == null || req.getTopK() <= 0) {
            throw new BizException(ErrorCodeConstant.OPEN_RAG_TOP_K_ERROR);
        }
        if (StringUtils.isBlank(req.getQuery())) {
            throw new BizException(ErrorCodeConstant.OPEN_RAG_QUERY_ERROR);
        }
        if (StringUtils.isBlank(req.getBizId())) {
            throw new BizException(ErrorCodeConstant.OPEN_RAG_BIZ_ID_ERROR);
        }
    }

    private List<SearchResp.SearchResult> privateSearch(BizSearchReq req) {
        // 没传 kbIds 根据传的 bizId userId projectId 计算 kbId
        if (CollectionUtils.isEmpty(req.getKbIds())) {
            if (StringUtils.isBlank(req.getUserId())) {
                throw new BizException(ErrorCodeConstant.OPEN_RAG_USER_ID_ERROR);
            }
            if (StringUtils.isBlank(req.getBizId())) {
                throw new BizException(ErrorCodeConstant.OPEN_RAG_BIZ_ID_ERROR);
            }

            List<String> projectIds = req.getProjectIds();
            if (CollectionUtils.isNotEmpty(projectIds)) {
                return getSearchResultsByProjectIds(req, projectIds);
            }

            String projectId = req.getProjectId();
            if (StrUtil.isNotEmpty(projectId)) {
                String kbId = BizMilvusService.calHashKey(req.getBizId(), req.getUserId(), projectId);
                String userProjectId = req.getUserId() + "_" + req.getProjectId();
                return getSearchResultsByKbIdPrivate(req, kbId, userProjectId);
            }
        }

        // 并发查 kbIds 里的所有 kb
        return getSearchResultsByKbIds(req);
    }

    private List<SearchResp.SearchResult> getSearchResultsByKbIds(BizSearchReq req) {
        List<CompletableFuture<List<SearchResp.SearchResult>>> searchFutures = new ArrayList<>();
        for (String kbId : req.getKbIds()) {
            String userProjectId = req.getUserId() + "_" + req.getProjectId();
            CompletableFuture<List<SearchResp.SearchResult>> searchFuture =
                    CompletableFuture.supplyAsync(() -> getSearchResultsByKbIdPrivate(req, kbId, userProjectId), bizSearchExecutor);
            searchFutures.add(searchFuture);
        }
        List<List<SearchResp.SearchResult>> lists = CompletableFutureUtil.syncGetJoinResult(searchFutures);
        List<SearchResp.SearchResult> searchResults = new ArrayList<>();
        for (List<SearchResp.SearchResult> list : lists) {
            if (CollectionUtils.isEmpty(list)) {
                continue;
            }
            searchResults.addAll(list);
        }
        return searchResults;
    }

    private List<SearchResp.SearchResult> getSearchResultsByProjectIds(BizSearchReq req, List<String> projectIds) {
        List<CompletableFuture<List<SearchResp.SearchResult>>> searchFutures = new ArrayList<>();
        for (String projectId : projectIds) {
            String kbId = BizMilvusService.calHashKey(req.getBizId(), req.getUserId(), projectId);
            String userProjectId = req.getUserId() + "_" + projectId;
            CompletableFuture<List<SearchResp.SearchResult>> searchFuture =
                    CompletableFuture.supplyAsync(() -> getSearchResultsByKbIdPrivate(req, kbId, userProjectId), bizSearchExecutor);
            searchFutures.add(searchFuture);
        }
        List<List<SearchResp.SearchResult>> lists = CompletableFutureUtil.syncGetJoinResult(searchFutures);
        List<SearchResp.SearchResult> searchResults = new ArrayList<>();
        for (List<SearchResp.SearchResult> list : lists) {
            if (CollectionUtils.isEmpty(list)) {
                continue;
            }
            searchResults.addAll(list);
        }
        return searchResults;
    }

    private List<SearchResp.SearchResult> getSearchResultsByKbIdPrivate(BizSearchReq req, String kbId, String userProjectId) {
        List<Float> embeddingVector = embeddingClient.getBgeEmbeddingVector(req.getQuery());
        HashMap<String, Object> filter = new HashMap<>(req.getFilter());
        filter.put(BizDocumentConstant.USER_PROJECT_ID, userProjectId);
        SearchResp search = bizMilvusService.search(kbId, null, embeddingVector, req.getTopK(), filter);
        return getSearchResults(search);
    }

    private static List<SearchResp.SearchResult> getSearchResults(SearchResp search) {
        if (search == null || CollectionUtils.isEmpty(search.getSearchResults())) {
            return new ArrayList<>();
        }
        // 一个查询参数只有一个查询结果
        return search.getSearchResults().get(0);
    }

    private List<SearchResp.SearchResult> publicSearch(BizSearchReq req) {
        String kbId = req.getBizId() + BizDocumentConstant.COLLECTION_SUFFIX_PUBLIC;
        List<Float> embeddingVector = embeddingClient.getBgeEmbeddingVector(req.getQuery());
        SearchResp search = bizMilvusService.search(kbId, req.getProjectIds(), embeddingVector, req.getTopK(), req.getFilter());
        return getSearchResults(search);
    }

}