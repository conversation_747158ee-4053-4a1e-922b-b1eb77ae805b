package com.xiaohongshu.codewiz.core.entity.rag;

import java.util.Map;

import javax.validation.constraints.NotEmpty;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.xiaohongshu.codewiz.core.service.rag.RagDataContext;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2025/2/27 17:38
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RagDataQueryRequest extends RagDataRequest {

    @NotEmpty
    private String query;

    private Extension extension;

    @Data
    public static class Extension {
        @JsonProperty("return_num")
        private Integer returnNum = 0;
        @JsonProperty("filter_fields")
        private Map<String, Object> filterFields;
        @JsonProperty("score_threshold")
        private Double scoreThreshold;
        @JsonProperty("collection_name")
        private String collectionName;
        @JsonProperty("recall_strategy")
        private int recallStrategy;
        @JsonProperty("embedding_model")
        private String embeddingModel;
        @JsonProperty("rerank_model")
        private String rerankModel;
        @JsonProperty("top_k")
        private RagDataContext.TopK topK;
    }

    /**
     * 埋点字段
     */
    @JsonProperty("business_tracking_id")
    private String businessTrackingId;
    @JsonProperty("xray_tracking_id")
    private String xrayTrackingId;
    @JsonProperty("uuid_tracking_id")
    private String uuidTrackingId;
}
