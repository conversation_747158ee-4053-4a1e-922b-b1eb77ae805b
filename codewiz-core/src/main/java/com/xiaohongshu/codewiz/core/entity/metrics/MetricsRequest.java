package com.xiaohongshu.codewiz.core.entity.metrics;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/4/23 15:51
 */
@Data
public class MetricsRequest {
    @JsonProperty("trace_id")
    @JsonAlias({"traceId", "trace_id"})
    private String traceId;
    @JsonProperty("session_id")
    @JsonAlias({"sessionId", "session_id"})
    private String sessionId;
    @JsonProperty("metrics_scene")
    @JsonAlias({"metricsScene", "metrics_scene"})
    private String metricsScene;
    @JsonProperty("metrics_key")
    @JsonAlias({"metricsKey", "metrics_key"})
    private String metricsKey;
    @JsonProperty("metrics_value")
    @JsonAlias({"metricsValue", "metrics_value"})
    private String metricsValue;
    private Long timestamp;
    @JsonProperty("user_info")
    @JsonAlias({"userInfo", "user_info"})
    private String userInfo;
    @JsonProperty("editor_info")
    @JsonAlias({"editorInfo", "editor_info"})
    private String editorInfo;
    @JsonProperty("config_info")
    @JsonAlias({"configInfo", "config_info"})
    private String configInfo;
    @JsonProperty("local_test")
    @JsonAlias({"localTest", "local_test"})
    private boolean localTest;
    private String extra1;
    private String extra2;
    private String extra3;
}
