package com.xiaohongshu.codewiz.core.client;

import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import com.google.common.collect.Lists;
import com.xiaohongshu.codewiz.core.annotation.LogExecutionTime;
import com.xiaohongshu.codewiz.core.entity.rerank.RerankRequest;
import com.xiaohongshu.codewiz.core.entity.rerank.RerankRequestDto;
import com.xiaohongshu.codewiz.core.entity.rerank.RerankResponse;
import com.xiaohongshu.codewiz.core.entity.rerank.RerankResultDto;
import com.xiaohongshu.codewiz.core.entity.rerank.RerankType;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2025/3/28
 */
@Slf4j
@Component
public class RerankClient {
    private final RestTemplate restTemplate;

    public RerankClient(RestTemplate modelRestTemplate) {
        this.restTemplate = modelRestTemplate;
    }

    @LogExecutionTime
    public List<RerankResultDto> getRerankResult(List<RerankRequestDto> queryList) {
        return getRerankResult(queryList, RerankType.BGE_RERANK);
    }

    @LogExecutionTime
    public List<RerankResultDto> getRerankResult(List<RerankRequestDto> queryList, RerankType rerankType) {
        if (CollectionUtils.isEmpty(queryList)) {
            return Lists.newArrayList();
        }
        try {
            log.info("getRerankResultParams:{}, rerankType:{}", queryList, rerankType);
            RerankRequest request = new RerankRequest();
            request.setTextPairs(queryList);
            RerankResponse response = getRerankResponse(request, rerankType);
            if (response == null) {
                return Lists.newArrayList();
            }
            return response.getData();
        } catch (Exception e) {
            log.error("get rerank result error {}", e.getMessage());
        }
        return Lists.newArrayList();
    }

    private RerankResponse getRerankResponse(RerankRequest request, RerankType rerankType) {
        // Set HTTP headers
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        // Create HttpEntity with the payload and headers
        HttpEntity<RerankRequest> requestEntity = new HttpEntity<>(request, headers);

        // 从枚举中获取URL
        String rerankUrl = rerankType.getUrl();

        // Send POST request
        ResponseEntity<RerankResponse> responseEntity = null;
        try {
            responseEntity = restTemplate.exchange(
                    rerankUrl,
                    HttpMethod.POST,
                    requestEntity,
                    RerankResponse.class
            );
        } catch (RestClientException e) {
            log.error("request rerank result error {}", e.getMessage());
            return RerankResponse.empty();
        }
        // Check response status
        if (responseEntity.getStatusCode().isError()) {
            throw new RuntimeException("request rerank result error, status code: " + responseEntity.getStatusCode());
        }
        return responseEntity.getBody();
    }
}
