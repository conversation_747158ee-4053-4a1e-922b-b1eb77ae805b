package com.xiaohongshu.codewiz.core.entity.chat;

import java.util.List;
import java.util.Map;

import org.eclipse.lsp4j.Range;

import lombok.Data;

/**
 * Author: liukunpeng Date: 2025-03-10 Description:
 */
@Data
public class ChatDialogRequest {
    private String sessionId;
    private Extra extra;
    private Map<String, Object> content;
    private Integer type;

    @Data
    public static class Extra {
        private String filename; // 文件名/路径
        private String lang; // 文件类型:java/js等
        // 代码解释等场景用关联代码列表，controller接受后马上转移到content中去
        private List<String> invocationCodeList;
        private List<ProtoSelection> chatReferences;
    }

    @Data
    public static class ProtoSelection {
        private String filename;
        private String text; // QA类型引用文件用：选中文本
        private Range range; // QA类型引用文件用：选中范围
        private Boolean selected;
        private String languageId;
    }
}
