package com.xiaohongshu.codewiz.core.dao.chat;

import java.util.List;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xiaohongshu.codewiz.core.constant.ChatCommonConstant;
import com.xiaohongshu.codewiz.core.entity.chat.ChatDialogDO;
import com.xiaohongshu.codewiz.core.mapper.chat.ChatDialogMapper;

import cn.hutool.core.collection.CollectionUtil;

/**
 * <p>
 * 会话表 mapper操作类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@Service
public class ChatDialogDao extends ServiceImpl<ChatDialogMapper, ChatDialogDO> {
    public ChatDialogDO selectFirstSessionDialog(String sessionId) {
        LambdaQueryWrapper<ChatDialogDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ChatDialogDO::getSessionId, sessionId)
                .eq(ChatDialogDO::getIsDelete, ChatCommonConstant.NO);
        queryWrapper.last(" limit 1");
        List<ChatDialogDO> dialogs = this.list(queryWrapper);
        return CollectionUtil.isEmpty(dialogs) ? null : dialogs.get(0);
    }
}
