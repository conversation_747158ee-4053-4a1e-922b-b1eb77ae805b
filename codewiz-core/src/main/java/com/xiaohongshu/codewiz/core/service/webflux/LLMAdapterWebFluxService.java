package com.xiaohongshu.codewiz.core.service.webflux;

import java.util.Optional;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;

import com.xiaohongshu.codewiz.core.entity.allin.ChatCompletionRequestDTO;

import reactor.core.publisher.Mono;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2025/3/4
 */
@Service
public class LLMAdapterWebFluxService {
    @Resource
    private WebClient llmAdapterWebClient;

    public static final String DONE = "[DONE]";

    private static final String URL_CHAT_COMPLETION = "/v1/chat/completions";

    public WebClient.ResponseSpec chatCompletion(ChatCompletionRequestDTO request, Boolean testFlag,
                                                 String serviceName) {
        return llmAdapterWebClient.post()
                .uri(uriBuilder -> uriBuilder.path(URL_CHAT_COMPLETION).build())
                .header("test-flag", Optional.ofNullable(testFlag).orElse(false).toString())
                .header("service-name", serviceName)
                .body(Mono.just(request), ChatCompletionRequestDTO.class)
                .retrieve();
    }

}
