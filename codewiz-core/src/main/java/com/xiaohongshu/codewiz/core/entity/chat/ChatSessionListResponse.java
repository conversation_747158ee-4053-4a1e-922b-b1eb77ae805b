package com.xiaohongshu.codewiz.core.entity.chat;

import java.time.LocalDateTime;
import java.util.List;

import lombok.Data;

/**
 * Author: liukunpeng Date: 2025-03-10 Description:
 */
@Data
public class ChatSessionListResponse {
    private List<SessionDTO> sessions;
    private Long total;

    @Data
    public static class SessionDTO {
        private String sessionId;
        private String sessionName;
        private String sessionContent;
        private LocalDateTime lastTime;
        private Integer sessionType;
    }
}
