package com.xiaohongshu.codewiz.core.service.rag.query;


import java.util.List;
import java.util.Objects;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.xiaohongshu.codewiz.core.constant.enums.RagDataTypeEnum;
import com.xiaohongshu.codewiz.core.dao.customrule.BusinessCustomRuleDao;
import com.xiaohongshu.codewiz.core.entity.customrule.BusinessCustomRuleDO;
import com.xiaohongshu.codewiz.core.entity.rag.CqRule;
import com.xiaohongshu.codewiz.core.entity.rag.RagDataQueryRequest;
import com.xiaohongshu.codewiz.core.entity.rag.RagDataQueryResponse;
import com.xiaohongshu.codewiz.core.service.rag.RagDataContext;

import cn.hutool.core.collection.CollectionUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2025/2/28 16:26
 */
@Slf4j
@Service
public class RagQueryCqRuleService extends AbsRagQueryDataService<CqRule> {

    @Resource
    BusinessCustomRuleDao businessCustomRuleDao;

    @Override
    protected void check(RagDataQueryRequest request) {

    }

    @Override
    protected void analyze(RagDataContext<CqRule> context) {

    }

    @Override
    protected void recall(RagDataContext<CqRule> context) {

    }

    @Override
    protected void merge(RagDataContext<CqRule> context) {
        List<BusinessCustomRuleDO> customRuleDOS = businessCustomRuleDao.selectFirstSessionDialog(context.getQueryRequest().getQuery());
        if (CollectionUtil.isEmpty(customRuleDOS)) {
            return;
        }
        if (Objects.isNull(context.getQueryResponse())) {
            context.setQueryResponse(RagDataQueryResponse.<CqRule>builder()
                    .scene(context.getQueryRequest().getScene())
                    .documents(CqRule.convertToVo(customRuleDOS))
                    .build());
        } else {
            context.getQueryResponse().setDocuments(context.getDocuments());
        }
        // context.getQueryResponse().setDocuments(CqRule.convertToVo(customRuleDOS));
    }


    @Override
    protected void rerank(RagDataContext<CqRule> context) {

    }

    @Override
    protected void filter(RagDataContext<CqRule> context) {

    }

    @Override
    protected void format(RagDataContext<CqRule> context) {

    }

    @Override
    protected String getCollectionName(RagDataQueryRequest request) {
        return "";
    }

    @Override
    public RagDataTypeEnum source() {
        return RagDataTypeEnum.CQ_RULE;
    }
}
