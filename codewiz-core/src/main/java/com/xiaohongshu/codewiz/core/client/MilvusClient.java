package com.xiaohongshu.codewiz.core.client;

import com.xiaohongshu.codewiz.core.annotation.LogExecutionTime;

import io.milvus.v2.client.MilvusClientV2;
import io.milvus.v2.service.collection.request.CreateCollectionReq;
import io.milvus.v2.service.collection.request.DropCollectionReq;
import io.milvus.v2.service.collection.request.GetLoadStateReq;
import io.milvus.v2.service.collection.request.HasCollectionReq;
import io.milvus.v2.service.collection.request.LoadCollectionReq;
import io.milvus.v2.service.vector.request.DeleteReq;
import io.milvus.v2.service.vector.request.HybridSearchReq;
import io.milvus.v2.service.vector.request.InsertReq;
import io.milvus.v2.service.vector.request.QueryReq;
import io.milvus.v2.service.vector.request.SearchReq;
import io.milvus.v2.service.vector.request.UpsertReq;
import io.milvus.v2.service.vector.response.DeleteResp;
import io.milvus.v2.service.vector.response.InsertResp;
import io.milvus.v2.service.vector.response.QueryResp;
import io.milvus.v2.service.vector.response.SearchResp;
import io.milvus.v2.service.vector.response.UpsertResp;

/**
 * <AUTHOR>
 * @date 2025/3/24 17:16
 */
public abstract class MilvusClient {

    public abstract MilvusClientV2 getClient();

    public boolean ready() {
        return getClient().clientIsReady();
    }

    public void createCollection(CreateCollectionReq request) {
        getClient().createCollection(request);
    }

    public boolean hasCollection(String collectionName) {
        HasCollectionReq req = HasCollectionReq.builder()
                .collectionName(collectionName)
                .build();
        return getClient().hasCollection(req);
    }

    public InsertResp insert(InsertReq request) {
        return getClient().insert(request);
    }

    public UpsertResp upsert(UpsertReq request) {
        return getClient().upsert(request);
    }

    @LogExecutionTime
    public SearchResp search(SearchReq request) {
        return getClient().search(request);
    }


    @LogExecutionTime
    public QueryResp query(QueryReq queryReq) {
        return getClient().query(queryReq);
    }

    public DeleteResp delete(DeleteReq deleteReq) {
        return getClient().delete(deleteReq);
    }

    public void dropCollection(String collectionName) {
        DropCollectionReq dropQuickSetupParam = DropCollectionReq.builder()
                .collectionName(collectionName)
                .build();
        getClient().dropCollection(dropQuickSetupParam);
    }

    public SearchResp hybridSearch(HybridSearchReq request) {
        return getClient().hybridSearch(request);
    }


    public Boolean getLoadState(GetLoadStateReq request) {
        return getClient().getLoadState(request);
    }

    public void loadCollection(LoadCollectionReq request) {
        getClient().loadCollection(request);
    }

    public CreateCollectionReq.CollectionSchema createSchema() {
        return getClient().createSchema();
    }
}
