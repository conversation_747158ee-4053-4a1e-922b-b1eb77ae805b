package com.xiaohongshu.codewiz.core.entity.config.dto;

import lombok.Data;

/**
 * 配置查询请求
 *
 * <AUTHOR>
 * @since 2025-04-21
 */
@Data
public class ConfigQueryRequest {

    /**
     * 用户邮箱
     */
    private String userEmail;

    /**
     * 插件版本
     */
    private String pluginVersion;

    /**
     * 配置类型 1-plugin 2-lsp
     */
    private Integer type;

    /**
     * ide平台:jetbrains or vscode
     */
    private String idePlatform;
} 