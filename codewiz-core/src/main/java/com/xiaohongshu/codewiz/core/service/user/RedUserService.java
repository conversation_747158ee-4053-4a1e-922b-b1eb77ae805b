package com.xiaohongshu.codewiz.core.service.user;

import java.util.Optional;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.google.common.collect.Lists;
import com.xhs.ep.redcity.rcoppublic.common.Authentication;
import com.xhs.ep.redcity.rcoppublic.integrationservice.contacts.ContactsQueryRpcService;
import com.xhs.ep.redcity.rcoppublic.integrationservice.contacts.domain.AccountInfo;
import com.xhs.ep.redcity.rcoppublic.integrationservice.contacts.request.QueryAccountInfoListRequest;
import com.xhs.ep.redcity.rcoppublic.integrationservice.contacts.response.QueryAccountInfoListResponse;
import com.xiaohongshu.codewiz.core.utils.JsonMapperUtils;
import com.xiaohongshu.infra.rpc.base.Context;

import lombok.extern.slf4j.Slf4j;


@Slf4j
@Component
public class RedUserService {

    // 查询用户行为的执行者
    // 通讯录那边必须要传一个调用者给他们，用来过滤敏感数据。"__system__"这个是一个特殊的调用者，表示不过滤。。。
    private static final String USER_SEARCH_MOCK_ADMIN = "__system__";

    @Value("${contact.auth.appId}")
    private String appId;
    @Value("${contact.auth.secret}")
    private String secret;

    @Resource
    private ContactsQueryRpcService.Iface userRpcService;

    public Optional<RedUserBO> searchRedUser(String accountEmail) {
        // 组装查询条件，准备查用户
        QueryAccountInfoListRequest request = new QueryAccountInfoListRequest();
        request.setAccountId(USER_SEARCH_MOCK_ADMIN);
        request.setAccountIdList(
                Lists.newArrayList(accountEmail)
        );

        // 去通讯录查用户
        QueryAccountInfoListResponse searchResponse = null;
        try {
            searchResponse = userRpcService.queryAccountInfoList(new Context(), new Authentication(appId, secret), request);
        } catch (Exception ex) {
            log.error("An error occurred while invoking userRpcService.queryAccountInfoList, request is {}",
                    JsonMapperUtils.toJson(request), ex);
            return Optional.empty();
        }

        // 解析查到的用户
        return Optional.ofNullable(buildRedUserPO(CollectionUtils.isEmpty(searchResponse.getAccountInfoList())
                ? null : searchResponse.getAccountInfoList().get(0))
        );
    }

    private RedUserBO buildRedUserPO(AccountInfo accountInfo) {
        if (accountInfo == null) {
            return null;
        }
        RedUserBO result = new RedUserBO();
        result.setUserEmail(accountInfo.getEmail());
        // 取accountId的前缀作为userName
        result.setUserName(accountInfo.getAccountId().split("@")[0]);
        result.setRealName(accountInfo.getRealName());
        result.setRedName(accountInfo.getRedName());
        result.setShowName(accountInfo.getShowName());
        result.setDepartmentId(accountInfo.getDepartmentId());
        result.setDepartmentName(accountInfo.getDepartmentName());
        result.setDepartmentNamePath(accountInfo.getDepartmentNamePath());
        return result;
    }
}
