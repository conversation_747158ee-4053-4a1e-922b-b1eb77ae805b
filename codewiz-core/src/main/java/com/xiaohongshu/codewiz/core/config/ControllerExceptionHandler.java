package com.xiaohongshu.codewiz.core.config;

import java.util.Map;

import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import com.xiaohongshu.codewiz.core.constant.ErrorCodeConstant;
import com.xiaohongshu.codewiz.core.entity.common.SingleResponse;
import com.xiaohongshu.codewiz.core.exception.BizException;
import com.xiaohongshu.xray.logging.LogTags;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2025/3/6
 */
@Slf4j
@ControllerAdvice
public class ControllerExceptionHandler {
    /**
     * 服务应用异常处理
     */
    @ResponseBody
    @ExceptionHandler(BizException.class)
    public SingleResponse<?> handleException(BizException exception) {
        log.error(LogTags.of(Map.of("exception_type", "biz_exception", "code", exception.getStatus(),
                "msg", exception.getMessage())), "biz_exception", exception);
        return SingleResponse.buildFailure(String.valueOf(exception.getStatus()), exception.getMessage());
    }

    /**
     * 未知异常兜底处理
     */
    @ResponseBody
    @ExceptionHandler(Throwable.class)
    public SingleResponse<?> handleAllException(Throwable throwable) {
        log.error(LogTags.of(Map.of("exception_type", "throwable", "msg", throwable.getMessage())),
                "biz_exception", throwable);
        return SingleResponse.buildFailure(String.valueOf(ErrorCodeConstant.SERVER_ERROR.getStatus()),
                throwable.getMessage());
    }
}
