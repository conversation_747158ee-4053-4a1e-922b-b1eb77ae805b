package com.xiaohongshu.codewiz.core.entity.chat;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

/**
 * <p>
 * 反馈表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@Data
@TableName("chat_feedback")
public class ChatFeedbackDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * pk_id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 反馈实体对话ID
     */
    private Long dialogId;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 点赞或点踩，0未操作，1-点赞，2-点踩
     */
    private Integer feedbackType;

    /**
     * 反馈内容文本
     */
    private String feedbackContent;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
