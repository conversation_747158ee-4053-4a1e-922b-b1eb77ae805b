package com.xiaohongshu.codewiz.core.entity.rag;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;

import com.google.common.collect.Lists;
import com.xiaohongshu.codewiz.core.entity.customrule.BusinessCustomRuleDO;

import cn.hutool.core.collection.CollectionUtil;
import lombok.Data;

/**
 * Author: sunliguo
 * Date: 2025/3/20 11:05
 * Description:
 */
@Data
public class CqRule implements RagDocument {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 仓库ID
     */
    private Long projectId;

    /**
     * 开发语言
     */
    private String devLanguage;

    /**
     * 规则的自然语言描述
     */
    private String ruleNatureLanguageDescription;

    /**
     * 问题类型
     */
    private String issueType;

    /**
     * 问题严重程度
     */
    private String severity;

    /**
     * 代码示例
     */
    private String codeExample;


    public static List<CqRule> convertToVo(List<BusinessCustomRuleDO> customRuleDOS) {
        if (CollectionUtil.isEmpty(customRuleDOS)) {
            return new ArrayList<>();
        }
        List<CqRule> cqRules = Lists.newLinkedList();
        for (BusinessCustomRuleDO customRuleDO : customRuleDOS) {
            CqRule cqRule = new CqRule();
            cqRule.setId(Long.parseLong(String.valueOf(customRuleDO.getId())));
            cqRule.setProjectId(StringUtils.isNotBlank(customRuleDO.getProjectId()) ? Long.parseLong(customRuleDO.getProjectId()) : null);
            cqRule.setDevLanguage(customRuleDO.getProgrammingLanguage());
            cqRule.setRuleNatureLanguageDescription(customRuleDO.getRuleDescription());
            cqRule.setIssueType(customRuleDO.getIssueType());
            cqRule.setSeverity(customRuleDO.getSeverity());
            cqRule.setCodeExample(customRuleDO.getCodeExample());

            cqRules.add(cqRule);
        }
        return cqRules;

    }
}
