package com.xiaohongshu.codewiz.core.entity.embedding;

import java.util.Collections;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/2/25 19:11
 */
@Data
public class GteEmbeddingResponse {

    private int code;

    private String msg;

    @JsonProperty("cost_time")
    private float costTime;

    private List<GteEmbeddingData> data;

    @Data
    public static class GteEmbeddingData {
        private String text;
        private List<Float> embedding;
    }

    public static GteEmbeddingResponse empty() {
        return new GteEmbeddingResponse();
    }

    public List<List<Float>> getEmbeddings() {
        if (data == null || data.isEmpty()) {
            return Collections.emptyList();
        }
        return data.stream()
                .map(GteEmbeddingData::getEmbedding)
                .collect(java.util.stream.Collectors.toList());
    }
} 