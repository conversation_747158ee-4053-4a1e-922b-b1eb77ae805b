package com.xiaohongshu.codewiz.core.service.milvus;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.google.gson.JsonObject;
import com.xiaohongshu.codewiz.core.annotation.LogExecutionTime;
import com.xiaohongshu.codewiz.core.client.OpenMilvusClient;
import com.xiaohongshu.codewiz.core.constant.BizDocumentConstant;
import com.xiaohongshu.codewiz.core.constant.ErrorCodeConstant;
import com.xiaohongshu.codewiz.core.exception.BizException;

import io.milvus.v2.common.DataType;
import io.milvus.v2.common.IndexParam;
import io.milvus.v2.service.collection.request.AddFieldReq;
import io.milvus.v2.service.collection.request.CreateCollectionReq;
import io.milvus.v2.service.collection.request.GetLoadStateReq;
import io.milvus.v2.service.collection.request.LoadCollectionReq;
import io.milvus.v2.service.vector.request.DeleteReq;
import io.milvus.v2.service.vector.request.InsertReq;
import io.milvus.v2.service.vector.request.QueryReq;
import io.milvus.v2.service.vector.request.SearchReq;
import io.milvus.v2.service.vector.request.UpsertReq;
import io.milvus.v2.service.vector.request.data.FloatVec;
import io.milvus.v2.service.vector.response.DeleteResp;
import io.milvus.v2.service.vector.response.QueryResp;
import io.milvus.v2.service.vector.response.SearchResp;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2025/3/25 10:25
 */
@Slf4j
@Service
public class BizMilvusService extends BaseMilvusService {

    private static final int VARCHAR_FIELD_LENGTH = 1024;
    public static final int VARCHAR_LONG_FIELD_LENGTH = 65535;
    public static final int MODEL_DIMENSION = 1024;

    @Autowired
    private OpenMilvusClient openMilvusClient;

    @Override
    public List<String> getOutputFields() {
        return Lists.newArrayList(
                "chunk_id",
                "kb_id",
                "doc_id",
                "doc_type",
                "content",
                "file_path",
                "file_type",
                "tag",
                "extra"
        );
    }

    /**
     * 创建公共知识库
     */
    public void createPublicCollection(String collectionName) {
        if (openMilvusClient.hasCollection(collectionName)) {
            log.info("collection {} already exists", collectionName);
            return;
        }

        CreateCollectionReq request = CreateCollectionReq.builder()
                .collectionName(collectionName)
                .autoID(false)
                .build();

        // 定义schema
        CreateCollectionReq.CollectionSchema schema = CreateCollectionReq
                .CollectionSchema
                .builder()
                .build();

        // 定义字段
        schema.addField(AddFieldReq.builder()
                .fieldName(BizDocumentConstant.CHUNK_ID)
                .dataType(DataType.VarChar)
                .isPrimaryKey(true)
                .autoID(false)
                .maxLength(VARCHAR_FIELD_LENGTH)
                .description("主键")
                .build());

        schema.addField(AddFieldReq.builder()
                .fieldName(BizDocumentConstant.PROJECT_ID)
                .dataType(DataType.VarChar)
                .maxLength(VARCHAR_FIELD_LENGTH)
                .description("项目ID")
                .build());

        schema.addField(AddFieldReq.builder()
                .fieldName(BizDocumentConstant.KB_ID)
                .dataType(DataType.VarChar)
                .maxLength(VARCHAR_FIELD_LENGTH)
                .description("知识库ID")
                .build());

        schema.addField(AddFieldReq.builder()
                .fieldName(BizDocumentConstant.DOC_ID)
                .dataType(DataType.VarChar)
                .maxLength(VARCHAR_FIELD_LENGTH)
                .description("文档ID")
                .build());

        schema.addField(AddFieldReq.builder()
                .fieldName(BizDocumentConstant.DOC_TYPE)
                .dataType(DataType.VarChar)
                .maxLength(VARCHAR_FIELD_LENGTH)
                .description("文档类型")
                .build());

        schema.addField(AddFieldReq.builder()
                .fieldName(BizDocumentConstant.CONTENT)
                .dataType(DataType.VarChar)
                .maxLength(VARCHAR_LONG_FIELD_LENGTH)
                .description("文档内容")
                .build());

        schema.addField(AddFieldReq.builder()
                .fieldName(BizDocumentConstant.FILE_PATH)
                .dataType(DataType.VarChar)
                .maxLength(VARCHAR_FIELD_LENGTH)
                .description("文件路径")
                .build());

        schema.addField(AddFieldReq.builder()
                .fieldName(BizDocumentConstant.FILE_TYPE)
                .dataType(DataType.VarChar)
                .maxLength(VARCHAR_FIELD_LENGTH)
                .description("文件类型")
                .build());

        schema.addField(AddFieldReq.builder()
                .fieldName(BizDocumentConstant.INDEX)
                .dataType(DataType.Int64)
                .description("索引")
                .build());

        schema.addField(AddFieldReq.builder()
                .fieldName(BizDocumentConstant.TAG)
                .dataType(DataType.VarChar)
                .maxLength(VARCHAR_FIELD_LENGTH)
                .description("标签")
                .build());

        schema.addField(AddFieldReq.builder()
                .fieldName(BizDocumentConstant.EXTRA)
                .dataType(DataType.VarChar)
                .maxLength(VARCHAR_LONG_FIELD_LENGTH)
                .description("额外信息")
                .build());

        schema.addField(AddFieldReq.builder()
                .fieldName(BizDocumentConstant.CREATED_AT)
                .dataType(DataType.Int64)
                .description("创建时间")
                .build());

        schema.addField(AddFieldReq.builder()
                .fieldName(BizDocumentConstant.UPDATED_AT)
                .dataType(DataType.Int64)
                .description("更新时间")
                .build());

        schema.addField(AddFieldReq.builder()
                .fieldName(BizDocumentConstant.EMBEDDING)
                .dataType(DataType.FloatVector)
                .dimension(MODEL_DIMENSION)
                .description("向量")
                .build());

        // 定义向量索引
        IndexParam indexParam = IndexParam.builder()
                .fieldName(BizDocumentConstant.EMBEDDING)
                .indexType(IndexParam.IndexType.HNSW)
                .metricType(IndexParam.MetricType.COSINE)
                .indexName(BizDocumentConstant.EMBEDDING + "_index")
                .extraParams(Map.of("M", "16", "efConstruction", "500"))
                .build();

        IndexParam userProjectInfoIndex = IndexParam.builder()
                .fieldName(BizDocumentConstant.PROJECT_ID)
                .indexType(IndexParam.IndexType.INVERTED)
                .indexName(BizDocumentConstant.PROJECT_ID + "_index")
                .build();

        request.setIndexParams(Lists.newArrayList(indexParam, userProjectInfoIndex));
        request.setCollectionSchema(schema);
        openMilvusClient.createCollection(request);
        log.info("create public collection {} success!", collectionName);
    }

    /**
     * 创建私有知识库
     */
    public void createPrivateCollection(String collectionName) {
        if (openMilvusClient.hasCollection(collectionName)) {
            log.info("collection {} already exists", collectionName);
            return;
        }

        CreateCollectionReq request = CreateCollectionReq.builder()
                .collectionName(collectionName)
                .autoID(false)
                .build();

        // 定义schema
        CreateCollectionReq.CollectionSchema schema = CreateCollectionReq
                .CollectionSchema
                .builder()
                .build();

        // 定义字段
        schema.addField(AddFieldReq.builder()
                .fieldName(BizDocumentConstant.CHUNK_ID)
                .dataType(DataType.VarChar)
                .isPrimaryKey(true)
                .autoID(false)
                .maxLength(VARCHAR_FIELD_LENGTH)
                .description("主键")
                .build());

        schema.addField(AddFieldReq.builder()
                .fieldName(BizDocumentConstant.USER_PROJECT_ID)
                .dataType(DataType.VarChar)
                .maxLength(VARCHAR_FIELD_LENGTH)
                .description("用户项目ID")
                .build());

        schema.addField(AddFieldReq.builder()
                .fieldName(BizDocumentConstant.KB_ID)
                .dataType(DataType.VarChar)
                .maxLength(VARCHAR_FIELD_LENGTH)
                .description("知识库ID")
                .build());

        schema.addField(AddFieldReq.builder()
                .fieldName(BizDocumentConstant.DOC_ID)
                .dataType(DataType.VarChar)
                .maxLength(VARCHAR_FIELD_LENGTH)
                .description("文档ID")
                .build());

        schema.addField(AddFieldReq.builder()
                .fieldName(BizDocumentConstant.DOC_TYPE)
                .dataType(DataType.VarChar)
                .maxLength(VARCHAR_FIELD_LENGTH)
                .description("文档类型")
                .build());

        schema.addField(AddFieldReq.builder()
                .fieldName(BizDocumentConstant.CONTENT)
                .dataType(DataType.VarChar)
                .maxLength(VARCHAR_LONG_FIELD_LENGTH)
                .description("文档内容")
                .build());

        schema.addField(AddFieldReq.builder()
                .fieldName(BizDocumentConstant.FILE_PATH)
                .dataType(DataType.VarChar)
                .maxLength(VARCHAR_FIELD_LENGTH)
                .description("文件路径")
                .build());

        schema.addField(AddFieldReq.builder()
                .fieldName(BizDocumentConstant.FILE_TYPE)
                .dataType(DataType.VarChar)
                .maxLength(VARCHAR_FIELD_LENGTH)
                .description("文件类型")
                .build());

        schema.addField(AddFieldReq.builder()
                .fieldName(BizDocumentConstant.INDEX)
                .dataType(DataType.Int64)
                .description("索引")
                .build());

        schema.addField(AddFieldReq.builder()
                .fieldName(BizDocumentConstant.TAG)
                .dataType(DataType.VarChar)
                .maxLength(VARCHAR_FIELD_LENGTH)
                .description("标签")
                .build());

        schema.addField(AddFieldReq.builder()
                .fieldName(BizDocumentConstant.EXTRA)
                .dataType(DataType.VarChar)
                .maxLength(VARCHAR_LONG_FIELD_LENGTH)
                .description("额外信息")
                .build());

        schema.addField(AddFieldReq.builder()
                .fieldName(BizDocumentConstant.CREATED_AT)
                .dataType(DataType.Int64)
                .description("创建时间")
                .build());

        schema.addField(AddFieldReq.builder()
                .fieldName(BizDocumentConstant.UPDATED_AT)
                .dataType(DataType.Int64)
                .description("更新时间")
                .build());

        schema.addField(AddFieldReq.builder()
                .fieldName(BizDocumentConstant.EMBEDDING)
                .dataType(DataType.FloatVector)
                .dimension(MODEL_DIMENSION)
                .description("向量")
                .build());

        // 定义向量索引
        IndexParam indexParam = IndexParam.builder()
                .fieldName(BizDocumentConstant.EMBEDDING)
                .indexType(IndexParam.IndexType.HNSW)
                .metricType(IndexParam.MetricType.COSINE)
                .indexName(BizDocumentConstant.EMBEDDING + "_index")
                .extraParams(Map.of("M", "16", "efConstruction", "500"))
                .build();

        // 标量索引，倒排索引
        IndexParam userProjectInfoIndex = IndexParam.builder()
                .fieldName(BizDocumentConstant.USER_PROJECT_ID)
                .indexType(IndexParam.IndexType.INVERTED)
                .indexName(BizDocumentConstant.USER_PROJECT_ID + "_index")
                .build();

        request.setIndexParams(Lists.newArrayList(indexParam, userProjectInfoIndex));
        request.setCollectionSchema(schema);
        openMilvusClient.createCollection(request);
        log.info("create private collection {} success!", collectionName);
    }

    @LogExecutionTime
    public SearchResp search(String collectionName,
                             List<String> projectIds,
                             List<Float> vector,
                             Integer topK,
                             Map<String, Object> filter) {

        try {
            loadBefore(collectionName);

            StringBuilder filterBuilder = new StringBuilder();

            if (filter != null && !filter.isEmpty()) {
                int count = 0;
                for (Map.Entry<String, Object> entry : filter.entrySet()) {
                    if (count > 0) {
                        filterBuilder.append(" and ");
                    }
                    if (entry.getValue() instanceof String) {
                        filterBuilder.append(entry.getKey()).append(" == \"").append(entry.getValue()).append("\"");
                    } else {
                        filterBuilder.append(entry.getKey()).append(" == ").append(entry.getValue());
                    }
                    count++;
                }
            }
            if (CollectionUtils.isNotEmpty(projectIds)) {
                if (filterBuilder.length() != 0) {
                    filterBuilder.append(" and ");
                }
                String expr = String.format(BizDocumentConstant.PROJECT_ID + " in [%s]",
                        StringUtils.join(projectIds, ","));
                filterBuilder.append(expr);
            }

            SearchReq request = SearchReq.builder()
                    .collectionName(collectionName)
                    .topK(topK)
                    .annsField(BizDocumentConstant.EMBEDDING)
                    .filter(filterBuilder.toString())
                    .data(Lists.newArrayList(new FloatVec(vector)))
                    .outputFields(getOutputFields())
                    .build();
            return openMilvusClient.search(request);
        } catch (Exception e) {
            log.error("search error", e);
        }
        return SearchResp.builder().searchResults(new ArrayList<>()).build();
    }

    /**
     * 判断并创建公共知识库
     */
    public String createPublicKnowledgeBase(String bizId) {
        String collectionName = bizId + BizDocumentConstant.COLLECTION_SUFFIX_PUBLIC;
        createPublicCollection(collectionName);
        return collectionName;
    }

    /**
     * 判断并创建私有知识库
     */
    public String createPrivateKnowledgeBase(String bizId, String userId, String projectId) {
        String collectionName = calHashKey(bizId, userId, projectId);
        createPrivateCollection(collectionName);
        return collectionName;
    }

    public static String calHashKey(String bizId, String userId, String projectId) {
        // 计算hash值，获取私有知识库序号
        int hash = Math.abs((userId + projectId).hashCode() % 10);
        return bizId + BizDocumentConstant.COLLECTION_TAG_PRIVATE + hash;
    }

    /**
     * 删除collection
     */
    public void deleteCollection(String collectionName) {
        openMilvusClient.dropCollection(collectionName);
    }

    /**
     * 根据用户ID和项目ID删除私有知识库中的记录
     *
     * @param collectionName 集合名称
     * @param userId         用户ID
     * @param projectId      项目ID
     * @return 删除的记录数
     */
    public long deletePrivateRecords(String collectionName, String userId, String projectId) {
        loadBefore(collectionName);

        // 拼接userProjectId
        String userProjectId = userId + "_" + projectId;

        // 创建删除条件，仅删除指定user_project_id的记录
        String expr = BizDocumentConstant.USER_PROJECT_ID + " == \"" + userProjectId + "\"";

        DeleteReq deleteReq = DeleteReq.builder()
                .collectionName(collectionName)
                .filter(expr)  // 使用filter替代expr
                .build();

        DeleteResp resp = openMilvusClient.delete(deleteReq);
        log.info("删除知识库 {} 中的记录，用户ID: {}, 项目ID: {}, 删除数量: {}",
                collectionName, userId, projectId, resp);
        return resp.getDeleteCnt();  // 使用getDeleteCnt替代getDeleteCount
    }

    @Override
    public Boolean getLoadState(String collectionName) {
        return openMilvusClient.getLoadState(GetLoadStateReq.builder().collectionName(collectionName).build());
    }

    @Override
    public void loadCollection(String collectionName) {
        openMilvusClient.loadCollection(LoadCollectionReq.builder().collectionName(collectionName).build());
    }

    public void deleteDocAndChunk(String collectionName, String docId, String chunkId) {
        loadBefore(collectionName);

        String expr = BizDocumentConstant.DOC_ID + " == \"" + docId + "\" or " + BizDocumentConstant.CHUNK_ID + " == \"" + chunkId + "\"";
        DeleteReq deleteReq = DeleteReq.builder()
                .collectionName(collectionName)
                .filter(expr)  // 使用filter替代expr
                .build();
        DeleteResp delete = openMilvusClient.delete(deleteReq);
        log.info("delete doc and chunks, delete count: {}", delete.getDeleteCnt());
    }

    public void deleteDoc(String collectionName, String docId) {
        loadBefore(collectionName);

        String expr = BizDocumentConstant.DOC_ID + " == \"" + docId + "\"";
        DeleteReq deleteReq = DeleteReq.builder()
                .collectionName(collectionName)
                .filter(expr)  // 使用filter替代expr
                .build();
        DeleteResp delete = openMilvusClient.delete(deleteReq);
        log.info("delete doc and chunks, delete count: {}", delete.getDeleteCnt());
    }

    public void deleteChunk(String collectionName, String chunkId) {
        loadBefore(collectionName);

        String expr = BizDocumentConstant.CHUNK_ID + " == \"" + chunkId + "\"";
        DeleteReq deleteReq = DeleteReq.builder()
                .collectionName(collectionName)
                .filter(expr)  // 使用filter替代expr
                .build();
        DeleteResp delete = openMilvusClient.delete(deleteReq);
        log.info("delete doc and chunks, delete count: {}", delete.getDeleteCnt());
    }

    public void updateChunk(String collectionName, JsonObject jsonObject) {
        loadBefore(collectionName);

        UpsertReq request = UpsertReq.builder()
                .collectionName(collectionName)
                .data(List.of(jsonObject))
                .build();
        openMilvusClient.upsert(request);
    }

    public boolean hasDoc(String collectionName, String docId) {
        loadBefore(collectionName);

        // 创建查询参数
        List<String> outputFields = Arrays.asList(BizDocumentConstant.DOC_ID);

        QueryReq queryReq = QueryReq.builder()
                .collectionName(collectionName)
                .filter(BizDocumentConstant.DOC_ID + " == \"" + docId + "\"")
                .outputFields(outputFields)
                .build();

        QueryResp query = openMilvusClient.query(queryReq);
        return query != null && query.getQueryResults() != null && !query.getQueryResults().isEmpty();
    }

    public Map<String, Object> queryByChunkId(String collectionName, String chunkId) {
        loadBefore(collectionName);

        QueryReq queryReq = QueryReq.builder()
                .collectionName(collectionName)
                .filter(BizDocumentConstant.CHUNK_ID + " == \"" + chunkId + "\"")
                .build();

        QueryResp query = openMilvusClient.query(queryReq);
        if (query != null && query.getQueryResults() != null && !query.getQueryResults().isEmpty()) {
            return query.getQueryResults().get(0).getEntity();
        }
        return new HashMap<>();
    }

    public List<QueryResp.QueryResult> listDocuments(String collectionName, Integer page, Integer pageSize) {
        loadBefore(collectionName);

        // 创建查询参数
        QueryReq queryReq = QueryReq.builder()
                .collectionName(collectionName)
                .filter(BizDocumentConstant.DOC_TYPE + " == \"doc\"")
                .offset((long) (page - 1) * (pageSize))
                .limit(pageSize)
                .build();

        QueryResp query = openMilvusClient.query(queryReq);
        if (query != null && query.getQueryResults() != null && !query.getQueryResults().isEmpty()) {
            return query.getQueryResults();
        }
        return new ArrayList<>();
    }

    public int countDocuments(String collectionName) {
        loadBefore(collectionName);

        // 创建查询参数
        QueryReq queryReq = QueryReq.builder()
                .collectionName(collectionName)
                .filter(BizDocumentConstant.DOC_TYPE + " == \"doc\"")
                .outputFields(List.of()) // 不需要返回具体字段
                .build();

        QueryResp query = openMilvusClient.query(queryReq);
        if (query != null && query.getQueryResults() != null && !query.getQueryResults().isEmpty()) {
            return query.getQueryResults().size();
        }
        return 0;
    }

    private void loadBefore(String collectionName) {
        if (!getLoadState(collectionName)) {
            log.info("collection {} is not loaded, load it first", collectionName);
            if (!hasCollection(collectionName)) {
                throw new BizException(ErrorCodeConstant.OPEN_RAG_KB_NOT_FOUND_ERROR);
            }
            loadCollection(collectionName);
        }
    }

    public List<QueryResp.QueryResult> getDocumentChunks(String collectionName, String docId) {
        loadBefore(collectionName);

        // 创建查询参数
        QueryReq queryReq = QueryReq.builder()
                .collectionName(collectionName)
                .filter(BizDocumentConstant.DOC_ID + " == \"" + docId + "\" and " + BizDocumentConstant.DOC_TYPE + " == \"chunk\"")
                .build();

        QueryResp query = openMilvusClient.query(queryReq);
        if (query != null && query.getQueryResults() != null && !query.getQueryResults().isEmpty()) {
            return query.getQueryResults();
        }
        return new ArrayList<>();
    }

    public void insert(String collectionName, List<JsonObject> list) {
        loadBefore(collectionName);

        InsertReq insertReq = InsertReq.builder()
                .collectionName(collectionName)
                .data(list)
                .build();
        openMilvusClient.insert(insertReq);
    }
}