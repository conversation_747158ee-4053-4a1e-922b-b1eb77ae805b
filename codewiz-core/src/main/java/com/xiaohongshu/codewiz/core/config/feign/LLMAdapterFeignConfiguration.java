package com.xiaohongshu.codewiz.core.config.feign;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;

import feign.RequestInterceptor;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2025/3/3
 */
public class LLMAdapterFeignConfiguration {
    @Value("${feign-client.llm-adapter.api-key}")
    private String apiKey;

    @Bean
    public RequestInterceptor requestInterceptor() {
        return template -> {
            template.header("api-key", apiKey);
        };
    }
}
