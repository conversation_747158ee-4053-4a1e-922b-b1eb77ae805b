package com.xiaohongshu.codewiz.core.entity.feedback;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

/**
 * <p>
 * 反馈表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-24
 */
@Data
@TableName("feedback")
public class FeedbackDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 反馈ID
     */
    private String feedbackId;

    /**
     * 反馈描述
     */
    private String feedbackDesc;

    /**
     * 日志内容, 对象存储key
     */
    private String feedbackLogFile;

    /**
     * 用户邮箱
     */
    private String feedbackUserEmail;

    /**
     * 编辑器信息
     */
    private String feedbackEditor;

    /**
     * 反馈扩展信息
     */
    private String feedbackExtra;

    /**
     * 创建时间
     */
    private Long createAt;
}