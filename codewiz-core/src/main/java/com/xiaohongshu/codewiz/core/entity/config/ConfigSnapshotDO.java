package com.xiaohongshu.codewiz.core.entity.config;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

/**
 * <p>
 * 配置快照表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-21
 */
@Data
@TableName("config_snapshot")
public class ConfigSnapshotDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 配置名称
     */
    private String configName;

    /**
     * 配置类型 1-plugin 2-lsp
     */
    private Integer configType;

    /**
     * 配置描述
     */
    private String configDesc;

    /**
     * 配置信息
     */
    private String configValue;

    /**
     * 配置版本
     */
    private String configVersion;

    /**
     * 是否最新
     */
    private Boolean isLasted;

    /**
     * 创建时间
     */
    private Long createAt;

    /**
     * 更新时间
     */
    private Long updateAt;
} 