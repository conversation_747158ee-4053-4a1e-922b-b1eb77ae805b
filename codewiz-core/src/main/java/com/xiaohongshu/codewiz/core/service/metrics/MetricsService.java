package com.xiaohongshu.codewiz.core.service.metrics;

import java.util.Objects;
import java.util.Properties;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.xiaohongshu.codewiz.core.entity.metrics.MetricsRequest;
import com.xiaohongshu.codewiz.core.utils.JsonMapperUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2025/4/23 15:55
 */
@Slf4j
@Service
public class MetricsService implements InitializingBean, DisposableBean {

    @Value("${kafka.bootstrap-servers}")
    private String bootstrapServers;

    @Value("${kafka.topic}")
    private String topic;

    private KafkaProducer<String, String> producer;

    private static final int MAX_JSON_LENGTH = 60000; // 数据库字段最大长度限制,防止有中文字符，设置要比65535严格一些

    @Override
    public void afterPropertiesSet() {
        try {
            Properties props = new Properties();
            props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
            props.put(ProducerConfig.ACKS_CONFIG, "1");
            props.put(ProducerConfig.RETRIES_CONFIG, 10);
            props.put(ProducerConfig.BATCH_SIZE_CONFIG, 65536);
            props.put(ProducerConfig.LINGER_MS_CONFIG, 500);
            props.put(ProducerConfig.COMPRESSION_TYPE_CONFIG, "lz4");
            props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class.getName());
            props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class.getName());

            // create a producer once during initialization
            this.producer = new KafkaProducer<>(props);
            log.info("MetricsService initialized with bootstrap servers: {}", bootstrapServers);
        } catch (Exception e) {
            log.error("Failed to initialize Kafka producer: {}", e.getMessage(), e);
        }
    }

    public void sendMetrics(MetricsRequest request) {
        log.info("Sending metrics: {}", JsonMapperUtils.toJson(request));
        switch (request.getMetricsKey()) {
            case "chat_code_suggested":
                chatSuggestedMetrics(request);
                break;
            default:
                mainSendMetrics(request);
                break;
        }
    }


    private void mainSendMetrics(MetricsRequest request) {
        try {
            String json = JsonMapperUtils.toJson(request);
            log.info("Sending main metrics: {}", json);

            if (!checkJson(json)) {
                log.error("check not pass! metrics not sent!");
                return;
            }

            producer.send(new ProducerRecord<>(topic, json));
            log.info("Metrics sent successfully");
        } catch (Exception e) {
            log.error("Failed to send metrics: {}", e.getMessage(), e);
        }
    }

    private boolean checkJson(String json) {
        return checkJsonLength(json);
    }

    /**
     * 检查 JSON 数据长度是否符合要求
     *
     * @param json JSON 字符串
     * @return 如果长度小于等于限制值返回 true，否则返回 false
     */
    private boolean checkJsonLength(String json) {
        if (json.length() > MAX_JSON_LENGTH) {
            log.error("Metrics JSON length exceeds limit: {} > {}", json.length(), MAX_JSON_LENGTH);
            return false;
        }
        return true;
    }

    private void chatSuggestedMetrics(MetricsRequest request) {
        if (!StringUtils.equals(request.getMetricsKey(), "chat_code_suggested")) {
            return;
        }

        log.info("chatSuggestedMetrics, request == {}", request);

        try {
            ChatSuggestedValue chatSuggestedValue = JsonMapperUtils.fromJson(request.getMetricsValue(), ChatSuggestedValue.class);
            if (Objects.isNull(chatSuggestedValue) || CollectionUtils.isEmpty(chatSuggestedValue.getCodeSuggestedSnippets())) {
                log.info("chatSuggestedValue is null, sent all metrics!");
                mainSendMetrics(request);
                return;
            }
            log.info("chatSuggestedValue, size == {}", CollectionUtils.size(chatSuggestedValue.getCodeSuggestedSnippets()));
            for (ChatSuggestedValue.CodeSuggestedSnippet codeSuggestedSnippet : chatSuggestedValue.getCodeSuggestedSnippets()) {
                chatSuggestedValue.setCodeSuggestedSnippetSingle(codeSuggestedSnippet);
                request.setMetricsValue(JsonMapperUtils.toJson(chatSuggestedValue));
                mainSendMetrics(request);
            }
        } catch (Exception e) {
            log.warn("Failed to send chat suggested metrics: {}", e.getMessage(), e);
        }
    }

    @Override
    public void destroy() {
        if (producer != null) {
            producer.close();
        }
    }
}