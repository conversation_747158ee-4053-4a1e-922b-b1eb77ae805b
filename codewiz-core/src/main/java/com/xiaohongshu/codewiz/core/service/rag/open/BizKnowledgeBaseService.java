package com.xiaohongshu.codewiz.core.service.rag.open;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.xiaohongshu.codewiz.core.constant.BizDocumentConstant;
import com.xiaohongshu.codewiz.core.constant.ErrorCodeConstant;
import com.xiaohongshu.codewiz.core.constant.enums.OpenKnowledgeTypeEnum;
import com.xiaohongshu.codewiz.core.entity.rag.open.BizKnowledgeCreateReq;
import com.xiaohongshu.codewiz.core.entity.rag.open.BizKnowledgeCreateResp;
import com.xiaohongshu.codewiz.core.entity.rag.open.BizKnowledgeDeleteReq;
import com.xiaohongshu.codewiz.core.exception.BizException;
import com.xiaohongshu.codewiz.core.service.milvus.BizMilvusService;
import com.xiaohongshu.infra.utils.ObjectMapperUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2025/3/24 15:55
 */
@Slf4j
@Service
public class BizKnowledgeBaseService {

    @Resource
    BizMilvusService bizMilvusService;
    @Resource
    BizPermissionService bizPermissionService;

    /**
     * 创建知识库
     *
     * @param bizKnowledge 知识库创建请求
     * @return 知识库创建响应
     */
    public BizKnowledgeCreateResp createBizKnowledgeBase(BizKnowledgeCreateReq bizKnowledge) {
        // 参数校验
        if (StringUtils.isBlank(bizKnowledge.getBizId())) {
            throw new BizException(ErrorCodeConstant.OPEN_RAG_BIZ_ID_ERROR);
        }

        if (StringUtils.isBlank(bizKnowledge.getKbType())) {
            throw new BizException(ErrorCodeConstant.OPEN_RAG_KB_TYPE_ERROR);
        }
        log.info("创建知识库: {}", ObjectMapperUtils.toJSON(bizKnowledge));

        // 权限校验
        bizPermissionService.checkPermission(bizKnowledge.getBizId(), true);

        String kbId;
        if (OpenKnowledgeTypeEnum.PUBLIC.getValue().equalsIgnoreCase(bizKnowledge.getKbType())) {
            // 创建公共知识库
            kbId = bizMilvusService.createPublicKnowledgeBase(bizKnowledge.getBizId());
        } else if (OpenKnowledgeTypeEnum.PRIVATE.getValue().equalsIgnoreCase(bizKnowledge.getKbType())) {
            // 创建私有知识库
            if (StringUtils.isBlank(bizKnowledge.getUserId())) {
                throw new BizException(ErrorCodeConstant.OPEN_RAG_USER_ID_ERROR);
            }
            if (StringUtils.isBlank(bizKnowledge.getProjectId())) {
                throw new BizException(ErrorCodeConstant.OPEN_RAG_PROJECT_ID_ERROR);
            }

            kbId = bizMilvusService.createPrivateKnowledgeBase(
                    bizKnowledge.getBizId(),
                    bizKnowledge.getUserId(),
                    bizKnowledge.getProjectId());
        } else {
            throw new BizException(ErrorCodeConstant.OPEN_RAG_KB_TYPE_ERROR);
        }

        BizKnowledgeCreateResp resp = new BizKnowledgeCreateResp();
        resp.setKbId(kbId);

        return resp;
    }

    /**
     * 删除知识库
     *
     * @param req 知识库删除请求
     */
    public void deleteBizKnowledgeBase(BizKnowledgeDeleteReq req) {
        // 参数校验
        if (StringUtils.isBlank(req.getKbId())) {
            throw new BizException(ErrorCodeConstant.OPEN_RAG_KB_ID_ERROR);
        }
        if (StringUtils.isBlank(req.getBizId())) {
            throw new BizException(ErrorCodeConstant.OPEN_RAG_BIZ_ID_ERROR);
        }

        String kbId = req.getKbId();

        log.info("删除知识库: {}", ObjectMapperUtils.toJSON(req));
        // 验证用户权限
        bizPermissionService.checkPermission(req.getBizId(), true);

        // 1. 判断是公共知识库还是私有知识库
        if (kbId.contains(BizDocumentConstant.COLLECTION_SUFFIX_PUBLIC)) {
            // 公共知识库，不支持删除
            log.warn("尝试删除公共知识库 {}, 操作被拒绝", kbId);
            throw new BizException(ErrorCodeConstant.PARAMETER_ERROR);
        } else if (kbId.contains(BizDocumentConstant.COLLECTION_TAG_PRIVATE)) {
            // 私有知识库，需要验证用户权限
            if (StringUtils.isBlank(req.getUserId())) {
                throw new BizException(ErrorCodeConstant.OPEN_RAG_USER_ID_ERROR);
            }

            if (StringUtils.isBlank(req.getBizId())) {
                throw new BizException(ErrorCodeConstant.OPEN_RAG_BIZ_ID_ERROR);
            }

            // 验证知识库ID格式是否正确（格式应为：{bizId}_private_{hash}）
            String[] parts = kbId.split(BizDocumentConstant.COLLECTION_TAG_PRIVATE);
            if (parts.length != 2 || !parts[0].equals(req.getBizId())) {
                log.warn("知识库ID格式错误: {}", kbId);
                throw new BizException(ErrorCodeConstant.PARAMETER_ERROR);
            }

            // 从Milvus中删除该知识库中指定用户项目的记录，而不是删除整个collection
            if (StringUtils.isBlank(req.getProjectId())) {
                throw new BizException(ErrorCodeConstant.OPEN_RAG_PROJECT_ID_ERROR);
            }

            long deletedCount = bizMilvusService.deletePrivateRecords(kbId, req.getUserId(), req.getProjectId());
            log.info("私有知识库 {} 中的记录已删除，用户ID: {}, 项目ID: {}, 删除记录数: {}",
                    kbId, req.getUserId(), req.getProjectId(), deletedCount);

        } else {
            log.warn("未知知识库类型: {}", kbId);
            throw new BizException(ErrorCodeConstant.PARAMETER_ERROR);
        }
    }
}
