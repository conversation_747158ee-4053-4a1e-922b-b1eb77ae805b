package com.xiaohongshu.codewiz.core.entity.rag.scenecase;

import java.util.List;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.xiaohongshu.codewiz.core.entity.rag.RagDocument;
import com.xiaohongshu.codewiz.core.utils.JsonMapperUtils;

import io.milvus.v2.service.vector.response.SearchResp;
import lombok.Data;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @date 2024/12/9 19:46
 */
@Data
@SuperBuilder
public class CaseMilvusSearchResponse implements RagDocument {
    private CaseKnowledgeSearchInfo entity;
    private Float score;
    private String annField;
    private String collectionName;
    private Object id;

    public static CaseMilvusSearchResponse from(SearchResp.SearchResult SearchResp,
                                                String annField,
                                                String collectionName) {
        return CaseMilvusSearchResponse.builder()
                .entity(covert(SearchResp.getEntity()))
                .score(SearchResp.getScore())
                .id(SearchResp.getId())
                .annField(annField)
                .collectionName(collectionName)
                .build();
    }

    private static CaseKnowledgeSearchInfo covert(Map<String, Object> entity) {
        Map<String, Object> itemData = JsonMapperUtils.fromJson(entity.get("item_data").toString());
        entity.put("item_data", itemData);
        return JsonMapperUtils.fromJson(JsonMapperUtils.toJson(entity), CaseKnowledgeSearchInfo.class);
    }

    @Data
    public static class CaseKnowledgeSearchInfo {
        @JsonProperty("knowledge_base")
        private String knowledgeBase;
        @JsonProperty("field_name")
        private String fieldName;
        @JsonProperty("field_content")
        private String fieldContent;
        @JsonProperty("item_data")
        private ItemData itemData;
        @JsonProperty("item_id")
        private String itemId;
        @JsonProperty("issue_id")
        private String issueId;
    }

    @Data
    public static class ItemData {
        @JsonProperty("code_snippet")
        private String codeSnippet;

        @JsonProperty("target_function")
        private String targetFunction;

        @JsonProperty("review_type")
        private String reviewType;

        @JsonProperty("issue_detail")
        private List<Map<String, Object>> issueDetail;

        @JsonProperty("repo")
        private String repo;

        @JsonProperty("language")
        private String language;

        @JsonProperty("extra")
        private Map<String, Object> extra;

        @JsonProperty("item_id")
        private String itemId;

        @JsonProperty("code_explain")
        private String code_explain;

        @JsonProperty("llm_structure")
        private String llmStructure;

        @JsonProperty("llm_concept")
        private List<Object> llmConcept;

        @JsonProperty("llm_hierarchy")
        private String llmHierarchy;

        @JsonProperty("llm_keyword")
        private String llmKeyword;
    }
}