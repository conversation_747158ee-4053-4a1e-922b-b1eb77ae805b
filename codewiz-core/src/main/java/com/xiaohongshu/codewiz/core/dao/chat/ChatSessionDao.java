package com.xiaohongshu.codewiz.core.dao.chat;

import java.time.LocalDateTime;
import java.util.Arrays;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xiaohongshu.codewiz.core.constant.ChatCommonConstant;
import com.xiaohongshu.codewiz.core.entity.chat.ChatSessionDO;
import com.xiaohongshu.codewiz.core.mapper.chat.ChatSessionMapper;

/**
 * <p>
 * 会话表 mapper操作类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@Service
public class ChatSessionDao extends ServiceImpl<ChatSessionMapper, ChatSessionDO> {
    public void delSession(String sessionId, String userId) {
        LambdaUpdateWrapper<ChatSessionDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ChatSessionDO::getUserId, userId)
                .eq(ChatSessionDO::getSessionId, sessionId)
                .set(ChatSessionDO::getIsDelete, ChatCommonConstant.YES)
                .set(ChatSessionDO::getUpdateTime, LocalDateTime.now());
        this.update(updateWrapper);
    }

    public boolean editSessionName(String sessionId, String newName) {
        // 长度超限时需要截取
        if (newName.getBytes().length > 512) {
            newName = new String(Arrays.copyOfRange(newName.getBytes(), 0, 512));
        }
        // 更新新名称&&更新时间
        LambdaUpdateWrapper<ChatSessionDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper
                .eq(ChatSessionDO::getSessionId, sessionId)
                .set(ChatSessionDO::getSessionName, newName)
                .set(ChatSessionDO::getUpdateTime, LocalDateTime.now());
        return this.update(updateWrapper);
    }

    public IPage<ChatSessionDO> querySessionList(String userId, String workspaceId, String key, Integer pageNo, Integer pageSize) {
        LambdaQueryWrapper<ChatSessionDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(ChatSessionDO::getUserId, userId)
                .eq(ChatSessionDO::getWorkspaceId, workspaceId)
                .eq(ChatSessionDO::getIsDelete, ChatCommonConstant.NO)
                .orderByDesc(ChatSessionDO::getUpdateTime);
        if (StringUtils.isNotBlank(key)) {
            queryWrapper.like(ChatSessionDO::getSessionName, key);
        }
        // 分页查询(pageNo从1开始
        return this.page(new Page<>(pageNo, pageSize), queryWrapper);
    }
}
