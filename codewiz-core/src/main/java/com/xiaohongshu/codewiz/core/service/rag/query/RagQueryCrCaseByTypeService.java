package com.xiaohongshu.codewiz.core.service.rag.query;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.function.BinaryOperator;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.xiaohongshu.codewiz.core.client.RerankClient;
import com.xiaohongshu.codewiz.core.constant.enums.RagDataTypeEnum;
import com.xiaohongshu.codewiz.core.entity.rag.FewShotCase;
import com.xiaohongshu.codewiz.core.entity.rag.RagDataQueryRequest;
import com.xiaohongshu.codewiz.core.entity.rerank.RerankRequestDto;
import com.xiaohongshu.codewiz.core.entity.rerank.RerankResultDto;
import com.xiaohongshu.codewiz.core.entity.rerank.RerankType;
import com.xiaohongshu.codewiz.core.service.rag.RagCollectionService;
import com.xiaohongshu.codewiz.core.service.rag.RagDataContext;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2025/2/28 16:26
 */
@Slf4j
@Service
public class RagQueryCrCaseByTypeService extends RagQueryCrCaseService {

    @Resource
    private RagCollectionService ragCollectionService;
    @Resource
    private RerankClient rerankClient;

    @Override
    protected void analyze(RagDataContext<FewShotCase> context) {
        super.analyze(context);
        // milvus不支持group，这里设置一个较大的数，目的是尽量覆盖较多的problem type
        context.getTopK().setRecallMilvusTopK(context.getQueryRequest().getExtension().getReturnNum() * 10);
        // 设置rerank topK
        context.getTopK().setRerankTopK(context.getQueryRequest().getExtension().getReturnNum());
    }

    @Override
    protected void rerank(RagDataContext<FewShotCase> context) {
        Integer topKey = context.getQueryRequest().getExtension().getReturnNum();
        if (context.getDocuments() == null || context.getDocuments().isEmpty()) {
            return;
        }

        // query 和case的fieldContent的相似度
        List<RerankRequestDto> requestDtos = context.getDocuments()
                .stream()
                .map(fewShotCase -> RerankRequestDto.convertFromFewShotCase(fewShotCase, context.getQueryRequest().getQuery()))
                .collect(Collectors.toList());
        List<RerankResultDto> rerankResultDtoList = rerankClient.getRerankResult(requestDtos, RerankType.GTE_RERANK);
        if (CollectionUtils.isEmpty(rerankResultDtoList)) {
            log.info("rerankResultDtoList is empty");
            return;
        }
        // 设置分数
        Map<String, RerankResultDto> fieldContentToRerankResultDto = rerankResultDtoList.stream()
                .collect(Collectors.toMap(RerankResultDto::getQuery, Function.identity(), (k1, k2) -> k1));
        for (FewShotCase fewShotCase : context.getDocuments()) {
            RerankResultDto rerankResultDto = fieldContentToRerankResultDto.get(fewShotCase.getFieldContent());
            if (rerankResultDto == null) {
                continue;
            }
            fewShotCase.setScore(rerankResultDto.getScore().floatValue());
            fewShotCase.setProblemType(fewShotCase.getIssueDetails().get(0).get("problemType").toString());
        }

        // 按照problem type分组, 取每个分组的分数最大值
        Map<String, FewShotCase> problemTypeToFewShotCase = context.getDocuments()
                .stream()
                .collect(Collectors.toMap(FewShotCase::getProblemType, Function.identity(),
                        BinaryOperator.maxBy(Comparator.comparingDouble(FewShotCase::getScore))));
        // 按照分数从高到低排序，取前topK
        List<FewShotCase> sortedFewShotCases = problemTypeToFewShotCase.values().stream()
                .sorted(Comparator.comparingDouble(FewShotCase::getScore).reversed())
                .limit(topKey)
                .collect(Collectors.toList());
        context.setDocuments(sortedFewShotCases);
    }

    @Override
    protected String getCollectionName(RagDataQueryRequest request) {
        String collectionName = ragCollectionService.getCrCollection();
        if (StringUtils.isNotEmpty(request.getExtension().getCollectionName())) {
            collectionName = request.getExtension().getCollectionName();
        }
        return collectionName;
    }

    @Override
    public RagDataTypeEnum source() {
        return RagDataTypeEnum.CR_CASE_BY_TYPE;
    }
} 