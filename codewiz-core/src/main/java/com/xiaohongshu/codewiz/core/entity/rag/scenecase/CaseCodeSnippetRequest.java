package com.xiaohongshu.codewiz.core.entity.rag.scenecase;

import java.util.Map;

import com.xiaohongshu.codewiz.core.client.EmbeddingClient;
import com.xiaohongshu.codewiz.core.constant.enums.RagCaseKnowledgeEnum;
import com.xiaohongshu.codewiz.core.entity.rag.FewShotCase;


/**
 * <AUTHOR>
 * @date 2025/2/25 19:11
 */
public class CaseCodeSnippetRequest extends CaseMilvusRequest {
    public CaseCodeSnippetRequest(FewShotCase fewShotCase,
                                  Map<String, Object> knowledge,
                                  EmbeddingClient embeddingClient) {
        super(fewShotCase, knowledge, embeddingClient);
    }

    @Override
    public RagCaseKnowledgeEnum getField() {
        return RagCaseKnowledgeEnum.CODE_SNIPPET;
    }
}
