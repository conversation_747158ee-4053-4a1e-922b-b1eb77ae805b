package com.xiaohongshu.codewiz.core.entity.customrule;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

/**
 * Author: sunliguo
 * Date: 2025/3/20 10:21
 * Description:
 */
@Data
@TableName("business_custom_rule")
public class BusinessCustomRuleDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID，自增长
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 仓库ID，标识代码所属的仓库
     */
    @TableField("project_id")
    private String projectId;

    /**
     * 开发语言，如Java、Python、Go等
     */
    @TableField("programming_language")
    private String programmingLanguage;

    /**
     * 规则的自然语言描述，详细说明该编码规则的内容和要求
     */
    @TableField("rule_description")
    private String ruleDescription;

    /**
     * 问题类型，如安全漏洞、代码质量、性能问题等
     */
    @TableField("issue_type")
    private String issueType;

    /**
     * 问题严重程度，表示违反该规则的严重性
     */
    @TableField("severity")
    private String severity;

    /**
     * 代码示例，展示违反规则的代码和正确的代码示例
     */
    @TableField("code_example")
    private String codeExample;

    /**
     * 创建人
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 更新人
     */
    @TableField("updated_by")
    private String updatedBy;

    /**
     * 创建时间
     */
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @TableField(value = "updated_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedTime;

    /**
     * 是否删除: 0-未删除, 1-已删除
     */
    @TableLogic
    @TableField("is_deleted")
    private Integer isDeleted;

}
