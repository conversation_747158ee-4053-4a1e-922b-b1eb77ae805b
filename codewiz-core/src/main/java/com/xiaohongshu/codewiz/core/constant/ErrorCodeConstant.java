package com.xiaohongshu.codewiz.core.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/2/25 19:11
 */
@Getter
@AllArgsConstructor
public enum ErrorCodeConstant {

    SERVER_ERROR(500, "服务器错误"),
    PARAMETER_ERROR(400, "参数错误"),

    // rag
    RAG_DATA_SCENE_ERROR(60001, "rag场景发现失败"),
    RAG_QUERY_NOT_FOUND_QUERY(60002, "rag查询失败，query为空"),
    RAG_QUERY_RETURN_NUM_ERROR(60003, "rag查询失败，returnNum参数错误"),
    RAG_DATA_ADD_DOCUMENTS_NOT_FOUND(60004, "rag添加文档失败，documents为空"),
    RAG_DATA_QUERY_EMBEDDING_FAILED(60005, "rag查询文档失败，向量获取失败"),
    RAG_DATA_QUERY_CODE_EXPLANATION_FAILED(60006, "rag查询文档失败，代码解释获取失败"),
    RAG_DATA_ADD_ERROR(60007, "rag添加文档失败"),
    // completion
    CODE_COMPLETION_SERVICE_ERROR(70001, "代码补全服务异常"),
    // OPEN RAG AI
    OPEN_RAG_BIZ_ID_ERROR(80001, "open rag 业务线ID不能为空"),
    OPEN_RAG_PROJECT_ID_ERROR(80002, "open rag 项目ID不能为空"),
    OPEN_RAG_USER_ID_ERROR(80003, "open rag 用户ID不能为空"),
    OPEN_RAG_KB_TYPE_ERROR(80004, "open rag 知识库类型不能为空"),
    OPEN_RAG_KB_ID_ERROR(80005, "open rag 知识库ID不能为空"),
    OPEN_RAG_FILE_PATH_ERROR(80006, "open rag 文件路径不能为空"),
    OPEN_RAG_FILE_TYPE_ERROR(80007, "open rag 文件类型不能为空"),
    OPEN_RAG_CONTENT_ERROR(80008, "open rag 内容不能为空"),
    OPEN_RAG_DOC_ID_ERROR(80009, "open rag 文档id不能为空"),
    OPEN_RAG_CHUNK_ID_ERROR(80010, "open rag chunk id不能为空"),
    OPEN_RAG_DOC_ERROR(80011, "open rag 文档不存在"),
    OPEN_RAG_CHUNK_ERROR(80012, "open rag chunk不存在"),
    OPEN_RAG_SEARCH_TYPE_ERROR(80013, "open rag 搜索场景不存在"),
    OPEN_RAG_QUERY_ERROR(80014, "open rag 搜索内容不存在"),
    OPEN_RAG_TOP_K_ERROR(80015, "open rag TOP_K无效"),
    OPEN_RAG_TOKEN_NOT_FOUND_ERROR(80016, "open rag  header 中 token 不能为空"),
    OPEN_RAG_TOKEN_PERMISSION_ERROR(80017, "open rag  token 权限不足"),
    OPEN_RAG_KB_ID_INVALID_ERROR(80018, "open rag  知识库ID格式错误"),
    OPEN_RAG_KB_NOT_FOUND_ERROR(80019, "open rag  知识库不存在"),
    OPEN_RAG_CONTENT_LENGTH_LIMIT(80020, "open rag 内容长度超过限制"),
    CALL_GRAPH_SEARCH_ERROR(90001, "调用图搜索失败"),
    ;

    private final int status;
    private final String message;
}