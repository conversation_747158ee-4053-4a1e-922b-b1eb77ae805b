package com.xiaohongshu.codewiz.core.service.rag.query;

import com.xiaohongshu.codewiz.core.constant.enums.RagDataTypeEnum;
import com.xiaohongshu.codewiz.core.entity.rag.RagDataQueryRequest;
import com.xiaohongshu.codewiz.core.entity.rag.RagDataQueryResponse;
import com.xiaohongshu.codewiz.core.service.factory.BaseService;

/**
 * <AUTHOR>
 * @date 2025/2/28 15:48
 */
public interface RagQueryDataService extends BaseService<RagDataTypeEnum> {

    RagDataQueryResponse query(RagDataQueryRequest request);
}
