package com.xiaohongshu.codewiz.core.service.rag.open.split;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service("base")
public class BizDocumentChunkSplitBase implements BizDocumentChunkSplitStrategy {

    public static final int CHUNK_DEFAULT_COUNT = 200;

    @Override
    public List<String> split(String content) {
        if (StringUtils.isBlank(content)) {
            log.info("content is empty");
            return new ArrayList<>();
        }
        List<String> lines = Arrays.asList(content.split("\n"));

        List<String> chunks = IntStream.range(0, (lines.size() + CHUNK_DEFAULT_COUNT - 1) / CHUNK_DEFAULT_COUNT)
                .mapToObj(i -> String.join("\n",
                        lines.subList(i * CHUNK_DEFAULT_COUNT, Math.min((i + 1) * CHUNK_DEFAULT_COUNT, lines.size()))))
                .collect(Collectors.toList());

        // 打印每个chunk的内容
        chunks.forEach(chunk -> {
            System.out.println("Chunk:");
            System.out.println(chunk);
            System.out.println();
        });
        return chunks;
    }
}
