package com.xiaohongshu.codewiz.core.dao.config;

import java.util.List;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xiaohongshu.codewiz.core.entity.config.PersonConfigDO;
import com.xiaohongshu.codewiz.core.mapper.config.PersonConfigMapper;

import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 个人配置表 DAO层
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-21
 */
@Slf4j
@Service
public class PersonConfigDao extends ServiceImpl<PersonConfigMapper, PersonConfigDO> {

    /**
     * 根据用户邮箱和配置快照ID列表查询用户个人配置
     *
     * @param userEmail   用户邮箱
     * @param snapshotIds 配置快照ID列表
     * @return 用户个人配置列表
     */
    public List<PersonConfigDO> queryByUserAndSnapshots(String userEmail, List<Long> snapshotIds) {
        LambdaQueryWrapper<PersonConfigDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PersonConfigDO::getUserEmail, userEmail);
        queryWrapper.in(PersonConfigDO::getConfigSnapshotId, snapshotIds);
        return this.list(queryWrapper);
    }

    /**
     * 根据用户邮箱查询最新的一条个人配置记录
     *
     * @param userEmail 用户邮箱
     * @return 用户最新的个人配置
     */
    public PersonConfigDO queryLatestByUserEmail(String userEmail, String configName) {
        LambdaQueryWrapper<PersonConfigDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PersonConfigDO::getUserEmail, userEmail)
                .eq(PersonConfigDO::getConfigName, configName)
                .orderByDesc(PersonConfigDO::getUpdateAt)
                .last("LIMIT 1");
        return this.getOne(queryWrapper);
    }

    /**
     * 更新或插入用户个人配置
     *
     * @param personConfig 用户个人配置
     * @return 是否成功
     */
    public boolean saveOrUpdatePersonConfig(PersonConfigDO personConfig) {
        // 先查询是否存在
        LambdaQueryWrapper<PersonConfigDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PersonConfigDO::getUserEmail, personConfig.getUserEmail());
        queryWrapper.eq(PersonConfigDO::getConfigSnapshotId, personConfig.getConfigSnapshotId());
        PersonConfigDO existConfig = this.getOne(queryWrapper);

        if (existConfig != null) {
            // 存在则更新
            log.info("updatePersonConfig, existConfig: {}", existConfig);
            LambdaUpdateWrapper<PersonConfigDO> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(PersonConfigDO::getId, existConfig.getId());
            updateWrapper.set(PersonConfigDO::getConfigValue, personConfig.getConfigValue());
            updateWrapper.set(PersonConfigDO::getUpdateAt, System.currentTimeMillis());
            return this.update(updateWrapper);
        } else {
            // 不存在则插入
            log.info("savePersonConfig, personConfig: {}", personConfig);
            personConfig.setCreateAt(System.currentTimeMillis());
            personConfig.setUpdateAt(System.currentTimeMillis());
            return this.save(personConfig);
        }
    }
} 