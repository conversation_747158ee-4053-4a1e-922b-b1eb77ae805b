package com.xiaohongshu.codewiz.core.entity.rerank;


import com.xiaohongshu.codewiz.core.entity.rag.FewShotCase;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/3/28
 */
@Data
public class RerankRequestDto {
    private String query;
    private String passage;

    public static RerankRequestDto convertFromFewShotCase(FewShotCase fewShotCase, String content) {
        RerankRequestDto rerankRequestDto = new RerankRequestDto();
        rerankRequestDto.setQuery(fewShotCase.getFieldContent());
        rerankRequestDto.setPassage(content);
        return rerankRequestDto;
    }
}
