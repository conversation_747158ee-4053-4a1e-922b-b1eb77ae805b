package com.xiaohongshu.codewiz.core.config;

import java.lang.reflect.Array;
import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.PropertyFilter;

import lombok.extern.slf4j.Slf4j;

/**
 * mysql-json映射处理
 *
 * @param <T>
 * <AUTHOR>
 */
@Slf4j
@MappedJdbcTypes(value = {JdbcType.VARCHAR})
public class MysqlJsonHandler<T> extends BaseTypeHandler<T> {

    private Class<T> classType;

    public MysqlJsonHandler(Class<T> classType) {
        this.classType = classType;
    }

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, T parameter, JdbcType jdbcType)
            throws SQLException {
        try {
            ps.setString(i, JSON.toJSONString(parameter, new JsonEmptyCollectionFilter()));
        } catch (Exception e) {
            log.warn("MysqlJsonHandler setNonNullParameter error", e);
            // 如果序列化失败，存储空对象
            ps.setString(i, "{}");
        }
    }

    @Override
    public T getNullableResult(ResultSet rs, String columnName) throws SQLException {
        return parseStrToObj(rs.getString(columnName));
    }

    @Override
    public T getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        return parseStrToObj(rs.getString(columnIndex));
    }

    @Override
    public T getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        return parseStrToObj(cs.getString(columnIndex));
    }

    private T parseStrToObj(String raw) {
        try {
            if (StringUtils.isBlank(raw)) {
                return null;
            }
            return JSON.parseObject(raw, this.classType);
        } catch (Exception e) {
            log.warn("MysqlJsonHandler parseStrToObj error", e);
            return null;
        }
    }

    public static class JsonEmptyCollectionFilter implements PropertyFilter {

        @Override
        public boolean apply(Object object, String name, Object value) {
            if (value == null) {
                return false;
            }
            if (value.getClass().isArray()) {
                if (Array.getLength(value) == 0) {
                    return false;
                }
            }
            if (value instanceof String && ((String) value).isEmpty()) {
                return false;
            }
            if (value instanceof List && ((List) value).size() == 0) {
                return false;
            }
            if (value instanceof Map && ((Map) value).size() == 0) {
                return false;
            }
            return true;
        }
    }
}
