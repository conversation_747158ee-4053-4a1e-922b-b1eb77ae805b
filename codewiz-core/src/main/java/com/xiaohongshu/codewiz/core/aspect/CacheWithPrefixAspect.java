package com.xiaohongshu.codewiz.core.aspect;

import javax.annotation.Resource;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

import com.xiaohongshu.codewiz.core.annotation.CacheWithPrefix;

import lombok.extern.slf4j.Slf4j;
import redis.clients.jedis.Jedis;

@Slf4j
@Aspect
@Component
public class CacheWithPrefixAspect {

    @Resource(name = "codewizJedis")
    private Jedis jedis;

    @Around("@annotation(cacheWithPrefix)")
    public Object around(ProceedingJoinPoint point, CacheWithPrefix cacheWithPrefix) throws Throwable {
        String prefix = cacheWithPrefix.prefix();
        int expireSeconds = cacheWithPrefix.expireSeconds();

        // 获取方法参数
        Object[] args = point.getArgs();
        if (args == null || args.length == 0) {
            return point.proceed();
        }

        // 构建缓存key
        String cacheKey = buildCacheKey(prefix, args[0]);

        // 尝试从缓存获取
        String cachedValue = jedis.get(cacheKey);
        if (cachedValue != null) {
            log.info("Cache hit for key: {}", cacheKey);
            return cachedValue;
        }

        // 缓存未命中，执行原方法
        Object result = point.proceed();
        if (result != null) {
            // 将结果存入缓存
            jedis.set(cacheKey, result.toString(), "NX", "EX", expireSeconds);
            log.info("Cache set for key: {}, expireSeconds: {}", cacheKey, expireSeconds);
        }

        return result;
    }

    private String buildCacheKey(String prefix, Object param) {
        return String.format("%s:%s", prefix, param.toString());
    }
} 