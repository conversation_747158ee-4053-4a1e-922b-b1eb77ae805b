package com.xiaohongshu.codewiz.core.service.rag.query;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xiaohongshu.codewiz.core.client.RerankClient;
import com.xiaohongshu.codewiz.core.constant.enums.BusinessTrackingLogRecordType;
import com.xiaohongshu.codewiz.core.constant.enums.RagCaseKnowledgeEnum;
import com.xiaohongshu.codewiz.core.constant.enums.RagDataTypeEnum;
import com.xiaohongshu.codewiz.core.constant.enums.RagRecallStrategyEnum;
import com.xiaohongshu.codewiz.core.dao.log.BusinessTrackingLogRecordDao;
import com.xiaohongshu.codewiz.core.entity.log.BusinessTrackingLogRecordDO;
import com.xiaohongshu.codewiz.core.entity.rag.FewShotCase;
import com.xiaohongshu.codewiz.core.entity.rag.RagDataQueryRequest;
import com.xiaohongshu.codewiz.core.entity.rag.RagDataQueryResponse;
import com.xiaohongshu.codewiz.core.entity.rerank.RerankRequestDto;
import com.xiaohongshu.codewiz.core.entity.rerank.RerankResultDto;
import com.xiaohongshu.codewiz.core.entity.rerank.RerankType;
import com.xiaohongshu.codewiz.core.service.elasticsearch.ElasticsearchService;
import com.xiaohongshu.codewiz.core.service.rag.RagCollectionService;
import com.xiaohongshu.codewiz.core.service.rag.RagDataContext;
import com.xiaohongshu.codewiz.core.service.rag.recall.RagRecallStrategyFactory;
import com.xiaohongshu.codewiz.core.utils.JsonMapperUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2025/3/14 16:30
 */
@Slf4j
@Service
public class RagQueryCrCommentsService extends AbsRagQueryDataService<FewShotCase> {

    @Resource
    private RagCollectionService ragCollectionService;
    @Resource
    private ElasticsearchService elasticsearchService;
    @Resource
    private RerankClient rerankClient;
    @Resource
    private RagRecallStrategyFactory ragRecallStrategyFactory;

    @Resource
    private Executor logRecordExecutor;

    @Resource
    private BusinessTrackingLogRecordDao businessTrackingLogRecordDao;


    @Override
    protected void check(RagDataQueryRequest request) {
        super.baseCheck(request);
        // score
        if (Objects.isNull(request.getExtension().getScoreThreshold())) {
            // 默认0.8, todo: 后续需要从apollo获取
            request.getExtension().setScoreThreshold(0.8);
        }
    }

    @Override
    protected void analyze(RagDataContext<FewShotCase> context) {
        String cmts = context.getQueryRequest().getQuery();
        Map<String, String> analysisQuery = Maps.newHashMap();
        analysisQuery.put(RagCaseKnowledgeEnum.COMMENTS.getFileName(), cmts);
        context.setAnalysisQuery(analysisQuery);

        // 设置milvus topK
        context.getTopK().setRecallMilvusTopK(context.getQueryRequest().getExtension().getReturnNum() * 2);

        // 设置rerank topK
        context.getTopK().setRerankTopK(context.getQueryRequest().getExtension().getReturnNum());

        // 设置ES topK
        context.getTopK().setRecallEsTopK(context.getQueryRequest().getExtension().getReturnNum());
        context.setCollectionName(getCollectionName(context.getQueryRequest()));

        // 设置召回策略
        setRecallStrategy(context, RagRecallStrategyEnum.COMMENTS_AND_ES);

        // 设置embeddingModel
        setEmbeddingModel(context);
    }

    @Override
    protected void recall(RagDataContext<FewShotCase> context) {
        super.recall(context);

        // 将各路召回的case，记录下来，完全异步执行，不需要等待执行结果，避免影响链路性能。即，尽最大可能记录即可。
        CompletableFuture.runAsync(() -> recordRecallTrackingLog(context, BusinessTrackingLogRecordType.RAG_COMMENT_FEW_SHOT_CALL_CHAIN),
                logRecordExecutor);
    }

    @Override
    protected void merge(RagDataContext<FewShotCase> context) {

    }

    @Override
    protected void rerank(RagDataContext<FewShotCase> context) {
        // query 和case的fieldContent的相似度
        List<RerankRequestDto> requestDtos = context.getDocuments()
                .stream()
                .map(fewShotCase -> RerankRequestDto.convertFromFewShotCase(fewShotCase, context.getQueryRequest().getQuery()))
                .collect(Collectors.toList());
        List<RerankResultDto> rerankResultDtoList = rerankClient.getRerankResult(requestDtos, RerankType.BGE_RERANK);
        if (CollectionUtils.isEmpty(rerankResultDtoList)) {
            log.info("rerankResultDtoList is empty");
            return;
        }
        // 设置分数
        Map<String, RerankResultDto> fieldContentToRerankResultDto = rerankResultDtoList.stream()
                .collect(Collectors.toMap(RerankResultDto::getQuery, Function.identity(), (k1, k2) -> k1));
        for (FewShotCase fewShotCase : context.getDocuments()) {
            RerankResultDto rerankResultDto = fieldContentToRerankResultDto.get(fewShotCase.getFieldContent());
            if (rerankResultDto == null) {
                continue;
            }
            fewShotCase.setScore(rerankResultDto.getScore().floatValue());
        }
        // 过滤分数，根据入参过滤
        Double scoreThreshold = context.getQueryRequest().getExtension().getScoreThreshold();
        if (scoreThreshold != null) {
            context.getDocuments().removeIf(caseData -> caseData.getScore() < scoreThreshold.floatValue());
        }


        CompletableFuture.runAsync(
                () -> recordRecallTrackingLog(context, BusinessTrackingLogRecordType.RAG_COMMENT_FEW_SHOT_CALL_RERANK_CHAIN),
                logRecordExecutor);
    }

    @Override
    protected void filter(RagDataContext<FewShotCase> context) {
        // filter level
        filterLevel(context);

        // 移除issueDetails为空的文档
        context.getDocuments().removeIf(caseData -> CollectionUtils.isEmpty(caseData.getIssueDetails()));

        log.info("filter case size == {}", context.getDocuments().size());
    }

    @Override
    protected void format(RagDataContext<FewShotCase> context) {
        if (Objects.isNull(context.getQueryResponse())) {
            context.setQueryResponse(RagDataQueryResponse.<FewShotCase>builder()
                    .scene(context.getQueryRequest().getScene())
                    .documents(context.getDocuments())
                    .build());
        } else {
            context.getQueryResponse().setDocuments(context.getDocuments());
        }
    }

    @Override
    protected String getCollectionName(RagDataQueryRequest request) {
        String collectionName = ragCollectionService.getCrCollection();
        if (StringUtils.isNotEmpty(request.getExtension().getCollectionName())) {
            collectionName = request.getExtension().getCollectionName();
        }
        return collectionName;
    }

    @Override
    public RagDataTypeEnum source() {
        return RagDataTypeEnum.CR_COMMENTS;
    }

    private void recordRecallTrackingLog(RagDataContext<FewShotCase> context, BusinessTrackingLogRecordType logRecordType) {
        String businessTrackingId = context.getQueryRequest().getBusinessTrackingId();
        String xrayTrackingId = context.getQueryRequest().getXrayTrackingId();
        String uuidTrackingId = context.getQueryRequest().getUuidTrackingId();
        List<FewShotCase> cases = context.getDocuments();
        String cmts = context.getAnalysisQuery().getOrDefault(RagCaseKnowledgeEnum.COMMENTS.getFileName(), StringUtils.EMPTY);
        Date actionDate = new Date();

        if (CollectionUtils.isEmpty(cases)) {
            return;
        }
        BusinessTrackingLogRecordDO mrCommentFewShotRequestRecord =
                buildLogRecord(businessTrackingId, xrayTrackingId, uuidTrackingId,
                        logRecordType, cmts, JsonMapperUtils.toJson(cases), actionDate);

        List<BusinessTrackingLogRecordDO> records = Lists.newArrayList(mrCommentFewShotRequestRecord);
        businessTrackingLogRecordDao.batchSaveBusinessTrackingLogRecord(records);
    }
}
