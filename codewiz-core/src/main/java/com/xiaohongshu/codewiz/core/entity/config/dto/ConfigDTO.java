package com.xiaohongshu.codewiz.core.entity.config.dto;

import lombok.Data;

/**
 * 配置DTO
 *
 * <AUTHOR>
 * @since 2025-04-21
 */
@Data
public class ConfigDTO {

    /**
     * 配置ID
     */
    private Long configSnapshotId;

    /**
     * 配置名称
     */
    private String configName;

    /**
     * 配置类型 1-plugin 2-lsp
     */
    private Integer configType;

    /**
     * 配置描述
     */
    private String configDesc;

    /**
     * 配置版本
     */
    private String configVersion;

    /**
     * 配置值
     */
    private String configValue;

    /**
     * 是否最新
     */
    private Boolean isLasted;

    /**
     * 创建时间
     */
    private Long createAt;

    /**
     * 更新时间
     */
    private Long updateAt;
}