package com.xiaohongshu.codewiz.core.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/2/27 20:17
 */
@Getter
@AllArgsConstructor
public enum RagDataTypeEnum {
    CR_CASE(1, "智能CR，召回相似case"),
    CQ_CASE(2, "智能CQ，召回相似case"),
    CODE_UNDERSTANDING(3, "代码理解，召回相似代码片段"),
    CR_COMMENTS(4, "用户评论，召回相似用户评论"),
    CQ_RULE(5, "CQ扫描规则，用户自定义"),
    NEGATIVE_COMMENTS(6, "负向评论召回"),
    CR_CASE_BY_TYPE(7, "智能CR，按照case类型召回相似case"),
    ;

    // get enum by scene
    public static RagDataTypeEnum getByScene(Integer scene) {
        for (RagDataTypeEnum value : values()) {
            if (value.getScene().equals(scene)) {
                return value;
            }
        }
        return null;
    }

    private final Integer scene;
    private final String desc;
}
