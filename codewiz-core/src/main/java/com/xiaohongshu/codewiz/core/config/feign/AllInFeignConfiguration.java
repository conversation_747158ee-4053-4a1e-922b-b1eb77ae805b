package com.xiaohongshu.codewiz.core.config.feign;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;

import feign.RequestInterceptor;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2025/3/3
 */
public class AllInFeignConfiguration {
    @Value("${feign-client.all-in.api-key}")
    private String apiKey;

    @Bean
    public RequestInterceptor requestInterceptor() {
        return template -> {
            template.header("api-key", apiKey);
        };
    }
}