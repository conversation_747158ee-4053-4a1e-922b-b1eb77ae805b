package com.xiaohongshu.codewiz.core.entity.common;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2025/2/26
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SingleResponse<T> extends Response {
    private T data;

    private SingleResponse() {
    }

    public static SingleResponse<?> buildSuccess() {
        SingleResponse<?> response = new SingleResponse<>();
        response.setSuccess(true);
        response.setCode("200");
        response.setMsg("success");
        return response;
    }

    public static <T> SingleResponse<T> buildFailure(String code, String msg) {
        SingleResponse<T> response = new SingleResponse<>();
        response.setSuccess(false);
        response.setCode(code);
        response.setMsg(msg);
        return response;
    }

    public static <T> SingleResponse<T> of(T data) {
        SingleResponse<T> response = new SingleResponse<>();
        response.setSuccess(true);
        response.setData(data);
        return response;
    }

    public static <T> SingleResponse<T> ok() {
        SingleResponse<T> response = new SingleResponse<>();
        response.setSuccess(true);
        response.setCode("200");
        response.setMsg("success");
        return response;
    }
}