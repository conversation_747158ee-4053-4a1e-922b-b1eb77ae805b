package com.xiaohongshu.codewiz.core.entity.pipeline;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * <AUTHOR>
 * Created on 2025/3/17
 */
@Data
public class PipelineRunRequest {
    private String pipelineId;
    private String projectId;
    private String branch;
    private String userId;
    private String pipelineInfoId;
    private List<PipelineVariable> pipelineVariableList;

    @Data
    @AllArgsConstructor
    public static class PipelineVariable {
        private String variableKey;
        private String variableValue;
    }
}
