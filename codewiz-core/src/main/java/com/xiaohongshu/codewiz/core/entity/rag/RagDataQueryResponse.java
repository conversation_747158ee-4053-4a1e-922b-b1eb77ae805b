package com.xiaohongshu.codewiz.core.entity.rag;

import java.util.List;

import com.google.common.collect.Lists;

import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/2/28 16:12
 */
@Data
@Builder
public class RagDataQueryResponse<T extends RagDocument> {
    private int scene;
    private List<T> documents;

    public static RagDataQueryResponse empty() {
        return RagDataQueryResponse.builder()
                .scene(0)
                .documents(Lists.newArrayList())
                .build();
    }
}
