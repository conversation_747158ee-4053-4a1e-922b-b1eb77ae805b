package com.xiaohongshu.codewiz.core.service.rag.recall;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.xiaohongshu.codewiz.core.client.EmbeddingClient;
import com.xiaohongshu.codewiz.core.constant.enums.RagCaseKnowledgeEnum;
import com.xiaohongshu.codewiz.core.entity.rag.FewShotCase;
import com.xiaohongshu.codewiz.core.service.milvus.CaseMilvusService;
import com.xiaohongshu.codewiz.core.service.rag.RagDataContext;

import lombok.extern.slf4j.Slf4j;

/**
 * 评论召回策略
 *
 * <AUTHOR>
 * @date 2025/4/20 10:30
 */
@Slf4j
@Service
public class CommentsMilvusRecallStrategy extends AbstractMilvusRecallStrategy<FewShotCase> {

    public CommentsMilvusRecallStrategy(CaseMilvusService caseMilvusService,
                                        EmbeddingClient embeddingClient) {
        super(caseMilvusService, embeddingClient);
    }

    @Override
    protected List<FewShotCase> doRecall(RagDataContext<FewShotCase> context) {
        log.info("执行评论召回策略");

        String commentsFileName = RagCaseKnowledgeEnum.COMMENTS.getFileName();
        String cmts = context.getAnalysisQuery().getOrDefault(commentsFileName, StringUtils.EMPTY);

        if (StringUtils.isBlank(cmts)) {
            log.warn("评论内容为空，无法进行评论召回");
            return Lists.newArrayList();
        }

        List<FewShotCase> commentsRecallResults = milvusVectorRecall(context, cmts, commentsFileName);
        log.info("评论召回结果数量: {}", commentsRecallResults.size());

        return commentsRecallResults;
    }
}