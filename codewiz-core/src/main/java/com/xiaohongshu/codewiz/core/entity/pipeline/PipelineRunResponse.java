package com.xiaohongshu.codewiz.core.entity.pipeline;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

/**
 * <AUTHOR>
 * Created on 2025/3/17
 */
@Data
public class PipelineRunResponse {
    private Boolean success;
    @JsonProperty("error_code")
    private Integer errorCode;
    @JsonProperty("err_msg")
    private String errMsg;
    private String msg;
    private ResponseData data;

    @Data
    public static class ResponseData {
        @JsonProperty("pipeline_history_id")
        private Long pipelineHistoryId;
        private Boolean success;
        private String link;
    }
}
