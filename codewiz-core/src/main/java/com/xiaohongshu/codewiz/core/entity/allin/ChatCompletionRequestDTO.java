package com.xiaohongshu.codewiz.core.entity.allin;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2025/3/3
 */
@Builder
@Data
public class ChatCompletionRequestDTO {
    private String model;

    private List<ChatMessage> messages;

    private Boolean stream;

    @JsonProperty("max_tokens")
    private Integer maxTokens;

    @JsonProperty("use_cache")
    private Boolean useCache;

    private Double topP;

    private Double temperature;

    private List<String> stop;

    @Builder
    @Data
    public static class ChatMessage {
        private String role;
        private String content;
    }

    private String baseUrl;
}