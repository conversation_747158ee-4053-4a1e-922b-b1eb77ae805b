package com.xiaohongshu.codewiz.core.feign;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import com.xiaohongshu.codewiz.core.config.feign.AllInFeignConfiguration;
import com.xiaohongshu.codewiz.core.entity.allin.ChatCompletionRequestDTO;
import com.xiaohongshu.codewiz.core.entity.allin.ChatCompletionResponseDTO;
import com.xiaohongshu.codewiz.core.entity.common.SingleResponse;

@FeignClient(name = "LLM-Adapter", url = "${feign-client.llm-adapter.url}", configuration = AllInFeignConfiguration.class)
public interface LLMAdapterFeignClient {
    @PostMapping("/v1/chat/completions")
    ResponseEntity<SingleResponse<ChatCompletionResponseDTO>> chatCompletions(@RequestBody ChatCompletionRequestDTO chatRequest,
                                                                              @RequestHeader(name = "test_flag") Boolean testFlag,
                                                                              @RequestHeader(name = "service_name") String serviceName);
}
