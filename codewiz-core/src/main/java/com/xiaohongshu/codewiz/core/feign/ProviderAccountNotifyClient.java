package com.xiaohongshu.codewiz.core.feign;

import com.xiaohongshu.codewiz.core.entity.config.dto.ProviderFreeAccountNotifyRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * Author: liukunpeng Date: 2025-06-23 Description:
 */
@FeignClient(name = "providerAccountNotify", url = "${lingma.free-account-notify}")
public interface ProviderAccountNotifyClient {
  @PostMapping
  void freeAccountUnEnoughNotify(@RequestBody ProviderFreeAccountNotifyRequest request);
}
