package com.xiaohongshu.codewiz.core.entity.chat;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

/**
 * <p>
 * 会话表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@Data
@TableName("chat_session")
public class ChatSessionDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 工作空间唯一标识
     */
    private String workspaceId;

    /**
     * 会话id
     */
    private String sessionId;

    /**
     * 会话名
     */
    private String sessionName;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 是否删除，0-未删除，1-删除
     */
    private Integer isDelete;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
