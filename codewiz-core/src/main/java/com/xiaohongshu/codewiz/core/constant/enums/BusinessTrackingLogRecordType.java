package com.xiaohongshu.codewiz.core.constant.enums;

import lombok.Getter;

/**
 * @Author: 取风
 * @Date: 2025/3/27 16:32
 */
@Getter
public enum BusinessTrackingLogRecordType {

    RAG_CASE_FEW_SHOT_CALL_CHAIN("Few-Shot整体调用链路"),
    RAG_CODE_SNIPPET_FEW_SHOT_CALL_CHAIN("代码片段类Few-Shot调用链路"),
    RAG_CODE_EXPLAIN_FEW_SHOT_CALL_CHAIN("代码解释类Few-Shot调用链路"),
    RAG_HYBRID_KNOWLEDGE_FEW_SHOT_CALL_CHAIN("混合知识类Few-Shot调用链路"),
    RAG_RERANK_FEW_SHOT_CALL_CHAIN("Rerank Few-Shot调用链路"),
    RAG_COMMENT_FEW_SHOT_CALL_CHAIN("MR评论类Few-Shot调用链路"),
    RAG_COMMENT_FEW_SHOT_CALL_RERANK_CHAIN("MR评论类Few-Shot重排序调用链路"),
    RAG_CASE_FEW_SHOT_RECALL("Case Few-Shot召回链路"),
    RAG_CASE_FEW_SHOT_RERANK("Case Few-Shot重排序链路");

    private final String desc;

    BusinessTrackingLogRecordType(String desc) {
        this.desc = desc;
    }
}
