package com.xiaohongshu.codewiz.core.service.rag.add;

import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import com.google.common.collect.Maps;
import com.xiaohongshu.codewiz.core.constant.enums.RagCaseKnowledgeEnum;
import com.xiaohongshu.codewiz.core.constant.enums.RagDataTypeEnum;
import com.xiaohongshu.codewiz.core.entity.rag.FewShotCase;
import com.xiaohongshu.codewiz.core.entity.rag.RagDataAddRequest;
import com.xiaohongshu.codewiz.core.service.milvus.CaseMilvusService;
import com.xiaohongshu.codewiz.core.service.rag.RagCodeExplainService;
import com.xiaohongshu.codewiz.core.service.rag.RagCollectionService;
import com.xiaohongshu.codewiz.core.service.rag.RagDataContext;
import com.xiaohongshu.codewiz.core.utils.JsonMapperUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2025/2/27 20:14
 */
@Slf4j
@Service
public class RagAddCrCaseService extends AbsRagAddDataServiceService<FewShotCase> {

    @Resource
    private RagCollectionService ragCollectionService;

    @Resource
    private CaseMilvusService caseMilvusService;

    @Resource
    private RagCodeExplainService ragCodeExplainService;

    public static final String HYBRID_KNOWLEDGE_FIELD_TEMPLATE =
            "%s: %s\n%s: %s";

    @Override
    protected RagDataContext<FewShotCase> buildContext(RagDataAddRequest request) {
        RagDataContext<FewShotCase> ragDataContext = super.buildContext(request);
        String source = request.getSource();
        String extra = JsonMapperUtils.toJson(Map.of("source", source));
        List<FewShotCase> cases = request
                .getDocuments()
                .stream()
                .map(doc -> JsonMapperUtils.convertMapToClass(doc, FewShotCase.class))
                .peek(fewShotCase -> fewShotCase.setIssueId(UUID.randomUUID().toString()))
                .peek(fewShotCase -> fewShotCase.setCaseTag(source))
                .peek(fewShotCase -> fewShotCase.setExtra(extra))
                .collect(Collectors.toList());
        ragDataContext.setDocuments(cases);
        log.info("build context success!");
        return ragDataContext;
    }

    @Override
    protected void check(RagDataAddRequest request) {
        baseCheck(request);
    }

    @Override
    protected void extendDocument(RagDataContext<FewShotCase> context) {
        // 提取特定知识
        context.getDocuments().forEach(fewShotCase -> {
            String code = fewShotCase.getCodeSnippet();
            String explain = ragCodeExplainService.explainCode(code);
            String codeExplainFileName = RagCaseKnowledgeEnum.CODE_EXPLAIN.getFileName();
            String codeSnippetFileName = RagCaseKnowledgeEnum.CODE_SNIPPET.getFileName();
            Map<String, Object> knowledge = Maps.newHashMap();
            knowledge.put(codeSnippetFileName, code);
            knowledge.put(RagCaseKnowledgeEnum.COMMENTS.getFileName(), extraIssueDescription(fewShotCase));
            knowledge.put(codeExplainFileName, explain);
            String hybridKnowledge = String.format(HYBRID_KNOWLEDGE_FIELD_TEMPLATE,
                    codeExplainFileName, code,
                    codeSnippetFileName, explain);
            knowledge.put(RagCaseKnowledgeEnum.HYBRID_KNOWLEDGE.getFileName(), hybridKnowledge);
            fewShotCase.setKnowledge(knowledge);
        });
        log.info("extend document success!");
    }

    private String extraIssueDescription(FewShotCase fewShotCase) {
        List<Map<String, Object>> issueDetails = fewShotCase.getIssueDetails();
        StringBuilder sb = new StringBuilder();
        if (CollectionUtils.isNotEmpty(issueDetails)) {
            issueDetails.forEach(issueDetail -> {
                String description = issueDetail.get("description").toString();
                sb.append(description).append("\n");
            });
        }
        return sb.toString();
    }

    @Override
    protected void saveToMysql(RagDataContext<FewShotCase> context) {
        // todo 后续存入
    }

    @Override
    protected String getCollectionName(RagDataAddRequest request) {
        return ragCollectionService.getCrCollection();
    }

    @Override
    protected void saveToMilvus(RagDataContext<FewShotCase> context) {
        caseMilvusService.insertCases(
                context.getDocuments(),
                getCollectionName(context.getAddRequest()),
                context);
    }

    @Override
    public RagDataTypeEnum source() {
        return RagDataTypeEnum.CR_CASE;
    }
}
