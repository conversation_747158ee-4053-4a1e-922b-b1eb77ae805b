package com.xiaohongshu.codewiz.core.entity.rag.open;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/3/24 15:53
 */
@Data
public class BizSearchResp {
    private List<Result> results;

    @Data
    public static class Result {
        @JsonProperty("kb_id")
        private String kbId;
        @JsonProperty("doc_id")
        private String docId;
        @JsonProperty("chunk_id")
        private String chunkId;
        private String content;
        @JsonProperty("file_path")
        private String filePath;
        @JsonProperty("file_type")
        private String fileType;
        private Float score;
    }
}
