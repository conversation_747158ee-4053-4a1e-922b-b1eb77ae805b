package com.xiaohongshu.codewiz.core.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/4/12 19:21
 */
@Getter
@AllArgsConstructor
public enum RagEmbeddingModelEnum {
    /**
     *
     */
    BGE_EMBEDDING("bge", "BGE embedding模型", "field_embedding", "http://codewiz.devops.xiaohongshu.com/rag/embeddings"),
    JINA_EMBEDDING("jina", "Jina embedding模型", "field_embedding", "http://codewiz.devops.xiaohongshu.com/rag/embeddings"),
    GTE_EMBEDDING("gte", "GTE embedding模型", "gte_embedding", "http://codewiz-rag.devops.xiaohongshu.com/rag/gte/embedding"),
    ;

    private final String model;
    private final String description;
    private final String annField;
    private final String url;

    public static RagEmbeddingModelEnum getByModel(String model) {
        if (model == null) {
            return BGE_EMBEDDING;
        }

        for (RagEmbeddingModelEnum embeddingModel : RagEmbeddingModelEnum.values()) {
            if (embeddingModel.getModel().equals(model)) {
                return embeddingModel;
            }
        }

        return BGE_EMBEDDING;
    }

}
