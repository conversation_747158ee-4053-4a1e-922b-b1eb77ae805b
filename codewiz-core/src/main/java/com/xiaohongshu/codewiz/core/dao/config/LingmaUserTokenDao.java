package com.xiaohongshu.codewiz.core.dao.config;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xiaohongshu.codewiz.core.entity.config.LingmaUserTokenDo;
import com.xiaohongshu.codewiz.core.mapper.config.LingmaUserTokenMapper;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 个人配置表 DAO层
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-21
 */
@Slf4j
@Service
public class LingmaUserTokenDao extends ServiceImpl<LingmaUserTokenMapper, LingmaUserTokenDo> {
    public LingmaUserTokenDo getByEmail(String email) {
        LambdaQueryWrapper<LingmaUserTokenDo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
            .eq(LingmaUserTokenDo::getIsDeleted, 0)
            .eq(LingmaUserTokenDo::getEmail, email);
        return this.getOne(queryWrapper);
    }
    public LingmaUserTokenDo getFreeUser(List<String> alreadyMapping) {
        LambdaQueryWrapper<LingmaUserTokenDo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
            .eq(LingmaUserTokenDo::getIsDeleted, 0)
            .notIn(CollectionUtils.isNotEmpty(alreadyMapping), LingmaUserTokenDo::getUserName, alreadyMapping)
            .and(qw -> qw.isNull(LingmaUserTokenDo::getEmail)
            .or()
            .eq(LingmaUserTokenDo::getEmail, ""));
        List<LingmaUserTokenDo> list = this.list(queryWrapper);
        return CollectionUtils.isEmpty(list) ? null : list.get(0);
    }
    public void add(LingmaUserTokenDo lingmaUserTokenDo) {
        this.save(lingmaUserTokenDo);
    }

    public int countValid(boolean isOnlyFree) {
        LambdaQueryWrapper<LingmaUserTokenDo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
            .eq(LingmaUserTokenDo::getIsDeleted, 0)
            .and(isOnlyFree, qw -> qw.isNull(LingmaUserTokenDo::getEmail).or()
                .eq(LingmaUserTokenDo::getEmail, ""));
        return this.count(queryWrapper);
    }
} 