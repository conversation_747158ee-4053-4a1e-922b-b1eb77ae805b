package com.xiaohongshu.codewiz.core.entity.rag.scenecase;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.DigestUtils;

import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.xiaohongshu.codewiz.core.client.EmbeddingClient;
import com.xiaohongshu.codewiz.core.constant.enums.RagCaseKnowledgeEnum;
import com.xiaohongshu.codewiz.core.entity.rag.FewShotCase;
import com.xiaohongshu.codewiz.core.service.milvus.MilvusEntity;
import com.xiaohongshu.codewiz.core.utils.JsonMapperUtils;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2025/2/25 19:11
 */
@Slf4j
@Data
public abstract class CaseMilvusRequest implements MilvusEntity {
    private FewShotCase fewShotCase;
    private Map<String, Object> knowledge;
    private List<Map<String, Object>> issueDetails;
    private EmbeddingClient embeddingClient;

    public CaseMilvusRequest(FewShotCase fewShotCase,
                             Map<String, Object> knowledge,
                             EmbeddingClient embeddingClient) {
        this.fewShotCase = fewShotCase;
        this.knowledge = knowledge;
        this.issueDetails = fewShotCase.getIssueDetails();
        this.embeddingClient = embeddingClient;
    }

    @Override
    public JsonObject covertJsonObject() {
        JsonObject jsonObject = new JsonObject();
        jsonObject.addProperty("id", buildId());
        jsonObject.addProperty("knowledge_base", getKnowledgeBase());
        jsonObject.addProperty("field_name", getFieldName());
        jsonObject.addProperty("field_content", getFieldContent());
        jsonObject.addProperty("item_id", getItemId());
        jsonObject.addProperty("item_data", getItemData());
        jsonObject.addProperty("repo", getRepo());
        jsonObject.addProperty("language", getLanguage());
        jsonObject.addProperty("review_type", getReviewType());
        jsonObject.addProperty("branch_name", getBranchName());
        jsonObject.addProperty("file_path", getFieldPath());
        jsonObject.addProperty("issue_id", getIssueId());
        jsonObject.addProperty("extra", JsonMapperUtils.toJson(getExtra()));
        jsonObject.addProperty("case_tag", getCaseTag());
        jsonObject.add("field_embedding", getFieldEmbedding());
        return jsonObject;
    }

    public String buildId() {
        return getItemId() + getField().getItemIdSuffix();
    }

    public String getKnowledgeBase() {
        return "codewiz";
    }

    public String getFieldName() {
        return getField().getFileName();
    }

    public String getFieldContent() {
        return getKnowledge().get(getFieldName()).toString();
    }

    public String getFieldPath() {
        return fewShotCase.getFilePath();
    }

    public String getBranchName() {
        return fewShotCase.getBranch();
    }

    public String getIssueId() {
        return getItemId();
    }

    public String getCaseTag() {
        return fewShotCase.getCaseTag();
    }

    public JsonElement getFieldEmbedding() {
        List<Float> embeddingVector = embeddingClient.getBgeEmbeddingVector(getFieldContent());
        Gson gson = new Gson();
        return gson.toJsonTree(embeddingVector);
    }


    public String getItemId() {
        Map<String, Object> extra = getExtra();
        if (MapUtils.isEmpty(extra) || !extra.containsKey("item_id")) {
            List<String> items = List.of(getRepo(),
                    getLanguage(),
                    getReviewType(),
                    fewShotCase.getCodeSnippet(),
                    fewShotCase.getIssueDetails().toString()
            );
            String itemStr = String.join("_", items);
            return DigestUtils.md5DigestAsHex(itemStr.getBytes(StandardCharsets.UTF_8));
        }

        String itemId = extra.get("item_id").toString();
        log.info("item_id has already exist in extra, item_id: {}", itemId);
        return itemId;
    }

    public String getItemData() {
        CaseMilvusSearchResponse.ItemData itemData = new CaseMilvusSearchResponse.ItemData();
        itemData.setCodeSnippet(fewShotCase.getCodeSnippet());
        itemData.setTargetFunction(fewShotCase.getTargetFunction());
        itemData.setReviewType(fewShotCase.getReviewType());
        itemData.setIssueDetail(fewShotCase.getIssueDetails());
        itemData.setRepo(fewShotCase.getRepo());
        itemData.setLanguage(fewShotCase.getLanguage());
        itemData.setExtra(getExtra());
        itemData.setItemId(getItemId());
        itemData.setCode_explain(knowledge.get(RagCaseKnowledgeEnum.CODE_EXPLAIN.getFileName()).toString());
        // itemData.setLlmStructure(knowledge.get("structure").toString());
        // itemData.setLlmConcept((List) knowledge.get("concept"));
        // itemData.setLlmHierarchy(knowledge.get("hierarchy").toString());
        // itemData.setLlmKeyword(knowledge.get("high_keyword").toString());

        return JsonMapperUtils.toJson(itemData);
    }

    public String getRepo() {
        return fewShotCase.getRepo();
    }

    public String getLanguage() {
        return fewShotCase.getLanguage();
    }

    public String getReviewType() {
        return fewShotCase.getReviewType();
    }

    public Map<String, Object> getExtra() {
        Map<String, Object> extra = Maps.newHashMap();
        if (StringUtils.isBlank(fewShotCase.getExtra())) {
            return extra;
        }
        extra = JsonMapperUtils.fromJson(fewShotCase.getExtra());
        return extra;
    }

    public abstract RagCaseKnowledgeEnum getField();
}
