package com.xiaohongshu.codewiz.core.entity.rag.open;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/3/24 15:53
 */
@Data
public class BizDocumentCreateOrUpdateReq {
    @JsonProperty("biz_id")
    private String bizId;
    @JsonProperty("user_id")
    private String userId;
    @JsonProperty("project_id")
    private String projectId;
    @JsonProperty("kb_id")
    private String kbId;
    @JsonProperty("kb_type")
    private String kbType;
    @JsonProperty("file_path")
    private String filePath;
    @JsonProperty("file_type")
    private String fileType;
    @JsonProperty("content")
    private String content;

    // update使用
    @JsonProperty("doc_id")
    private String docId;
    @JsonProperty("chunk_id")
    private String chunkId;
} 