package com.xiaohongshu.codewiz.core.entity.graph;

import java.util.List;

import lombok.Data;

/**
 * <AUTHOR>
 * Created on 2025/6/26
 */
@Data
public class CallGraphConfig {
    /**
     * 灰度项目列表
     * null 表示所有项目都参与灰度
     * 非null 表示只有灰度项目参与灰度
     */
    private List<Long> grayProjectIds;
    /**
     * 方法签名策略配置
     */
    private NodeSignatureStrategy nodeSignatureStrategy;

    /**
     * 最大节点引用数
     * null 表示不限制
     */
    private Integer maxNodeReferenceCount;

    /**
     * 方法签名策略配置
     */
    @Data
    public static class NodeSignatureStrategy {
        /**
         * 排除的语言列表，这些语言不需要限制方法签名长度
         */
        private List<String> excludeLanguages;

        /**
         * 方法签名长度规则列表
         */
        private List<SignatureRule> rules;
    }

    /**
     * 方法签名长度规则
     */
    @Data
    public static class SignatureRule {
        /**
         * 节点数量范围起始值（包含）
         */
        private int startNodeCount;

        /**
         * 节点数量范围结束值（不包含）
         */
        private int endNodeCount;

        /**
         * 该范围内的最小签名长度要求
         */
        private int minSignatureLength;
    }

    public boolean isProjectInGrayList(Long projectId) {
        if (grayProjectIds == null) {
            return true;
        }
        return grayProjectIds.contains(projectId);
    }
}
