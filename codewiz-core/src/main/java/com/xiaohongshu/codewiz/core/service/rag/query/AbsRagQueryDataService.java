package com.xiaohongshu.codewiz.core.service.rag.query;

import static com.xiaohongshu.codewiz.core.constant.ErrorCodeConstant.RAG_DATA_SCENE_ERROR;
import static com.xiaohongshu.codewiz.core.constant.ErrorCodeConstant.RAG_QUERY_NOT_FOUND_QUERY;
import static com.xiaohongshu.codewiz.core.constant.ErrorCodeConstant.RAG_QUERY_RETURN_NUM_ERROR;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;

import com.xiaohongshu.codewiz.core.client.EmbeddingClient;
import com.xiaohongshu.codewiz.core.constant.enums.BusinessTrackingLogRecordType;
import com.xiaohongshu.codewiz.core.constant.enums.RagEmbeddingModelEnum;
import com.xiaohongshu.codewiz.core.constant.enums.RagRecallStrategyEnum;
import com.xiaohongshu.codewiz.core.entity.log.BusinessTrackingLogRecordDO;
import com.xiaohongshu.codewiz.core.entity.rag.FewShotCase;
import com.xiaohongshu.codewiz.core.entity.rag.RagDataQueryRequest;
import com.xiaohongshu.codewiz.core.entity.rag.RagDataQueryResponse;
import com.xiaohongshu.codewiz.core.entity.rag.RagDocument;
import com.xiaohongshu.codewiz.core.exception.BizException;
import com.xiaohongshu.codewiz.core.service.milvus.CaseMilvusService;
import com.xiaohongshu.codewiz.core.service.rag.RagDataContext;
import com.xiaohongshu.codewiz.core.service.rag.recall.IRecallStrategy;
import com.xiaohongshu.codewiz.core.service.rag.recall.RagRecallStrategyFactory;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2025/2/28 16:14
 */
@Slf4j
public abstract class AbsRagQueryDataService<T extends RagDocument> implements RagQueryDataService {

    @Resource
    private EmbeddingClient embeddingClient;
    @Resource
    private CaseMilvusService caseMilvusService;
    @Resource
    private RagRecallStrategyFactory recallStrategyFactory;

    public RagDataQueryResponse<T> query(RagDataQueryRequest request) {
        // check
        check(request);
        // build context
        RagDataContext<T> context = buildContext(request);
        // 分析并扩展query
        analyze(context);
        // 召回document
        recall(context);
        // 对多路召回的document合并
        merge(context);
        // 根据规则过滤
        filter(context);
        // document重排序
        rerank(context);
        // 格式化结果
        format(context);

        return context.getQueryResponse();
    }

    protected abstract void check(RagDataQueryRequest request);

    protected abstract void analyze(RagDataContext<T> context);

    protected abstract void rerank(RagDataContext<T> context);

    protected abstract void merge(RagDataContext<T> context);

    protected abstract void filter(RagDataContext<T> context);

    protected abstract void format(RagDataContext<T> context);

    protected abstract String getCollectionName(RagDataQueryRequest request);

    protected void baseCheck(RagDataQueryRequest request) {
        // 检查scene是否为空
        if (Objects.isNull(request.getScene())) {
            throw new BizException(RAG_DATA_SCENE_ERROR);
        }

        // 检查query是否为空
        if (StringUtils.isBlank(request.getQuery())) {
            throw new BizException(RAG_QUERY_NOT_FOUND_QUERY);
        }

        // 检查returnNum是否为空
        if (Objects.isNull(request.getExtension())
                || Objects.isNull(request.getExtension().getReturnNum())
                || request.getExtension().getReturnNum() == 0) {
            throw new BizException(RAG_QUERY_RETURN_NUM_ERROR);
        }
    }

    protected void recall(RagDataContext<T> context) {
        RagRecallStrategyEnum recallStrategy = context.getRecallStrategy();
        log.info("rag data recall strategy == {}", recallStrategy);
        IRecallStrategy<T> strategy = recallStrategyFactory.createStrategy(recallStrategy);
        List<T> recall = strategy.recall(context);
        log.info("recall size == {}", recall.size());
        context.setDocuments(recall);
    }

    protected RagDataContext<T> buildContext(RagDataQueryRequest request) {
        String collectionName = getCollectionName(request);
        return RagDataContext.<T>builder()
                .queryRequest(request)
                .topK(new RagDataContext.TopK())
                .queryResponse(RagDataQueryResponse.empty())
                .collectionName(collectionName).build();
    }

    protected void filterLevel(RagDataContext<FewShotCase> context) {
        String level = Optional.ofNullable(context.getQueryRequest().getExtension())
                .map(RagDataQueryRequest.Extension::getFilterFields)
                .map(fields -> fields.get("issue_level"))
                .map(String::valueOf)
                .orElse(StringUtils.EMPTY);
        log.info("filter level == {}", level);

        if (StringUtils.isNotEmpty(level)) {
            // 保留issueDetails中level字段一直的fewshot case
            context.getDocuments().forEach(caseData -> {
                caseData.getIssueDetails().removeIf(item -> !level.equals(item.get("level")));
            });
        }
    }

    protected void setRecallStrategy(RagDataContext<T> context, RagRecallStrategyEnum defaultStrategy) {
        int recallStrategy = context.getQueryRequest().getExtension().getRecallStrategy();
        if (recallStrategy == 0) {
            recallStrategy = defaultStrategy.getId();
            context.getQueryRequest().getExtension().setRecallStrategy(recallStrategy);
        }
        context.setRecallStrategy(RagRecallStrategyEnum.getById(recallStrategy));
    }

    protected void setEmbeddingModel(RagDataContext<T> context) {
        String embeddingModel = context.getQueryRequest().getExtension().getEmbeddingModel();
        if (StringUtils.isNotEmpty(embeddingModel)) {
            context.setEmbeddingModel(RagEmbeddingModelEnum.getByModel(embeddingModel));
        } else {
            context.setEmbeddingModel(RagEmbeddingModelEnum.BGE_EMBEDDING);
        }
    }

    protected BusinessTrackingLogRecordDO buildLogRecord(String businessTrackingId, String xrayTrackingId, String uuidTrackingId,
                                                         BusinessTrackingLogRecordType businessTrackingLogRecordType,
                                                         String requestLogRecord, String responseLogRecord,
                                                         Date actionDate) {
        BusinessTrackingLogRecordDO logRecord = new BusinessTrackingLogRecordDO();
        logRecord.setBusinessTrackingId(businessTrackingId);
        logRecord.setXrayTrackingId(xrayTrackingId);
        logRecord.setUuidTrackingId(uuidTrackingId);
        logRecord.setBusinessTrackingLogRecordType(businessTrackingLogRecordType.name());
        logRecord.setBusinessTrackingLogRecordTypeDesc(businessTrackingLogRecordType.getDesc());
        logRecord.setRequestLogRecord(requestLogRecord);
        logRecord.setResponseLogRecord(responseLogRecord);
        logRecord.setActionTime(actionDate);
        return logRecord;
    }
}
