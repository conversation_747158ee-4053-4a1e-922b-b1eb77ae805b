package com.xiaohongshu.codewiz.core.feign;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.xiaohongshu.codewiz.core.config.feign.AllInFeignConfiguration;
import com.xiaohongshu.codewiz.core.entity.allin.ChatCompletionRequestDTO;
import com.xiaohongshu.codewiz.core.entity.allin.ChatCompletionResponseDTO;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2025/3/3
 */
@FeignClient(name = "ALL-IN", url = "${feign-client.all-in.url}", configuration = AllInFeignConfiguration.class)
public interface AllInFeignClient {
    @PostMapping("/v1/chat/completions")
    ResponseEntity<ChatCompletionResponseDTO> chatCompletions(@RequestBody ChatCompletionRequestDTO chatRequest);
}