package com.xiaohongshu.codewiz.core.service.rag.open;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.xiaohongshu.codewiz.core.constant.ErrorCodeConstant;
import com.xiaohongshu.codewiz.core.constant.enums.BizPermissionTypeEnum;
import com.xiaohongshu.codewiz.core.entity.rag.open.BizPermissionConfig;
import com.xiaohongshu.codewiz.core.entity.rag.open.BizPermissionInfo;
import com.xiaohongshu.codewiz.core.entity.rag.open.BizPermissionToken;
import com.xiaohongshu.codewiz.core.exception.BizException;
import com.xiaohongshu.codewiz.core.utils.OpenBizTokenHolder;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class BizPermissionService {
    @ApolloJsonValue("${open.biz.permission}")
    BizPermissionConfig config;


    public void checkPermission(String bizId, boolean writeScene) {
        if (StringUtils.isBlank(bizId)) {
            throw new BizException(ErrorCodeConstant.OPEN_RAG_KB_ID_ERROR);
        }
        String token = OpenBizTokenHolder.getToken();

        Boolean enable = config.getEnable();
        if (Boolean.FALSE.equals(enable)) {
            log.info("open biz permission is disabled");
            return;
        }
        List<BizPermissionInfo> infos = config.getConfig();
        for (BizPermissionInfo info : infos) {
            String bizIdFromConfig = info.getBizId();
            if (!bizIdFromConfig.equals(bizId)) {
                continue;
            }
            List<BizPermissionToken> tokens = info.getTokens();
            for (BizPermissionToken bizPermissionToken : tokens) {
                String tokenFromConfig = bizPermissionToken.getToken();
                if (!tokenFromConfig.equals(token)) {
                    continue;
                }
                String type = bizPermissionToken.getType();
                if (BizPermissionTypeEnum.SUPER_ADMIN.name().equals(type)) {
                    return;
                }
                if (writeScene) {
                    boolean canWrite = BizPermissionTypeEnum.BIZ_WRITE.name().equals(type);
                    if (canWrite) {
                        return;
                    }
                } else {
                    return;
                }
            }
        }
        throw new BizException(ErrorCodeConstant.OPEN_RAG_TOKEN_PERMISSION_ERROR);
    }
}
