package com.xiaohongshu.codewiz.core.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/3/11 15:42
 */
@Getter
@AllArgsConstructor
public enum RagCaseKnowledgeEnum {
    CODE_SNIPPET("code_snippet", "_1"),
    CODE_EXPLAIN("code_explain", "_2"),
    COMMENTS("comments", "_3"),
    HYBRID_KNOWLEDGE("hybrid_knowledge", "_4"),
    ;

    private final String fileName;
    private final String itemIdSuffix;
}
