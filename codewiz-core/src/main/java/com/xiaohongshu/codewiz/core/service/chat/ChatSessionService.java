package com.xiaohongshu.codewiz.core.service.chat;

import static com.xiaohongshu.codewiz.core.constant.ChatCommonConstant.TOKEN_USER_ID;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Lists;
import com.xiaohongshu.codewiz.core.dao.chat.ChatDialogDao;
import com.xiaohongshu.codewiz.core.dao.chat.ChatSessionDao;
import com.xiaohongshu.codewiz.core.entity.chat.ChatDialogDO;
import com.xiaohongshu.codewiz.core.entity.chat.ChatSessionDO;
import com.xiaohongshu.codewiz.core.entity.chat.ChatSessionDetailResponse;
import com.xiaohongshu.codewiz.core.entity.chat.ChatSessionListRequest;
import com.xiaohongshu.codewiz.core.entity.chat.ChatSessionListResponse;
import com.xiaohongshu.codewiz.core.entity.chat.ChatSessionListResponse.SessionDTO;
import com.xiaohongshu.codewiz.core.entity.chat.ChatSessionRequest;
import com.xiaohongshu.codewiz.core.entity.chat.ChatSessionResponse;

import cn.hutool.core.collection.CollectionUtil;

/**
 * Author: liukunpeng Date: 2025-03-10 Description:
 */
@Service
public class ChatSessionService {
    @Autowired
    private ChatSessionDao chatSessionDao;
    @Autowired
    private ChatDialogDao chatDialogDao;
    ;

    public ChatSessionResponse createChatSession(String workspaceId) {
        ChatSessionResponse response = new ChatSessionResponse();
        UUID uuid = UUID.randomUUID();
        response.setSessionId(uuid.toString());

        LocalDateTime addTime = LocalDateTime.now();
        ChatSessionDO chatSession = new ChatSessionDO();
        chatSession.setSessionId(response.getSessionId());
        chatSession.setCreateTime(addTime);
        chatSession.setSessionName(addTime.toString()); // 新建时先以时间为名称
        chatSession.setUserId(TOKEN_USER_ID.get());
        if (StringUtils.isNotBlank(workspaceId)) {
            chatSession.setWorkspaceId(workspaceId);
        }
        chatSessionDao.save(chatSession);
        return response;
    }

    public void delSession(String sessionId) {
        chatSessionDao.delSession(sessionId, TOKEN_USER_ID.get());
    }

    /**
     * 编辑会话名称
     *
     * @param request
     */
    public void editSessionName(ChatSessionRequest request) {
        if (StringUtils.isBlank(request.getSessionId())) {
            return;
        }
        if (StringUtils.isBlank(request.getSessionName())) {
            return;
        }
        chatSessionDao.editSessionName(request.getSessionId(), request.getSessionName());
    }

    /**
     * 查询历史会话列表
     *
     * @param req
     * @return
     */
    public ChatSessionListResponse sessionList(ChatSessionListRequest req) {
        ChatSessionListResponse response = new ChatSessionListResponse();
        response.setSessions(Lists.newArrayList());
        response.setTotal(0L);
        String userId = TOKEN_USER_ID.get();
        // 不传工作空间，那么直接返回
        if (StringUtils.isBlank(req.getWorkspaceId())) {
            return response;
        }
        IPage<ChatSessionDO> sessionIpage = chatSessionDao
                .querySessionList(userId, req.getWorkspaceId(), req.getKeyword(), req.getPageNo(), req.getPageSize());
        List<ChatSessionDO> sessionList = sessionIpage.getRecords();
        if (CollectionUtil.isNotEmpty(sessionList)) {
            List<SessionDTO> dtoList = Lists.newArrayList();
            dtoList = sessionList.stream()
                    .map(session -> {
                        SessionDTO sessionDTO = new SessionDTO();
                        sessionDTO.setSessionId(session.getSessionId());
                        sessionDTO.setSessionName(session.getSessionName());
                        sessionDTO.setLastTime(session.getUpdateTime());
                        listSessionInfo(sessionDTO);
                        return sessionDTO;
                    }).collect(Collectors.toList());
            // 移除无效会话
            dtoList.removeIf(dto -> StringUtils.isBlank(dto.getSessionContent()));
            response.setSessions(dtoList);
            response.setTotal(sessionIpage.getTotal());
        }
        return response;
    }

    public ChatSessionDetailResponse sessionDetail(ChatSessionRequest request) {
        ChatSessionDetailResponse response = new ChatSessionDetailResponse();
        // TODO 构建dialogDTO，这个需要等对话实现后，再处理
        // List<DialogDTO> dialogDTOS = buildDialogDTO();
        // response.setDialogs(dialogDTOS);
        response.setSessionId(request.getSessionId());

        return response;
    }

    /**
     * 会话列表内容填充
     *
     * @param sessionDTO
     */
    private void listSessionInfo(SessionDTO sessionDTO) {
        ChatDialogDO dialog = chatDialogDao.selectFirstSessionDialog(sessionDTO.getSessionId());
        if (null != dialog) {
            // 列表中session的type与最早对话的类型一致
            sessionDTO.setSessionType(dialog.getDialogEnum());
            // TODO 描述是首次对话的answer，需要对话流程通了以后再处理
            // sessionDTO.setSessionContent();
        }
    }
}
