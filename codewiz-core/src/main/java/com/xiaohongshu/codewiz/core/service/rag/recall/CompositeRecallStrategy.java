package com.xiaohongshu.codewiz.core.service.rag.recall;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;

import com.google.common.collect.Lists;
import com.xiaohongshu.codewiz.core.entity.rag.RagDocument;
import com.xiaohongshu.codewiz.core.service.rag.RagDataContext;

import lombok.extern.slf4j.Slf4j;

/**
 * 组合召回策略
 *
 * <AUTHOR>
 * @date 2025/4/10 11:20
 */
@Slf4j
public class CompositeRecallStrategy<T extends RagDocument> implements IRecallStrategy<T> {

    /**
     * 子策略列表
     */
    private final List<IRecallStrategy<T>> strategies;

    private final ExecutorService recallExecutor;

    /**
     * 创建组合召回策略
     */
    public CompositeRecallStrategy(List<IRecallStrategy<T>> strategies, ExecutorService recallExecutor) {
        this.strategies = strategies != null ? strategies : Lists.newArrayList();
        this.recallExecutor = recallExecutor;
    }

    /**
     * 执行组合召回策略
     *
     * @param context RAG数据上下文
     */
    public List<T> recall(RagDataContext<T> context) {
        log.info("执行组合召回策略，子策略数量: {}", strategies.size());

        if (CollectionUtils.isEmpty(strategies)) {
            log.warn("组合召回策略中没有子策略");
            return Lists.newArrayList();
        }

        // 使用CompletableFuture并行执行各个策略
        List<CompletableFuture<List<T>>> futures = new ArrayList<>();
        for (IRecallStrategy<T> strategy : strategies) {
            futures.add(CompletableFuture.supplyAsync(() -> strategy.recall(context), recallExecutor));
        }

        // 等待所有策略执行完成,合并返回
        return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .thenApply(v -> futures.stream()
                        .map(CompletableFuture::join)
                        .flatMap(List::stream)
                        .collect(Collectors.toList()))
                .join();
    }
}