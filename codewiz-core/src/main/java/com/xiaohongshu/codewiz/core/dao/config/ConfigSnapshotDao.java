package com.xiaohongshu.codewiz.core.dao.config;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xiaohongshu.codewiz.core.entity.config.ConfigSnapshotDO;
import com.xiaohongshu.codewiz.core.mapper.config.ConfigSnapshotMapper;

/**
 * <p>
 * 配置快照表 DAO层
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-21
 */
@Service
public class ConfigSnapshotDao extends ServiceImpl<ConfigSnapshotMapper, ConfigSnapshotDO> {

    /**
     * 根据插件版本和配置类型查询配置快照
     *
     * @param pluginVersion 插件版本
     * @param configType    配置类型
     * @return 配置快照列表
     */
    public List<ConfigSnapshotDO> queryConfigSnapshot(String pluginVersion, Integer configType, String configName) {
        LambdaQueryWrapper<ConfigSnapshotDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ConfigSnapshotDO::getConfigVersion, pluginVersion);
        if (configType != null) {
            queryWrapper.eq(ConfigSnapshotDO::getConfigType, configType);
        }
        if (StringUtils.isNotBlank(configName)) {
            queryWrapper.eq(ConfigSnapshotDO::getConfigName, configName);
        }
        return this.list(queryWrapper);
    }

    public List<ConfigSnapshotDO> queryConfigSnapshot(String pluginVersion, Integer configType) {
        LambdaQueryWrapper<ConfigSnapshotDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ConfigSnapshotDO::getConfigVersion, pluginVersion);
        if (configType != null) {
            queryWrapper.eq(ConfigSnapshotDO::getConfigType, configType);
        }
        return this.list(queryWrapper);
    }

    /**
     * 根据配置快照ID列表批量查询配置快照
     *
     * @param ids 配置快照ID列表
     * @return 配置快照列表
     */
    public List<ConfigSnapshotDO> batchQueryByIds(List<Long> ids) {
        LambdaQueryWrapper<ConfigSnapshotDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ConfigSnapshotDO::getId, ids);
        return this.list(queryWrapper);
    }

    /**
     * 查询指定类型的最新配置
     *
     * @param configType 配置类型，可为null
     * @return 最新配置列表
     */
    public List<ConfigSnapshotDO> queryLatestByType(Integer configType) {
        LambdaQueryWrapper<ConfigSnapshotDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ConfigSnapshotDO::getIsLasted, true);
        if (configType != null) {
            queryWrapper.eq(ConfigSnapshotDO::getConfigType, configType);
        }
        return this.list(queryWrapper);
    }
}