package com.xiaohongshu.codewiz.core.utils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;

import com.google.common.collect.Lists;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class CompletableFutureUtil {

    private static final ExecutorService executors =
            Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors() * 2);

    /**
     * 遍历sources集合，并对每一个元素执行function函数，将结果存储到results集合中
     * 该过程是异步和线程安全的
     * 需要注意的是，返回的集合结果正确，但是顺序可能并不是自然排序
     *
     * @param sources  需要执行遍历操作的集合
     * @param function 执行的具体操作函数
     * @return results 操作集合存入线程安全的集合
     **/
    public static <T, U> List<U> foreachOperateAddResult(Collection<T> sources, Function<T, U> function) {
        return foreachOperateAddResult(sources, function, executors);
    }

    public static <T, U> List<U> foreachOperateAddResult(Collection<T> sources,
                                                         Function<T, U> function,
                                                         Executor executors) {
        List<CompletableFuture<Void>> futures = Lists.newArrayList();
        List<U> results = Collections.synchronizedList(new ArrayList<U>());
        if (CollectionUtils.isEmpty(sources)) {
            return results;
        }
        // 循环执行异步任务
        sources.forEach(source -> {
            CompletableFuture<Void> taskFuture = CompletableFuture.runAsync(() -> {
                try {
                    U apply = function.apply(source);
                    if (Objects.nonNull(apply)) {
                        results.add(apply);
                    }
                } catch (Exception e) {
                    log.error("foreachAddResult failed! msg == {}", e.getMessage(), e);
                }
            }, executors);
            futures.add(taskFuture);
        });

        // 等待所有异步任务完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        return results;
    }

    public static <T> List<T> syncGetJoinResult(List<CompletableFuture<T>> futures) {
        try {
            return CompletableFuture
                    .allOf(futures.toArray(new CompletableFuture[0]))
                    .thenApply(v -> futures
                            .stream()
                            .map(CompletableFuture::join)
                            .collect(Collectors.toList()))
                    .get();
        } catch (Exception e) {
            log.error("syncGetJoinResult failed exception!", e);
            throw new RuntimeException("syncGetJoinResult failed");
        }
    }

    public static <T> List<T> syncGetJoinResult(List<CompletableFuture<T>> futures,
                                                Predicate<? super T> predicate) {
        try {
            return CompletableFuture
                    .allOf(futures.toArray(new CompletableFuture[0]))
                    .thenApply(v -> futures
                            .stream()
                            .map(CompletableFuture::join)
                            .filter(predicate)
                            .collect(Collectors.toList()))
                    .get();
        } catch (Exception e) {
            log.error("syncGetJoinResult failed exception!", e);
            throw new RuntimeException("syncGetJoinResult failed");
        }
    }

    /**
     * 无返回值异步操作
     */
    public static <T> void asyncOperate(Collection<T> sources, Consumer<T> consumer) {
        // 循环执行异步任务
        sources.forEach(source -> {
            executors.execute(() -> consumer.accept(source));
        });
    }
}