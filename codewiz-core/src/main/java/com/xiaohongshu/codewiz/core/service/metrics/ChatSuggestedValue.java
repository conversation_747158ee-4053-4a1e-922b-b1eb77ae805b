package com.xiaohongshu.codewiz.core.service.metrics;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/6/16 14:48
 */
@Data
public class ChatSuggestedValue {
    private String time;
    private String sessionId;
    private String requestId;
    @JsonProperty("code_suggested_snippet")
    private List<CodeSuggestedSnippet> codeSuggestedSnippets;
    @JsonProperty("code_suggested_snippet_single")
    private CodeSuggestedSnippet codeSuggestedSnippetSingle;

    @Data
    public static class CodeSuggestedSnippet {
        private int index;
        @JsonProperty("code_line_count")
        private int codeLineCount;
        private String language;
        @JsonProperty("code_snippet")
        private String codeSnippet;
    }
}
