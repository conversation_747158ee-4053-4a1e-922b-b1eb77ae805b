package com.xiaohongshu.codewiz.core.service.rag.query;

import java.util.List;

import com.xiaohongshu.codewiz.core.entity.rag.RagDataQueryRequest;
import com.xiaohongshu.codewiz.core.entity.rag.RagDocument;

import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/2/28 15:49
 */
@Data
@Builder
public class RagQueryDataContext<T extends RagDocument> {
    private RagDataQueryRequest request;
    // collectionName
    private String collectionName;
    private List<T> datas;
}
