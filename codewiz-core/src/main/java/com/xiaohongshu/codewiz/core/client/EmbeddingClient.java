package com.xiaohongshu.codewiz.core.client;

import java.util.List;

import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import com.google.common.collect.Lists;
import com.xiaohongshu.codewiz.core.annotation.LogExecutionTime;
import com.xiaohongshu.codewiz.core.constant.ErrorCodeConstant;
import com.xiaohongshu.codewiz.core.constant.enums.RagEmbeddingModelEnum;
import com.xiaohongshu.codewiz.core.entity.embedding.EmbeddingRequest;
import com.xiaohongshu.codewiz.core.entity.embedding.EmbeddingResponse;
import com.xiaohongshu.codewiz.core.entity.embedding.GteEmbeddingResponse;
import com.xiaohongshu.codewiz.core.exception.BizException;

import lombok.extern.slf4j.Slf4j;


/**
 * <AUTHOR>
 * @date 2025/2/25 19:11
 */
@Slf4j
@Component
public class EmbeddingClient {

    private final RestTemplate restTemplate;

    public EmbeddingClient(RestTemplate modelRestTemplate) {
        this.restTemplate = modelRestTemplate;
    }

    @LogExecutionTime
    public List<Float> getBgeEmbeddingVector(String sentence) {
        return getBgeJinaEmbeddingVector(sentence, RagEmbeddingModelEnum.BGE_EMBEDDING);
    }

    @LogExecutionTime
    public List<Float> getEmbeddingVector(String sentence, RagEmbeddingModelEnum model) {
        switch (model) {
            case JINA_EMBEDDING:
                return getBgeJinaEmbeddingVector(sentence, model);
            case GTE_EMBEDDING:
                return getGteEmbeddingVector(sentence);
            default:
                return getBgeEmbeddingVector(sentence);
        }
    }

    @LogExecutionTime
    public List<Float> getBgeJinaEmbeddingVector(String sentence, RagEmbeddingModelEnum model) {
        try {
            log.info("getEmbeddingVector by model:{}", model);
            EmbeddingRequest request = new EmbeddingRequest();
            request.setTexts(Lists.newArrayList(sentence));
            request.setModelType(model.getModel());
            EmbeddingResponse response = getBgeJinaEmbeddingVector(request, model);
            return response.getEmbeddings().get(0);
        } catch (Exception e) {
            log.error("error when get embedding vector, request text == {}", sentence, e);
            throw new BizException(ErrorCodeConstant.RAG_DATA_QUERY_EMBEDDING_FAILED);
        }
    }

    @LogExecutionTime
    public List<Float> getGteEmbeddingVector(String sentence) {
        try {
            log.info("getGteEmbeddingVector for text");
            EmbeddingRequest request = new EmbeddingRequest();
            request.setTexts(Lists.newArrayList(sentence));
            GteEmbeddingResponse response = getGteEmbeddingVector(request, RagEmbeddingModelEnum.GTE_EMBEDDING);
            if (response.getData() == null || response.getData().isEmpty()) {
                log.error("empty response from gte embedding API, request text == {}", sentence);
                throw new BizException(ErrorCodeConstant.RAG_DATA_QUERY_EMBEDDING_FAILED);
            }
            return response.getData().get(0).getEmbedding();
        } catch (Exception e) {
            log.error("error when get gte embedding vector, request text == {}", sentence, e);
            throw new BizException(ErrorCodeConstant.RAG_DATA_QUERY_EMBEDDING_FAILED);
        }
    }


    private EmbeddingResponse getBgeJinaEmbeddingVector(EmbeddingRequest request, RagEmbeddingModelEnum model) {
        // Set HTTP headers
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        // Create HttpEntity with the payload and headers
        HttpEntity<EmbeddingRequest> requestEntity = new HttpEntity<>(request, headers);

        // Send POST request
        ResponseEntity<EmbeddingResponse> responseEntity = null;
        try {
            responseEntity = restTemplate.exchange(
                    model.getUrl(),
                    HttpMethod.POST,
                    requestEntity,
                    EmbeddingResponse.class
            );
        } catch (RestClientException e) {
            log.error("error when get embedding vector, request text == {}", request.getTexts(), e);
            return EmbeddingResponse.empty();
        }

        // Check response status
        if (responseEntity.getStatusCode().isError()) {
            throw new RuntimeException("Failed to get embedding vector, status code: " + responseEntity.getStatusCode());
        }

        // Parse and return the embedding vector
        EmbeddingResponse responseData = responseEntity.getBody();
        if (responseData == null || responseData.getEmbeddings().isEmpty()) {
            throw new RuntimeException("Invalid response received from embedding API.");
        }
        return responseData;
    }

    private GteEmbeddingResponse getGteEmbeddingVector(EmbeddingRequest request, RagEmbeddingModelEnum model) {
        // 设置HTTP头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        // 创建HttpEntity，包含请求体和头信息
        HttpEntity<EmbeddingRequest> requestEntity = new HttpEntity<>(request, headers);

        // 发送POST请求
        ResponseEntity<GteEmbeddingResponse> responseEntity = null;
        try {
            responseEntity = restTemplate.exchange(
                    model.getUrl(),
                    HttpMethod.POST,
                    requestEntity,
                    GteEmbeddingResponse.class
            );
        } catch (RestClientException e) {
            log.error("error when get gte embedding vector, request text == {}", request.getTexts(), e);
            return GteEmbeddingResponse.empty();
        }

        // 检查响应状态
        if (responseEntity.getStatusCode().isError()) {
            throw new RuntimeException("Failed to get gte embedding vector, status code: " + responseEntity.getStatusCode());
        }

        // 解析并返回嵌入向量响应
        GteEmbeddingResponse responseData = responseEntity.getBody();
        if (responseData == null || responseData.getCode() != 0) {
            throw new RuntimeException("Invalid response received from gte embedding API: " +
                    (responseData != null ? responseData.getMsg() : "null response"));
        }
        return responseData;
    }
}