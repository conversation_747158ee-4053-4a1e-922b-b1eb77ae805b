package com.xiaohongshu.codewiz.core.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface CacheWithPrefix {
    /**
     * 缓存前缀
     */
    String prefix() default "";

    /**
     * 过期时间(秒)
     */
    int expireSeconds() default 30;
} 