package com.xiaohongshu.codewiz.core.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * RAG召回策略枚举
 *
 * <AUTHOR>
 * @date 2025/4/10 11:20
 */
@Getter
@AllArgsConstructor
public enum RagRecallStrategyEnum {
    /**
     * 代码向量召回
     */
    CODE_SNIPPET(1, "代码向量召回"),

    /**
     * 代码含义向量召回
     */
    CODE_EXPLAIN(2, "代码含义向量召回"),

    /**
     * 混合文本向量召回
     */
    HYBRID_KNOWLEDGE(3, "混合文本向量召回"),

    /**
     * 代码+代码含义组合召回
     */
    CODE_SNIPPET_AND_EXPLAIN(4, "代码+代码含义组合召回"),

    /**
     * 评论向量召回
     */
    COMMENTS(5, "评论向量召回"),

    /**
     * ES召回
     */
    COMMENTS_ES(6, "评论ES召回"),

    /**
     * 评论+ES组合召回
     */
    COMMENTS_AND_ES(7, "评论向量+ES组合召回"),
    ;

    /**
     * 策略ID
     */
    private final Integer id;

    /**
     * 策略描述
     */
    private final String description;

    /**
     * 根据ID获取枚举
     *
     * @param id 策略ID
     * @return 召回策略枚举
     */
    public static RagRecallStrategyEnum getById(Integer id) {
        if (id == null) {
            return CODE_SNIPPET;
        }

        for (RagRecallStrategyEnum strategy : RagRecallStrategyEnum.values()) {
            if (strategy.getId().equals(id)) {
                return strategy;
            }
        }

        return CODE_SNIPPET;
    }
}