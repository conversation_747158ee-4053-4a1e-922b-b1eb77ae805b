package com.xiaohongshu.codewiz.core.entity.rag;

import java.util.List;
import java.util.Map;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xiaohongshu.codewiz.core.entity.elasticsearch.EsSearchResultDto;
import com.xiaohongshu.infra.utils.ObjectMapperUtils;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/2/25 19:11
 */
@Data
public class FewShotCase implements RagDocument {
    /**
     * 自增id
     */
    // @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 代码片段
     */
    private String codeSnippet;

    /**
     * Review 的目标函数
     */
    private String targetFunction;

    private String fieldName;
    private String fieldContent;

    /**
     * json 结构，review 的 issue 详情
     */
    private List<Map<String, Object>> issueDetails;

    /**
     * 问题类型
     */
    private String problemType;

    /**
     * case类型，方便区分不同场景的case
     */
    private String caseTag;

    /**
     * 审查类型（function、design、patch）
     */
    private String reviewType;

    /**
     * 所属仓库
     */
    private String repo;

    /**
     * 编程语言
     */
    private String language;

    /**
     * 是否删除，0-有效，1-删除
     */
    private Integer isDelete;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * json 结构，额外信息
     */
    private String extra;

    private String branch;
    private String filePath;
    private String issueId;
    private Map<String, Object> knowledge;

    private String collectionName;
    private String embeddingModel;
    private String annField;
    private Float score;

    public static FewShotCase convertFromEsSearchResultDto(EsSearchResultDto dto) {
        FewShotCase fewShotCase = new FewShotCase();
        fewShotCase.setScore((float) dto.getScore());
        fewShotCase.setFieldContent(dto.getContent());
        List<Map<String, Object>> issueDetails = Lists.newArrayList();
        Map<String, Object> issueDetail = Maps.newHashMap();
        issueDetail.put("level", "low");
        issueDetail.put("description", dto.getContent());
        issueDetails.add(issueDetail);
        fewShotCase.setIssueDetails(issueDetails);
        fewShotCase.setFieldName("es_recall");
        Map<String, Object> meta = dto.getMeta();
        if (meta != null) {
            fewShotCase.setExtra(ObjectMapperUtils.toJSON(meta));
        }
        return fewShotCase;
    }
}
