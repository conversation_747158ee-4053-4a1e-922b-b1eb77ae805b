package com.xiaohongshu.codewiz.core.service.rag;

import static com.xiaohongshu.codewiz.core.constant.ErrorCodeConstant.RAG_DATA_ADD_ERROR;
import static com.xiaohongshu.codewiz.core.constant.ErrorCodeConstant.RAG_DATA_SCENE_ERROR;

import java.util.Optional;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.xiaohongshu.codewiz.core.constant.enums.RagDataTypeEnum;
import com.xiaohongshu.codewiz.core.entity.rag.RagDataAddRequest;
import com.xiaohongshu.codewiz.core.entity.rag.RagDataQueryRequest;
import com.xiaohongshu.codewiz.core.entity.rag.RagDataQueryResponse;
import com.xiaohongshu.codewiz.core.entity.rag.RagDataRequest;
import com.xiaohongshu.codewiz.core.entity.rag.RagDocument;
import com.xiaohongshu.codewiz.core.exception.BizException;
import com.xiaohongshu.codewiz.core.service.rag.RagDataContext.RagAddResult;
import com.xiaohongshu.codewiz.core.service.rag.add.RagAddDataFactory;
import com.xiaohongshu.codewiz.core.service.rag.add.RagAddDataService;
import com.xiaohongshu.codewiz.core.service.rag.query.RagQueryDataFactory;
import com.xiaohongshu.codewiz.core.service.rag.query.RagQueryDataService;
import com.xiaohongshu.codewiz.core.utils.JsonMapperUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2025/2/27 21:29
 */
@Slf4j
@Service
public class RagDataDriver {
    @Autowired
    private RagAddDataFactory ragAddDataFactory;
    @Autowired
    private RagQueryDataFactory ragQueryDataFactory;

    public RagAddResult<RagDocument> addData(RagDataAddRequest request) {
        try {
            log.info("rag data add scene == {}, source == {}, data size == {}",
                    request.getScene(), request.getSource(), request.getDocuments().size());
            RagDataTypeEnum ragDataTypeEnum = getRagDataTypeEnum(request);
            RagAddDataService service = ragAddDataFactory.createFactory(ragDataTypeEnum);
            RagAddResult<RagDocument> addResult = service.addData(request);
            log.info("add data success");
            return addResult;
        } catch (Exception e) {
            log.error("add data failed!", e);
            throw new BizException(RAG_DATA_ADD_ERROR);
        }
    }

    private static RagDataTypeEnum getRagDataTypeEnum(RagDataRequest request) {
        Optional.ofNullable(request)
                .map(RagDataRequest::getScene)
                .orElseThrow(() -> new BizException(RAG_DATA_SCENE_ERROR));
        Integer scene = request.getScene();
        RagDataTypeEnum ragDataTypeEnum = RagDataTypeEnum.getByScene(scene);
        if (ragDataTypeEnum == null) {
            throw new BizException(RAG_DATA_SCENE_ERROR);
        }
        log.info("rag data type enum == {}", ragDataTypeEnum.name());
        return ragDataTypeEnum;
    }

    public RagDataQueryResponse queryData(RagDataQueryRequest request) {
        try {
            log.info("rag data query data == {}", JsonMapperUtils.toJson(request));
            RagDataTypeEnum ragDataTypeEnum = getRagDataTypeEnum(request);
            RagQueryDataService service = ragQueryDataFactory.createFactory(ragDataTypeEnum);
            RagDataQueryResponse query = service.query(request);
            log.info("query data success, documents count == {}", CollectionUtils.size(query.getDocuments()));
            return query;
        } catch (BizException e) {
            throw e;
        } catch (Exception e) {
            log.error("query data failed!", e);
        }
        return RagDataQueryResponse.empty();
    }
}
