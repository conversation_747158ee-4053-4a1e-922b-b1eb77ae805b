package com.xiaohongshu.codewiz.core.interceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import com.xiaohongshu.codewiz.core.constant.ErrorCodeConstant;
import com.xiaohongshu.codewiz.core.exception.BizException;
import com.xiaohongshu.codewiz.core.utils.OpenBizTokenHolder;

@Component
public class OpenBizTokenInterceptor implements HandlerInterceptor {


    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        // 从请求头中获取token
        String token = request.getHeader("Authorization");

        // 如果token为空或空字符串，抛出异常或返回错误
        if (token == null || token.trim().isEmpty()) {
            throw new BizException(ErrorCodeConstant.OPEN_RAG_TOKEN_NOT_FOUND_ERROR);
        }

        // 如果需要，可以在这里验证token的有效性

        // 将token存储到ThreadLocal
        OpenBizTokenHolder.setToken(token);
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        // 请求完成后清除ThreadLocal，防止内存泄漏
        OpenBizTokenHolder.clear();
    }
}
