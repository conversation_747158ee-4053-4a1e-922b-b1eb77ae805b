package com.xiaohongshu.codewiz.core.config;

import java.util.concurrent.Executor;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.xiaohongshu.infra.concurrent.PlatformExecutors;

/**
 * <AUTHOR>
 * @date 2025/2/27 21:18
 */
@Configuration
public class ExecutorConfig {


    private static final int GENERATE_DETAIL_POOL_SIZE = 128;
    private static final int CHAT_SESSION_DIALOG_POOL_SIZE = 16;

    @Bean
    public Executor ragAddDataExecutor() {
        return new ThreadPoolExecutor(GENERATE_DETAIL_POOL_SIZE, GENERATE_DETAIL_POOL_SIZE, 0L, TimeUnit.MILLISECONDS,
                new LinkedBlockingQueue<Runnable>());
    }

    @Bean
    public Executor logRecordExecutor() {
        return new ThreadPoolExecutor(GENERATE_DETAIL_POOL_SIZE / 4, GENERATE_DETAIL_POOL_SIZE / 4, 0L, TimeUnit.SECONDS,
                new LinkedBlockingQueue<Runnable>(1000));
    }

    @Bean
    public ExecutorService codewizExecutor() {
        return PlatformExecutors.dynamicExecutor("codewiz-executor", GENERATE_DETAIL_POOL_SIZE, GENERATE_DETAIL_POOL_SIZE, 100, true,
                new ThreadPoolExecutor.AbortPolicy());
    }

    @Bean
    public ExecutorService callGraphExecutor() {
        return PlatformExecutors.dynamicExecutor("codewiz-call-graph", GENERATE_DETAIL_POOL_SIZE, GENERATE_DETAIL_POOL_SIZE, 1024, true,
                new ThreadPoolExecutor.AbortPolicy());
    }

    @Bean
    public ExecutorService javaParserExecutor() {
        // CPU密集型操作，最大/核心线程数=CPU核数*2
        int coreNum = Runtime.getRuntime().availableProcessors();
        return new ThreadPoolExecutor(coreNum * 2,           // 核心线程数
                coreNum * 2,           // 最大线程数
                0L,                    // 空闲线程存活时间
                TimeUnit.MILLISECONDS, // 时间单位
                new LinkedBlockingQueue<>() // 无界队列，永远不会拒绝
        );
    }

    @Bean
    public ExecutorService astParseExecutor() {
        // CPU密集型操作，最大/核心线程数=CPU核数*2
        int coreNum = Runtime.getRuntime().availableProcessors();
        return PlatformExecutors.dynamicExecutor("ast-parse-executor", coreNum * 2, coreNum * 2, 1024, true,
                new ThreadPoolExecutor.AbortPolicy());
    }


    @Bean
    public ExecutorService bizSearchExecutor() {
        return PlatformExecutors.dynamicExecutor("open-biz-search-executor", GENERATE_DETAIL_POOL_SIZE, GENERATE_DETAIL_POOL_SIZE, 100,
                true, new ThreadPoolExecutor.AbortPolicy());
    }

    @Bean
    public ExecutorService recallExecutor() {
        return PlatformExecutors.dynamicExecutor("recall-executor", GENERATE_DETAIL_POOL_SIZE, GENERATE_DETAIL_POOL_SIZE, 100, true,
                new ThreadPoolExecutor.AbortPolicy());
    }

    @Bean
    public ExecutorService mavenBatchFetchExecutor() {
        // IO密集型操作，线程数可以设置为CPU核数*3~4倍，适用于网络请求
        return new ThreadPoolExecutor(32,           // 核心线程数
                64,           // 最大线程数
                60L,                   // 空闲线程存活时间60秒
                TimeUnit.SECONDS,      // 时间单位
                new LinkedBlockingQueue<>(500) // 有界队列，避免任务堆积过多
        );
    }

    @Bean
    public ExecutorService logUploadExecutor() {
        // 日志上报专用线程池 - IO密集型操作，4个固定线程处理日志输出
        AtomicInteger threadNumber = new AtomicInteger(1);
        return new ThreadPoolExecutor(4, 4, 60L, TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(20000),
                r -> new Thread(r, "log-upload-thread-" + threadNumber.getAndIncrement()));
    }

}
