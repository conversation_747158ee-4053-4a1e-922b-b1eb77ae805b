package com.xiaohongshu.codewiz.core.service.factory;

import java.lang.reflect.ParameterizedType;
import java.util.Map;
import java.util.Objects;
import java.util.function.Supplier;

import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

import com.google.common.collect.Maps;

import lombok.extern.slf4j.Slf4j;

/**
 * 抽象工厂类
 *
 * <AUTHOR>
 * @date 2025/2/25 19:11
 */
@Slf4j
public abstract class AbstractServiceFactory<T, U extends BaseService<T>> implements InitializingBean, ApplicationContextAware {
    private ApplicationContext context;
    private final Map<T, Supplier<U>> SERVICE_MAP = Maps.newHashMap();

    public U createFactory(T source) {
        Supplier<U> tSupplier = SERVICE_MAP.get(source);
        if (Objects.nonNull(tSupplier)) {
            return tSupplier.get();
        }

        log.info("not found ServiceFactoryTemplate service, type == {}", getUClass());
        return null;
    }

    @Override
    public void afterPropertiesSet() {
        Map<String, U> beansOfType = context.getBeansOfType(getUClass());
        beansOfType.values().forEach(service -> SERVICE_MAP.put(service.source(), () -> service));
        log.info("construct service factory, map == {}", SERVICE_MAP);
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.context = applicationContext;
    }

    @SuppressWarnings("unchecked")
    private Class<U> getUClass() {
        return (Class<U>) ((ParameterizedType) getClass().getGenericSuperclass()).getActualTypeArguments()[1];
    }
}
