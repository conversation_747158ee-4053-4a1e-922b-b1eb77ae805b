package com.xiaohongshu.codewiz.core.remote;

import java.util.Base64;
import java.util.Map;
import java.util.Optional;

import javax.annotation.PreDestroy;

import org.gitlab4j.api.CommitsApi;
import org.gitlab4j.api.GitLabApi;
import org.gitlab4j.api.GitLabApiException;
import org.gitlab4j.api.ProjectApi;
import org.gitlab4j.api.RepositoryApi;
import org.gitlab4j.api.RepositoryFileApi;
import org.gitlab4j.api.models.Commit;
import org.gitlab4j.api.models.CompareResults;
import org.gitlab4j.api.models.Project;
import org.gitlab4j.api.models.RepositoryFile;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * Created on 2025/3/15
 */
@Slf4j
@Component
public class GitlabMediator {
    private final GitLabApi gitLabApi;
    private final RepositoryApi repositoryApi;
    private final RepositoryFileApi repositoryFileApi;
    private final ProjectApi projectApi;
    private final CommitsApi commitsApi;

    public GitlabMediator(@Value("${gitlab.host}") String gitlabHost, @Value("${gitlab.admin-private-token}") String token) {
        this.gitLabApi = new GitLabApi(gitlabHost, token);
        this.repositoryApi = this.gitLabApi.getRepositoryApi();
        this.repositoryFileApi = gitLabApi.getRepositoryFileApi();
        this.projectApi = gitLabApi.getProjectApi();
        this.commitsApi = gitLabApi.getCommitsApi();
    }

    /**
     * 获取特定的mr快照信息
     */
    public CompareResults loadMrSnapshotInfo(Long projectId, String baseCommitSha, String headCommitSha) throws GitLabApiException {
        return repositoryApi.compare(projectId, baseCommitSha, headCommitSha, true);
    }

    /**
     * 加载文件内容
     */
    public Optional<String> loadFileContent(Long projectId, String filePath, String commitSha) {
        try {
            RepositoryFile newRepositoryFile = repositoryFileApi.getFile(projectId, filePath, commitSha);
            return Optional.of(new String(Base64.getDecoder().decode(newRepositoryFile.getContent())));
        } catch (Exception ex) {
            log.error("An error occurred while loading file content, projectId: {}, filePath: {}, commitSha: {}",
                    projectId, filePath, commitSha, ex);
            return Optional.empty();
        }
    }

    /**
     * 获取项目信息
     */
    public Optional<Project> loadProject(Object projectIdOrPath) {
        try {
            return Optional.ofNullable(projectApi.getProject(projectIdOrPath));
        } catch (GitLabApiException e) {
            log.error("An error occurred while loading project info, projectIdOrPath:{}", projectIdOrPath, e);
            return Optional.empty();
        }
    }

    /**
     * 获取commit
     */
    public Optional<Commit> getCommit(Object projectIdOrPath, String commitSha) {
        try {
            return Optional.ofNullable(commitsApi.getCommit(projectIdOrPath, commitSha));
        } catch (GitLabApiException e) {
            log.error("An error occurred while loading project commit, projectIdOrPath:{}, sha: {}", projectIdOrPath, commitSha, e);
            return Optional.empty();
        }
    }

    /**
     * 获取项目语言占比
     *
     * @param projectIdOrPath 项目id或者路径
     * @return 项目语言占比
     */
    public Optional<Map<String, Float>> getProjectLanguages(Object projectIdOrPath) {
        try {
            return Optional.ofNullable(projectApi.getProjectLanguages(projectIdOrPath));
        } catch (GitLabApiException e) {
            log.error("An error occurred while loading project languages, projectIdOrPath:{}", projectIdOrPath, e);
            return Optional.empty();
        }
    }

    /**
     * 获取项目主语言
     *
     * @param projectIdOrPath 项目id或者路径
     * @return 项目主语言
     */
    public Optional<String> getPrimaryLanguage(Object projectIdOrPath) {
        return getProjectLanguages(projectIdOrPath).flatMap(map -> map.entrySet().stream().max(Map.Entry.comparingByValue()))
                .map(Map.Entry::getKey);
    }

    @PreDestroy
    public void destroy() {
        if (gitLabApi != null) {
            gitLabApi.close();
        }
    }
}
