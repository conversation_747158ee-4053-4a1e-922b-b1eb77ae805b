package com.xiaohongshu.codewiz.core.service.rag.recall;

import java.util.List;

import com.google.common.collect.Lists;
import com.xiaohongshu.codewiz.core.entity.rag.RagDocument;
import com.xiaohongshu.codewiz.core.service.elasticsearch.ElasticsearchService;
import com.xiaohongshu.codewiz.core.service.rag.RagDataContext;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2025/4/13 10:33
 */
@Slf4j
public abstract class AbstractEsRecallStrategy<T extends RagDocument> implements IRecallStrategy<T> {

    protected ElasticsearchService elasticsearchService;

    public AbstractEsRecallStrategy(ElasticsearchService elasticsearchService) {
        this.elasticsearchService = elasticsearchService;
    }

    @Override
    public List<T> recall(RagDataContext<T> context) {
        try {
            return doRecall(context);
        } catch (Exception e) {
            log.error("执行es召回策略失败，context: {}", context, e);
            return Lists.newArrayList();
        }
    }

    protected abstract List<T> doRecall(RagDataContext<T> context);
}
