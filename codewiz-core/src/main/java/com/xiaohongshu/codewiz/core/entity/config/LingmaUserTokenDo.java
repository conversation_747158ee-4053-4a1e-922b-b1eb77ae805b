package com.xiaohongshu.codewiz.core.entity.config;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * <p>
 * 服务商token表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-21
 */
@Data
@TableName("lingma_user_token")
public class LingmaUserTokenDo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID，自增长
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * lingma用户ID
     */
    private String lingmaUserId;

    /**
     * lingma登陆用户：邮箱前缀
     */
    private String userName;

    /**
     * 用户名称
     */
    private String name;

    /**
     * 昵称
     */
    private String nickName;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 个人token
     */
    private String lingmaToken;

    /**
     * 个人token id
     */
    private String lingmaTokenId;

    /**
     * 有效期至
     */
    private LocalDateTime expiredTime;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;

    /**
     * 是否删除: 0-未删除, 1-已删除
     */
    private Integer isDeleted;

}


