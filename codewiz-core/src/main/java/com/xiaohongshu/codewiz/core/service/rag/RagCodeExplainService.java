package com.xiaohongshu.codewiz.core.service.rag;

import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import com.xiaohongshu.codewiz.core.annotation.CacheWithPrefix;
import com.xiaohongshu.codewiz.core.entity.allin.ChatCompletionRequestDTO;
import com.xiaohongshu.codewiz.core.entity.allin.ChatCompletionResponseDTO;
import com.xiaohongshu.codewiz.core.entity.common.SingleResponse;
import com.xiaohongshu.codewiz.core.feign.LLMAdapterFeignClient;
import com.xiaohongshu.xray.logging.LogTags;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2025/3/11 14:30
 */
@Slf4j
@Service
public class RagCodeExplainService {

    @Resource
    private LLMAdapterFeignClient llmAdapterFeignClient;

    @Value("${rag.case.code.explain.model}")
    private String model;

    private static final int MAX_TOKENS = 1024;
    private static final double TEMPERATURE = 0.1;
    private static final Duration TIMEOUT = Duration.ofSeconds(30);
    private static final String TEMPLATE_CODE =
            "```{code}```\n\n请简要分析这段代码实现的功能，不要大段的细节描述，只需要给我整个代码的业务功能实现即可";
    public static final String ERROR_PREFIX = "代码解释失败 ";

    @CacheWithPrefix(prefix = "code_explain", expireSeconds = 30)
    public String explainCode(String code) {
        long startMillis = System.currentTimeMillis();
        try {
            List<ChatCompletionRequestDTO.ChatMessage> messages = List.of(
                    ChatCompletionRequestDTO.ChatMessage.builder()
                            .role("user")
                            .content(TEMPLATE_CODE.replace("{code}", code))
                            .build()
            );

            ChatCompletionRequestDTO chatRequest = ChatCompletionRequestDTO.builder()
                    .model(model)
                    .messages(messages)
                    .stream(false)
                    .maxTokens(MAX_TOKENS)
                    .temperature(TEMPERATURE)
                    .build();

            ResponseEntity<SingleResponse<ChatCompletionResponseDTO>> response =
                    llmAdapterFeignClient.chatCompletions(chatRequest, false, System.getenv("XHS_SERVICE"));

            return Optional.ofNullable(response.getBody())
                    .map(SingleResponse::getData)
                    .map(ChatCompletionResponseDTO::getChoices)
                    .flatMap(choices -> choices.stream().findFirst())
                    .map(ChatCompletionResponseDTO.Choice::getMessage)
                    .map(ChatCompletionResponseDTO.ChatMessage::getContent)
                    .orElse(StringUtils.EMPTY);
        } catch (Exception e) {
            log.error(LogTags.of(Map.of("cost", System.currentTimeMillis() - startMillis)),
                    ERROR_PREFIX, e);
            return StringUtils.EMPTY;
        } finally {
            long time = System.currentTimeMillis() - startMillis;
            log.info(LogTags.of(Map.of("rag_code_explain_cost", time)), "代码解释完成 cost == {}", time);
        }
    }
}
