package com.xiaohongshu.codewiz.core.service.rag.add;

import com.xiaohongshu.codewiz.core.constant.enums.RagDataTypeEnum;
import com.xiaohongshu.codewiz.core.entity.rag.RagDataAddRequest;
import com.xiaohongshu.codewiz.core.service.factory.BaseService;
import com.xiaohongshu.codewiz.core.service.rag.RagDataContext;

/**
 * <AUTHOR>
 * @date 2025/2/27 19:32
 */
public interface RagAddDataService extends BaseService<RagDataTypeEnum> {

    RagDataContext.RagAddResult addData(RagDataAddRequest request);

}
