package com.xiaohongshu.codewiz.core.service.rag.add;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.common.collect.Maps;
import com.xiaohongshu.codewiz.core.constant.enums.RagDataTypeEnum;
import com.xiaohongshu.codewiz.core.entity.rag.NegativeComment;
import com.xiaohongshu.codewiz.core.entity.rag.RagDataAddRequest;
import com.xiaohongshu.codewiz.core.service.elasticsearch.ElasticsearchService;
import com.xiaohongshu.codewiz.core.service.rag.RagDataContext;
import com.xiaohongshu.codewiz.core.utils.JsonMapperUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 负向评论添加服务
 *
 * <AUTHOR>
 * @date 2025/3/28 10:30
 */
@Slf4j
@Service
public class RagAddNegativeCommentsService extends AbsRagAddDataServiceService<NegativeComment> {

    @Autowired
    private ElasticsearchService elasticsearchService;

    @Override
    protected RagDataContext<NegativeComment> buildContext(RagDataAddRequest request) {
        RagDataContext<NegativeComment> context = super.buildContext(request);
        String source = request.getSource();

        List<NegativeComment> comments = request
                .getDocuments()
                .stream()
                .map(doc -> {
                    NegativeComment comment = new NegativeComment();
                    comment.setId(UUID.randomUUID().toString());

                    // 从原始数据中提取字段
                    if (doc.containsKey("cmt_rule")) {
                        comment.setCmtRule(doc.get("cmt_rule").toString());
                    }

                    if (doc.containsKey("create_at")) {
                        Object createAt = doc.get("create_at");
                        if (createAt instanceof Number) {
                            comment.setCreateAt(((Number) createAt).longValue());
                        } else if (createAt instanceof String) {
                            try {
                                comment.setCreateAt(Long.parseLong((String) createAt));
                            } catch (NumberFormatException e) {
                                log.warn("Invalid create_at value: {}", createAt);
                                comment.setCreateAt(System.currentTimeMillis());
                            }
                        } else {
                            comment.setCreateAt(System.currentTimeMillis());
                        }
                    } else {
                        comment.setCreateAt(System.currentTimeMillis());
                    }

                    if (doc.containsKey("language")) {
                        comment.setLanguage(doc.get("language").toString());
                    }

                    if (doc.containsKey("meta") && doc.get("meta") instanceof Map) {
                        @SuppressWarnings("unchecked")
                        Map<String, Object> meta = (Map<String, Object>) doc.get("meta");
                        comment.setMeta(meta);
                    }

                    return comment;
                })
                .collect(Collectors.toList());

        context.setDocuments(comments);
        log.info("Negative comments context built with {} comments", comments.size());
        return context;
    }

    @Override
    protected void check(RagDataAddRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("Request cannot be null");
        }

        if (CollectionUtils.isEmpty(request.getDocuments())) {
            throw new IllegalArgumentException("Documents cannot be empty");
        }

        // 创建ES索引（如果不存在）
        boolean indexCreated = elasticsearchService.createNegativeCommentsIndex();
        log.info("Negative comments index check: {}", indexCreated ? "created/exists" : "failed to create");
    }

    @Override
    protected String getCollectionName(RagDataAddRequest request) {
        return elasticsearchService.getNegativeCommentsIndexName();
    }

    @Override
    protected void extendDocument(RagDataContext<NegativeComment> context) {
        // 对负向评论不需要额外处理
        log.info("No extra processing needed for negative comments");
    }

    @Override
    protected void saveToMysql(RagDataContext<NegativeComment> context) {
        // 负向评论不需要存入MySQL
        log.info("Negative comments don't need to be saved to MySQL");
    }

    /**
     * 注意：为了保持与现有接口一致，我们重用了saveToMilvus方法，
     * 但实际上这里是将数据保存到Elasticsearch而不是Milvus
     */
    @Override
    protected void saveToMilvus(RagDataContext<NegativeComment> context) {
        // 将数据存入ES
        List<Map<String, Object>> documents = new ArrayList<>();

        for (NegativeComment comment : context.getDocuments()) {
            // 先转成JSON字符串，再转成Map
            String json = JsonMapperUtils.toJson(comment);
            Map<String, Object> document = JsonMapperUtils.fromJson(json);
            documents.add(document);
        }
        List<Map<String, Object>> newDocuments = convertKeysToSnakeCase(documents);
        boolean success = elasticsearchService.bulkInsertNegativeComments(newDocuments);

        // 设置操作结果
        RagDataContext.RagAddResult<NegativeComment> result = RagDataContext.RagAddResult.<NegativeComment>builder()
                .successCount(success ? documents.size() : 0)
                .failedCount(success ? 0 : documents.size())
                .failedDocuments(success ? new ArrayList<>() : context.getDocuments())
                .build();

        context.setAddResult(result);

        log.info("Saved {} negative comments to Elasticsearch: {}", documents.size(), success ? "success" : "failed");
    }

    public List<Map<String, Object>> convertKeysToSnakeCase(List<Map<String, Object>> documents) {
        List<Map<String, Object>> result = new ArrayList<>();
        for (Map<String, Object> document : documents) {
            Map<String, Object> newDocument = Maps.newHashMap();
            for (Map.Entry<String, Object> entry : document.entrySet()) {
                String snakeCaseKey = camelToSnake(entry.getKey());
                newDocument.put(snakeCaseKey, entry.getValue());
            }
            result.add(newDocument);
        }
        return result;
    }

    private static String camelToSnake(String str) {
        return str.replaceAll("([a-z])([A-Z])", "$1_$2").toLowerCase();
    }

    @Override
    public RagDataTypeEnum source() {
        return RagDataTypeEnum.NEGATIVE_COMMENTS;
    }
} 