package com.xiaohongshu.codewiz.core.entity.rag;

import java.util.List;
import java.util.Map;

import javax.validation.constraints.NotEmpty;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2025/2/27 19:33
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RagDataAddRequest extends RagDataRequest {
    @NotEmpty()
    private String source;
    private List<Map<String, Object>> documents;
}
