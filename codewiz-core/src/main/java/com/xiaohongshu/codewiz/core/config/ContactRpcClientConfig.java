package com.xiaohongshu.codewiz.core.config;

import java.time.Duration;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.xhs.ep.redcity.rcoppublic.integrationservice.contacts.ContactsQueryRpcService;
import com.xiaohongshu.infra.rpc.client.ClientBuilder;

@Configuration
public class ContactRpcClientConfig {

    @Value("${rpc.contact.service-name}")
    private String serviceName;

    @Value("${rpc.contact.timeout}")
    private int rpcTimeout;


    // @Resource(name = "thriftServerMultiRegionEdsSubscriber")
    // private ThriftServerAddressProvider serverAddressProvider;
    //
    // @Bean("userRpcService")
    // public NettyClientProxyFactory pluginRpcSyncClient() {
    //     NettyClientProxyFactory factory = new NettyClientProxyFactory();
    //     factory.setLoadBalancer(new RoundRobinLoadBalancer<>());
    //     factory.setThriftClass(ContactsQueryRpcService.class);
    //     factory.setServiceName(serviceName);
    //     factory.setServerAddressProvider(serverAddressProvider);
    //     factory.setRpcTimeout(rpcTimeout);
    //     factory.setConnectTimeout(rpcTimeout);
    //     // 强弱依赖配置,默认为true, 根据对下游的强弱依赖性进行设置
    //     factory.setStrongDependency(false);
    //     return factory;
    // }

    @Bean
    public ContactsQueryRpcService.Iface userRpcService() {
        return ClientBuilder
                .create(ContactsQueryRpcService.Iface.class, serviceName)
                .withTimeout(Duration.ofMillis(rpcTimeout))
                .buildStub();
    }
}
