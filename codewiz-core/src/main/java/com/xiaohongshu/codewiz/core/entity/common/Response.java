package com.xiaohongshu.codewiz.core.entity.common;

import org.slf4j.MDC;

import com.xiaohongshu.xray.logging.LogConstants;

import lombok.Data;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2025/2/26
 */
@Data
public class Response {

    private boolean success;

    private String code;

    private String msg;

    private String xrayTraceId;

    Response() {
        // 配合com.xiaohongshu.infra.gatewaystarter.HttpInterceptor进行请求日志链路追踪
        this.xrayTraceId = MDC.get(LogConstants.XRAY_TRACE_ID);
    }
}
