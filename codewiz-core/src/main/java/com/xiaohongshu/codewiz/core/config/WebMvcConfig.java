package com.xiaohongshu.codewiz.core.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import com.xiaohongshu.codewiz.core.interceptor.OpenBizTokenInterceptor;

@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    private final OpenBizTokenInterceptor tokenInterceptor;

    public WebMvcConfig(OpenBizTokenInterceptor tokenInterceptor) {
        this.tokenInterceptor = tokenInterceptor;
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 添加拦截器并配置拦截路径
        registry.addInterceptor(tokenInterceptor)
                .addPathPatterns("/rag/data/open/**");  // 拦截所有请求
//                .excludePathPatterns("/login", "/register", "/public/**");  // 排除不需要验证token的路径
    }
}
