milvus:
  url: http://c-a2ad458bc36db0aa-internal.milvus.aliyuncs.com:19530
  username: root
  password: XHS20250221<PERSON>lvus@#123
  dbName: default
  openDbName: openBiz

elasticsearch:
  url: https://es-0v9e5d9t.public.tencentelasticsearch.com:9200
  username: elastic
  password: 7x#L9@qZ2!
#  negative-comments-index: negative_comments

# Spring 格式日志，方便本地开发使用
logging:
  config: classpath:logback-spring.xml

redschedule:
  appid: codewiz
  domain: ee

spring:
  datasource:
    codewiz:
      jdbc-ref: mysql@codewiz@codewiz@qsjrpudd@online@v2

feign-client:
  all-in:
    url: https://redservingapi.devops.xiaohongshu.com
    api-key: QSTdbc028bad4d43be67cd818d5ca9bd90f
  llm-adapter:
    url: https://codewiz.devops.xiaohongshu.com/llmadapter
    api-key: QSTdbc028bad4d43be67cd818d5ca9bd90f
  pipeline:
    url: https://yunxiao.devops.xiaohongshu.com/v2/ci/
    projectId: 51610
    pipelineId: 26430

gitlab:
  host: https://code.devops.xiaohongshu.com
  admin-private-token: yryVZV8ZiYfUDaEM48J1

ros:
  endpoint: http://ros-cn-sh-prod.devops.xiaohongshu.com
  region: ros-cn-sh
  access-key: 3tHokX1P9HAYoBNyRoL8VUZF588sXh2l
  secret-key: aIktj1sbyhm40occ3Jt2QTBbKEEEVeFdjZAI
  bucket: codewiz

kafka:
  bootstrap-servers: eds://kafka-s-sit
  topic: codewiz_metrics_kafka_sit

rpc:
  server:
    port: 6789
    name: redcopilot-service-default-rpc
  contact:
    service-name: com.xhs.ep.redcity.rcoppublic.integrationservice.contacts.contactsqueryrpcservice
    timeout: 3000

contact:
  auth:
    appId: 52c7317b7ed17f4aa14e44df3d8310df
    secret: 7421D6D848A070CEA86FB304DC5600C7

lingma:
  domain: https://rednote-codewizllm-lingma-cn-shanghai.rdc.aliyuncs.com
  x-yunxiao-token: pt-ZZCJQY69Bon28YIgzlBK7sJv_07a17cec-0496-4d95-a1a5-86e146025e11
  free-account-notify: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=b45101c7-8bed-4a0c-a150-8b38eb51cf2a