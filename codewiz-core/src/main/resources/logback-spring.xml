<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>

    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>
                <![CDATA[%clr(%d{HH:mm:ss}) %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(--){faint} %clr([%25.25t]){faint} %clr(%-50.50logger{39}){cyan} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}]]>
            </pattern>
            <charset>utf8</charset>
        </encoder>
    </appender>

    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>log/application.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>log/application.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
            <charset>utf8</charset>
        </encoder>
    </appender>

    <!-- DependencyRunner专用appender -->
    <appender name="DEPENDENCY_RUNNER_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>log/dependency-runner.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>log/dependency-runner.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
            <charset>utf8</charset>
        </encoder>
    </appender>

    <!-- DependencyRunner专用logger配置 -->
    <logger name="com.xiaohongshu.codewiz.complete.runner.DependencyUpdateRunner" level="INFO" additivity="false">
        <appender-ref ref="DEPENDENCY_RUNNER_FILE"/>
        <appender-ref ref="CONSOLE"/>
    </logger>

    <!-- 日志上报专用logger - 只输出到控制台，不写入文件，支持所有级别 -->
    <logger name="CLIENT_LOG" level="DEBUG" additivity="false">
        <appender-ref ref="CONSOLE"/>
    </logger>

    <root level="INFO">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="FILE"/>
    </root>
</configuration>