
## 本地idea启动

启动命令的中environment variables添加以下内容：
```
XHS_ENV=sit;XHS_SERVICE=codewiz-rag-default;XHS_ZONE=qcsh4;APPID=codewiz;EDS_HTTP_HOST=**********:8085;EDS_HOST=**********:80
```
如果是jdk <= 11，则需要在vm options中添加：
```
-Denv=sit --add-opens java.base/jdk.internal.loader=ALL-UNNAMED
```



codewiz-server提供codewiz项目的服务端功能，不同功能以module划分

## module划分

### codewiz-rag

提供rag服务

### codewiz-complete

提供代码补全服务

### codewiz-core

基础服务组件，
并结合小红书内部的各种中间件，监控，日志等功能