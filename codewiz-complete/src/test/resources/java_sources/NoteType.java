/**
 * Autogenerated by Thrift Compiler (0.10.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.xiaohongshu.sns.rpc.notecore;


import java.util.Map;
import java.util.HashMap;
import org.apache.thrift.TEnum;

public enum NoteType implements org.apache.thrift.TEnum {
  NORMAL(1),
  VIDEO(2),
  MULTI(3);

  private final int value;

  private NoteType(int value) {
    this.value = value;
  }

  /**
   * Get the integer value of this enum value, as defined in the Thrift IDL.
   */
  public int getValue() {
    return value;
  }

  /**
   * Find a the enum type by its integer value, as defined in the Thrift IDL.
   * @return null if the value is not found.
   */
  public static NoteType findByValue(int value) { 
    switch (value) {
      case 1:
        return NORMAL;
      case 2:
        return VIDEO;
      case 3:
        return MULTI;
      default:
        return null;
    }
  }
}
