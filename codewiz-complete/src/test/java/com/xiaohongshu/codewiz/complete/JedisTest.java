package com.xiaohongshu.codewiz.complete;

import javax.annotation.Resource;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import lombok.extern.slf4j.Slf4j;
import redis.clients.jedis.Jedis;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2025/3/4
 */
@Slf4j
@SpringBootTest
@RunWith(SpringRunner.class)
public class JedisTest {
    @Resource(name = "codewizJedis")
    private Jedis jedis;

    @Test
    public void test() {
        log.info("set: {}", jedis.set("test", "ttttt"));
        log.info("get: {}", jedis.get("test"));
        log.info("del: {}", jedis.del("test"));
    }
}
