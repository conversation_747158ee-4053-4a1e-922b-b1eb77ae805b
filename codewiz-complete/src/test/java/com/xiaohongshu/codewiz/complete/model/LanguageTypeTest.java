package com.xiaohongshu.codewiz.complete.model;

import org.junit.jupiter.api.Test;

import com.xiaohongshu.codewiz.complete.model.lang.LanguageType;

import static org.junit.jupiter.api.Assertions.*;

/**
 * LanguageType枚举测试
 */
class LanguageTypeTest {
    
    @Test
    void testDisplayName() {
        assertEquals("Java", LanguageType.JAVA.getDisplayName());
        assertEquals("Go", LanguageType.GOLANG.getDisplayName());
        assertEquals("JavaScript", LanguageType.JAVASCRIPT.getDisplayName());
        assertEquals("Python", LanguageType.PYTHON.getDisplayName());
        assertEquals("C++", LanguageType.CPP.getDisplayName());
    }
    
    @Test
    void testFromDisplayName() {
        assertEquals(LanguageType.JAVA, LanguageType.fromDisplayName("Java"));
        assertEquals(LanguageType.GOLANG, LanguageType.fromDisplayName("Go"));
        assertEquals(LanguageType.JAVASCRIPT, LanguageType.fromDisplayName("JavaScript"));
        assertEquals(LanguageType.PYTHON, LanguageType.fromDisplayName("Python"));
        assertEquals(LanguageType.CPP, LanguageType.fromDisplayName("C++"));
        
        // 测试大小写不敏感
        assertEquals(LanguageType.JAVA, LanguageType.fromDisplayName("java"));
        assertEquals(LanguageType.GOLANG, LanguageType.fromDisplayName("go"));
    }
    
    @Test
    void testFromDisplayNameWithInvalidName() {
        assertThrows(IllegalArgumentException.class, () -> {
            LanguageType.fromDisplayName("Unknown");
        });
    }
    
    @Test
    void testFromName() {
        assertEquals(LanguageType.JAVA, LanguageType.fromName("JAVA"));
        assertEquals(LanguageType.GOLANG, LanguageType.fromName("GOLANG"));
        assertEquals(LanguageType.JAVASCRIPT, LanguageType.fromName("JAVASCRIPT"));
        
        // 测试大小写不敏感
        assertEquals(LanguageType.JAVA, LanguageType.fromName("java"));
        assertEquals(LanguageType.GOLANG, LanguageType.fromName("golang"));
        
        // 通过显示名称匹配
        assertEquals(LanguageType.JAVA, LanguageType.fromName("Java"));
        assertEquals(LanguageType.GOLANG, LanguageType.fromName("Go"));
    }
    
    @Test
    void testFromNameWithInvalidName() {
        assertThrows(IllegalArgumentException.class, () -> {
            LanguageType.fromName("Unknown");
        });
    }
} 