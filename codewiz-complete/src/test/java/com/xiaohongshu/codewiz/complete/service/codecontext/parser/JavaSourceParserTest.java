package com.xiaohongshu.codewiz.complete.service.codecontext.parser;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;

import org.junit.jupiter.api.Test;

import com.xiaohongshu.codewiz.complete.model.lang.SourceFile;
import com.xiaohongshu.codewiz.complete.runner.parser.JavaSourceParser;

/**
 * JavaParserBasedSourceParser 测试类
 */
class JavaSourceParserTest {

    /**
     * 从测试资源文件中读取Java源码
     */
    private String readTestJavaSource(String fileName) throws IOException {
        try (InputStream inputStream = getClass().getClassLoader()
                .getResourceAsStream("java_sources/" + fileName)) {
            if (inputStream == null) {
                throw new IOException("测试文件未找到: " + fileName);
            }
            return new String(inputStream.readAllBytes(), StandardCharsets.UTF_8);
        }
    }

    @Test
    void testParseSimpleClass() {
        String javaCode = "package com.example.test;\n" +
                "\n" +
                "import java.util.List;\n" +
                "import java.util.ArrayList;\n" +
                "\n" +
                "/**\n" +
                " * 这是一个示例类\n" +
                " */\n" +
                "@Component\n" +
                "@Service(\"userService\")\n" +
                "public class UserService extends BaseService implements UserInterface {\n" +
                "    \n" +
                "    /**\n" +
                "     * 用户名字段\n" +
                "     */\n" +
                "    @Autowired\n" +
                "    private String userName;\n" +
                "    \n" +
                "    /**\n" +
                "     * 用户列表字段\n" +
                "     */\n" +
                "    private final List<String> userList = new ArrayList<>();\n" +
                "    \n" +
                "    /**\n" +
                "     * 默认构造函数\n" +
                "     */\n" +
                "    public UserService() {\n" +
                "        super();\n" +
                "        this.userName = \"default\";\n" +
                "    }\n" +
                "    \n" +
                "    /**\n" +
                "     * 获取用户名\n" +
                "     * @param userId 用户ID\n" +
                "     * @return 用户名\n" +
                "     * @throws IllegalArgumentException 参数异常\n" +
                "     */\n" +
                "    @Override\n" +
                "    public String getUserName(@NotNull String userId) throws IllegalArgumentException {\n" +
                "        if (userId == null) {\n" +
                "            throw new IllegalArgumentException(\"用户ID不能为空\");\n" +
                "        }\n" +
                "        return userName + \"_\" + userId;\n" +
                "    }\n" +
                "    \n" +
                "    /**\n" +
                "     * 泛型方法示例\n" +
                "     */\n" +
                "    public <T extends Comparable<T>> T getMaxValue(T a, T b) {\n" +
                "        return a.compareTo(b) > 0 ? a : b;\n" +
                "    }\n" +
                "    \n" +
                "    /**\n" +
                "     * 内部类示例\n" +
                "     */\n" +
                "    public static class InnerClass {\n" +
                "        private String innerField;\n" +
                "        \n" +
                "        public String getInnerField() {\n" +
                "            return innerField;\n" +
                "        }\n" +
                "    }\n" +
                "}";

        SourceFile result = JavaSourceParser.parseJavaSource("UserService.java", javaCode);

        // 验证基本信息
        assertNotNull(result);
        assertEquals("UserService.java", result.getFileName());
        assertEquals("com.example.test", result.getPackageName());
        assertEquals(2, result.getImports().size());
        assertTrue(result.getImports().contains("java.util.List"));
        assertTrue(result.getImports().contains("java.util.ArrayList"));

        // 验证类信息
        assertEquals(1, result.getClasses().size());
        SourceFile.ClassInfo classInfo = result.getClasses().get(0);
        assertEquals("UserService", classInfo.getClassName());
        assertEquals("class", classInfo.getClassType());
        assertEquals("public", classInfo.getVisibility());
        assertEquals("BaseService", classInfo.getExtendsClass());
        assertTrue(classInfo.getImplementsInterfaces().contains("UserInterface"));
        
        // 验证注解
        assertEquals(2, classInfo.getAnnotations().size());
        assertEquals("Component", classInfo.getAnnotations().get(0).getAnnotationName());
        assertEquals("Service", classInfo.getAnnotations().get(1).getAnnotationName());
        
        // 验证JavaDoc
        assertTrue(classInfo.getJavadoc().contains("这是一个示例类"));
        
        // 验证字段
        assertEquals(2, classInfo.getFields().size());
        SourceFile.FieldInfo userNameField = classInfo.getFields().stream()
                .filter(field -> "userName".equals(field.getFieldName()))
                .findFirst()
                .orElse(null);
        assertNotNull(userNameField);
        assertEquals("String", userNameField.getFieldType());
        assertEquals("private", userNameField.getVisibility());
        assertEquals(1, userNameField.getAnnotations().size());
        assertEquals("Autowired", userNameField.getAnnotations().get(0).getAnnotationName());
        
        // 验证方法
        assertTrue(classInfo.getMethods().size() >= 3); // 构造函数 + getUserName + getMaxValue
        
        // 验证构造函数
        SourceFile.MethodInfo constructor = classInfo.getMethods().stream()
                .filter(SourceFile.MethodInfo::isConstructor)
                .findFirst()
                .orElse(null);
        assertNotNull(constructor);
        assertEquals("UserService", constructor.getMethodName());
        assertNull(constructor.getReturnType());
        
        // 验证普通方法
        SourceFile.MethodInfo getUserNameMethod = classInfo.getMethods().stream()
                .filter(method -> "getUserName".equals(method.getMethodName()))
                .findFirst()
                .orElse(null);
        assertNotNull(getUserNameMethod);
        assertEquals("String", getUserNameMethod.getReturnType());
        assertEquals(1, getUserNameMethod.getParameters().size());
        assertTrue(getUserNameMethod.getExceptions().contains("IllegalArgumentException"));
        assertEquals(1, getUserNameMethod.getAnnotations().size());
        assertEquals("Override", getUserNameMethod.getAnnotations().get(0).getAnnotationName());
        
        // 验证参数注解
        SourceFile.ParameterInfo param = getUserNameMethod.getParameters().get(0);
        assertEquals("userId", param.getParameterName());
        assertEquals("String", param.getParameterType());
        assertEquals(1, param.getAnnotations().size());
        assertEquals("NotNull", param.getAnnotations().get(0).getAnnotationName());
        
        // 验证泛型方法
        SourceFile.MethodInfo genericMethod = classInfo.getMethods().stream()
                .filter(method -> "getMaxValue".equals(method.getMethodName()))
                .findFirst()
                .orElse(null);
        assertNotNull(genericMethod);
        assertEquals(1, genericMethod.getGenericTypes().size());
        assertEquals("T", genericMethod.getGenericTypes().get(0));
        
        // 验证内部类
        assertEquals(1, classInfo.getInnerClasses().size());
        SourceFile.ClassInfo innerClass = classInfo.getInnerClasses().get(0);
        assertEquals("InnerClass", innerClass.getClassName());
        assertTrue(innerClass.getModifiers().contains("static"));
    }

    @Test
    void testParseInterface() {
        String javaCode = "package com.example.test;\n" +
                "\n" +
                "/**\n" +
                " * 用户接口\n" +
                " */\n" +
                "public interface UserInterface<T> extends BaseInterface {\n" +
                "    \n" +
                "    /**\n" +
                "     * 获取用户信息\n" +
                "     */\n" +
                "    T getUserInfo(String userId);\n" +
                "    \n" +
                "    /**\n" +
                "     * 默认方法\n" +
                "     */\n" +
                "    default String getDefaultInfo() {\n" +
                "        return \"default\";\n" +
                "    }\n" +
                "}";

        SourceFile result = JavaSourceParser.parseJavaSource("UserInterface.java", javaCode);

        assertNotNull(result);
        assertEquals(1, result.getClasses().size());
        
        SourceFile.ClassInfo interfaceInfo = result.getClasses().get(0);
        assertEquals("UserInterface", interfaceInfo.getClassName());
        assertEquals("interface", interfaceInfo.getClassType());
        assertEquals(1, interfaceInfo.getGenericTypes().size());
        assertEquals("T", interfaceInfo.getGenericTypes().get(0));
        assertTrue(interfaceInfo.getExtendsClass().contains("BaseInterface"));
        assertEquals(2, interfaceInfo.getMethods().size());
    }

    @Test
    void testParseEnum() {
        String javaCode = "package com.example.test;\n" +
                "\n" +
                "/**\n" +
                " * 状态枚举\n" +
                " */\n" +
                "public enum Status {\n" +
                "    \n" +
                "    /**\n" +
                "     * 活跃状态\n" +
                "     */\n" +
                "    ACTIVE(\"active\", 1),\n" +
                "    \n" +
                "    /**\n" +
                "     * 非活跃状态\n" +
                "     */\n" +
                "    INACTIVE(\"inactive\", 0);\n" +
                "    \n" +
                "    private final String name;\n" +
                "    private final int code;\n" +
                "    \n" +
                "    Status(String name, int code) {\n" +
                "        this.name = name;\n" +
                "        this.code = code;\n" +
                "    }\n" +
                "    \n" +
                "    public String getName() {\n" +
                "        return name;\n" +
                "    }\n" +
                "    \n" +
                "    public int getCode() {\n" +
                "        return code;\n" +
                "    }\n" +
                "}";

        SourceFile result = JavaSourceParser.parseJavaSource("Status.java", javaCode);

        assertNotNull(result);
        assertEquals(1, result.getClasses().size());
        
        SourceFile.ClassInfo enumInfo = result.getClasses().get(0);
        assertEquals("Status", enumInfo.getClassName());
        assertEquals("enum", enumInfo.getClassType());
        assertEquals(2, enumInfo.getFields().size());
        assertEquals(3, enumInfo.getMethods().size()); // 构造函数 + 2个getter方法
    }
    
    @Test
    void testParseEnumWithConstants() throws IOException {
        String javaCode = readTestJavaSource("NoteType.java");

        SourceFile result = JavaSourceParser.parseJavaSource("NoteType.java", javaCode);

        assertNotNull(result);
        assertEquals(1, result.getClasses().size());
        
        SourceFile.ClassInfo enumInfo = result.getClasses().get(0);
        assertEquals("NoteType", enumInfo.getClassName());
        assertEquals("enum", enumInfo.getClassType());
        assertEquals("public", enumInfo.getVisibility());
        
        // 验证实现的接口
        assertNotNull(enumInfo.getImplementsInterfaces());
        assertTrue(enumInfo.getImplementsInterfaces().contains("org.apache.thrift.TEnum"));
        
        // 验证enum常量
        assertNotNull(enumInfo.getEnumConstants());
        assertEquals(3, enumInfo.getEnumConstants().size());
        
        // 验证具体的enum常量
        var constants = enumInfo.getEnumConstants();
        
        // 验证NORMAL常量
        var normalConstant = constants.stream()
                .filter(c -> "NORMAL".equals(c.getConstantName()))
                .findFirst()
                .orElse(null);
        assertNotNull(normalConstant);
        assertEquals(1, normalConstant.getArguments().size());
        assertEquals("1", normalConstant.getArguments().get(0));
        assertTrue(normalConstant.getJavadoc().contains("正常笔记"));
        
        // 验证VIDEO常量
        var videoConstant = constants.stream()
                .filter(c -> "VIDEO".equals(c.getConstantName()))
                .findFirst()
                .orElse(null);
        assertNotNull(videoConstant);
        assertEquals(1, videoConstant.getArguments().size());
        assertEquals("2", videoConstant.getArguments().get(0));
        assertTrue(videoConstant.getJavadoc().contains("视频笔记"));
        
        // 验证MULTI常量
        var multiConstant = constants.stream()
                .filter(c -> "MULTI".equals(c.getConstantName()))
                .findFirst()
                .orElse(null);
        assertNotNull(multiConstant);
        assertEquals(1, multiConstant.getArguments().size());
        assertEquals("3", multiConstant.getArguments().get(0));
        assertTrue(multiConstant.getJavadoc().contains("多媒体笔记"));
        
        // 验证字段和方法
        assertNotNull(enumInfo.getFields());
        assertTrue(enumInfo.getFields().size() > 0);
        
        assertNotNull(enumInfo.getMethods());
        assertTrue(enumInfo.getMethods().size() > 0);
        
        // 验证方法名是否正确解析
        boolean hasGetValue = enumInfo.getMethods().stream()
                .anyMatch(method -> "getValue".equals(method.getMethodName()));
        assertTrue(hasGetValue);
        
        boolean hasFindByValue = enumInfo.getMethods().stream()
                .anyMatch(method -> "findByValue".equals(method.getMethodName()));
        assertTrue(hasFindByValue);
        
        System.out.println("=== Enum常量解析结果 ===");
        if (enumInfo.getEnumConstants() != null) {
            enumInfo.getEnumConstants().forEach(constant -> {
                System.out.println("常量名: " + constant.getConstantName());
                System.out.println("参数: " + constant.getArguments());
                System.out.println("JavaDoc: " + constant.getJavadoc());
                System.out.println("行号: " + constant.getLineNumber());
                System.out.println("---");
            });
        }
        
        System.out.println("\n=== 字段信息 ===");
        enumInfo.getFields().forEach(field -> {
            System.out.println("字段名: " + field.getFieldName());
            System.out.println("字段类型: " + field.getFieldType());
            System.out.println("可见性: " + field.getVisibility());
            System.out.println("---");
        });
        
        System.out.println("\n=== 方法信息 ===");
        enumInfo.getMethods().forEach(method -> {
            System.out.println("方法名: " + method.getMethodName());
            System.out.println("返回类型: " + method.getReturnType());
            System.out.println("可见性: " + method.getVisibility());
            System.out.println("是否为构造函数: " + method.isConstructor());
            System.out.println("---");
        });
    }
} 