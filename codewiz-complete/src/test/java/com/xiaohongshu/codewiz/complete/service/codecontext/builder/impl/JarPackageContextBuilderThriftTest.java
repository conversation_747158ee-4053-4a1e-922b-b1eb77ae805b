package com.xiaohongshu.codewiz.complete.service.codecontext.builder.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import com.xiaohongshu.codewiz.complete.model.lang.SourceFile;
import com.xiaohongshu.codewiz.complete.runner.builder.impl.JarPackageContextBuilder;

import lombok.extern.slf4j.Slf4j;

/**
 * JarPackageContextBuilder Thrift DTO简化功能测试
 */
@Slf4j
class JarPackageContextBuilderThriftTest {

    @Mock
    private ExecutorService astParseExecutor;

    @InjectMocks  
    private JarPackageContextBuilder jarPackageContextBuilder;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        // 设置一个真实的ExecutorService用于测试
        jarPackageContextBuilder = new JarPackageContextBuilder();
        try {
            java.lang.reflect.Field field = JarPackageContextBuilder.class.getDeclaredField("astParseExecutor");
            field.setAccessible(true);
            field.set(jarPackageContextBuilder, Executors.newFixedThreadPool(2));
        } catch (Exception e) {
            log.warn("无法设置executor字段", e);
        }
    }

    @Test
    void testIsThriftDto_ShouldReturnTrue_WhenImplementsTBase() throws Exception {
        // 准备测试数据 - 实现TBase接口的类
        SourceFile.ClassInfo thriftDto = createThriftDtoClassInfo();
        
        // 使用反射调用私有方法
        Method isThriftDtoMethod = JarPackageContextBuilder.class.getDeclaredMethod("isThriftDto", SourceFile.ClassInfo.class);
        isThriftDtoMethod.setAccessible(true);
        
        // 执行测试
        boolean result = (boolean) isThriftDtoMethod.invoke(jarPackageContextBuilder, thriftDto);
        
        // 验证结果
        assertTrue(result, "应该识别为Thrift DTO类");
    }

    @Test
    void testIsThriftDto_ShouldReturnFalse_WhenNotImplementsTBase() throws Exception {
        // 准备测试数据 - 普通类（不实现TBase接口）
        SourceFile.ClassInfo normalClass = SourceFile.ClassInfo.builder()
                .className("NormalClass")
                .classType("class")
                .visibility("public")
                .implementsInterfaces(Arrays.asList("java.io.Serializable"))
                .build();
        
        // 使用反射调用私有方法
        Method isThriftDtoMethod = JarPackageContextBuilder.class.getDeclaredMethod("isThriftDto", SourceFile.ClassInfo.class);
        isThriftDtoMethod.setAccessible(true);
        
        // 执行测试
        boolean result = (boolean) isThriftDtoMethod.invoke(jarPackageContextBuilder, normalClass);
        
        // 验证结果
        assertFalse(result, "不应该识别为Thrift DTO类");
    }

    @Test
    void testGenerateThriftDtoSummary_ShouldGenerateLombokStyle() throws Exception {
        // 准备测试数据
        SourceFile.ClassInfo thriftDto = createThriftDtoClassInfo();
        
        // 使用反射调用私有方法
        Method generateThriftDtoSummaryMethod = JarPackageContextBuilder.class
                .getDeclaredMethod("generateThriftDtoSummary", SourceFile.ClassInfo.class);
        generateThriftDtoSummaryMethod.setAccessible(true);
        
        // 执行测试
        String result = (String) generateThriftDtoSummaryMethod.invoke(jarPackageContextBuilder, thriftDto);
        
        // 验证结果
        assertNotNull(result, "生成的摘要不应该为空");
        
        // 验证包含Lombok注解
        assertTrue(result.contains("@Data"), "应该包含@Data注解");
        assertTrue(result.contains("@NoArgsConstructor"), "应该包含@NoArgsConstructor注解");
        assertTrue(result.contains("@Accessors(chain = true)"), "应该包含@Accessors注解");
        
        // 验证包含类声明
        assertTrue(result.contains("public class ExportDto"), "应该包含类声明");
        
        // 验证不包含接口实现（为了保持简洁）
        assertFalse(result.contains("implements"), "不应该包含接口实现");
        assertFalse(result.contains("TBase"), "不应该包含TBase接口");
        
        // 验证包含字段
        assertTrue(result.contains("public String taskId;"), "应该包含taskId字段");
        
        // 验证不包含元数据字段和private字段
        assertFalse(result.contains("metaDataMap"), "不应该包含metaDataMap元数据字段");
        assertFalse(result.contains("privateField"), "不应该包含private字段");
        
        // 验证不包含任何方法（所有方法都被忽略了）
        assertFalse(result.contains("getTaskId"), "不应该包含任何方法");
        assertFalse(result.contains("setTaskId"), "不应该包含任何方法");
        assertFalse(result.contains("deepCopy"), "不应该包含任何方法");
        assertFalse(result.contains("customBusinessMethod"), "不应该包含任何方法（包括自定义方法）");
        
        // 验证不包含内部类（如_Fields枚举）
        assertFalse(result.contains("_Fields"), "不应该包含_Fields枚举");
        assertFalse(result.contains("findByThriftId"), "不应该包含Thrift内部方法");
        
        log.info("生成的Thrift DTO摘要:\n{}", result);
    }

    @Test
    void testGenerateClassSummary_ShouldUseThriftDtoSummary_WhenIsThriftDto() throws Exception {
        // 准备测试数据
        SourceFile.ClassInfo thriftDto = createThriftDtoClassInfo();
        
        // 创建一个模拟的SourceFile
        SourceFile sourceFile = SourceFile.builder()
                .content("// Autogenerated by Thrift Compiler\npublic class ExportDto {\n}")
                .build();
        
        // 使用反射调用私有方法
        Method generateClassSummaryMethod = JarPackageContextBuilder.class
                .getDeclaredMethod("generateClassSummary", SourceFile.class, SourceFile.ClassInfo.class);
        generateClassSummaryMethod.setAccessible(true);
        
        // 执行测试
        String result = (String) generateClassSummaryMethod.invoke(jarPackageContextBuilder, sourceFile, thriftDto);
        
        // 验证结果 - 应该使用Lombok风格的摘要
        assertNotNull(result, "生成的摘要不应该为空");
        assertTrue(result.contains("// thrift::dto"), "应该包含thrift::dto标识");
        assertTrue(result.contains("@Data"), "应该包含Lombok注解，说明使用了Thrift DTO摘要");
        
        log.info("主方法生成的摘要:\n{}", result);
    }

    @Test
    void testGenerateThriftServiceSummary_ShouldGenerateServiceStyle() throws Exception {
        // 准备测试数据 - Thrift Service类
        SourceFile.ClassInfo thriftService = createThriftServiceClassInfo();
        
        // 创建包含Thrift注释的SourceFile
        SourceFile sourceFile = SourceFile.builder()
                .content("// Autogenerated by Thrift Compiler\npublic class UserService {\n}")
                .build();
        
        // 使用反射调用私有方法
        Method generateClassSummaryMethod = JarPackageContextBuilder.class
                .getDeclaredMethod("generateClassSummary", SourceFile.class, SourceFile.ClassInfo.class);
        generateClassSummaryMethod.setAccessible(true);
        
        // 执行测试
        String result = (String) generateClassSummaryMethod.invoke(jarPackageContextBuilder, sourceFile, thriftService);
        
        // 验证结果
        assertNotNull(result, "生成的摘要不应该为空");
        
        // 验证包含Service标识
        assertTrue(result.contains("// thrift::service"), "应该包含thrift::service标识");
        
        // 验证包含类声明
        assertTrue(result.contains("public class UserService"), "应该包含类声明");
        
        // 验证包含Iface接口
        assertTrue(result.contains("public interface Iface"), "应该包含Iface接口");
        
        // 验证包含接口方法
        assertTrue(result.contains("getUserById("), "应该包含接口方法");
        
        log.info("生成的Thrift Service摘要:\n{}", result);
    }



    /**
     * 创建测试用的Thrift DTO ClassInfo
     */
    private SourceFile.ClassInfo createThriftDtoClassInfo() {
        // 创建字段信息
        List<SourceFile.FieldInfo> fields = Arrays.asList(
                SourceFile.FieldInfo.builder()
                        .fieldName("taskId")
                        .fieldType("java.lang.String")
                        .visibility("public")
                        .build(),
                // 元数据字段（应该被过滤掉）
                SourceFile.FieldInfo.builder()
                        .fieldName("metaDataMap")
                        .fieldType("java.util.Map<_Fields,org.apache.thrift.meta_data.FieldMetaData>")
                        .visibility("public")
                        .modifiers(Arrays.asList("static", "final"))
                        .build(),
                // private字段（应该被过滤掉）
                SourceFile.FieldInfo.builder()
                        .fieldName("privateField")
                        .fieldType("java.lang.String")
                        .visibility("private")
                        .build()
        );

        // 创建方法信息（包含样板方法和自定义方法）
        List<SourceFile.MethodInfo> methods = Arrays.asList(
                // getter方法（样板方法）
                SourceFile.MethodInfo.builder()
                        .methodName("getTaskId")
                        .returnType("java.lang.String")
                        .visibility("public")
                        .build(),
                // setter方法（样板方法）
                SourceFile.MethodInfo.builder()
                        .methodName("setTaskId")
                        .returnType("ExportDto")
                        .visibility("public")
                        .parameters(Arrays.asList(
                                SourceFile.ParameterInfo.builder()
                                        .parameterName("taskId")
                                        .parameterType("java.lang.String")
                                        .build()
                        ))
                        .build(),
                // deepCopy方法（样板方法）
                SourceFile.MethodInfo.builder()
                        .methodName("deepCopy")
                        .returnType("ExportDto")
                        .visibility("public")
                        .build(),
                // equals方法（样板方法）
                SourceFile.MethodInfo.builder()
                        .methodName("equals")
                        .returnType("boolean")
                        .visibility("public")
                        .parameters(Arrays.asList(
                                SourceFile.ParameterInfo.builder()
                                        .parameterName("that")
                                        .parameterType("java.lang.Object")
                                        .build()
                        ))
                        .build(),
                // 自定义业务方法（非样板方法）
                SourceFile.MethodInfo.builder()
                        .methodName("customBusinessMethod")
                        .returnType("void")
                        .visibility("public")
                        .build()
        );

        // 创建内部类（_Fields枚举）
        List<SourceFile.ClassInfo> innerClasses = Arrays.asList(
                SourceFile.ClassInfo.builder()
                        .className("_Fields")
                        .classType("enum")
                        .visibility("public")
                        .modifiers(Arrays.asList("static"))
                        .build()
        );

        return SourceFile.ClassInfo.builder()
                .className("ExportDto")
                .classType("class")
                .visibility("public")
                .implementsInterfaces(Arrays.asList(
                        "org.apache.thrift.TBase<ExportDto,ExportDto._Fields>",
                        "java.io.Serializable",
                        "Cloneable",
                        "Comparable<ExportDto>"
                ))
                .fields(fields)
                .methods(methods)
                .innerClasses(innerClasses)
                .build();
    }

    /**
     * 创建测试用的Thrift Service ClassInfo
     */
    private SourceFile.ClassInfo createThriftServiceClassInfo() {
        // 创建Iface接口中的方法
        List<SourceFile.MethodInfo> ifaceMethods = Arrays.asList(
                SourceFile.MethodInfo.builder()
                        .methodName("getUserById")
                        .returnType("UserDto")
                        .visibility("public")
                        .parameters(Arrays.asList(
                                SourceFile.ParameterInfo.builder()
                                        .parameterName("userId")
                                        .parameterType("java.lang.String")
                                        .build()
                        ))
                        .build(),
                SourceFile.MethodInfo.builder()
                        .methodName("createUser")
                        .returnType("void")
                        .visibility("public")
                        .parameters(Arrays.asList(
                                SourceFile.ParameterInfo.builder()
                                        .parameterName("user")
                                        .parameterType("UserDto")
                                        .build()
                        ))
                        .build()
        );

        // 创建Iface内部接口
        SourceFile.ClassInfo ifaceInterface = SourceFile.ClassInfo.builder()
                .className("Iface")
                .classType("interface")
                .visibility("public")
                .modifiers(Arrays.asList("static"))
                .methods(ifaceMethods)
                .build();

        // 创建Service类的其他方法（样板方法）
        List<SourceFile.MethodInfo> serviceMethods = Arrays.asList(
                SourceFile.MethodInfo.builder()
                        .methodName("getProcessor")
                        .returnType("TProcessor")
                        .visibility("public")
                        .build()
        );

        return SourceFile.ClassInfo.builder()
                .className("UserService")
                .classType("class")
                .visibility("public")
                .methods(serviceMethods)
                .innerClasses(Arrays.asList(ifaceInterface))
                .build();
    }
} 