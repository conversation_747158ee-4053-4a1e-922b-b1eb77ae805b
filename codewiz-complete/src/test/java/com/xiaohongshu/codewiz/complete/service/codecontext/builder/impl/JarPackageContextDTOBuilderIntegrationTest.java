package com.xiaohongshu.codewiz.complete.service.codecontext.builder.impl;

import java.util.Optional;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.web.reactive.function.client.WebClient;

import com.xiaohongshu.codewiz.complete.model.task.PackageContextBuildResult;
import com.xiaohongshu.codewiz.complete.model.dependency.Dependency;
import com.xiaohongshu.codewiz.complete.model.dependency.Artifact;
import com.xiaohongshu.codewiz.complete.runner.builder.impl.JarPackageContextBuilder;
import com.xiaohongshu.codewiz.complete.runner.fetcher.impl.MavenFetcher;
import com.xiaohongshu.codewiz.complete.runner.parser.JavaSourceParser;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import lombok.extern.slf4j.Slf4j;

/**
 * JarKnowledgeBuilder集成测试
 * 真实调用fetcher获取JAR包，然后测试parseJarFile方法
 */
@Slf4j
class JarPackageContextDTOBuilderIntegrationTest {

    private JarPackageContextBuilder jarKnowledgeBuilder;
    private MavenFetcher mavenFetcher;
    private JavaSourceParser javaSourceParser;

    @BeforeEach
    void setUp() {
        // 创建真实的WebClient
        WebClient webClient = WebClient.builder()
                .build();

        // 创建真实的fetcher
        mavenFetcher = new MavenFetcher(webClient);

        // 创建真实的parser
        javaSourceParser = new JavaSourceParser();

        // 创建被测试的对象
        jarKnowledgeBuilder = new JarPackageContextBuilder();

        // 通过反射设置私有字段（如果需要）
        try {
            java.lang.reflect.Field parserField = JarPackageContextBuilder.class.getDeclaredField("javaSourceParser");
            parserField.setAccessible(true);
            parserField.set(jarKnowledgeBuilder, javaSourceParser);
        } catch (Exception e) {
            log.warn("无法设置parser字段，可能需要调整测试", e);
        }
    }

    @Test
    void testParseJarFile_WithRealDependency_CommonDependency() throws Exception {
        log.info("开始测试parseJarFile");

        // 使用一个常见的小型依赖，比如工具类库
        Dependency dependency = Dependency.builder()
                .namespace("com.xiaohongshu.xray")
                .name("xray-logging")
                .version("0.0.8")
                .build();

        log.info("正在获取依赖: {}", dependency.getDependencyKey());
        Optional<Artifact> artifactOpt = mavenFetcher.fetchArtifact(dependency);
        Artifact artifact = artifactOpt.get();
        assertNotNull(artifact, "获取依赖失败");

        PackageContextBuildResult result = jarKnowledgeBuilder.buildPackageContextWithDetails(artifact);
        assertNotNull(result, "构建知识结果不能为空");
        assertNotNull(result.getSuccessItems(), "构建知识失败");
        assertEquals(3, result.getSuccessItems().size(), "应该构建3个知识");
    }

}