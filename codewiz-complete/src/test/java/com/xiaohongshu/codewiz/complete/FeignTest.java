package com.xiaohongshu.codewiz.complete;

import java.util.Collection;
import java.util.Collections;
import java.util.Optional;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.junit4.SpringRunner;

import com.xiaohongshu.codewiz.core.entity.allin.ChatCompletionRequestDTO;
import com.xiaohongshu.codewiz.core.entity.allin.ChatCompletionResponseDTO;
import com.xiaohongshu.codewiz.core.entity.common.SingleResponse;
import com.xiaohongshu.codewiz.core.feign.AllInFeignClient;
import com.xiaohongshu.codewiz.core.feign.LLMAdapterFeignClient;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2025/3/3
 */
@Slf4j
@SpringBootTest
@RunWith(SpringRunner.class)
public class FeignTest {
    @Resource
    private AllInFeignClient allInFeignClient;
    @Resource
    private LLMAdapterFeignClient llmAdapterFeignClient;

    @Test
    public void testAllInChat() {
        ChatCompletionRequestDTO.ChatMessage message =
                ChatCompletionRequestDTO.ChatMessage.builder().role("user").content("你是谁？").build();
        ChatCompletionRequestDTO chatRequest = ChatCompletionRequestDTO.builder()
                .model("deepseek-v3")
                .messages(Collections.singletonList(message))
                .stream(true)
                .temperature(0.0)
                .maxTokens(100)
                .build();
        try {
            ResponseEntity<ChatCompletionResponseDTO> response = allInFeignClient.chatCompletions(chatRequest);
            String content =
                    Optional.ofNullable(response.getBody()).map(ChatCompletionResponseDTO::getChoices).stream().flatMap(Collection::stream)
                            .findFirst().map(
                                    ChatCompletionResponseDTO.Choice::getMessage).map(ChatCompletionResponseDTO.ChatMessage::getContent)
                            .orElse(StringUtils.EMPTY);
            log.info(content);
        } catch (Exception e) {
            log.error("Feign Test Error:", e);
        }
    }

    @Test
    public void testLLMAdapter() {
        ChatCompletionRequestDTO.ChatMessage message =
                ChatCompletionRequestDTO.ChatMessage.builder().role("user").content("你是谁？").build();
        ChatCompletionRequestDTO chatRequest = ChatCompletionRequestDTO.builder()
                .model("deepseek-v3")
                .messages(Collections.singletonList(message))
                .stream(false)
                .temperature(0.0)
                .maxTokens(100)
                .build();
        try {
            ResponseEntity<SingleResponse<ChatCompletionResponseDTO>> response =
                    llmAdapterFeignClient.chatCompletions(chatRequest, true, "codewiz-complete-default");
            String content =
                    Optional.ofNullable(response.getBody()).map(SingleResponse::getData).map(ChatCompletionResponseDTO::getChoices).stream()
                            .flatMap(Collection::stream).findFirst().map(ChatCompletionResponseDTO.Choice::getMessage)
                            .map(ChatCompletionResponseDTO.ChatMessage::getContent).orElse(StringUtils.EMPTY);
            log.info(content);
        } catch (Exception e) {
            log.error("Feign Test Error:", e);
        }
    }
}
