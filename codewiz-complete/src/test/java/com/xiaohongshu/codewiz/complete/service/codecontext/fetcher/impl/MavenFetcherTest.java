package com.xiaohongshu.codewiz.complete.service.codecontext.fetcher.impl;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.web.reactive.function.client.WebClient;

import com.xiaohongshu.codewiz.complete.model.dependency.Dependency;
import com.xiaohongshu.codewiz.complete.model.dependency.Artifact;
import com.xiaohongshu.codewiz.complete.model.dependency.DependencyType;
import com.xiaohongshu.codewiz.complete.model.lang.LanguageType;
import com.xiaohongshu.codewiz.complete.runner.fetcher.impl.MavenFetcher;

import static org.junit.Assert.assertArrayEquals;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;
import reactor.core.publisher.Mono;

/**
 * MavenFetcher单元测试
 * 纯单元测试，不依赖Spring容器
 */
public class MavenFetcherTest {

    @Mock
    private WebClient webClient;

    @Mock
    private WebClient.RequestHeadersUriSpec requestHeadersUriSpec;

    @Mock
    private WebClient.RequestHeadersSpec requestHeadersSpec;

    @Mock
    private WebClient.ResponseSpec responseSpec;

    private MavenFetcher mavenFetcher;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        mavenFetcher = new MavenFetcher(webClient);
    }

    @Test
    public void testSupports() {
        // 测试支持的语言类型
        assertTrue("应该支持Java语言", mavenFetcher.supports(DependencyType.MAVEN));

        // 如果有其他语言类型，测试不支持的情况
        // assertFalse("不应该支持其他语言", mavenFetcher.supports(LanguageType.PYTHON));
    }

    @Test
    public void testFetchArtifact_Success() {
        // 准备测试数据
        Dependency dependency = Dependency.builder()
                .namespace("com.xiaohongshu")
                .name("test-artifact")
                .version("1.0.0")
                .build();

        byte[] jarContent = "mock jar content".getBytes();
        String pomContent = "<?xml version=\"1.0\" encoding=\"UTF-8\"?><project></project>";

        // Mock WebClient调用
        mockWebClientSuccess(jarContent, pomContent);

        // 执行测试
        Optional<Artifact> result = mavenFetcher.fetchArtifact(dependency);

        // 验证结果
        assertTrue("应该成功获取依赖", result.isPresent());
        Artifact artifact = result.get();
        assertEquals("命名空间应该正确", "com.xiaohongshu", artifact.getDependency().getNamespace());
        assertEquals("名称应该正确", "test-artifact", artifact.getDependency().getName());
        assertEquals("版本应该正确", "1.0.0", artifact.getDependency().getVersion());
        assertEquals("语言类型应该正确", LanguageType.JAVA, artifact.getDependency().getLanguage());
        assertArrayEquals("源码内容应该正确", jarContent, artifact.getSourceContent());
        //assertEquals("元数据内容应该正确", pomContent, artifact.getMetadataContent());
        assertEquals("大小应该正确", jarContent.length, artifact.getSize());
    }

    @Test
    public void testFetchArtifact_UnsupportedLanguage() {
        Dependency dependency = Dependency.builder()
                .namespace("com.xiaohongshu")
                .name("test-artifact")
                .version("1.0.0")
                .build();

        // 创建一个不支持的语言类型进行测试
        // 这里假设有其他语言类型，如果没有则可以跳过此测试
        // Optional<DependencyArtifact> result = mavenFetcher.fetchArtifact(dependency, LanguageType.PYTHON);
        // assertFalse("不支持的语言应该返回空", result.isPresent());
    }

    @Test
    public void testFetchArtifact_EmptyVersion() {
        Dependency dependency = Dependency.builder()
                .namespace("com.xiaohongshu")
                .name("test-artifact")
                .version("")
                .build();

        Optional<Artifact> result = mavenFetcher.fetchArtifact(dependency);

        assertFalse("版本为空应该返回空", result.isPresent());
    }

    @Test
    public void testFetchArtifact_NullVersion() {
        Dependency dependency = Dependency.builder()
                .namespace("com.xiaohongshu")
                .name("test-artifact")
                .version(null)
                .build();

        Optional<Artifact> result = mavenFetcher.fetchArtifact(dependency);

        assertFalse("版本为null应该返回空", result.isPresent());
    }

    @Test
    public void testFetchArtifact_DownloadFailure() {
        Dependency dependency = Dependency.builder()
                .namespace("com.xiaohongshu")
                .name("test-artifact")
                .version("1.0.0")
                .build();

        // Mock WebClient调用失败
        mockWebClientFailure();

        Optional<Artifact> result = mavenFetcher.fetchArtifact(dependency);

        assertFalse("下载失败应该返回空", result.isPresent());
    }

    @Test
    public void testFetchArtifact_SnapshotVersion() {
        Dependency dependency = Dependency.builder()
                .namespace("com.xiaohongshu")
                .name("test-artifact")
                .version("1.0.0-20240101.123456-1")
                .snapshotVersion("1.0.0-SNAPSHOT")
                .build();

        byte[] jarContent = "mock jar content".getBytes();
        String pomContent = "<?xml version=\"1.0\" encoding=\"UTF-8\"?><project></project>";

        mockWebClientSuccess(jarContent, pomContent);

        Optional<Artifact> result = mavenFetcher.fetchArtifact(dependency);

        assertTrue("应该成功获取SNAPSHOT依赖", result.isPresent());
        Artifact artifact = result.get();
        assertEquals("快照版本应该正确", "1.0.0-SNAPSHOT", artifact.getDependency().getSnapshotVersion());
        assertEquals("实际版本应该正确", "1.0.0-20240101.123456-1", artifact.getDependency().getVersion());
    }

    @Test
    public void testBatchFetcherArtifact_Success() {
        List<Dependency> dependencies = Arrays.asList(
                Dependency.builder()
                        .namespace("com.xiaohongshu")
                        .name("test-artifact-1")
                        .version("1.0.0")
                        .build(),
                Dependency.builder()
                        .namespace("com.xiaohongshu")
                        .name("test-artifact-2")
                        .version("2.0.0")
                        .build()
        );

        byte[] jarContent = "mock jar content".getBytes();
        String pomContent = "<?xml version=\"1.0\" encoding=\"UTF-8\"?><project></project>";

        mockWebClientSuccess(jarContent, pomContent);

        List<Artifact> results = mavenFetcher.batchFetcherArtifact(dependencies);

        assertEquals("应该返回2个依赖", 2, results.size());
    }

    @Test
    public void testResolveVersions_Success() {
        List<Dependency> dependencies = Arrays.asList(
                Dependency.builder()
                        .namespace("com.xiaohongshu")
                        .name("test-artifact")
                        .version("LATEST")
                        .build(),
                Dependency.builder()
                        .namespace("com.xiaohongshu")
                        .name("test-artifact-2")
                        .version("1.0.0-SNAPSHOT")
                        .build()
        );

        // Mock版本解析的XML响应
        String latestVersionXml = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>" +
                "<metadata>" +
                "<versioning>" +
                "<latest>1.2.0</latest>" +
                "<release>1.2.0</release>" +
                "</versioning>" +
                "</metadata>";

        String snapshotVersionXml = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>" +
                "<metadata>" +
                "<versioning>" +
                "<snapshotVersions>" +
                "<snapshotVersion>" +
                "<extension>jar</extension>" +
                "<value>1.0.0-20240101.123456-1</value>" +
                "</snapshotVersion>" +
                "</snapshotVersions>" +
                "</versioning>" +
                "</metadata>";

        when(webClient.get()).thenReturn(requestHeadersUriSpec);
        when(requestHeadersUriSpec.uri(anyString())).thenReturn(requestHeadersSpec);
        when(requestHeadersSpec.retrieve()).thenReturn(responseSpec);
        when(responseSpec.bodyToMono(String.class))
                .thenReturn(Mono.just(latestVersionXml))
                .thenReturn(Mono.just(snapshotVersionXml));

        List<Dependency> results = mavenFetcher.resolveVersions(dependencies);

        assertEquals("应该返回2个依赖", 2, results.size());
        assertEquals("第一个依赖版本应该被解析", "1.2.0", results.get(0).getVersion());
        assertEquals("第二个依赖版本应该被解析", "1.0.0-20240101.123456-1", results.get(1).getVersion());
        assertEquals("SNAPSHOT版本应该被保存", "1.0.0-SNAPSHOT", results.get(1).getSnapshotVersion());
    }

    @Test
    public void testResolveVersions_EmptyList() {
        List<Dependency> results = mavenFetcher.resolveVersions(null);
        assertTrue("空列表应该返回空List", results.isEmpty());

        results = mavenFetcher.resolveVersions(List.of());
        assertTrue("空列表应该返回空List", results.isEmpty());
    }

    @Test
    public void testResolveVersions_NormalVersion() {
        List<Dependency> dependencies = Collections.singletonList(
                Dependency.builder()
                        .namespace("com.xiaohongshu")
                        .name("test-artifact")
                        .version("1.0.0")
                        .build()
        );

        List<Dependency> results = mavenFetcher.resolveVersions(dependencies);

        assertEquals("应该返回1个依赖", 1, results.size());
        assertEquals("版本应该保持不变", "1.0.0", results.get(0).getVersion());
    }

    @Test(expected = RuntimeException.class)
    public void testResolveVersions_FailureThrowsException() {
        List<Dependency> dependencies = Collections.singletonList(
                Dependency.builder()
                        .namespace("com.xiaohongshu")
                        .name("test-artifact")
                        .version("LATEST")
                        .build()
        );

        // Mock失败的响应
        when(webClient.get()).thenReturn(requestHeadersUriSpec);
        when(requestHeadersUriSpec.uri(anyString())).thenReturn(requestHeadersSpec);
        when(requestHeadersSpec.retrieve()).thenReturn(responseSpec);
        when(responseSpec.bodyToMono(String.class)).thenReturn(Mono.error(new RuntimeException("Network error")));

        mavenFetcher.resolveVersions(dependencies);
    }

    /**
     * Mock WebClient成功响应
     */
    private void mockWebClientSuccess(byte[] jarContent, String pomContent) {
        when(webClient.get()).thenReturn(requestHeadersUriSpec);
        when(requestHeadersUriSpec.uri(anyString())).thenReturn(requestHeadersSpec);
        when(requestHeadersSpec.retrieve()).thenReturn(responseSpec);

        // Mock JAR文件下载
        when(responseSpec.bodyToMono(byte[].class)).thenReturn(Mono.just(jarContent));

        // Mock POM文件下载
        when(responseSpec.bodyToMono(String.class)).thenReturn(Mono.just(pomContent));
    }

    /**
     * Mock WebClient失败响应
     */
    private void mockWebClientFailure() {
        when(webClient.get()).thenReturn(requestHeadersUriSpec);
        when(requestHeadersUriSpec.uri(anyString())).thenReturn(requestHeadersSpec);
        when(requestHeadersSpec.retrieve()).thenReturn(responseSpec);
        when(responseSpec.bodyToMono(byte[].class)).thenReturn(Mono.error(new RuntimeException("Network error")));
        when(responseSpec.bodyToMono(String.class)).thenReturn(Mono.error(new RuntimeException("Network error")));
    }
} 