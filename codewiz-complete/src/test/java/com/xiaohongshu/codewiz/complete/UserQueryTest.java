package com.xiaohongshu.codewiz.complete;

import java.util.Optional;

import javax.annotation.Resource;

import org.junit.Test;

import com.xiaohongshu.codewiz.core.service.user.RedUserBO;
import com.xiaohongshu.codewiz.core.service.user.RedUserService;
import com.xiaohongshu.codewiz.core.utils.JsonMapperUtils;

/**
 * <AUTHOR>
 * @date 2025/5/12 19:58
 */
public class UserQueryTest extends SpringBaseTest {

    @Resource
    private RedUserService redUserService;

    @Test
    public void test() {
        Optional<RedUserBO> redUserBO = redUserService.searchRedUser("<EMAIL>");
        System.out.println(JsonMapperUtils.toJson(redUserBO.orElseGet(RedUserBO::new)));
    }
}
