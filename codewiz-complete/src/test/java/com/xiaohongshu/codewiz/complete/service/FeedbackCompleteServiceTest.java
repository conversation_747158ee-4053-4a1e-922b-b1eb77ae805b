package com.xiaohongshu.codewiz.complete.service;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;

import org.junit.Test;

import com.xiaohongshu.codewiz.complete.SpringBaseTest;
import com.xiaohongshu.codewiz.complete.s3.CompleteS3Client;
import com.xiaohongshu.codewiz.core.entity.common.SingleResponse;
import com.xiaohongshu.codewiz.core.entity.feedback.dto.FeedbackInfo;
import com.xiaohongshu.codewiz.core.entity.feedback.dto.FeedbackReportRequest;
import com.xiaohongshu.codewiz.core.entity.feedback.dto.FeedbackQueryResponse;
import com.xiaohongshu.codewiz.core.service.feedback.FeedbackService;
import com.xiaohongshu.codewiz.core.utils.GzipUtil;
import com.xiaohongshu.codewiz.core.utils.JsonMapperUtils;

/**
 * 反馈完整服务测试
 *
 * <AUTHOR>
 * @since 2024-05-24
 */
public class FeedbackCompleteServiceTest extends SpringBaseTest {

    @Resource
    private FeedbackService feedbackService;

    @Resource
    private CompleteS3Client completeS3Client;

    @Resource
    private FeedbackCompleteService feedbackCompleteService;

    private FeedbackReportRequest buildRequest() {
        // 准备测试数据
        FeedbackReportRequest request = new FeedbackReportRequest();
        request.setFeedbackId("test-feedback-123");

        FeedbackInfo feedback = new FeedbackInfo();
        feedback.setDescription("这是一个测试反馈");

        // 模拟gzip压缩的日志内容
        String originalLogContent = "这是测试日志内容\n包含多行数据\n[ERROR] Plugin exception occurred\n[INFO] User action:" +
                " code completion\n[DEBUG] Processing time: 1234ms";
        try {
            String compressedLogContent = GzipUtil.compress(originalLogContent);
            feedback.setPluginLogFile(compressedLogContent);
            System.out.println("Test setup - Original log size: " + originalLogContent.length() +
                    ", Compressed size: " + compressedLogContent.length());
        } catch (Exception e) {
            System.err.println("Failed to compress test log content: " + e.getMessage());
            feedback.setPluginLogFile(originalLogContent); // 降级为普通文本
        }

        feedback.setUserEmail("<EMAIL>");

        Map<String, Object> editor = new HashMap<>();
        editor.put("name", "vscode");
        editor.put("version", "1.80.0");
        feedback.setEditor(editor);

        request.setFeedback(JsonMapperUtils.fromJson(JsonMapperUtils.toJson(feedback)));

        return request;
    }

    @Test
    public void testReportFeedback_Success() {
        // 执行测试
        SingleResponse<Void> result = feedbackCompleteService.reportFeedback(buildRequest());

        // 验证结果
        assertNotNull(result);
        assertEquals("200", result.getCode());
        assertEquals("success", result.getMsg());
    }

    @Test
    public void testReportFeedback_S3UploadFailed() {
        // 模拟S3上传失败
        when(completeS3Client.uploadFeedbackLog(anyString())).thenReturn(null);

        // 执行测试
        SingleResponse<Void> result = feedbackCompleteService.reportFeedback(buildRequest());

        // 验证结果
        assertNotNull(result);
        assertEquals("500", result.getCode());
        assertEquals("Failed to upload log file", result.getMsg());
    }

    @Test
    public void testReportFeedback_NoLogFile() {
        // 测试没有日志文件的情况

        // 模拟核心服务保存成功
        when(feedbackService.reportFeedback(any(FeedbackReportRequest.class), any()))
                .thenReturn(SingleResponse.ok());

        // 执行测试
        SingleResponse<Void> result = feedbackCompleteService.reportFeedback(buildRequest());

        // 验证结果
        assertNotNull(result);
        assertEquals("200", result.getCode());
        assertEquals("success", result.getMsg());
    }

    @Test
    public void testReportFeedback_CoreServiceFailed() {
        // 模拟S3上传成功
        String mockS3Key = "feedback/logs/test-uuid";
        when(completeS3Client.uploadFeedbackLog(anyString())).thenReturn(mockS3Key);

        // 模拟核心服务保存失败
        when(feedbackService.reportFeedback(any(FeedbackReportRequest.class), anyString()))
                .thenReturn(SingleResponse.buildFailure("500", "Database error"));

        // 执行测试
        SingleResponse<Void> result = feedbackCompleteService.reportFeedback(buildRequest());

        // 验证结果
        assertNotNull(result);
        assertEquals("500", result.getCode());
        assertEquals("Database error", result.getMsg());
    }

    @Test
    public void testReportFeedback_Exception() {
        // 模拟异常
        when(completeS3Client.uploadFeedbackLog(anyString())).thenThrow(new RuntimeException("Network error"));

        // 执行测试
        SingleResponse<Void> result = feedbackCompleteService.reportFeedback(buildRequest());

        // 验证结果
        assertNotNull(result);
        assertEquals("500", result.getCode());
        assertNotNull(result.getMsg());
    }

    @Test
    public void testQueryFeedback_Success() {
        // 先提交一个反馈
        FeedbackReportRequest request = buildRequest();
        SingleResponse<Void> reportResult = feedbackCompleteService.reportFeedback(request);
        assertEquals("200", reportResult.getCode());

        // 查询反馈
        SingleResponse<FeedbackQueryResponse> queryResult = feedbackCompleteService.queryFeedback(request.getFeedbackId());

        // 验证结果
        assertNotNull(queryResult);
        assertEquals("200", queryResult.getCode());
        assertNotNull(queryResult.getData());
        
        FeedbackQueryResponse response = queryResult.getData();
        assertEquals(request.getFeedbackId(), response.getFeedbackId());
        assertEquals("这是一个测试反馈", response.getFeedbackDesc());
        assertEquals("<EMAIL>", response.getFeedbackUserEmail());
        assertNotNull(response.getFeedbackExtra());
        assertNotNull(response.getPluginLogFileContent());
        assertNotNull(response.getCreateAt());
    }

    @Test
    public void testQueryFeedback_NotFound() {
        // 查询不存在的反馈
        SingleResponse<FeedbackQueryResponse> result = feedbackCompleteService.queryFeedback("non-existent-id");

        // 验证结果
        assertNotNull(result);
        assertEquals("404", result.getCode());
        assertEquals("Feedback not found", result.getMsg());
    }

    @Test
    public void testQueryFeedback_EmptyFeedbackId() {
        // 查询空的反馈ID
        SingleResponse<FeedbackQueryResponse> result = feedbackCompleteService.queryFeedback("");

        // 验证结果
        assertNotNull(result);
        assertEquals("400", result.getCode());
        assertEquals("Feedback ID cannot be empty", result.getMsg());
    }
}