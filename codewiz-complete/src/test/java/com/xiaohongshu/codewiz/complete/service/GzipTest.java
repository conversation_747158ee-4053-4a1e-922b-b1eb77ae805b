package com.xiaohongshu.codewiz.complete.service;

import org.junit.Test;

import com.xiaohongshu.codewiz.core.utils.GzipUtil;

/**
 * <AUTHOR>
 * @date 2025/5/26 10:40
 */
public class GzipTest {

    @Test
    public void testGzip() {
        String str = "hello world,这是一个日志内容";
        try {
            String compressed = GzipUtil.compress(str);
            System.out.println("Compressed: " + compressed);
            String decompressed = GzipUtil.decompress(compressed);
            System.out.println("Decompressed: " + decompressed);
        } catch (Exception e) {
            System.out.println("Error: " + e.getMessage());
        }
    }
}
