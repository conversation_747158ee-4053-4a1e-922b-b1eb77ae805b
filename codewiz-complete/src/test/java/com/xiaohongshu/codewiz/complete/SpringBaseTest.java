package com.xiaohongshu.codewiz.complete;

import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @date 2024/7/15 19:16
 */
@RunWith(SpringRunner.class)
@ActiveProfiles("sit")
@SpringBootTest(
        classes = CompleteServerApplication.class,
        webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT
)
@EnableAspectJAutoProxy
public abstract class SpringBaseTest {
}
