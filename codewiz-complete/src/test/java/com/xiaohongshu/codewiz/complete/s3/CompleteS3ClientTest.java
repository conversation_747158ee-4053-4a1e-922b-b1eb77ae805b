package com.xiaohongshu.codewiz.complete.s3;

import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;

import javax.annotation.Resource;

import org.junit.Test;

import com.xiaohongshu.codewiz.complete.SpringBaseTest;
import com.xiaohongshu.codewiz.core.utils.GzipUtil;

/**
 * S3客户端测试
 *
 * <AUTHOR>
 * @since 2024-05-24
 */
public class CompleteS3ClientTest extends SpringBaseTest {

    @Resource
    private CompleteS3Client completeS3Client;

    private String testBucket = "test-bucket";
    private String testLogContent = "这是测试日志内容\n包含多行数据\n用于测试S3上传下载功能";

    // @Before
    // public void setUp() {
    //     // 使用反射设置bucket字段
    //     try {
    //         java.lang.reflect.Field bucketField = CompleteS3Client.class.getDeclaredField("bucket");
    //         bucketField.setAccessible(true);
    //         bucketField.set(completeS3Client, testBucket);
    //     } catch (Exception e) {
    //         throw new RuntimeException("Failed to set bucket field", e);
    //     }
    // }

    @Test
    public void testUploadFeedbackLog_Success() {
        // 先压缩测试日志内容
        String compressedLogContent;
        try {
            compressedLogContent = GzipUtil.compress(testLogContent);
            System.out.println("Original log length: " + testLogContent.length());
            System.out.println("Compressed log length: " + compressedLogContent.length());
        } catch (Exception e) {
            System.err.println("Failed to compress test log content: " + e.getMessage());
            return;
        }

        // 执行测试
        String result = completeS3Client.uploadFeedbackLog(compressedLogContent);

        System.out.println("Upload result: " + result);

        // 验证结果
        assertNotNull(result);
        assert result.startsWith("feedback/logs/");
    }

    @Test
    public void testUploadFeedbackLog_PlainText() {
        // 测试普通文本（非gzip压缩）
        String result = completeS3Client.uploadFeedbackLog(testLogContent);

        System.out.println("Plain text upload result: " + result);

        // 验证结果
        assertNotNull(result);
        assert result.startsWith("feedback/logs/");
    }

    @Test
    public void testUploadFeedbackLog_BlankContent() {
        // 测试空内容
        String result1 = completeS3Client.uploadFeedbackLog("");
        String result2 = completeS3Client.uploadFeedbackLog(null);
        String result3 = completeS3Client.uploadFeedbackLog("   ");

        // 验证结果
        assertNull(result1);
        assertNull(result2);
        assertNull(result3);
    }

    @Test
    public void testGzipUtil() {
        // 测试Gzip工具类
        try {
            String originalText = "这是一个测试文本\n包含换行符\n用于测试gzip压缩和解压功能";
            
            // 压缩
            String compressed = GzipUtil.compress(originalText);
            System.out.println("Original: " + originalText);
            System.out.println("Compressed: " + compressed);
            System.out.println("Original length: " + originalText.length());
            System.out.println("Compressed length: " + compressed.length());
            
            // 解压
            String decompressed = GzipUtil.decompress(compressed);
            System.out.println("Decompressed: " + decompressed);
            
            // 验证
            assert originalText.equals(decompressed);
            System.out.println("Gzip test passed!");
            
        } catch (Exception e) {
            System.err.println("Gzip test failed: " + e.getMessage());
            e.printStackTrace();
        }
    }
} 