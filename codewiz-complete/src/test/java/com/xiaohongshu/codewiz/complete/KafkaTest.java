package com.xiaohongshu.codewiz.complete;

import java.util.Properties;

import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.common.serialization.StringSerializer;
import org.junit.Test;

/**
 * <AUTHOR>
 * @date 2025/4/22 20:46
 */
public class KafkaTest extends SpringBaseTest {

    @Test
    public void testProducer() {
        Properties props = new Properties();
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, "eds://kafka-s-sit");
        props.put(ProducerConfig.ACKS_CONFIG, "1");
        props.put(ProducerConfig.RETRIES_CONFIG, 10);
        props.put(ProducerConfig.BATCH_SIZE_CONFIG, 65536);
        props.put(ProducerConfig.LINGER_MS_CONFIG, 500);
        props.put(ProducerConfig.COMPRESSION_TYPE_CONFIG, "lz4");
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class.getName());
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class.getName());

        // create a producer
        KafkaProducer<String, String> producer = new KafkaProducer<>(props);

        // send messages
        String topic1 = "codewiz_metrics_kafka";
        String topic = "codewiz_metrics_kafka_sit";
        for (int i = 0; i < 5; i++) {
            ProducerRecord<String, String> record = new ProducerRecord<>(topic,
                    "{\"traceId\": \"1234567890\",\"sessionId\": \"1234567890\",\"metricsScene\": \"metrics_scene\",\"metricsKey\": \"metrics_key\",\"metricsValue\": \"metrics_value\",\"timestamp\": 1719168000,\"user\": \"<EMAIL>\",\"ide\": \"jetbrain idea2025.1\",\"plugin\": \"codewiz 1.0.0\",\"config\": \"codewiz config\"}");
            producer.send(record);
        }

        // close the producer before program exit
        producer.close();
    }
}
