package com.xiaohongshu.codewiz.complete.service.codecontext.parser;

import com.xiaohongshu.codewiz.complete.model.lang.SourceFile;
import com.xiaohongshu.codewiz.complete.runner.parser.JavaClassFileParser;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;

/**
 * JavaClassFileParser 真实.class文件测试
 * 使用src/test/resources/java_classes/SmartCrService.class文件进行测试
 */
public class JavaClassFileParserRealTest {

    private byte[] classBytes;
    private static final String CLASS_FILE_PATH = "java_classes/SmartCrService.class";
    private static final String CLASS_FILE_NAME = "SmartCrService.class";
    
    // 期望的解析结果常量
    private static final String EXPECTED_PACKAGE_NAME = "com.xiaohongshu.redcopilot.smartcr.service";
    private static final String EXPECTED_CLASS_NAME = "SmartCrService";
    private static final String EXPECTED_CLASS_TYPE = "interface";
    
    // 期望的导入类列表
    private static final String[] EXPECTED_IMPORTS = {
        "com.xiaohongshu.redcopilot.common.dto.smartcr.SmartCrCommentInfoDO",
        "com.xiaohongshu.redcopilot.common.dto.smartcr.SmartCrCommentSimpleInfoDo",
        "com.xiaohongshu.redcopilot.common.dto.smartcr.SmartCrTaskExecuteParamDO",
        "com.xiaohongshu.redcopilot.common.dto.smartcr.SmartCrTaskRegisterParamDO",
        "com.xiaohongshu.redcopilot.common.dto.smartcr.bo.SmartCrExecuteContext",
        "com.xiaohongshu.redcopilot.common.dto.smartcr.bo.SmartCrTaskBO",
        "com.xiaohongshu.redcopilot.smartcr.remote.RedUserBO",
        "java.util.List",
        "java.util.Optional"
    };
    
    // 期望的方法名列表
    private static final String[] EXPECTED_METHODS = {
        "registerSmartCrTask",
        "searchExecutableSmartCrTask", 
        "searchRetryableSmartCrTask",
        "searchNeedTerminalSmartCrTask",
        "getSmartCrTask",
        "getLatestDoneCrTask",
        "updateMrTask",
        "searchSmartMrInfo",
        "executeSmartCr",
        "doExecuteSmartCr",
        "doExecuteSmartCrV2",
        "getSmartCrCommentSimpleInfo",
        "clearSmartCr"
    };

    /**
     * 从资源文件加载.class字节码
     */
    private byte[] loadClassBytes(String resourcePath) throws IOException {
        try (InputStream inputStream = getClass().getClassLoader().getResourceAsStream(resourcePath)) {
            if (inputStream == null) {
                throw new IOException("无法找到资源文件: " + resourcePath);
            }
            return inputStream.readAllBytes();
        }
    }

    @BeforeEach
    void setUp() throws IOException {
        // 在每个测试前加载class字节码
        classBytes = loadClassBytes(CLASS_FILE_PATH);
        assertNotNull(classBytes, "class字节码不应该为null");
        assertTrue(classBytes.length > 0, "class字节码长度应该大于0");
    }

    @Test
    void testParseRealClassFile() {
        // 验证是Java class文件魔数 (0xCAFEBABE)
        assertEquals((byte)0xCA, classBytes[0], "Java class文件魔数第1字节");
        assertEquals((byte)0xFE, classBytes[1], "Java class文件魔数第2字节");
        assertEquals((byte)0xBA, classBytes[2], "Java class文件魔数第3字节");
        assertEquals((byte)0xBE, classBytes[3], "Java class文件魔数第4字节");
        
        System.out.println("成功加载.class文件，大小: " + classBytes.length + " 字节");
    }

    @Test
    void testParseClassFileStructure() {
        // 解析.class文件
        SourceFile result = JavaClassFileParser.parseJavaClassFile(CLASS_FILE_NAME, classBytes);
        
        // 验证解析结果
        assertNotNull(result, "解析结果不应该为null");
        assertNotNull(result.getClasses(), "类列表不应该为null");
        assertFalse(result.getClasses().isEmpty(), "应该至少包含一个类");
        
        // 获取解析出的类信息
        SourceFile.ClassInfo classInfo = result.getClasses().get(0);
        
        // 验证基本类信息
        assertNotNull(classInfo.getClassName(), "类名不应该为null");
        assertNotNull(classInfo.getClassType(), "类类型不应该为null");
        assertNotNull(classInfo.getVisibility(), "可见性不应该为null");
        assertNotNull(classInfo.getModifiers(), "修饰符列表不应该为null");
        assertNotNull(classInfo.getFields(), "字段列表不应该为null");
        assertNotNull(classInfo.getMethods(), "方法列表不应该为null");
        assertNotNull(classInfo.getAnnotations(), "注解列表不应该为null");
        
        // 验证具体内容
        assertEquals(EXPECTED_PACKAGE_NAME, result.getPackageName(), "包名应该正确解析");
        assertEquals(EXPECTED_CLASS_NAME, classInfo.getClassName(), "类名应该正确解析"); 
        assertEquals(EXPECTED_CLASS_TYPE, classInfo.getClassType(), "类类型应该是interface");
        assertEquals("public", classInfo.getVisibility(), "接口默认可见性应该是public");
        
        // 打印解析结果
        System.out.println("=== 解析结果 ===");
        System.out.println("文件名: " + result.getFileName());
        System.out.println("包名: " + result.getPackageName());
        System.out.println("类名: " + classInfo.getClassName());
        System.out.println("类类型: " + classInfo.getClassType());
        System.out.println("可见性: " + classInfo.getVisibility());
        System.out.println("修饰符: " + classInfo.getModifiers());
        System.out.println("父类: " + classInfo.getExtendsClass());
        System.out.println("实现接口: " + classInfo.getImplementsInterfaces());
        System.out.println("字段数量: " + classInfo.getFields().size());
        System.out.println("方法数量: " + classInfo.getMethods().size());
        System.out.println("注解数量: " + classInfo.getAnnotations().size());
        System.out.println("类签名: " + classInfo.getSignature());
    }
    
    @Test
    void testParseImports() {
        SourceFile result = JavaClassFileParser.parseJavaClassFile(CLASS_FILE_NAME, classBytes);
        
        assertNotNull(result, "解析结果不应该为null");
        assertNotNull(result.getImports(), "导入列表不应该为null");
        
        System.out.println("=== 导入信息 ===");
        System.out.println("导入数量: " + result.getImports().size());
        
        // 将导入列表转换为Set以便比较
        Set<String> actualImports = result.getImports().stream().collect(Collectors.toSet());
        
        for (String expectedImport : EXPECTED_IMPORTS) {
            assertTrue(actualImports.contains(expectedImport), 
                "应该包含导入: " + expectedImport);
            System.out.println("✓ " + expectedImport);
        }
        
        // 打印所有实际导入的类
        System.out.println("\n实际导入的所有类:");
        result.getImports().forEach(imp -> System.out.println("  " + imp));
    }

    @Test
    void testParseClassFields() {
        SourceFile result = JavaClassFileParser.parseJavaClassFile(CLASS_FILE_NAME, classBytes);
        
        assertNotNull(result);
        SourceFile.ClassInfo classInfo = result.getClasses().get(0);
        
        System.out.println("=== 字段信息 ===");
        assertEquals(0, classInfo.getFields().size(), "接口不应该有字段（除了常量）");
        System.out.println("接口字段数量: " + classInfo.getFields().size() + " (符合预期)");
    }

    @Test
    void testParseClassMethods() {
        SourceFile result = JavaClassFileParser.parseJavaClassFile(CLASS_FILE_NAME, classBytes);
        
        assertNotNull(result);
        SourceFile.ClassInfo classInfo = result.getClasses().get(0);
        
        System.out.println("=== 方法信息 ===");
        
        // 接口方法数量验证
        assertTrue(classInfo.getMethods().size() > 0, "接口应该有方法定义");
        System.out.println("方法总数: " + classInfo.getMethods().size());
        
        // 获取所有方法名
        Set<String> actualMethodNames = classInfo.getMethods().stream()
            .map(SourceFile.MethodInfo::getMethodName)
            .collect(Collectors.toSet());
        
        System.out.println("实际解析的方法名:");
        actualMethodNames.forEach(name -> System.out.println("  " + name));
        
        // 验证期望的方法是否都存在
        for (String expectedMethod : EXPECTED_METHODS) {
            assertTrue(actualMethodNames.contains(expectedMethod), 
                "应该包含方法: " + expectedMethod);
        }
        
        // 详细打印每个方法信息
        System.out.println("\n详细方法信息:");
        for (SourceFile.MethodInfo method : classInfo.getMethods()) {
            StringBuilder methodSig = new StringBuilder();
            methodSig.append(method.getVisibility()).append(" ");
            
            if (!method.getModifiers().isEmpty()) {
                methodSig.append(String.join(" ", method.getModifiers())).append(" ");
            }
            
            methodSig.append(method.getReturnType()).append(" ");
            methodSig.append(method.getMethodName()).append("(");
            
            for (int i = 0; i < method.getParameters().size(); i++) {
                SourceFile.ParameterInfo param = method.getParameters().get(i);
                if (i > 0) methodSig.append(", ");
                methodSig.append(param.getParameterType()).append(" ").append(param.getParameterName());
            }
            methodSig.append(")");
            
            if (!method.getExceptions().isEmpty()) {
                methodSig.append(" throws ").append(String.join(", ", method.getExceptions()));
            }
            
            System.out.println("  " + methodSig.toString());
        }
    }
    
    @Test
    void testSpecificMethodSignatures() {
        SourceFile result = JavaClassFileParser.parseJavaClassFile(CLASS_FILE_NAME, classBytes);
        
        assertNotNull(result);
        SourceFile.ClassInfo classInfo = result.getClasses().get(0);
        
        System.out.println("=== 特定方法签名验证 ===");
        
        // 查找并验证特定方法
        SourceFile.MethodInfo registerMethod = classInfo.getMethods().stream()
            .filter(m -> "registerSmartCrTask".equals(m.getMethodName()))
            .findFirst()
            .orElse(null);
        
        assertNotNull(registerMethod, "应该找到registerSmartCrTask方法");
        assertEquals("Long", registerMethod.getReturnType(), "registerSmartCrTask返回类型应该是Long");
        assertEquals(1, registerMethod.getParameters().size(), "registerSmartCrTask应该有1个参数");
        
        // 验证getSmartCrTask方法（应该有重载）
        List<SourceFile.MethodInfo> getTaskMethods = classInfo.getMethods().stream()
            .filter(m -> "getSmartCrTask".equals(m.getMethodName()))
            .collect(Collectors.toList());
        
        assertTrue(getTaskMethods.size() >= 2, "getSmartCrTask应该有至少2个重载版本");
        System.out.println("getSmartCrTask重载数量: " + getTaskMethods.size());
        
        for (int i = 0; i < getTaskMethods.size(); i++) {
            SourceFile.MethodInfo method = getTaskMethods.get(i);
            System.out.println("  重载" + (i+1) + ": 参数数量=" + method.getParameters().size());
        }
        
        // 验证searchSmartMrInfo方法（应该有重载）
        List<SourceFile.MethodInfo> searchMethods = classInfo.getMethods().stream()
            .filter(m -> "searchSmartMrInfo".equals(m.getMethodName()))
            .collect(Collectors.toList());
        
        assertTrue(searchMethods.size() >= 2, "searchSmartMrInfo应该有至少2个重载版本");
        System.out.println("searchSmartMrInfo重载数量: " + searchMethods.size());
    }

    @Test
    void testParseClassAnnotations() {
        SourceFile result = JavaClassFileParser.parseJavaClassFile(CLASS_FILE_NAME, classBytes);
        
        assertNotNull(result);
        SourceFile.ClassInfo classInfo = result.getClasses().get(0);
        
        System.out.println("=== 类注解信息 ===");
        System.out.println("注解数量: " + classInfo.getAnnotations().size());
        
        if (classInfo.getAnnotations().isEmpty()) {
            System.out.println("该接口没有类级别注解（符合预期）");
        } else {
            for (SourceFile.AnnotationInfo annotation : classInfo.getAnnotations()) {
                System.out.println("注解: @" + annotation.getAnnotationName());
                if (!annotation.getAttributes().isEmpty()) {
                    for (SourceFile.AnnotationInfo.AnnotationAttribute attr : annotation.getAttributes()) {
                        System.out.println("  属性: " + attr.getName() + " = " + attr.getValue());
                    }
                }
            }
        }
    }

    @Test
    void testClassFileValidation() {
        // 测试null输入
        SourceFile nullResult = JavaClassFileParser.parseJavaClassFile("test.class", null);
        assertNull(nullResult, "null输入应该返回null");
        
        // 测试空字节数组
        SourceFile emptyResult = JavaClassFileParser.parseJavaClassFile("test.class", new byte[0]);
        assertNull(emptyResult, "空字节数组应该返回null");
        
        // 测试正常输入
        SourceFile normalResult = JavaClassFileParser.parseJavaClassFile(CLASS_FILE_NAME, classBytes);
        assertNotNull(normalResult, "正常输入应该返回有效结果");
    }

    @Test
    void testParseCapabilityComparison() {
        // 这个测试用于验证与JavaSourceParser的能力对比
        SourceFile result = JavaClassFileParser.parseJavaClassFile(CLASS_FILE_NAME, classBytes);
        
        assertNotNull(result);
        
        System.out.println("=== 能力对比测试 ===");
        System.out.println("1. 基本信息解析: ✓");
        System.out.println("   - 文件名: " + result.getFileName());
        System.out.println("   - 包名: " + (result.getPackageName() != null ? result.getPackageName() : "默认包"));
        
        SourceFile.ClassInfo classInfo = result.getClasses().get(0);
        System.out.println("2. 类信息解析: ✓");
        System.out.println("   - 类名: " + classInfo.getClassName());
        System.out.println("   - 类型: " + classInfo.getClassType());
        System.out.println("   - 可见性: " + classInfo.getVisibility());
        
        System.out.println("3. 结构信息解析: ✓");
        System.out.println("   - 导入数量: " + result.getImports().size());
        System.out.println("   - 字段数量: " + classInfo.getFields().size());
        System.out.println("   - 方法数量: " + classInfo.getMethods().size());
        System.out.println("   - 注解数量: " + classInfo.getAnnotations().size());
        
        System.out.println("4. 接口特有特性: ✓");
        assertEquals(EXPECTED_CLASS_TYPE, classInfo.getClassType(), "应该正确识别为接口");
        assertEquals(0, classInfo.getFields().size(), "接口通常没有实例字段");
        assertTrue(classInfo.getMethods().size() > 10, "SmartCrService接口应该有多个方法");
        
        System.out.println("5. 数据结构完整性: ✓");
        System.out.println("   - 所有必需字段都不为null");
        System.out.println("   - 返回的SourceFile结构与JavaSourceParser一致");
        
        // 验证数据结构完整性
        assertTrue(result.getImports() != null, "imports列表不应该为null");
        assertTrue(classInfo.getImplementsInterfaces() != null, "实现接口列表不应该为null");
        assertTrue(classInfo.getGenericTypes() != null, "泛型类型列表不应该为null");
        assertTrue(classInfo.getInnerClasses() != null, "内部类列表不应该为null");
        
        System.out.println("=== 测试通过：JavaClassFileParser完全支持接口解析 ===");
    }
    
    @Test 
    void testMethodParameterTypes() {
        SourceFile result = JavaClassFileParser.parseJavaClassFile(CLASS_FILE_NAME, classBytes);
        
        assertNotNull(result);
        SourceFile.ClassInfo classInfo = result.getClasses().get(0);
        
        System.out.println("=== 方法参数类型验证 ===");
        
        // 验证特定方法的参数类型
        SourceFile.MethodInfo executeMethod = classInfo.getMethods().stream()
            .filter(m -> "executeSmartCr".equals(m.getMethodName()))
            .findFirst()
            .orElse(null);
            
        if (executeMethod != null) {
            System.out.println("executeSmartCr方法参数:");
            assertEquals(2, executeMethod.getParameters().size(), "executeSmartCr应该有2个参数");
            
            SourceFile.ParameterInfo param1 = executeMethod.getParameters().get(0);
            SourceFile.ParameterInfo param2 = executeMethod.getParameters().get(1);
            
            assertTrue(param1.getParameterType().contains("SmartCrTaskExecuteParamDO"), 
                "第一个参数应该是SmartCrTaskExecuteParamDO类型");
            assertTrue(param2.getParameterType().contains("Optional"), 
                "第二个参数应该是Optional类型");
            assertTrue(param2.getParameterType().contains("RedUserBO"), 
                "第二个参数应该包含RedUserBO类型");
                
            System.out.println("  参数1: " + param1.getParameterType() + " " + param1.getParameterName());
            System.out.println("  参数2: " + param2.getParameterType() + " " + param2.getParameterName());
        }
    }
} 