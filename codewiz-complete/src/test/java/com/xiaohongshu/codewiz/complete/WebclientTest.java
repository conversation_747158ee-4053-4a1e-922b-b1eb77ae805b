package com.xiaohongshu.codewiz.complete;

import java.util.List;

import org.junit.Test;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.util.UriComponentsBuilder;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.xiaohongshu.codewiz.core.utils.JsonMapperUtils;

import lombok.Data;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @date 2025/4/24 11:29
 */
public class WebclientTest extends SpringBaseTest {

    @Test
    public void testCustomModel() {
        String baseUrl = "https://codewiz-hongshu.devops.xiaohongshu.com";
        String path = "/v1/chat/completions";
        WebClient webClient = WebClient.builder()
                // .baseUrl(baseUrl)
                .build();
        ChatCompletionRequestTest convert = convert();
        String block = webClient.post()
                .uri(UriComponentsBuilder.fromHttpUrl(baseUrl).path(path).build().toUri())
                .body(Mono.just(convert), ChatCompletionRequestTest.class)
                .retrieve()
                .bodyToMono(String.class)
                .doOnNext(System.out::println)
                .block();
        System.out.println("ok");
    }

    private ChatCompletionRequestTest convert() {
        String request = "{\n" +
                "    \"model\": \"codewiz-qwen32b-stf-v2\",\n" +
                "    \"messages\": [\n" +
                "        {\n" +
                "            \"role\": \"system\",\n" +
                "            \"content\": \"You are a helpful assistant.\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"role\": \"user\",\n" +
                "            \"content\": \"你是一个专业的代码和注释的补全助手，可以根据用户输入的代码片段，补全[此处光标]所在位置的代码。\\n注意：请务必直接输出要补全代码，不要输出其他内容，也不要去重复上下文已有的代码；补全的代码中如有用于对齐的空格也请保留下来。\\n\\\\u003cfim_prefix\\\\u003e\\n\\\\u003cfilename\\\\u003eMetricsService.java\\npackage com.xiaohongshu.codewiz.core.service.metrics;\\n\\nimport java.util.Properties;\\n\\nimport org.apache.kafka.clients.producer.KafkaProducer;\\nimport org.apache.kafka.clients.producer.ProducerConfig;\\nimport org.apache.kafka.clients.producer.ProducerRecord;\\nimport org.apache.kafka.common.serialization.StringSerializer;\\nimport org.springframework.beans.factory.DisposableBean;\\nimport org.springframework.beans.factory.InitializingBean;\\nimport org.springframework.beans.factory.annotation.Value;\\nimport org.springframework.stereotype.Service;\\n\\nimport com.xiaohongshu.codewiz.core.entity.metrics.MetricsRequest;\\nimport com.xiaohongshu.codewiz.core.utils.JsonMapperUtils;\\n\\nimport lombok.extern.slf4j.Slf4j;\\n\\n/**\\n * <AUTHOR> * @date 2025/4/23 15:55\\n */\\n@Slf4j\\n@Service\\npublic class MetricsService implements InitializingBean, DisposableBean {\\n\\n    @Value(\\\"${kafka.bootstrap-servers}\\\")\\n    private String bootstrapServers;\\n\\n    @Value(\\\"${kafka.topic}\\\")\\n    private String topic;\\n\\n    private KafkaProducer\\\\u003cString, String\\\\u003e producer;\\n\\n    @Override\\n    public void afterPropertiesSet() {\\n        Properties props \\\\u003d new Properties();\\n        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);\\n        props.put(ProducerConfig.ACKS_CONFIG, \\\"1\\\");\\n        props.put(ProducerConfig.RETRIES_CONFIG, 10);\\n        props.put(ProducerConfig.BATCH_SIZE_CONFIG, 65536);\\n        props.put(ProducerConfig.LINGER_MS_CONFIG, 500);\\n        props.put(ProducerConfig.COMPRESSION_TYPE_CONFIG, \\\"lz4\\\");\\n        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class.getName());\\n        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class.getName());\\n\\n        // create a producer once during initialization\\n        this.producer \\\\u003d new KafkaProducer\\\\u003c\\\\u003e(props);\\n        log.info(\\\"MetricsService initialized with bootstrap servers: {}\\\", bootstrapServers);\\n    }\\n\\n    public void sendMetrics(MetricsRequest request) {\\n        try {\\\\u003cfim_suffix\\\\u003e\\n            String json \\\\u003d JsonMapperUtils.toJson(request);\\n            log.info(\\\"Sending metrics: {}\\\", json);\\n            producer.send(new ProducerRecord\\\\u003c\\\\u003e(topic, json));\\n            log.info(\\\"Metrics sent successfully\\\");\\n        } catch (Exception e) {\\n            log.error(\\\"Failed to send metrics: {}\\\", e.getMessage(), e);\\n        }\\n    }\\n\\n[此处光标]\"\n" +
                "        }\n" +
                "    ],\n" +
                "    \"stream\": false,\n" +
                "    \"max_tokens\": 512,\n" +
                "    \"temperature\": 0.1\n" +
                // "    \",top_p\": 0.85\n" +
                "}";
        return JsonMapperUtils.fromJson(request, ChatCompletionRequestTest.class);
    }

    @Data
    public static class ChatCompletionRequestTest {
        private String model;

        private List<ChatMessage> messages;

        private Boolean stream;

        @JsonProperty("max_tokens")
        private Integer maxTokens;

        @JsonProperty("use_cache")
        private Boolean useCache;

        private Double topP;

        private Double temperature;

        private List<String> stop;

        @Data
        public static class ChatMessage {
            private String role;
            private String content;
        }

        private String baseUrl;
    }
}
