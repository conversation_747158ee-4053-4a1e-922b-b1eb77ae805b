package com.xiaohongshu.codewiz.complete;

import static org.hibernate.validator.internal.util.Contracts.assertNotNull;

import java.sql.Connection;
import java.sql.SQLException;

import javax.sql.DataSource;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2025/3/3
 */
@SpringBootTest
@RunWith(SpringRunner.class)
public class DataSourceTest {
    @Autowired
    private DataSource dataSource;

    @Test
    public void testConnection() throws SQLException {
        // 尝试获取数据库连接，不报错则说明连接正常
        Connection connection = dataSource.getConnection();
        assertNotNull(connection);
        connection.close();
    }
}
