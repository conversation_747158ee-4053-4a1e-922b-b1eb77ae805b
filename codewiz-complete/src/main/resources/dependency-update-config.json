{"version": "1.0", "description": "依赖更新配置文件 - 定义需要处理的依赖列表", "dependencyGroups": [{"name": "小红书特色代码包", "language": "JAVA", "dependencyType": "MAVEN", "description": "Java Maven 依赖包", "dependencies": [{"namespace": "com.red.data.gateway", "name": "swift2thrift-maven-plugin"}, {"namespace": "com.red.nlp.library", "name": "springboot-configurable-thrift-client"}, {"namespace": "com.xiaohongshu", "name": "ad-serving-api-thrift"}, {"namespace": "com.xiaohongshu", "name": "realtime-data-thrift"}, {"namespace": "com.xiaohongshu", "name": "redpgy-api-thrift"}, {"namespace": "com.xiaohongshu", "name": "rpc-thrift-plugin"}, {"namespace": "com.xiaohongshu", "name": "rpc-thrift-plugin-cat"}, {"namespace": "com.xiaohongshu", "name": "thrift"}, {"namespace": "com.xiaohongshu", "name": "thrift-netty-transport"}, {"namespace": "com.xiaohongshu", "name": "thrift-preload"}, {"namespace": "com.xiaohongshu", "name": "thrift-rpc"}, {"namespace": "com.xiaohongshu", "name": "thrift-rpc-cat"}, {"namespace": "com.xiaohongshu", "name": "thrift-springboot"}, {"namespace": "com.xiaohongshu", "name": "thrift-springboot-cat"}, {"namespace": "com.xiaohongshu", "name": "utils-sns-thrift"}, {"namespace": "com.xiaohongshu", "name": "utils-thrift"}, {"namespace": "com.xiaohongshu", "name": "xhs-common-utils-jackson-thrift"}, {"namespace": "com.xiaohongshu.abroad.thrift", "name": "kamino-content-sdk"}, {"namespace": "com.xiaohongshu.abroad.thrift", "name": "kamino-front-sdk"}, {"namespace": "com.xiaohongshu.abroad.thrift", "name": "kamino-transfer-sdk"}, {"namespace": "com.xiaohongshu.abroad.thrift", "name": "kaminotask-sdk"}, {"namespace": "com.xiaohongshu.agi_gov.thrift", "name": "llm-services"}, {"namespace": "com.xiaohongshu.agi_llm.thrift", "name": "llm-services"}, {"namespace": "com.xiaohongshu.agi_llm.thrift", "name": "llm_services"}, {"namespace": "com.xiaohongshu.aimedia.thrift", "name": "ads-cover-sdk"}, {"namespace": "com.xiaohongshu.aimedia.thrift", "name": "adv-rek-sdk"}, {"namespace": "com.xiaohongshu.aimedia.thrift", "name": "api-cv-ecology"}, {"namespace": "com.xiaohongshu.aimedia.thrift", "name": "audio-high-risk-recognition"}, {"namespace": "com.xiaohongshu.aimedia.thrift", "name": "audio-spoken-language-recognition"}, {"namespace": "com.xiaohongshu.aimedia.thrift", "name": "body-album-sdk"}, {"namespace": "com.xiaohongshu.aimedia.thrift", "name": "business-license-recognition-sdk"}, {"namespace": "com.xiaohongshu.aimedia.thrift", "name": "character-cutting-recognition-sdk"}, {"namespace": "com.xiaohongshu.aimedia.thrift", "name": "colorpick-sdk"}, {"namespace": "com.xiaohongshu.aimedia.thrift", "name": "cover-blur-recognition-sdk"}, {"namespace": "com.xiaohongshu.aimedia.thrift", "name": "dup-live-cover-sdk"}, {"namespace": "com.xiaohongshu.aimedia.thrift", "name": "general<PERSON>r"}, {"namespace": "com.xiaohongshu.aimedia.thrift", "name": "highriskVideo-v1-sdk"}, {"namespace": "com.xiaohongshu.aimedia.thrift", "name": "human-vulgar-and-porn-recognition-sdk"}, {"namespace": "com.xiaohongshu.aimedia.thrift", "name": "leader-detection"}, {"namespace": "com.xiaohongshu.aimedia.thrift", "name": "media-audio-security-sdk"}, {"namespace": "com.xiaohongshu.aimedia.thrift", "name": "media-bgm-detector-sdk"}, {"namespace": "com.xiaohongshu.aimedia.thrift", "name": "minor-language-recognition-sdk"}, {"namespace": "com.xiaohongshu.aimedia.thrift", "name": "moan-detection"}, {"namespace": "com.xiaohongshu.aimedia.thrift", "name": "model-interface-sdk"}, {"namespace": "com.xiaohongshu.aimedia.thrift", "name": "piece-model-<PERSON><PERSON><PERSON>"}, {"namespace": "com.xiaohongshu.aimedia.thrift", "name": "portrait-sdk"}, {"namespace": "com.xiaohongshu.aimedia.thrift", "name": "redImageSearch-v1-sdk"}, {"namespace": "com.xiaohongshu.aimedia.thrift", "name": "shuicai-sdk"}, {"namespace": "com.xiaohongshu.aimedia.thrift", "name": "spu-retrieval-sdk"}, {"namespace": "com.xiaohongshu.aimedia.thrift", "name": "tag_recom"}, {"namespace": "com.xiaohongshu.base.thrift", "name": "red-counter-sdk"}, {"namespace": "com.xiaohongshu.business.architecture.thrift", "name": "phonenumberguard-sdk"}, {"namespace": "com.xiaohongshu.crm", "name": "crm-boot-autoconfigure-thrift"}, {"namespace": "com.xiaohongshu.cybertron", "name": "cybertron-client-thrift"}, {"namespace": "com.xiaohongshu.cybertron", "name": "cybertron-service-thrift"}, {"namespace": "com.xiaohongshu.ecology.thrift", "name": "noteecologyinfo-sdk"}, {"namespace": "com.xiaohongshu.fls", "name": "lib-thrift-skywalker-platform"}, {"namespace": "com.xiaohongshu.fls", "name": "skywalker-thriftrpc"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-HSBCBankService"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-account"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-activity"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-activity_center"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-ad-marketing-idl"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-ad-publish"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-ad-scheduler-client-idls"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-ad-scheduler-flow-idls"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-ad-social-idl"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-ad_asset"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-ad_audit_engine"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-ad_brand"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-ad_center"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-ad_chips"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-ad_common_tools"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-ad_core_data"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-ad_core_service"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-ad_creative"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-ad_creativity"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-ad_data"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-ad_data_dmp"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-ad_data_engine"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-ad_data_graph"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-ad_data_industry"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-ad_data_permission"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-ad_data_report"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-ad_data_setting"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-ad_db_service"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-ad_history"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-ad_landingpage"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-ad_light"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-ad_lite_idl"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-ad_market"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-ad_meta_data"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-ad_open_center"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-ad_order_center"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-ad_phusis"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-ad_portal_auth"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-ad_pro_idl"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-ad_qms_service"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-ad_scheduler"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-ad_search"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-ad_ugc_heat"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-ad_wind"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-adapterplms"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-adchat_thirdparty"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-addatafactory"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-ads-contract-center"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-ads-invoice-center"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-ads-order-center"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-ads-usergrowth-idls"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-ads_crm"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-ads_crm_client"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-ads_crm_data"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-ads_crm_finance"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-ads_crm_partner"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-ads_data_aggregate"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-ads_fadada"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-ads_mcc"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-ads_message"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-ads_product"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-ads_school_center"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-adsdatafactory"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-adsvision"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-adv-material-center"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-adv_gw"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-agent-platform-idl"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-aicopilot"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-alipayservice"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-alita"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-ams"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-app-rems"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-archetype"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-ares"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-ark_common"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-atlas-archive"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-atmospherecenter-idl"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-atmosphererender-idl"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-bangdaservice"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-banner_service"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-bcp"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-bds"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-begetatag"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-bicms"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-bigan"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-bims"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-biz-plms"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-biz-rems"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-blrs"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-brand_activity_service"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-browse_center"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-business_data"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-business_strategy_service"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-businesscenter"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-buy"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-campaign_center"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-cart"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-cartv2"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-cashiercenter"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-castle"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-category"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-categorycenter"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-cbms"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-cdms"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-ceres"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-chappie"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-chat-app-idl"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-chat-commerce-idl"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-chat-flow-idl"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-chat-router-idl"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-chat_trade"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-chatflow_idl"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-chatline"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-checkout_center"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-chicky"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-cims"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-citytask-idls"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-clims"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-clue_marketing_platform"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-cm-oms"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-common"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-compass"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-coms"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-conference-manager"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-contactsviewcompatibleidl"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-contactsviewserviceidl"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-contract"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-core-rems"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-cosmos-message"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-cosmos-provider"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-cosmos-purchase"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-cosmos-scms"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-coupon"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-coupon_center"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-couponbizcenter"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-cqms"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-crm-basic-idl"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-crm-customer-idl"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-crm-entity-idl"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-crm-object-idl"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-crm-revenue"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-crm-sales-idl"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-crm_crowd"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-crm_service"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-cross-border"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-cs-common"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-cs_platform_service"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-csbff"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-csds"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-cses"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-csflow"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-csms"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-csmsg"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-csqc"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-cstage"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-csts"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-csus"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-csws"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-curation"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-customer"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-da-cdms"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-darkhorse"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-darwin"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-data-cdms"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-data_center"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-datacopilot-idl"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-datahub"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-davinci"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-dbis"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-dbis_algo"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-dcs"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-dds"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-dealer"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-decision_center"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-deimos"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-detail"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-dirac"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-dmp-ad-service-idls"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-dmp-data-manager-service-idls"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-dmp-engine-service-idls"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-dmp-idea-service-idls"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-dmp-open-service-idls"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-dmp_ad_service"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-dmp_data_manager_service"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-dmp_engine_service"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-dmp_idea_service"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-dms"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-docaggcoreidl"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-docanchorservice"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-docbehaviorserviceidls"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-docbizserviceidl"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-doccommentservice"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-doccoreserviceidl"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-docfeedserviceidl"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-docflowdiagramcoreidl"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-docknowledgebaseserviceidl"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-docmiscserviceidl"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-docnetdiskserviceidl"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-docnoticeserviceidls"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-docpermissioncoreidl"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-docpermissionopenserviceidl"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-docpermissionserviceidl"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-doc<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-docsearch-service-idls"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-docssearchindexidl"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-docsuiteopenserviceidl"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-docs<PERSON><PERSON>r<PERSON><PERSON><PERSON>"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-doctaskserviceidl"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-doraemon"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-dos"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-ds_activity"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-ds_billing"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-ds_commerce_center"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-ds_crm_center"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-ds_merchant_center"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-ds_mixer"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-ds_op_center"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-ds_user_relation"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-dubhe"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-dubhe-api-idl"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-dus"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-e-crm"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-e_rms"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-ecomlivedata"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-ehr-data-api"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-ehr-payroll-api"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-ehr-private"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-ehr-public"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-ehr-public-v2"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-ehrservice"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-employee-engagement"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-empservice"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-epusertag"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-euler"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-eva-wiki"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-experience_center"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-favourite"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-fds"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-feature-index"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-finance-common"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-financeoa"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-fission"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-flow-center"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-fls-recommend-service"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-fls_growth"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-forumidl"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-freight_forwarder"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-fulfillment_center"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-fulfillment_info"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-fulishe-recommend-server"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-general-product"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-general_trade_service"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-gondar"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-gongming"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-goods_selector"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-governance"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-governance-audit"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-governance-center"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-governance-event"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-governance-inform"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-governance-judge"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-governance-namelist"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-governance-rule"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-governance-traction"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-groupservice"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-guide_center"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-hawkdata"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-hawkeye"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-himalaya"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-honor<PERSON><PERSON><PERSON>l"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-hubble"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-ideaservice"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-identity"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-iis"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-ildms"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-interlogistics"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-<PERSON><PERSON><PERSON>"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-inventory"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-inventory_center"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-inventoryv2"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-invoiceservice"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-ipms"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-iqaservice"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-iris"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-isms"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-item"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-item_aggregate_service"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-item_center"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-jarvis"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-jbds"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-jcims"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-jcsms"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-jingwei"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-jpmis"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-jpromotion_center"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-jroms"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-judgement"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-justitia-service-b"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-kaer"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-kairos"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-kayle"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-kbms"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-km"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-libra"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-life-struct-common"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-lifeAccount"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-lifeactivity"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-lifebutterfly"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-lifecny"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-lifecommuity"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-lifecore"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-lifemarketing"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-lifemerchant"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-lifepoi"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-lifepromotion"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-lifestock"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-lifesupply"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-lifetask"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-lifeticket"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-lifetrade"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-limit"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-link"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-listings"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-live-commerce"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-live-mall-draw"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-live-mall-dynamic"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-live-mall-fans-group"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-live_trade_service"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-liveeventplatformidl"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-livemalltrailer"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-logistics"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-logistics-center"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-logistics-warehouse"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-long_task"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-lottery-plateform"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-luna"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-mail_center"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-malfurion"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-mammon"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-mario"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-market-integration"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-marketing-center"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-marketing-config-service"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-marketing-rule-service"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-marketing_interact_service"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-marketing_selection_service"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-mcs_center_idl"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-mcscore"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-medivhopen"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-medivhphysical"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-medivhservice"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-member_center"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-merchant-supply"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-merchant_enter"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-merchant_helper"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-merchantinvoiceservice"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-merchantmanagement"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-mercury-security"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-message_center"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-midas"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-mkt"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-mkt-hawkeye"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-mktanalysis-idls"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-ml-bims"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-muses"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-nakoruru"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-nezha"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-nft"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-note_binding_service"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-note_item_binding_service"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-note_recommend_service"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-note_trade_center"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-notion-service"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-oa-common"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-oa-public"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-oasiser"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-obcs"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-ocean"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-offerings"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-okrservice"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-omnicenter"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-oms"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-omsservice"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-op_center"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-op_rule_center"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-open_center"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-openai"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-openchannel"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-opencontrol"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-opencore"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-openicp"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-openmerchant"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-openmessage"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-opensale"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-openstorage"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-opentrade"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-operation_info_center"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-operation_log_service"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-operationapp"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-oqcs"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-order"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-order_center"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-orderv2"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-orms"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-otcs"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-parser-plms"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-payment"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-pcs"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-performance-public"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-permission"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-permission-calc-service"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-pgy_bk_service"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-pgy_data_service"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-phobos"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-phoebeservice"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-pigsy"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-pinganservice"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-platform-robot"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-play_center"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-pluto"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-pmis"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-point"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-points"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-policy-center"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-policy-rems"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-porchaccount"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-porchlogin"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-<PERSON><PERSON><PERSON>"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-presale"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-price_center"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-pricing"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-prize_center"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-proaccount-idl"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-procontent"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-prodsrv"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-product-center"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-product-group-sdk"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-product-service"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-product_center"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-product_info_center"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-promotion"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-promox"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-provider"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-pudge"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-punishmentservice"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-push"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-q<PERSON><PERSON>ong"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-qualcenter"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-qualification-center"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-qualservice"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-questionnaire-core"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-questionnaire-service-idls"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-rbac-idl"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-rcaiserviceidl"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-rcds"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-rclink-idl"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-rclink-openidl"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-rclongconnectiongatewayidl"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-rclongconnectionserviceidl"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-rcopcoreidl"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-r<PERSON>pintegrationidl"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-rcoppublicidl"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-recreation_center"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-recruit-core"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-recruit-open"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-red-conference-task-idls"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-red-heart"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-red-partner"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-red-product"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-redaccountcore-idl"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-redbreast"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-redcityasnidl"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-redcitycardserviceidl"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-redcitymisc"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-redcityopen-public-idl"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-redcityopencore-service-idl"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-redcitypushidl"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-redcityworkbenchidl"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-redfundallocate-idl"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-redfundgateway"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-redgardencore-idl"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-red<PERSON>enidl"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-redimaccessgatewayidl"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-redimcustomerser<PERSON><PERSON>l"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-redimgeneralbizidl"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-redimgeneralcoreidl"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-redimmessagecoreidl"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-redimnameaiserviceidl"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-redimopenabilityidl"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-redimpushidl"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-redimsecondarybizidl"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-redimsecondarycoreidl"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-redimsequenceserviceidl"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-redocadminidl"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-redopencenter"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-red<PERSON><PERSON><PERSON><PERSON>"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-redpaycashdesk"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-redpaycommon"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-redpaygateway"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-redpaymember"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-redpaymerchant"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-red<PERSON><PERSON><PERSON>er"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-redpgy-idls"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-redsettlement-idl"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-redsinglesettlement"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-redsmartengine"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-returns"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-review"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-rf-manualvoucher-idl"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-rfaccountingcenter"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-rfforex"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-rffundgateway"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-rftreasury"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-riskauditcenter"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-rmpproduct"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-robin"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-rocking"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-roms"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-rwms"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-schedule<PERSON>nidl"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-schoolcenter"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-scms"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-searchindexidl"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-seer"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-self_fms"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-seller-operation-center"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-seller_center"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-seller_fms"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-seller_member_center"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-seller_qms"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-sellerregister"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-sentiment-service"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-sfms"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-shop"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-shop2b"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-shoppingguide"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-silk"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-sis"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-skywalker-<PERSON><PERSON><PERSON>an"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-skywalker-platform"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-sleipnir"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-sltms"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-sms"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-solar"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-solarc"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-solomon"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-spaceport"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-sparkle-cms"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-sparkle-promotion"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-spreadsheetcoreidl"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-spucenter"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-starry"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-statement"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-stockage"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-stub_service"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-suite"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-supply_chain_adapter"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-tag-thrift-sdk"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-talentcard-api"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-tgms"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-third_open_service"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-tian<PERSON><PERSON>min"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-tianchidata"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-tian<PERSON><PERSON>is"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-tianchiengine"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-ticket-service"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-ticketservice"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-tms"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-tnc"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-tnmc"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-tnms"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-tnsc"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-trade_core_idl"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-trade_data_center"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-trade_task"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-trade_user"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-trade_utility"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-tradeguide"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-tradeimagecore"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-transformers"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-treasure_chest"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-tsalgo"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-tscenter"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-unified-gateway"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-uniqueid"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-user"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-user-info"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-useraccountservice"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-userinvoicecenter"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-userqms"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-virtual_item"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-vision"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-vision-platform-idl"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-watermark"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-wbms"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-wind_lite_idl"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-wishlist"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-wms"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-wms-api"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-wms-router"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-wordlibrary"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-workflowservice"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-xhs-oa"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-xhsipp"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-xmp"}, {"namespace": "com.xiaohongshu.fls.thrift", "name": "lib-thrift-ziyajiang"}, {"namespace": "com.xiaohongshu.force.thrift", "name": "bail-rpc-client"}, {"namespace": "com.xiaohongshu.force.thrift", "name": "bail-workflow-client"}, {"namespace": "com.xiaohongshu.force.thrift", "name": "carl-rpc-client"}, {"namespace": "com.xiaohongshu.force.thrift", "name": "impersonation"}, {"namespace": "com.xiaohongshu.force.thrift", "name": "it-workspace"}, {"namespace": "com.xiaohongshu.force.thrift", "name": "lando-rpc-client"}, {"namespace": "com.xiaohongshu.force.thrift", "name": "lobot-client"}, {"namespace": "com.xiaohongshu.force.thrift", "name": "meeting-rpc-client"}, {"namespace": "com.xiaohongshu.force.thrift", "name": "panaka-client"}, {"namespace": "com.xiaohongshu.force.thrift", "name": "paploo-pub-client"}, {"namespace": "com.xiaohongshu.force.thrift", "name": "plo-overseas-client"}, {"namespace": "com.xiaohongshu.force.thrift", "name": "plo-paploo-client"}, {"namespace": "com.xiaohongshu.force.thrift", "name": "plo-sdk"}, {"namespace": "com.xiaohongshu.infra", "name": "redtao-sdk-thrift"}, {"namespace": "com.xiaohongshu.infra", "name": "redtao-thrift"}, {"namespace": "com.xiaohongshu.infra.midware", "name": "thrift"}, {"namespace": "com.xiaohongshu.infra.midware", "name": "thrift-api"}, {"namespace": "com.xiaohongshu.infra.midware", "name": "thrift-netty-transport"}, {"namespace": "com.xiaohongshu.infra.midware", "name": "thrift-static"}, {"namespace": "com.xiaohongshu.kasa.thrift", "name": "kasa-cfs-sdk"}, {"namespace": "com.xiaohongshu.kasa.thrift", "name": "kasa-dts-sdk"}, {"namespace": "com.xiaohongshu.kasa.thrift", "name": "kasa-exp-center-sdk"}, {"namespace": "com.xiaohongshu.kasa.thrift", "name": "kasa-gslb-pull-sdk"}, {"namespace": "com.xiaohongshu.kasa.thrift", "name": "kasa-gslb-push-sdk"}, {"namespace": "com.xiaohongshu.kasa.thrift", "name": "kasa-host-data-sdk"}, {"namespace": "com.xiaohongshu.kasa.thrift", "name": "kasa-las-sdk"}, {"namespace": "com.xiaohongshu.kasa.thrift", "name": "kasa-mpds-sdk"}, {"namespace": "com.xiaohongshu.kasa.thrift", "name": "kasa-mrs-sdk"}, {"namespace": "com.xiaohongshu.kasa.thrift", "name": "kasa-pds-sdk"}, {"namespace": "com.xiaohongshu.kasa.thrift", "name": "kasa-watchdog-sdk"}, {"namespace": "com.xiaohongshu.kasa.thrift", "name": "kasa-watchman-sdk"}, {"namespace": "com.xiaohongshu.kasalsp.thrift", "name": "kasalsp-service-rpc-sdk"}, {"namespace": "com.xiaohongshu.media.thrift", "name": "avi-gateway-sdk"}, {"namespace": "com.xiaohongshu.media.thrift", "name": "avinfo-service-parent"}, {"namespace": "com.xiaohongshu.media.thrift", "name": "main-media-center-sdk"}, {"namespace": "com.xiaohongshu.media.thrift", "name": "media-audio-service-sdk"}, {"namespace": "com.xiaohongshu.media.thrift", "name": "media-avinfo-common"}, {"namespace": "com.xiaohongshu.media.thrift", "name": "media-avinfo-service-sdk"}, {"namespace": "com.xiaohongshu.media.thrift", "name": "media-blackwhite-service-sdk"}, {"namespace": "com.xiaohongshu.media.thrift", "name": "media-cdn-service-sdk"}, {"namespace": "com.xiaohongshu.media.thrift", "name": "media-config-strategy-sdk"}, {"namespace": "com.xiaohongshu.media.thrift", "name": "media-image-resource-sdk"}, {"namespace": "com.xiaohongshu.media.thrift", "name": "media-prediction-service-sdk"}, {"namespace": "com.xiaohongshu.media.thrift", "name": "media-upload-service-sdk"}, {"namespace": "com.xiaohongshu.media.thrift", "name": "media-video-service-sdk"}, {"namespace": "com.xiaohongshu.media.thrift", "name": "mediacloud-user-info-sdk"}, {"namespace": "com.xiaohongshu.media.thrift", "name": "play-service-sdk"}, {"namespace": "com.xiaohongshu.media.thrift", "name": "playservice-fallback-sdk"}, {"namespace": "com.xiaohongshu.media.thrift", "name": "playservice-fallbak-sdk"}, {"namespace": "com.xiaohongshu.media.thrift", "name": "redprocess-service-sdk"}, {"namespace": "com.xiaohongshu.media.thrift", "name": "rmms-sdk"}, {"namespace": "com.xiaohongshu.media.thrift", "name": "rrs"}, {"namespace": "com.xiaohongshu.media.thrift", "name": "upload-key-service-sdk"}, {"namespace": "com.xiaohongshu.media.thrift", "name": "video-stream-sdk"}, {"namespace": "com.xiaohongshu.mediacloud.thrift", "name": "media-cloud-service-rpc-sdk"}, {"namespace": "com.xiaohongshu.mediacloud.thrift", "name": "media-imageplay-sdk"}, {"namespace": "com.xiaohongshu.mediacloud.thrift", "name": "video-process-sdk"}, {"namespace": "com.xiaohongshu.moss.thrift", "name": "carl_new"}, {"namespace": "com.xiaohongshu.moss.thrift", "name": "hrp"}, {"namespace": "com.xiaohongshu.moss.thrift", "name": "itservices"}, {"namespace": "com.xiaohongshu.moss.thrift", "name": "ocoperation"}, {"namespace": "com.xiaohongshu.moss.thrift", "name": "officemap"}, {"namespace": "com.xiaohongshu.multicdn.thrift", "name": "multicdn-service-rpc-sdk"}, {"namespace": "com.xiaohongshu.quid.thrift", "name": "quid-ai-sdk"}, {"namespace": "com.xiaohongshu.rec.thrift", "name": "ace-algo-sdk"}, {"namespace": "com.xiaohongshu.rec.thrift", "name": "ace-rec-sdk"}, {"namespace": "com.xiaohongshu.rec.thrift", "name": "ann-service-sdk"}, {"namespace": "com.xiaohongshu.rec.thrift", "name": "ark-common-rec-sdk"}, {"namespace": "com.xiaohongshu.rec.thrift", "name": "ark-coupon-sdk"}, {"namespace": "com.xiaohongshu.rec.thrift", "name": "ark-mallfeed-common-rec-sdk"}, {"namespace": "com.xiaohongshu.rec.thrift", "name": "arkflsnote-idl"}, {"namespace": "com.xiaohongshu.rec.thrift", "name": "cobra-idl"}, {"namespace": "com.xiaohongshu.rec.thrift", "name": "comment-ranking-sdk"}, {"namespace": "com.xiaohongshu.rec.thrift", "name": "dolphin-sdk"}, {"namespace": "com.xiaohongshu.rec.thrift", "name": "featureplatform-meta-idl"}, {"namespace": "com.xiaohongshu.rec.thrift", "name": "follow-feed-sdk"}, {"namespace": "com.xiaohongshu.rec.thrift", "name": "larc-meta-service"}, {"namespace": "com.xiaohongshu.rec.thrift", "name": "local-feed-sdk"}, {"namespace": "com.xiaohongshu.rec.thrift", "name": "login-topic-service-sdk"}, {"namespace": "com.xiaohongshu.rec.thrift", "name": "manta-sdk"}, {"namespace": "com.xiaohongshu.rec.thrift", "name": "note-feed-sdk"}, {"namespace": "com.xiaohongshu.rec.thrift", "name": "note-pool-service-thrift"}, {"namespace": "com.xiaohongshu.rec.thrift", "name": "note-profile-sdk"}, {"namespace": "com.xiaohongshu.rec.thrift", "name": "octopus-thrift"}, {"namespace": "com.xiaohongshu.rec.thrift", "name": "op-meta-service-thrift"}, {"namespace": "com.xiaohongshu.rec.thrift", "name": "penguin-sdk"}, {"namespace": "com.xiaohongshu.rec.thrift", "name": "rec-pymk-sdk"}, {"namespace": "com.xiaohongshu.rec.thrift", "name": "user-profile-sdk"}, {"namespace": "com.xiaohongshu.rec.thrift", "name": "z-profile-service"}, {"namespace": "com.xiaohongshu.redair.thrift", "name": "redair-sdk"}, {"namespace": "com.xiaohongshu.redcity.thrift", "name": "contacts-core-client"}, {"namespace": "com.xiaohongshu.risk.platform", "name": "insight-starter-thrift"}, {"namespace": "com.xiaohongshu.rnft.thrift", "name": "braque-sdk"}, {"namespace": "com.xiaohongshu.rnft.thrift", "name": "carnival-sdk"}, {"namespace": "com.xiaohongshu.rnft.thrift", "name": "cpcr-sdk"}, {"namespace": "com.xiaohongshu.rnft.thrift", "name": "louvre-sdk"}, {"namespace": "com.xiaohongshu.rnft.thrift", "name": "moma-sdk"}, {"namespace": "com.xiaohongshu.rnft.thrift", "name": "monet-sdk"}, {"namespace": "com.xiaohongshu.rnft.thrift", "name": "oasis-sdk"}, {"namespace": "com.xiaohongshu.rnft.thrift", "name": "rspace-sdk"}, {"namespace": "com.xiaohongshu.rnft.thrift", "name": "themet-sdk"}, {"namespace": "com.xiaohongshu.rnft.thrift", "name": "turner-sdk"}, {"namespace": "com.xiaohongshu.search.thrift", "name": "retrieval-proxy-client"}, {"namespace": "com.xiaohongshu.search.thrift", "name": "search-goods-image-sdk"}, {"namespace": "com.xiaohongshu.search.thrift", "name": "search-image-product-sdk"}, {"namespace": "com.xiaohongshu.search.thrift", "name": "search-image-sdk"}, {"namespace": "com.xiaohongshu.search.thrift", "name": "utsearchscheduler-sdk"}, {"namespace": "com.xiaohongshu.search.thrift", "name": "utsearchservice-sdk"}, {"namespace": "com.xiaohongshu.sns", "name": "octopus-thrift-api"}, {"namespace": "com.xiaohongshu.sns", "name": "octopus-thrift-impl"}, {"namespace": "com.xiaohongshu.sns", "name": "penance-stare-thrift"}, {"namespace": "com.xiaohongshu.sns.content.security", "name": "commons-thrift"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "-search-image-producct-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "account-center-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "activity-other-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "activityweb-idl"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "adler-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "ads-search-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "adsaesthetic_model_service"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "aicentricmark"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "aigclink-idl"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "airforce-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "aisdram_model_service"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "aislogan_model_service"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "aislogan_service"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "album_emb_model_service"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "algo-api-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "any-activity-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "any-rank"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "aphro"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "aries-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "ark-liverec"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "ark-vertical-search-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "ark-vertival-search-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "arkaicommdmleads-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "arkdatacenter"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "arkedith0"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "arkedithsearch"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "arkflssearch-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "arkhipposearch-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "arkimagesearch-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "arkkbsearch"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "arklivesearch-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "arknearlinesearch"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "arkqueryrec"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "arksearch-autocomplete"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "arksearch-vertical"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "arksearchleo"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "arksearchqr"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "arksug"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "arkusersearch"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "arkwukong"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "audit-material-center"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "auth-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "authority-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "autocomplete-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "bach-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "badge-client"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "bailongma-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "bamboo-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "bifrost-push"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "bifrostim"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "bifrostim-core"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "bifrostim-domain"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "bifrostim-eventcenter"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "bifrostim-interaction"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "bifrostim-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "bifrostim-search-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "bifrostim-send"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "bootes-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "bootes-tribe-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "brand-center"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "brandcooperate"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "busybee-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "buyer-activity-core-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "buyer-activity-manage-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "buyer-activity-reward-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "buyer-task-engine-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "buyer-task-filter-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "capa-ai-topic-recommend-service"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "capa_challenge2topic_sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "capa_longtext_algorithm_sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "capa_poi_title_rank_service"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "capa_resourcepos_rec_sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "capa_topic_rec"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "capapoititlerec_ranking"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "carter-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "celestial-config-api"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "cerberus"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "charon"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "charon-oversea"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "chopin-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "circle-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "cliometrics"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "cny-conf"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "cny-entry-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "cny-envelope-rain-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "cny-wallet-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "cnylocal-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "cnysnake-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "conan-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "config-strategy"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "content-assembler-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "content-center-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "crazy-sister"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "creator"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "creator-message-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "creator-report-idl"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "creatorportal-idl"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "cron-worker-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "custom-service-guess-idl"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "dafenqi_text2img_service"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "dasheng-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "data-center-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "dejavurecall"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "dish-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "dispatch-center--sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "dispatch-center-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "doc_emb_model_service"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "doc_grap_service"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "doc_rec_rank_service"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "doraemon-idl"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "dorami-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "ds-basic"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "earth"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "earth-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "easy-activity-core-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "easy-activity-manage"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "easy-engine-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "easy-filter-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "easy-lottery-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "easy-rank-data-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "easy-rank-engine-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "easy-rank-filter-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "easy-reward-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "eco-symphony-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "ecological-station-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "ee-workflow-plugin-rpc-client"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "eris"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "euphoria-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "excover-api"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "experimentgroup-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "external-api-gateway-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "fallback-reader-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "fallback-writer-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "fantasy-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "featureplat-hamster-idl"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "flow-coupon-idl"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "flowcontrol-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "flssearch-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "gaia-idl"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "generic-java-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "govern-rpc"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "graffiti_model_service"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "growlanser"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "growth-ads-data-manager-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "growth-ads-delivery-manager-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "growth-delivery-account-manager-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "growth-fastlane-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "growth-lightweight-gateway-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "growth-notice-center-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "growth-tech-idl-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "growth-ug-asset-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "growth_activity_nationalday_media_service"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "guard"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "hashtag_rec_ranking_sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "hercules-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "hermes"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "heuristic-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "hey"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "hongyan-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "horae"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "image2caption_sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "img2img_model_service"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "incentive-idl"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "inspire_search"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "interaction-core"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "interaction-mobile"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "interaction-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "interaction-web"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "invoker-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "janus"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "jay-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "juliet-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "kakaluote-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "kanon-vector-api-idls"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "kiceberg-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "kmessage-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "knowledgebase-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "ktieredstorage-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "lds-thrift-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "leica-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "linksecurity-client"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "live-2o-service"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "live-activity"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "live-activity-template"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "live-admin"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "live-anchor"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "live-api"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "live-base-api"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "live-base-data"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "live-business-api"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "live-ceremony"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "live-chat"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "live-core"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "live-data"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "live-dispatch"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "live-draw"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "live-fallback"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "live-fans-group"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "live-generic-spi"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "live-gift"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "live-goldmine"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "live-goods"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "live-guide"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "live-interaction-trick"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "live-lesson"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "live-lesson-admin"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "live-line"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "live-mall"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "live-mall-2b-api"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "live-mall-activity"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "live-mall-api"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "live-mall-cache"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "live-mcn"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "live-pay"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "live-push"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "live-rank"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "live-rec-api"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "live-room"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "live-service"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "live-service-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "live-square"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "live-stream-frame-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "live-supply"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "live-tag-service"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "live-task"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "live-user-api"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "live-versailles"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "live-video"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "livecomment"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "liveecologyapi"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "livefeedapi"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "livelinecenter"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "livemedia"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "liveqiangua"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "liveroomcenter"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "liveroomcentre"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "livewidgets"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "lms-client-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "lrretriever-inflow-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "lrretriever-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "magneto"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "mainseg_model_service"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "marple-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "mars"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "mars-oversea"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "material-manager-idl"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "material_rank"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "materialsignal_service"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "matrix-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "mcn-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "media-audio-moan"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "media-challenge-embeding"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "media-challenge-ranking"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "media-cike-ranking"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "media-emoji-feed"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "media-multi-vad"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "media-ocr-recall"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "media-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "media-template-matching"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "media-textimg2video"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "media_ai_unity_model_service"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "media_aigc_imagestyle_generate_service"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "media_audio_embedding"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "media_music_recommendation_service"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "mediak8s-aislogandetect-marker2nd"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "mediak8s-audio-aitag"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "mediak8s-audiocomment-asr"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "mediak8s-chatbox-asr"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "mediak8s-movie-match"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "mediak8s-music-moodtagging"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "mediak8s-musiclabel-rpc"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "mediak8s-smartfilter-recommend"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "mediak8s-topic-models"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "mediak8s_aislogan_qwen2vl"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "mediak8s_commonsegmentation_marker2nd"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "mediak8s_personalized_topic_lib_recall"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "mercurius-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "meteor"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "metis"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "minos"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "mipha"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "miracle"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "mobile-profile-service-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "mobius-auth-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "model_service"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "moment_model_service"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "multilanguage-admin-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "music-platform-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "music_slides_service"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "musictag_intelligent_recommender_service"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "naboo-admin-client"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "naboo-portal-client"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "navigator-center-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "neso"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "newyear-person-det"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "nike-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "nlp-onboarding-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "norns-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "note-base-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "note-board-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "note-business"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "note-collection-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "note-comment-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "note-common-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "note-compose-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "note-core"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "note-decorate-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "note-like-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "note-mark-user-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "note-post"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "note-relation-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "note_attach_relevance"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "nova"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "nvwa-ads-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "nvwa-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "octopus-live"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "octopus-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "odysseus"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "olana-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "outerwilds-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "<PERSON><PERSON><PERSON>"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "paddles-api"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "pangu-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "paphos"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "peoplefeed"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "petimg2img_model_service"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "poi-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "post-gateway"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "post-note"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "post-task"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "post-uploader"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "processing-center"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "product-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "push-batman-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "push-hummingbird-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "push-mercury-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "push-rec-ranker-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "push-render-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "quality-note-pool-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "questionnaire-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "rbs-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "red-association-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "red-city-ip-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "red-data-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "red-envelope-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "red-house"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "red-sticker-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "red-tenant-auth-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "red-tenant-passport-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "red-tenant-relation-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "red-tenant-tool-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "red-tenant-user-profile-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "red-topic-api"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "red-user-data-encryption-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "red-user-growth-api"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "red-user-mis-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "red-user-profile-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "red-user-security-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "redchat-api"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "redcity-client"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "reddify-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "redhouse-service"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "redlrretriever-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "redwork"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "release_reason_model_service"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "resourcedispatch"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "risk-hades"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "riskparser"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "rmeta-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "rmp-base-common"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "rmp-base-control-mp"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "rmp-base-control-third"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "rmp-base-data"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "rmp-base-demo"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "rmp-base-info"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "rmp-base-open-mp"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "rmp-base-open-third"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "rmp-base-runtime"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "rmp-base-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "rmpdeal-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "rmpdemo-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "rms"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "rns-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "rts-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "rus-job-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "rus-retry-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "rus-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "schmidt-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "schubert-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "search-api-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "search-image-producct-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "search-image-product-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "search-leo-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "search-mix"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "search-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "serviceGateway-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "shake"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "shake-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "sherlock-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "simlrretriever-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "slark"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "sms-api"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "sns-activity-asset-idl"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "sns-activity-config-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "sns-activity-content-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "sns-activity-crowd-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "sns-activity-portal-idl"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "sns-activity-register-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "sns-activity-task-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "sns-activity-taskflow-idl"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "sns-assist-center-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "sns-bridge-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "sns-business-platform-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "sns-creator-inspiration"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "sns-homefeed-downgrade-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "sns-loki-idl"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "sns-media-idl"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "sns-momo-survey-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "sns-multilanguage-idl"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "sns-note-board-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "sns-note-collection-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "sns-note-like-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "sns-note-metric-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "sns-note-next-step-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "sns-note-pk-vote-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "sns-note-seed-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "sns-note-video-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "sns-note-web-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "sns-note-widgets-idl"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "sns-redtube-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "sns-system-config-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "sns-thoth-decision-idl"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "sns-trading-platform-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "sns-user-account-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "sns-user-feedback-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "sns-user-identity-idl"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "sns-user-login-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "sns-user-onboarding-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "sns-user-permission"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "sns-user-profile-review-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "sns-user-relation-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "sns-user-verify-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "sns.creator.inspiration"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "s<PERSON><PERSON><PERSON><PERSON>"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "snsjupiter-oversea"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "snsusercenter-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "socialinteract-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "sunwukong-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "tars"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "template_intelligent_recommender_service"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "template_recommender_service"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "template_video_recommender_service"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "text2image_keyword4capa_service"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "text2image_style_recommend_service"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "text2img_model_service"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "thanos-center"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "thor-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "time-shift"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "time-shift-oversea"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "title_rec"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "trading-scene-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "translation-callback-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "trend-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "trial-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "uatu-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "user-badge-idl"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "user-profile-service-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "user-relation-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "userpreference-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "userrecommend-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "velen"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "venom-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "voice-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "vulcan"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "vulcan-oversea"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "wallet-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "watson-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "web_creator_cover_recommender"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "wish-lamp-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "wolverine-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "world-cup-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "wow-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "wukong-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "xingtian-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "yugong-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "zhurong-sdk"}, {"namespace": "com.xiaohongshu.sns.thrift", "name": "zootopia-sdk"}, {"namespace": "com.xiaohongshu.speechai.thrift", "name": "music-label-sdk"}, {"namespace": "com.xiaohongshu.speechai.thrift", "name": "spechai-textinsight-sdk"}, {"namespace": "com.xiaohongshu.speechai.thrift", "name": "speechai-asr-sdk"}, {"namespace": "com.xiaohongshu.speechai.thrift", "name": "speechai-caption-sdk"}, {"namespace": "com.xiaohongshu.speechai.thrift", "name": "speechai-check-sdk"}, {"namespace": "com.xiaohongshu.speechai.thrift", "name": "speechai-live-sdk"}, {"namespace": "com.xiaohongshu.speechai.thrift", "name": "speechai-redchat-sdk"}, {"namespace": "com.xiaohongshu.speechai.thrift", "name": "speechai-tts-sdk"}, {"namespace": "com.xiaohongshu.speechai.videonote.thrift", "name": "speechai-videonote-caption-sdk"}, {"namespace": "com.xiaohongshu.vapp.thrift", "name": "vappbase-sdk"}, {"namespace": "com.xiaohongshu.vapp.thrift", "name": "vappfeed-sdk"}, {"namespace": "com.xiaohongshu.vapp.thrift", "name": "vappmaterial-sdk"}, {"namespace": "com.xiaohongshu.vapp.thrift", "name": "vappuser-sdk"}]}]}