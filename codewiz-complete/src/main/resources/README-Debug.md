# IntelliJ IDEA Debug 配置指南

## 两种启动方式

现在有两种方式来运行依赖更新 Runner：

### 方式1: 独立启动 (推荐) 🚀

使用专门的启动类，不会启动 HTTP 服务器，更快更干净。

#### 主启动类:
```
com.xiaohongshu.codewiz.complete.runner.DependencyUpdateRunnerApplication
```

#### VM Options (可选):
```bash
# 所有参数都有默认值，可以不设置任何参数直接运行！
# 如需自定义：
-Dspring.profiles.active=sit
-Dcodewiz.dependency-update.runner.force-refresh=true
-Dcodewiz.dependency-update.runner.batch-size=10
```

### 方式2: 与 HTTP 服务一起启动

使用原来的启动类，同时启动 HTTP 服务器和 Runner。

#### 主启动类:
```
com.xiaohongshu.codewiz.complete.CompleteServerApplication
```

#### VM Options (必需):
```bash
-Dspring.profiles.active=sit
-Dcodewiz.dependency-update.runner.enabled=true
```

## 推荐配置

### 快速开始 (零配置)

1. **右键点击** `DependencyUpdateRunnerApplication.java`
2. **选择** "Debug 'DependencyUpdateRunnerApplication.main()'"
3. **直接运行** - 无需任何额外配置！

### 自定义配置

如果需要自定义参数，创建 Run Configuration：

```
Name: Dependency Update Runner (Debug)
Main class: com.xiaohongshu.codewiz.complete.runner.DependencyUpdateRunnerApplication
VM options: -Dcodewiz.dependency-update.runner.force-refresh=true
Working directory: /Users/<USER>/codebase/codewiz-server
Use classpath of module: codewiz-complete
```

## 默认配置说明

| 参数 | 默认值 | 说明 |
|------|--------|------|
| 配置文件 | `classpath:dependency-update-config.json` | 使用内置配置 |
| 日志目录 | `./logs/dependency-update` | 项目根目录下 |
| 强制刷新 | `false` | 增量更新模式 |
| 批处理大小 | `20` | 每批处理20个依赖 |
| 处理缺失依赖 | `true` | 优先处理缺失依赖表 |
| 缺失依赖批次 | `100` | 每次读取100个 |
| 冷启动版本数 | `5` | 获取最近5个版本 |
| Spring Profile | `sit` | 自动设置 |
| Web 应用类型 | `NONE` | 不启动 HTTP 服务器 |

## 内置配置说明

内置配置文件包含以下示例依赖：

**Java Maven:**
- org.springframework:spring-core
- com.fasterxml.jackson.core:jackson-core  
- org.apache.commons:commons-lang3

**JavaScript NPM:**
- @types:node
- lodash:lodash
- express:express

**Python PyPI:**
- requests
- numpy
- pandas

## Debug 要点

1. **断点位置**: 在 `DependencyUpdateRunner.run()` 方法开始处
2. **日志查看**: 控制台 + `./logs/dependency-update` 目录
3. **启动速度**: 独立启动比完整服务启动快很多
4. **默认行为**: 
   - 先处理缺失依赖表中的依赖
   - 再处理配置文件中的依赖
   - 增量更新模式（只更新新版本）

## 常见问题

**Q: 推荐使用哪种启动方式？**
A: 推荐使用 `DependencyUpdateRunnerApplication`，启动更快，不会启动不必要的 HTTP 服务。

**Q: 如何只处理配置文件中的依赖，不处理缺失依赖表？**
A: 添加参数 `-Dcodewiz.dependency-update.runner.process-missing-dependencies=false`

**Q: 如何强制重新处理所有依赖？**
A: 添加参数 `-Dcodewiz.dependency-update.runner.force-refresh=true`

**Q: 如何使用自定义配置文件？**
A: 添加参数 `-Dcodewiz.dependency-update.runner.config-file=/path/to/config.json`

**Q: 如何调整批处理大小？**
A: 添加参数 `-Dcodewiz.dependency-update.runner.batch-size=50`

**Q: 两种启动方式有什么区别？**
A: 
- `DependencyUpdateRunnerApplication`: 只运行 Runner，不启动 HTTP 服务，更快更轻量
- `CompleteServerApplication`: 启动完整服务，包括 HTTP API，需要手动启用 Runner 