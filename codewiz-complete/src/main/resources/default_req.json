{"dependencies": [{"namespace": "com.xiaohongshu.infra.midware", "name": "mysql-spring", "version": ""}, {"namespace": "com.xiaohongshu.infra.midware", "name": "common-api", "version": ""}, {"namespace": "com.xiaohongshu", "name": "monitor-starter", "version": ""}, {"namespace": "com.xiaohongshu", "name": "utils-log", "version": ""}, {"namespace": "com.xiaohongshu", "name": "events-client", "version": ""}, {"namespace": "com.xiaohongshu", "name": "infra-framework-rpc-spring", "version": ""}, {"namespace": "com.xiaohongshu", "name": "utils-rocketmq", "version": ""}, {"namespace": "com.xiaohongshu", "name": "infra-framework-monitor-impl", "version": ""}, {"namespace": "com.xiaohongshu", "name": "events-client-common", "version": ""}, {"namespace": "com.xiaohongshu.infra.midware", "name": "mq-provider", "version": ""}, {"namespace": "com.xiaohongshu", "name": "utils-http-client", "version": ""}, {"namespace": "com.xiaohongshu", "name": "springframe-plugin", "version": ""}, {"namespace": "com.xiaohongshu", "name": "utils-elasticsearch", "version": ""}, {"namespace": "com.xiaohongshu", "name": "infra-framework-rpc-core", "version": ""}, {"namespace": "com.xiaohongshu", "name": "utils-memcached", "version": ""}, {"namespace": "com.xiaohongshu", "name": "utils-redjob-client", "version": ""}, {"namespace": "com.xiaohongshu", "name": "infra-framework-monitor-legacy-tracing", "version": ""}, {"namespace": "com.xiaohongshu", "name": "infra-framework-utils", "version": ""}, {"namespace": "com.xiaohongshu", "name": "rpc-context", "version": ""}, {"namespace": "com.xiaohongshu.infra.midware", "name": "monitor", "version": ""}, {"namespace": "com.xiaohongshu.infra.midware", "name": "midware-common", "version": ""}, {"namespace": "com.xiaohongshu", "name": "rpc-thrift-plugin", "version": ""}, {"namespace": "com.xiaohongshu", "name": "apollo-client-helper", "version": ""}, {"namespace": "com.xiaohongshu", "name": "utils-jedis", "version": ""}, {"namespace": "com.xiaohongshu", "name": "thunderbolt", "version": ""}, {"namespace": "com.xiaohongshu", "name": "utils-mysql", "version": ""}, {"namespace": "com.xiaohongshu", "name": "infra-framework-common", "version": ""}, {"namespace": "com.xiaohongshu", "name": "thrift-springboot", "version": ""}, {"namespace": "com.xiaohongshu", "name": "infra-redis-client-core", "version": ""}, {"namespace": "com.xiaohongshu", "name": "thrift-rpc", "version": ""}, {"namespace": "com.xiaohongshu", "name": "infra-framework-monitor-legacy-metrics", "version": ""}, {"namespace": "com.xiaohongshu.infra.midware", "name": "thrift", "version": ""}, {"namespace": "com.xiaohongshu", "name": "utils-mongodb", "version": ""}, {"namespace": "com.xiaohongshu.infra.midware", "name": "common-spring", "version": ""}, {"namespace": "com.xiaohongshu", "name": "infra-framework-xds", "version": ""}, {"namespace": "com.xiaohongshu", "name": "utils-rabbitmq", "version": ""}, {"namespace": "com.xiaohongshu.infra.midware", "name": "common-env", "version": ""}, {"namespace": "com.xiaohongshu.infra.midware", "name": "redis-spring", "version": ""}, {"namespace": "com.xiaohongshu", "name": "sentinel", "version": ""}, {"namespace": "com.xiaohongshu", "name": "infra-framework-monitor-base", "version": ""}, {"namespace": "com.xiaohongshu.infra.midware", "name": "common-static", "version": ""}, {"namespace": "com.xiaohongshu", "name": "apm", "version": ""}, {"namespace": "com.xiaohongshu.infra.midware", "name": "redis", "version": ""}, {"namespace": "com.xiaohongshu", "name": "infra-framework-monitor-legacy-starter", "version": ""}, {"namespace": "com.xiaohongshu", "name": "gateway-starter", "version": ""}, {"namespace": "com.xiaohongshu", "name": "infra-framework-rpc-mq-common", "version": ""}, {"namespace": "com.xiaohongshu.infra.midware", "name": "redis-lite", "version": ""}, {"namespace": "com.xiaohongshu", "name": "utils-utility", "version": ""}, {"namespace": "com.xiaohongshu", "name": "streaming", "version": ""}, {"namespace": "com.xiaohongshu", "name": "infra-framework-rpc-base", "version": ""}, {"namespace": "com.xiaohongshu.infra.midware", "name": "mq-api", "version": ""}], "dependencyType": "maven", "language": "java"}