#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志上报接口压测脚本 - 基于Locust实现

启动方式:
1. 指定批次大小启动 (推荐):
   BATCH_SIZE=10 locust -f locustfile.py --host=http://codewiz.devops.beta.xiaohongshu.com
   BATCH_SIZE=20 locust -f locustfile.py --host=http://codewiz.devops.beta.xiaohongshu.com
   BATCH_SIZE=50 locust -f locustfile.py --host=http://codewiz.devops.beta.xiaohongshu.com

2. Web UI 模式:
   BATCH_SIZE=10 locust -f locustfile.py --host=http://codewiz.devops.beta.xiaohongshu.com
   然后打开浏览器访问: http://localhost:8089

3. 无头模式示例:
   BATCH_SIZE=20 locust -f locustfile.py --host=http://codewiz.devops.beta.xiaohongshu.com \
                        -u 10 -r 5 -t 5m --headless

4. 混合批次大小 (如果不指定BATCH_SIZE):
   locust -f locustfile.py --host=http://codewiz.devops.beta.xiaohongshu.com

用户类说明:
- SpecificBatchUser: 指定批次大小，使用对应的.gz文件 (当设置BATCH_SIZE时使用)
- FixedBatchUser: 固定批次大小，动态生成并压缩数据 (混合模式时使用)
- RandomBatchUser: 随机批次大小，动态生成并压缩数据 (混合模式时使用)

注意：所有用户类都使用gzip压缩数据传输
"""

import json
import time
import uuid
import random
import gzip
import os
from datetime import datetime
from locust import HttpUser, task, between
from typing import Dict


# 配置选项 - 可根据需要调整
BATCH_SIZES = [10, 20, 50]
LOG_SIZE_KB = 1
TARGET_QPS_PER_USER = 1  # 每个用户每秒的请求数

# 从环境变量读取指定的批次大小
SPECIFIED_BATCH_SIZE = os.getenv('BATCH_SIZE')
if SPECIFIED_BATCH_SIZE:
    try:
        SPECIFIED_BATCH_SIZE = int(SPECIFIED_BATCH_SIZE)
        if SPECIFIED_BATCH_SIZE not in BATCH_SIZES:
            print(f"❌ 错误: BATCH_SIZE={SPECIFIED_BATCH_SIZE} 不在支持的范围内 {BATCH_SIZES}")
            SPECIFIED_BATCH_SIZE = None
        else:
            print(f"✅ 检测到指定批次大小: {SPECIFIED_BATCH_SIZE}")
    except ValueError:
        print(f"❌ 错误: BATCH_SIZE='{os.getenv('BATCH_SIZE')}' 不是有效的数字")
        SPECIFIED_BATCH_SIZE = None


class SpecificBatchUser(HttpUser):
    """
    指定批次大小的日志上报用户 (通过环境变量BATCH_SIZE指定)
    
    特点:
    - 使用环境变量指定的批次大小
    - 优先使用预生成的.gz压缩文件
    - 如果没有.gz文件则动态生成并压缩
    - 精确的QPS控制
    """
    
    # 只有设置了BATCH_SIZE才使用这个用户类
    weight = 1 if SPECIFIED_BATCH_SIZE else 0
    wait_time = between(1, 1)  # 每用户每秒1次请求
    
    def on_start(self):
        """用户初始化"""
        if not SPECIFIED_BATCH_SIZE:
            raise ValueError("SpecificBatchUser 需要设置环境变量 BATCH_SIZE")
            
        self.batch_size = SPECIFIED_BATCH_SIZE
        self.user_id = f"specific_user_{random.randint(1, 10000)}"
        self.workspace_uri = f"/workspace/specific_test_{random.randint(1, 100)}"
        self.session_id = f"session_{uuid.uuid4().hex[:8]}"
        
        # 尝试加载指定批次大小的压缩数据
        self.compressed_data = None
        file_path = f"request_batch_{self.batch_size}.json.gz"
        
        if os.path.exists(file_path):
            with open(file_path, 'rb') as f:
                self.compressed_data = f.read()
            print(f"✅ 用户 {self.user_id} 启动 - 使用预生成数据: {file_path}")
        else:
            print(f"⚠️ 用户 {self.user_id} 启动 - 未找到 {file_path}，将动态生成并压缩数据")
            
        print(f"📊 批次大小: {self.batch_size}")

    def generate_and_compress_data(self) -> bytes:
        """动态生成数据并压缩"""
        logs = []
        for i in range(self.batch_size):
            current_time = datetime.now().isoformat()
            
            # 生成约1KB的日志内容
            base_msg = f"指定批次压测日志 - 时间: {current_time} - 用户: {self.user_id} - 索引: {i} - UUID: {uuid.uuid4()}"
            target_bytes = LOG_SIZE_KB * 1024
            current_bytes = len(base_msg.encode('utf-8'))
            remaining_bytes = max(0, target_bytes - current_bytes)
            
            if remaining_bytes > 0:
                base_msg += " " + "X" * remaining_bytes
            
            log_entry = {
                "level": random.choice(["INFO", "DEBUG", "WARN", "ERROR"]),
                "msg": base_msg,
                "ide": "idea",
                "module": f"module_{random.choice(['core', 'ui', 'auth', 'api', 'service'])}",
                "clientTimestamp": current_time,
                "traceId": f"trace_{uuid.uuid4().hex[:16]}",
                "sessionId": self.session_id,
                "requestId": f"req_{uuid.uuid4().hex[:12]}",
                "env": {
                    "userId": self.user_id,
                    "workspaceUri": self.workspace_uri,
                    "pluginVersion": "1.0.0",
                    "os": random.choice(["Windows", "macOS", "Linux"])
                }
            }
            logs.append(log_entry)
        
        request_data = {"logs": logs}
        json_str = json.dumps(request_data, ensure_ascii=False)
        return gzip.compress(json_str.encode('utf-8'))

    @task
    def upload_logs_specific_batch(self):
        """上报指定批次大小的日志"""
        # 使用预生成数据或动态生成并压缩
        if self.compressed_data:
            data = self.compressed_data
        else:
            data = self.generate_and_compress_data()
        
        headers = {
            'Content-Type': 'application/json',
            'Content-Encoding': 'gzip',
            'User-Agent': 'LocustStressTest/1.0'
        }
        
        with self.client.post(
            "/complete/api/v1/logs/batch",
            data=data,
            headers=headers,
            catch_response=True,
            name=f"/complete/api/v1/logs/batch [batch_size={self.batch_size}]"
        ) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"状态码: {response.status_code}, 响应: {response.text[:200]}")


class FixedBatchUser(HttpUser):
    """
    固定批次大小的日志上报用户
    
    特点:
    - 每个用户固定使用一种批次大小
    - 动态生成数据并gzip压缩
    - QPS控制精确 (每用户每秒1次请求)
    """
    
    # 只有未设置BATCH_SIZE才使用这个用户类
    weight = 0 if SPECIFIED_BATCH_SIZE else 1
    wait_time = between(1, 1)  # 每用户每秒1次请求
    
    def on_start(self):
        """用户初始化"""
        # 随机选择一个批次大小，整个生命周期保持不变
        self.batch_size = random.choice(BATCH_SIZES)
        self.user_id = f"fixed_user_{random.randint(1, 10000)}"
        self.workspace_uri = f"/workspace/fixed_test_{random.randint(1, 100)}"
        self.session_id = f"session_{uuid.uuid4().hex[:8]}"
        
        print(f"✅ 固定批次用户 {self.user_id} 启动 - 批次大小: {self.batch_size}")

    def generate_log_content(self, target_size_kb: int = LOG_SIZE_KB) -> str:
        """生成指定大小的日志内容"""
        target_bytes = target_size_kb * 1024
        
        # 基础内容 - 包含时间戳和UUID确保唯一性
        base_content = f"固定批次压测日志 - 时间: {datetime.now().isoformat()} - 用户: {self.user_id} - UUID: {uuid.uuid4()}"
        
        # 计算需要填充的字符数
        current_bytes = len(base_content.encode('utf-8'))
        remaining_bytes = max(0, target_bytes - current_bytes)
        
        # 生成填充内容
        if remaining_bytes > 0:
            filler = "X" * remaining_bytes
            base_content += " " + filler
            
        return base_content

    def create_log_entry(self, index: int) -> Dict:
        """创建单条日志条目"""
        current_time = datetime.now().isoformat()
        
        return {
            "level": random.choice(["INFO", "DEBUG", "WARN", "ERROR"]),
            "msg": self.generate_log_content(),
            "ide": "idea",
            "module": f"module_{random.choice(['core', 'ui', 'auth', 'api', 'service'])}",
            "clientTimestamp": current_time,
            "traceId": f"trace_{uuid.uuid4().hex[:16]}",
            "sessionId": self.session_id,
            "requestId": f"req_{uuid.uuid4().hex[:12]}",
            "env": {
                "userId": self.user_id,
                "workspaceUri": self.workspace_uri,
                "pluginVersion": "1.0.0",
                "os": random.choice(["Windows", "macOS", "Linux"])
            }
        }

    def create_batch_request_and_compress(self) -> bytes:
        """创建批量请求并压缩"""
        logs = []
        for i in range(self.batch_size):
            logs.append(self.create_log_entry(i))
        
        request_data = {"logs": logs}
        
        # 转换为JSON并压缩
        json_str = json.dumps(request_data, ensure_ascii=False)
        return gzip.compress(json_str.encode('utf-8'))

    @task
    def upload_logs_batch(self):
        """上报批量日志 - 动态生成并gzip压缩"""
        compressed_data = self.create_batch_request_and_compress()
        
        headers = {
            'Content-Type': 'application/json',
            'Content-Encoding': 'gzip',
            'User-Agent': 'LocustStressTest/1.0'
        }
        
        with self.client.post(
            "/complete/api/v1/logs/batch",
            data=compressed_data,
            headers=headers,
            catch_response=True,
            name=f"/complete/api/v1/logs/batch [batch_size={self.batch_size}]"
        ) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"状态码: {response.status_code}, 响应: {response.text[:200]}")


class RandomBatchUser(HttpUser):
    """
    随机批次大小的日志上报用户
    
    特点:
    - 每次请求随机选择批次大小
    - 动态生成数据并gzip压缩
    - 模拟真实场景中的混合负载
    """
    
    # 只有未设置BATCH_SIZE才使用这个用户类
    weight = 0 if SPECIFIED_BATCH_SIZE else 1
    wait_time = between(1, 1)  # 每用户每秒1次请求
    
    def on_start(self):
        """用户初始化"""
        self.user_id = f"random_user_{random.randint(1, 10000)}"
        self.workspace_uri = f"/workspace/random_test_{random.randint(1, 100)}"
        self.session_id = f"session_{uuid.uuid4().hex[:8]}"
        
        print(f"✅ 随机批次用户 {self.user_id} 启动")

    def generate_log_content(self, target_size_kb: int = LOG_SIZE_KB) -> str:
        """生成指定大小的日志内容"""
        target_bytes = target_size_kb * 1024
        base_content = f"随机批次压测日志 - 时间: {datetime.now().isoformat()} - 用户: {self.user_id} - UUID: {uuid.uuid4()}"
        
        current_bytes = len(base_content.encode('utf-8'))
        remaining_bytes = max(0, target_bytes - current_bytes)
        
        if remaining_bytes > 0:
            filler = "X" * remaining_bytes
            base_content += " " + filler
            
        return base_content

    def create_log_entry(self, index: int, batch_size: int) -> Dict:
        """创建单条日志条目"""
        current_time = datetime.now().isoformat()
        
        return {
            "level": random.choice(["INFO", "DEBUG", "WARN", "ERROR"]),
            "msg": self.generate_log_content(),
            "ide": "idea",
            "module": f"module_{random.choice(['core', 'ui', 'auth', 'api', 'service'])}",
            "clientTimestamp": current_time,
            "traceId": f"trace_{uuid.uuid4().hex[:16]}",
            "sessionId": self.session_id,
            "requestId": f"req_{uuid.uuid4().hex[:12]}",
            "env": {
                "userId": self.user_id,
                "workspaceUri": self.workspace_uri,
                "pluginVersion": "1.0.0",
                "os": random.choice(["Windows", "macOS", "Linux"])
            }
        }

    def create_batch_request_and_compress(self, batch_size: int) -> bytes:
        """创建批量请求并压缩"""
        logs = []
        for i in range(batch_size):
            logs.append(self.create_log_entry(i, batch_size))
        
        request_data = {"logs": logs}
        
        # 转换为JSON并压缩
        json_str = json.dumps(request_data, ensure_ascii=False)
        return gzip.compress(json_str.encode('utf-8'))

    @task
    def upload_logs_random_batch(self):
        """上报随机批次大小的日志"""
        batch_size = random.choice(BATCH_SIZES)
        compressed_data = self.create_batch_request_and_compress(batch_size)
        
        headers = {
            'Content-Type': 'application/json',
            'Content-Encoding': 'gzip',
            'User-Agent': 'LocustStressTest/1.0'
        }
        
        with self.client.post(
            "/complete/api/v1/logs/batch",
            data=compressed_data,
            headers=headers,
            catch_response=True,
            name=f"/complete/api/v1/logs/batch [batch_size={batch_size}]"
        ) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"状态码: {response.status_code}, 响应: {response.text[:200]}")


# 帮助函数
def run_stress_test(batch_size=10, target_qps=5, duration_minutes=10):
    """
    便捷函数 - 生成启动命令
    
    参数:
    - batch_size: 批次大小 (10, 20, 50)
    - target_qps: 目标QPS
    - duration_minutes: 持续时间（分钟）
    
    返回启动命令字符串
    """
    cmd = f"""
指定批次大小启动 (推荐):
BATCH_SIZE={batch_size} locust -f locustfile.py --host=http://codewiz.devops.beta.xiaohongshu.com \\
                       -u {target_qps} -r {min(target_qps, 10)} -t {duration_minutes}m --headless

或者启动 Web UI:
BATCH_SIZE={batch_size} locust -f locustfile.py --host=http://codewiz.devops.beta.xiaohongshu.com

然后在浏览器中访问: http://localhost:8089
设置参数:
- Number of users: {target_qps}
- Spawn rate: {min(target_qps, 10)}
- Duration: {duration_minutes}m (可选)
"""
    print(cmd)
    return cmd


if __name__ == "__main__":
    # 如果直接运行此文件，显示使用说明
    print("""
===========================================
📖 日志上报压测脚本使用说明
===========================================

🚀 启动方式:

1. 指定批次大小 (推荐):
   BATCH_SIZE=10 locust -f locustfile.py --host=http://codewiz.devops.beta.xiaohongshu.com
   BATCH_SIZE=20 locust -f locustfile.py --host=http://codewiz.devops.beta.xiaohongshu.com
   BATCH_SIZE=50 locust -f locustfile.py --host=http://codewiz.devops.beta.xiaohongshu.com

2. Web UI 模式:
   BATCH_SIZE=20 locust -f locustfile.py --host=http://codewiz.devops.beta.xiaohongshu.com
   然后访问: http://localhost:8089

3. 无头模式:
   BATCH_SIZE=10 locust -f locustfile.py --host=http://codewiz.devops.beta.xiaohongshu.com \\
                        -u 10 -r 5 -t 5m --headless

4. 混合批次大小 (不指定BATCH_SIZE):
   locust -f locustfile.py --host=http://codewiz.devops.beta.xiaohongshu.com

🎯 用户类说明:

当设置 BATCH_SIZE 环境变量时:
- 使用 SpecificBatchUser
- 优先使用 request_batch_{BATCH_SIZE}.json.gz 文件
- 如果文件不存在，则动态生成数据并压缩

当不设置 BATCH_SIZE 时 (混合模式):
- FixedBatchUser: 固定批次大小，动态生成并压缩
- RandomBatchUser: 随机批次大小，动态生成并压缩

💡 QPS 控制:
- 用户数 = 目标QPS
- 每个用户每秒发送1次请求
- 例如: 10个用户 = 10 QPS

📁 预生成文件 (可选，提升性能):
- request_batch_10.json.gz
- request_batch_20.json.gz
- request_batch_50.json.gz

⚙️ 参数说明:
- BATCH_SIZE: 环境变量，指定批次大小 (10, 20, 50)
- -u: 用户数 (目标QPS)
- -r: 启动速率 (每秒启动多少用户)
- -t: 运行时间 (如: 5m, 30s, 1h)
- --headless: 无头模式

💡 最佳实践:
1. 使用 BATCH_SIZE 环境变量指定批次大小
2. 如果有对应的 .json.gz 文件会自动使用，提升性能
3. 所有数据都使用 gzip 压缩传输
4. 先进行小规模测试验证连通性
===========================================
""")
    
    # 显示当前配置
    if SPECIFIED_BATCH_SIZE:
        print(f"\n🎯 当前指定批次大小: {SPECIFIED_BATCH_SIZE}")
        file_path = f"request_batch_{SPECIFIED_BATCH_SIZE}.json.gz"
        if os.path.exists(file_path):
            print(f"✅ 找到预生成文件: {file_path} - 将优先使用")
        else:
            print(f"⚠️ 未找到预生成文件: {file_path}")
            print(f"   将动态生成数据并压缩")
    else:
        print(f"\n💡 未指定批次大小，将使用混合模式 (FixedBatchUser + RandomBatchUser)")
        available_files = []
        for batch_size in BATCH_SIZES:
            file_path = f"request_batch_{batch_size}.json.gz"
            if os.path.exists(file_path):
                available_files.append(file_path)
        if available_files:
            print(f"📁 检测到预生成文件: {available_files}")
            print(f"   注意：混合模式下不会使用这些文件，建议指定BATCH_SIZE")
        else:
            print(f"📁 未找到任何预生成文件，将全部动态生成并压缩") 