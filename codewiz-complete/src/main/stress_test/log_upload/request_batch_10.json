{"logs": [{"level": "ERROR", "msg": "Mock日志内容 - 时间戳: 2025-06-30T16:17:21.536341 - UUID: e3a595fd-f6c4-40ed-be6e-1f0fc81b3fdf 这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。", "ide": "idea", "module": "module_ls", "clientTimestamp": "2025-06-30T16:17:21.536328", "traceId": "trace_38ed6273a0804d1e", "sessionId": "test_session_10_1", "requestId": "req_6adde83eb65a", "env": {"userId": "test_user_1", "workspaceUri": "/workspace/test_project_1", "pluginVersion": "1.0.0", "os": "Windows"}}, {"level": "ERROR", "msg": "Mock日志内容 - 时间戳: 2025-06-30T16:17:21.536373 - UUID: 5ed73409-5469-41fb-b230-6124fe27a5b0 这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。", "ide": "idea", "module": "module_webview", "clientTimestamp": "2025-06-30T16:17:21.536371", "traceId": "trace_20acf85840e04c66", "sessionId": "test_session_10_1", "requestId": "req_da3fc5253fb7", "env": {"userId": "test_user_1", "workspaceUri": "/workspace/test_project_1", "pluginVersion": "1.0.0", "os": "macOS"}}, {"level": "ERROR", "msg": "Mock日志内容 - 时间戳: 2025-06-30T16:17:21.536388 - UUID: 71beb8d1-81ff-4efd-85ad-151d3cac3b05 这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。", "ide": "idea", "module": "module_webview", "clientTimestamp": "2025-06-30T16:17:21.536387", "traceId": "trace_ad92a012495e4c6a", "sessionId": "test_session_10_1", "requestId": "req_452eb9f291b4", "env": {"userId": "test_user_1", "workspaceUri": "/workspace/test_project_1", "pluginVersion": "1.0.0", "os": "macOS"}}, {"level": "WARN", "msg": "Mock日志内容 - 时间戳: 2025-06-30T16:17:21.536402 - UUID: 855c86f0-29ec-46e6-b694-3f43db433bad 这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。", "ide": "idea", "module": "module_plugin", "clientTimestamp": "2025-06-30T16:17:21.536401", "traceId": "trace_60711c2d8b2f4395", "sessionId": "test_session_10_1", "requestId": "req_7b98f7f4c69e", "env": {"userId": "test_user_1", "workspaceUri": "/workspace/test_project_1", "pluginVersion": "1.0.0", "os": "Linux"}}, {"level": "INFO", "msg": "Mock日志内容 - 时间戳: 2025-06-30T16:17:21.536416 - UUID: 09c6118f-6479-4f7f-a9b3-4a258a50d4b4 这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。", "ide": "idea", "module": "module_webview", "clientTimestamp": "2025-06-30T16:17:21.536414", "traceId": "trace_82e56408fd4b442d", "sessionId": "test_session_10_1", "requestId": "req_fc0cf7b16f11", "env": {"userId": "test_user_1", "workspaceUri": "/workspace/test_project_1", "pluginVersion": "1.0.0", "os": "Windows"}}, {"level": "INFO", "msg": "Mock日志内容 - 时间戳: 2025-06-30T16:17:21.536429 - UUID: f791f11d-625b-4291-a00a-a805ef41c415 这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。", "ide": "idea", "module": "module_ls", "clientTimestamp": "2025-06-30T16:17:21.536428", "traceId": "trace_c44b18e374f54aa9", "sessionId": "test_session_10_1", "requestId": "req_b5bb2f5c07be", "env": {"userId": "test_user_1", "workspaceUri": "/workspace/test_project_1", "pluginVersion": "1.0.0", "os": "Windows"}}, {"level": "WARN", "msg": "Mock日志内容 - 时间戳: 2025-06-30T16:17:21.536442 - UUID: 54fcf3a0-485d-4e03-b5bc-503d645713ee 这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。", "ide": "idea", "module": "module_webview", "clientTimestamp": "2025-06-30T16:17:21.536441", "traceId": "trace_6b696fe825ce454d", "sessionId": "test_session_10_1", "requestId": "req_e08decc1315f", "env": {"userId": "test_user_1", "workspaceUri": "/workspace/test_project_1", "pluginVersion": "1.0.0", "os": "Windows"}}, {"level": "WARN", "msg": "Mock日志内容 - 时间戳: 2025-06-30T16:17:21.536455 - UUID: 51251491-ca18-448c-abb9-86f890e81856 这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。", "ide": "idea", "module": "module_webview", "clientTimestamp": "2025-06-30T16:17:21.536454", "traceId": "trace_96059fd8c33c4588", "sessionId": "test_session_10_1", "requestId": "req_b7eb41f0333b", "env": {"userId": "test_user_1", "workspaceUri": "/workspace/test_project_1", "pluginVersion": "1.0.0", "os": "macOS"}}, {"level": "WARN", "msg": "Mock日志内容 - 时间戳: 2025-06-30T16:17:21.536470 - UUID: c84a0fa7-53d2-44a4-aa48-dbbaceede771 这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。", "ide": "idea", "module": "module_ls", "clientTimestamp": "2025-06-30T16:17:21.536469", "traceId": "trace_b65d983171a541e2", "sessionId": "test_session_10_1", "requestId": "req_debb3a5a1abb", "env": {"userId": "test_user_1", "workspaceUri": "/workspace/test_project_1", "pluginVersion": "1.0.0", "os": "Windows"}}, {"level": "WARN", "msg": "Mock日志内容 - 时间戳: 2025-06-30T16:17:21.536483 - UUID: 30b87ef8-c0fc-44b4-8c7e-d07664d07572 这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。", "ide": "idea", "module": "module_ls", "clientTimestamp": "2025-06-30T16:17:21.536482", "traceId": "trace_13077f3236a4455b", "sessionId": "test_session_10_1", "requestId": "req_caa94bf4d27b", "env": {"userId": "test_user_1", "workspaceUri": "/workspace/test_project_1", "pluginVersion": "1.0.0", "os": "Windows"}}]}