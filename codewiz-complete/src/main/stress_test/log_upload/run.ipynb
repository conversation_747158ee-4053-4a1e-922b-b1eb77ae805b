#%%
# 设置压测环境
# 调用staging环境
URL = "http://codewiz.devops.beta.xiaohongshu.com"
UPLOAD = "/complete/api/v1/logs/batch"
#%%
# 简单调用，接口连通性测试
import requests

# 读取压缩文件的原始字节数据
with open('request_batch_10.json.gz', 'rb') as f:
    compressed_data = f.read()

# 发送POST请求验证接口连通性
response = requests.post(
    URL + UPLOAD,
    data=compressed_data,
    headers={
        'Content-Type': 'application/json',
        'Content-Encoding': 'gzip'
    }
)

# 打印响应结果
print(f"状态码: {response.status_code}")
print(f"响应头: {response.headers}")
print(f"响应内容: {response.text}")

#%% raw
