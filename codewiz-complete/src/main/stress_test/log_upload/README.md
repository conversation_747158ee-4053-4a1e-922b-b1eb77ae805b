# 日志上报接口压测工具

## 文件说明

### 压测脚本
- `locustfile.py` - 基于Locust的专业压测脚本
- `mock_log_generator.py` - Mock日志请求生成器
- `requirements.txt` - Python依赖包

### Mock请求文件
已生成的示例请求文件（每条日志约1KB）:

| 文件名 | 批次大小 | 原始大小 | 压缩大小 | 压缩率 |
|--------|---------|---------|---------|--------|
| `request_batch_10.json` | 10条日志 | 16KB | 4KB | 75% |
| `request_batch_20.json` | 20条日志 | 32KB | 4KB | 87.5% |
| `request_batch_50.json` | 50条日志 | 76KB | 4KB | 94.7% |

对应的gzip压缩文件:
- `request_batch_10.json.gz`
- `request_batch_20.json.gz`
- `request_batch_50.json.gz`

## 使用方法

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 生成新的Mock请求（可选）
```bash
# 生成默认批次大小的请求 (10, 20, 50)，同时生成JSON和GZ格式
python3 mock_log_generator.py

# 生成自定义批次大小的请求
python3 mock_log_generator.py --batch-sizes 5 15 25

# 生成不同日志大小的请求 (默认1KB)
python3 mock_log_generator.py --log-size 2

# 完整示例：生成批次大小10、30、60，每条日志0.5KB
python3 mock_log_generator.py --batch-sizes 10 30 60 --log-size 0.5
```

**输出文件说明：**
- 每个批次大小会生成两个文件：
  - `request_batch_{size}.json` - 原始JSON文件
  - `request_batch_{size}.json.gz` - GZ压缩文件
- 文件格式为单个请求对象：`{"logs": [...]}`

### 3. 运行Locust压测

#### Web界面模式
```bash
locust -f locustfile.py --host=http://localhost:8080
```
然后访问 http://localhost:8089 配置压测参数。

#### 命令行模式
```bash
# 50个并发用户，每秒启动10个用户，运行5分钟
locust -f locustfile.py --host=http://localhost:8080 -u 50 -r 10 -t 5m --headless

# 100个并发用户，每秒启动20个用户，运行10分钟
locust -f locustfile.py --host=http://localhost:8080 -u 100 -r 20 -t 10m --headless
```

## 压测配置

### 关键参数
- **单条日志大小**: 约1KB（实际msg内容约992字节）
- **IDE**: 固定为"idea"
- **批次大小**: 10、20、50条（随机选择）
- **日志级别**: INFO、DEBUG、WARN、ERROR（随机）
- **模块**: core、ui、auth、api、service、config、util、data（随机）

### 日志数据结构
每条日志包含:
```json
{
  "level": "INFO",
  "msg": "1KB大小的日志内容...",
  "ide": "idea",
  "module": "module_core",
  "clientTimestamp": "2025-06-30T15:33:45.318697",
  "traceId": "trace_6e3b3493f00243f9",
  "sessionId": "test_session_20_1",
  "requestId": "req_fa3c5b9464c9",
  "env": {
    "userId": "test_user_1",
    "workspaceUri": "/workspace/test_project_1",
    "pluginVersion": "1.0.0",
    "os": "Windows"
  }
}
```

## 压缩效果

gzip压缩显示出很好的压缩效果:
- 10条日志: 16KB → 4KB (压缩率75%)
- 20条日志: 32KB → 4KB (压缩率87.5%)
- 50条日志: 76KB → 4KB (压缩率94.7%)

这说明日志内容具有很高的重复性，适合使用gzip压缩传输。

## 压测场景

Locust脚本提供两种用户类型:

1. **LogUploadUser**: 分别测试批次大小10、20、50（权重相等）
2. **LogUploadUserWithRandomBatch**: 随机选择批次大小进行测试

每个用户会:
- 随机选择批次大小
- 生成对应数量的1KB日志
- 发送POST请求到 `/api/v1/logs/batch`
- 监控响应时间和成功率

## ⚠️ 重要说明 - 异步处理机制

### 客户端延迟 vs 实际处理时间
这个接口采用**异步处理**模式：

🔸 **客户端测量的延迟** (压测工具显示的响应时间)：
- HTTP请求解析和验证
- 生成batchId和启动异步任务  
- 返回接受响应 (`200 OK`)
- **通常在几毫秒内完成**

🔸 **实际的日志处理** (在后台异步进行)：
- 逐条输出日志到Logger
- 监控指标收集和统计
- 实际的数据持久化处理
- **真正的业务处理时间**

### 压测意义
- **接口吞吐能力**: 测试服务器能接受多少并发请求
- **请求分发性能**: 验证异步任务启动的效率
- **系统稳定性**: 在高并发下接口是否稳定
- **资源消耗**: 观察CPU、内存、线程池的使用情况

⚠️ **不要以响应时间判断系统处理能力**，而应关注：
- 请求成功率 
- 服务器资源使用率
- 异步任务队列积压情况
- 实际日志输出的速率

## 注意事项

1. **服务器配置**: 确保目标服务器能处理压测流量
2. **网络环境**: 建议内网测试避免网络延迟  
3. **监控重点**: 关注服务器处理能力而非客户端响应时间
4. **系统监控**: 同时监控CPU、内存、线程池、异步队列等
5. **业务指标**: 观察实际日志输出速率和数据完整性

## 📈 Locust压测输出

Locust会提供详细的统计信息，重点关注：

### 关键指标
- **请求成功率**: 应该接近100%
- **请求速率 (RPS)**: 服务器能接受的每秒请求数
- **响应时间分布**: 异步接口通常响应很快(几ms)
- **不同批次大小的性能对比**: batch_10 vs batch_20 vs batch_50

### 服务器端监控 (更重要)
- **CPU使用率**: 异步处理线程的CPU消耗
- **内存使用**: 请求缓存和异步队列占用
- **线程池状态**: `logUploadExecutor`的活跃线程数
- **日志输出速率**: 实际Logger的输出频率

## 故障排查

1. **高请求成功率但服务器压力大**: 正常，异步处理在后台消耗资源
2. **响应时间突然增加**: 可能异步队列积压，检查线程池配置
3. **请求失败**: 检查请求格式、网络连接、服务器状态
4. **服务器资源耗尽**: 调整并发数或检查异步处理逻辑 