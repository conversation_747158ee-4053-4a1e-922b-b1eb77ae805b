{"logs": [{"level": "INFO", "msg": "Mock日志内容 - 时间戳: 2025-06-30T16:17:21.540797 - UUID: b1157c88-2e6a-43d8-a8b0-6932d5878be6 这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。", "ide": "idea", "module": "module_ls", "clientTimestamp": "2025-06-30T16:17:21.540794", "traceId": "trace_e9d5fc2a451f4ae8", "sessionId": "test_session_50_1", "requestId": "req_4e36db801985", "env": {"userId": "test_user_1", "workspaceUri": "/workspace/test_project_1", "pluginVersion": "1.0.0", "os": "Linux"}}, {"level": "ERROR", "msg": "Mock日志内容 - 时间戳: 2025-06-30T16:17:21.540818 - UUID: 7b9b2ac1-6d21-49e4-8824-4919d3558ef9 这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。", "ide": "idea", "module": "module_plugin", "clientTimestamp": "2025-06-30T16:17:21.540817", "traceId": "trace_1a25167000824293", "sessionId": "test_session_50_1", "requestId": "req_51e81bf25d73", "env": {"userId": "test_user_1", "workspaceUri": "/workspace/test_project_1", "pluginVersion": "1.0.0", "os": "Windows"}}, {"level": "WARN", "msg": "Mock日志内容 - 时间戳: 2025-06-30T16:17:21.540831 - UUID: 3573721b-6130-4abe-aee8-9d8ae270d41b 这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。", "ide": "idea", "module": "module_webview", "clientTimestamp": "2025-06-30T16:17:21.540830", "traceId": "trace_4a12b8efb5524e83", "sessionId": "test_session_50_1", "requestId": "req_6b73757fba55", "env": {"userId": "test_user_1", "workspaceUri": "/workspace/test_project_1", "pluginVersion": "1.0.0", "os": "Linux"}}, {"level": "ERROR", "msg": "Mock日志内容 - 时间戳: 2025-06-30T16:17:21.540844 - UUID: 14c3b14d-f069-4a10-a961-328b0e1da7df 这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。", "ide": "idea", "module": "module_ls", "clientTimestamp": "2025-06-30T16:17:21.540843", "traceId": "trace_cfcb44d4a28042e6", "sessionId": "test_session_50_1", "requestId": "req_d49dc7375189", "env": {"userId": "test_user_1", "workspaceUri": "/workspace/test_project_1", "pluginVersion": "1.0.0", "os": "Windows"}}, {"level": "ERROR", "msg": "Mock日志内容 - 时间戳: 2025-06-30T16:17:21.540857 - UUID: 7860f9fa-81c7-45e1-8eb3-faf1ea44c397 这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。", "ide": "idea", "module": "module_ls", "clientTimestamp": "2025-06-30T16:17:21.540856", "traceId": "trace_f0e648801523430d", "sessionId": "test_session_50_1", "requestId": "req_56364e9866c1", "env": {"userId": "test_user_1", "workspaceUri": "/workspace/test_project_1", "pluginVersion": "1.0.0", "os": "macOS"}}, {"level": "WARN", "msg": "Mock日志内容 - 时间戳: 2025-06-30T16:17:21.540870 - UUID: e5ab3df1-5318-4bef-8eab-94e41cd79ebb 这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。", "ide": "idea", "module": "module_plugin", "clientTimestamp": "2025-06-30T16:17:21.540869", "traceId": "trace_4529ad1b25184030", "sessionId": "test_session_50_1", "requestId": "req_696a5a5e91b5", "env": {"userId": "test_user_1", "workspaceUri": "/workspace/test_project_1", "pluginVersion": "1.0.0", "os": "Linux"}}, {"level": "INFO", "msg": "Mock日志内容 - 时间戳: 2025-06-30T16:17:21.540883 - UUID: 41283cbe-5ec9-4c9d-a7f6-59f86bc1ea87 这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。", "ide": "idea", "module": "module_plugin", "clientTimestamp": "2025-06-30T16:17:21.540882", "traceId": "trace_dae8fb5f663b4585", "sessionId": "test_session_50_1", "requestId": "req_969e82f41b53", "env": {"userId": "test_user_1", "workspaceUri": "/workspace/test_project_1", "pluginVersion": "1.0.0", "os": "Linux"}}, {"level": "WARN", "msg": "Mock日志内容 - 时间戳: 2025-06-30T16:17:21.540895 - UUID: 20080b3c-5a6d-4502-b65b-b71370f96a9a 这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。", "ide": "idea", "module": "module_plugin", "clientTimestamp": "2025-06-30T16:17:21.540894", "traceId": "trace_13975cb4e7b04231", "sessionId": "test_session_50_1", "requestId": "req_3e84c207da50", "env": {"userId": "test_user_1", "workspaceUri": "/workspace/test_project_1", "pluginVersion": "1.0.0", "os": "macOS"}}, {"level": "WARN", "msg": "Mock日志内容 - 时间戳: 2025-06-30T16:17:21.540908 - UUID: ae2089e1-27ac-4d63-8b12-0b7ca989c782 这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。", "ide": "idea", "module": "module_ls", "clientTimestamp": "2025-06-30T16:17:21.540907", "traceId": "trace_9ff0a45c7bd0438b", "sessionId": "test_session_50_1", "requestId": "req_43cd426c27ea", "env": {"userId": "test_user_1", "workspaceUri": "/workspace/test_project_1", "pluginVersion": "1.0.0", "os": "Linux"}}, {"level": "ERROR", "msg": "Mock日志内容 - 时间戳: 2025-06-30T16:17:21.540920 - UUID: 936d25a8-0e17-4210-b665-8bcc4a1af720 这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。", "ide": "idea", "module": "module_ls", "clientTimestamp": "2025-06-30T16:17:21.540919", "traceId": "trace_2fa4033303f04c51", "sessionId": "test_session_50_1", "requestId": "req_f1e4146b55bb", "env": {"userId": "test_user_1", "workspaceUri": "/workspace/test_project_1", "pluginVersion": "1.0.0", "os": "macOS"}}, {"level": "INFO", "msg": "Mock日志内容 - 时间戳: 2025-06-30T16:17:21.540933 - UUID: c2b19dc9-370d-4e31-915f-dcd9fed6f7b9 这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。", "ide": "idea", "module": "module_ls", "clientTimestamp": "2025-06-30T16:17:21.540932", "traceId": "trace_d58a0a5011f44715", "sessionId": "test_session_50_1", "requestId": "req_687d70134e8b", "env": {"userId": "test_user_1", "workspaceUri": "/workspace/test_project_1", "pluginVersion": "1.0.0", "os": "macOS"}}, {"level": "ERROR", "msg": "Mock日志内容 - 时间戳: 2025-06-30T16:17:21.540946 - UUID: 4aa8a1d4-f24a-4224-83ce-06f46db39ac9 这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。", "ide": "idea", "module": "module_ls", "clientTimestamp": "2025-06-30T16:17:21.540945", "traceId": "trace_9946cee359bb47ba", "sessionId": "test_session_50_1", "requestId": "req_5a6471a0baaa", "env": {"userId": "test_user_1", "workspaceUri": "/workspace/test_project_1", "pluginVersion": "1.0.0", "os": "macOS"}}, {"level": "WARN", "msg": "Mock日志内容 - 时间戳: 2025-06-30T16:17:21.540958 - UUID: 7dd4baf6-277e-4bbe-8a9f-1b3204005478 这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。", "ide": "idea", "module": "module_ls", "clientTimestamp": "2025-06-30T16:17:21.540957", "traceId": "trace_d0c7608bae734340", "sessionId": "test_session_50_1", "requestId": "req_52de7150ddfc", "env": {"userId": "test_user_1", "workspaceUri": "/workspace/test_project_1", "pluginVersion": "1.0.0", "os": "macOS"}}, {"level": "INFO", "msg": "Mock日志内容 - 时间戳: 2025-06-30T16:17:21.540971 - UUID: 68443d1b-d4e1-4d39-8ed1-e7ed8fb60686 这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。", "ide": "idea", "module": "module_ls", "clientTimestamp": "2025-06-30T16:17:21.540970", "traceId": "trace_64d535c028f24c90", "sessionId": "test_session_50_1", "requestId": "req_53afe8ffc70e", "env": {"userId": "test_user_1", "workspaceUri": "/workspace/test_project_1", "pluginVersion": "1.0.0", "os": "Windows"}}, {"level": "WARN", "msg": "Mock日志内容 - 时间戳: 2025-06-30T16:17:21.540984 - UUID: 57fbf990-a031-41f0-82d0-8b215cb3ddfb 这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。", "ide": "idea", "module": "module_ls", "clientTimestamp": "2025-06-30T16:17:21.540983", "traceId": "trace_1b9c8a61dccd42a8", "sessionId": "test_session_50_1", "requestId": "req_504133eab260", "env": {"userId": "test_user_1", "workspaceUri": "/workspace/test_project_1", "pluginVersion": "1.0.0", "os": "Linux"}}, {"level": "INFO", "msg": "Mock日志内容 - 时间戳: 2025-06-30T16:17:21.540997 - UUID: 4f4b55ef-95cc-4a7e-8f92-b4ef5b9233a5 这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。", "ide": "idea", "module": "module_plugin", "clientTimestamp": "2025-06-30T16:17:21.540996", "traceId": "trace_f212caefb49f4aa6", "sessionId": "test_session_50_1", "requestId": "req_c87bc4a6df41", "env": {"userId": "test_user_1", "workspaceUri": "/workspace/test_project_1", "pluginVersion": "1.0.0", "os": "macOS"}}, {"level": "WARN", "msg": "Mock日志内容 - 时间戳: 2025-06-30T16:17:21.541009 - UUID: d529aded-02d9-48e5-8bf1-b3f155aa0e32 这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。", "ide": "idea", "module": "module_webview", "clientTimestamp": "2025-06-30T16:17:21.541008", "traceId": "trace_a1c4c82f6f4248ab", "sessionId": "test_session_50_1", "requestId": "req_55131417cf44", "env": {"userId": "test_user_1", "workspaceUri": "/workspace/test_project_1", "pluginVersion": "1.0.0", "os": "macOS"}}, {"level": "INFO", "msg": "Mock日志内容 - 时间戳: 2025-06-30T16:17:21.541022 - UUID: 0695509f-5d53-4763-af1b-4ebde9c99e36 这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。", "ide": "idea", "module": "module_ls", "clientTimestamp": "2025-06-30T16:17:21.541021", "traceId": "trace_1cb60e7d609347c6", "sessionId": "test_session_50_1", "requestId": "req_c07830ee7c8d", "env": {"userId": "test_user_1", "workspaceUri": "/workspace/test_project_1", "pluginVersion": "1.0.0", "os": "macOS"}}, {"level": "WARN", "msg": "Mock日志内容 - 时间戳: 2025-06-30T16:17:21.541035 - UUID: 204ed007-bfd3-427f-af8f-6a2435e28c57 这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。", "ide": "idea", "module": "module_ls", "clientTimestamp": "2025-06-30T16:17:21.541034", "traceId": "trace_425682c2c73a45b0", "sessionId": "test_session_50_1", "requestId": "req_59f2cf044f52", "env": {"userId": "test_user_1", "workspaceUri": "/workspace/test_project_1", "pluginVersion": "1.0.0", "os": "Linux"}}, {"level": "ERROR", "msg": "Mock日志内容 - 时间戳: 2025-06-30T16:17:21.541047 - UUID: 7a6b5d46-1752-4005-acf7-8cf7c0501407 这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。", "ide": "idea", "module": "module_webview", "clientTimestamp": "2025-06-30T16:17:21.541046", "traceId": "trace_317aa8ee17b14a7b", "sessionId": "test_session_50_1", "requestId": "req_b8070eab0333", "env": {"userId": "test_user_1", "workspaceUri": "/workspace/test_project_1", "pluginVersion": "1.0.0", "os": "Linux"}}, {"level": "WARN", "msg": "Mock日志内容 - 时间戳: 2025-06-30T16:17:21.541060 - UUID: 354045a0-e362-43aa-84be-b15851c88169 这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。", "ide": "idea", "module": "module_ls", "clientTimestamp": "2025-06-30T16:17:21.541059", "traceId": "trace_025f08cb5cad4359", "sessionId": "test_session_50_1", "requestId": "req_028a7e619f06", "env": {"userId": "test_user_1", "workspaceUri": "/workspace/test_project_1", "pluginVersion": "1.0.0", "os": "Windows"}}, {"level": "INFO", "msg": "Mock日志内容 - 时间戳: 2025-06-30T16:17:21.541129 - UUID: 1cb3098b-e91a-44ea-b9dc-0da4a4de0d4c 这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。", "ide": "idea", "module": "module_ls", "clientTimestamp": "2025-06-30T16:17:21.541127", "traceId": "trace_84cf21420fb74a44", "sessionId": "test_session_50_1", "requestId": "req_bccfc2d8fc1b", "env": {"userId": "test_user_1", "workspaceUri": "/workspace/test_project_1", "pluginVersion": "1.0.0", "os": "Windows"}}, {"level": "INFO", "msg": "Mock日志内容 - 时间戳: 2025-06-30T16:17:21.541142 - UUID: 55bbb4f6-f0b2-4496-905f-716965263bbe 这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。", "ide": "idea", "module": "module_webview", "clientTimestamp": "2025-06-30T16:17:21.541141", "traceId": "trace_cc48dd55ccb641a2", "sessionId": "test_session_50_1", "requestId": "req_ca362e4baaac", "env": {"userId": "test_user_1", "workspaceUri": "/workspace/test_project_1", "pluginVersion": "1.0.0", "os": "Linux"}}, {"level": "ERROR", "msg": "Mock日志内容 - 时间戳: 2025-06-30T16:17:21.541156 - UUID: ff3f6c78-8d12-4964-bf4b-c3f95ea49915 这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。", "ide": "idea", "module": "module_ls", "clientTimestamp": "2025-06-30T16:17:21.541155", "traceId": "trace_60b28675afca461c", "sessionId": "test_session_50_1", "requestId": "req_6008ac13d91b", "env": {"userId": "test_user_1", "workspaceUri": "/workspace/test_project_1", "pluginVersion": "1.0.0", "os": "macOS"}}, {"level": "INFO", "msg": "Mock日志内容 - 时间戳: 2025-06-30T16:17:21.541169 - UUID: 9957944b-9eec-40c6-8432-28838b5672cd 这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。", "ide": "idea", "module": "module_plugin", "clientTimestamp": "2025-06-30T16:17:21.541168", "traceId": "trace_f096d4ea63554ecc", "sessionId": "test_session_50_1", "requestId": "req_9dde46542818", "env": {"userId": "test_user_1", "workspaceUri": "/workspace/test_project_1", "pluginVersion": "1.0.0", "os": "macOS"}}, {"level": "ERROR", "msg": "Mock日志内容 - 时间戳: 2025-06-30T16:17:21.541182 - UUID: ed151cc7-308e-418d-9843-bef8ce11ec78 这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。", "ide": "idea", "module": "module_ls", "clientTimestamp": "2025-06-30T16:17:21.541181", "traceId": "trace_f8bc69151e404bf7", "sessionId": "test_session_50_1", "requestId": "req_591f47876b39", "env": {"userId": "test_user_1", "workspaceUri": "/workspace/test_project_1", "pluginVersion": "1.0.0", "os": "Linux"}}, {"level": "WARN", "msg": "Mock日志内容 - 时间戳: 2025-06-30T16:17:21.541195 - UUID: eabbb568-b4f2-4c6c-9eff-e717cc2c0f9e 这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。", "ide": "idea", "module": "module_ls", "clientTimestamp": "2025-06-30T16:17:21.541194", "traceId": "trace_af91cd0597ef4749", "sessionId": "test_session_50_1", "requestId": "req_53d342bad465", "env": {"userId": "test_user_1", "workspaceUri": "/workspace/test_project_1", "pluginVersion": "1.0.0", "os": "Windows"}}, {"level": "ERROR", "msg": "Mock日志内容 - 时间戳: 2025-06-30T16:17:21.541208 - UUID: 3ac37b4b-a85e-40de-a556-2984dfc71d2f 这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。", "ide": "idea", "module": "module_ls", "clientTimestamp": "2025-06-30T16:17:21.541207", "traceId": "trace_a81213808639420f", "sessionId": "test_session_50_1", "requestId": "req_fb17abc15912", "env": {"userId": "test_user_1", "workspaceUri": "/workspace/test_project_1", "pluginVersion": "1.0.0", "os": "macOS"}}, {"level": "INFO", "msg": "Mock日志内容 - 时间戳: 2025-06-30T16:17:21.541220 - UUID: 6d28a141-36c1-4cd8-adcc-37d32aec0e56 这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。", "ide": "idea", "module": "module_ls", "clientTimestamp": "2025-06-30T16:17:21.541219", "traceId": "trace_64639585f6bb48ad", "sessionId": "test_session_50_1", "requestId": "req_98615e69e064", "env": {"userId": "test_user_1", "workspaceUri": "/workspace/test_project_1", "pluginVersion": "1.0.0", "os": "Linux"}}, {"level": "INFO", "msg": "Mock日志内容 - 时间戳: 2025-06-30T16:17:21.541233 - UUID: 867944c1-65ac-44ad-88e9-c24ba7967581 这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。", "ide": "idea", "module": "module_webview", "clientTimestamp": "2025-06-30T16:17:21.541232", "traceId": "trace_d673208389dc482f", "sessionId": "test_session_50_1", "requestId": "req_3ad4509f1440", "env": {"userId": "test_user_1", "workspaceUri": "/workspace/test_project_1", "pluginVersion": "1.0.0", "os": "Linux"}}, {"level": "ERROR", "msg": "Mock日志内容 - 时间戳: 2025-06-30T16:17:21.541247 - UUID: b0975194-3224-4297-b9f0-a4bba904a39c 这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。", "ide": "idea", "module": "module_webview", "clientTimestamp": "2025-06-30T16:17:21.541246", "traceId": "trace_34bad433f4a240b4", "sessionId": "test_session_50_1", "requestId": "req_77acb64f4aaf", "env": {"userId": "test_user_1", "workspaceUri": "/workspace/test_project_1", "pluginVersion": "1.0.0", "os": "Windows"}}, {"level": "INFO", "msg": "Mock日志内容 - 时间戳: 2025-06-30T16:17:21.541260 - UUID: 6afff388-48f7-4ee2-a73d-42bd80bc574e 这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。", "ide": "idea", "module": "module_webview", "clientTimestamp": "2025-06-30T16:17:21.541259", "traceId": "trace_a13beb52f35141fb", "sessionId": "test_session_50_1", "requestId": "req_e0bc582f5bfe", "env": {"userId": "test_user_1", "workspaceUri": "/workspace/test_project_1", "pluginVersion": "1.0.0", "os": "macOS"}}, {"level": "ERROR", "msg": "Mock日志内容 - 时间戳: 2025-06-30T16:17:21.541272 - UUID: c4ddec3e-6465-477a-a139-65f222785e55 这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。", "ide": "idea", "module": "module_webview", "clientTimestamp": "2025-06-30T16:17:21.541271", "traceId": "trace_d17d3bc8b6f24822", "sessionId": "test_session_50_1", "requestId": "req_e5a3ace3995f", "env": {"userId": "test_user_1", "workspaceUri": "/workspace/test_project_1", "pluginVersion": "1.0.0", "os": "Linux"}}, {"level": "ERROR", "msg": "Mock日志内容 - 时间戳: 2025-06-30T16:17:21.541285 - UUID: 6df40aa7-4cb6-4559-8cca-387239923b14 这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。", "ide": "idea", "module": "module_webview", "clientTimestamp": "2025-06-30T16:17:21.541284", "traceId": "trace_ff80651424354acd", "sessionId": "test_session_50_1", "requestId": "req_b55861d53c62", "env": {"userId": "test_user_1", "workspaceUri": "/workspace/test_project_1", "pluginVersion": "1.0.0", "os": "Linux"}}, {"level": "ERROR", "msg": "Mock日志内容 - 时间戳: 2025-06-30T16:17:21.541297 - UUID: fcaff2d6-166f-4e04-b974-f5332d4327a1 这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。", "ide": "idea", "module": "module_plugin", "clientTimestamp": "2025-06-30T16:17:21.541296", "traceId": "trace_7ca0506b1b884940", "sessionId": "test_session_50_1", "requestId": "req_484acafd6224", "env": {"userId": "test_user_1", "workspaceUri": "/workspace/test_project_1", "pluginVersion": "1.0.0", "os": "Windows"}}, {"level": "WARN", "msg": "Mock日志内容 - 时间戳: 2025-06-30T16:17:21.541310 - UUID: 139394df-4eef-4099-aed3-c00364afc69b 这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。", "ide": "idea", "module": "module_ls", "clientTimestamp": "2025-06-30T16:17:21.541309", "traceId": "trace_9b83c399c89b4332", "sessionId": "test_session_50_1", "requestId": "req_e447a7925fae", "env": {"userId": "test_user_1", "workspaceUri": "/workspace/test_project_1", "pluginVersion": "1.0.0", "os": "macOS"}}, {"level": "ERROR", "msg": "Mock日志内容 - 时间戳: 2025-06-30T16:17:21.541322 - UUID: bbcf0306-2943-4246-9a9a-cab054b4ec54 这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。", "ide": "idea", "module": "module_plugin", "clientTimestamp": "2025-06-30T16:17:21.541321", "traceId": "trace_94badfdab1ea48ce", "sessionId": "test_session_50_1", "requestId": "req_2c283093eaf7", "env": {"userId": "test_user_1", "workspaceUri": "/workspace/test_project_1", "pluginVersion": "1.0.0", "os": "Windows"}}, {"level": "ERROR", "msg": "Mock日志内容 - 时间戳: 2025-06-30T16:17:21.541335 - UUID: 0743e5dc-7f16-439c-8811-4bb83dace03f 这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。", "ide": "idea", "module": "module_plugin", "clientTimestamp": "2025-06-30T16:17:21.541334", "traceId": "trace_c2f8c31e726e4aaa", "sessionId": "test_session_50_1", "requestId": "req_9d4e1766ecf3", "env": {"userId": "test_user_1", "workspaceUri": "/workspace/test_project_1", "pluginVersion": "1.0.0", "os": "macOS"}}, {"level": "INFO", "msg": "Mock日志内容 - 时间戳: 2025-06-30T16:17:21.541348 - UUID: b0e254bd-61fe-489f-bcbc-8912ce8bc4b7 这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。", "ide": "idea", "module": "module_ls", "clientTimestamp": "2025-06-30T16:17:21.541347", "traceId": "trace_ca3a053cdac74cf4", "sessionId": "test_session_50_1", "requestId": "req_3646ca5f4561", "env": {"userId": "test_user_1", "workspaceUri": "/workspace/test_project_1", "pluginVersion": "1.0.0", "os": "Linux"}}, {"level": "WARN", "msg": "Mock日志内容 - 时间戳: 2025-06-30T16:17:21.541360 - UUID: 68638a52-d243-4762-a1fe-5b27651d1bd3 这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。", "ide": "idea", "module": "module_ls", "clientTimestamp": "2025-06-30T16:17:21.541359", "traceId": "trace_4a2d7e3bedc54820", "sessionId": "test_session_50_1", "requestId": "req_bb469dd840c4", "env": {"userId": "test_user_1", "workspaceUri": "/workspace/test_project_1", "pluginVersion": "1.0.0", "os": "Linux"}}, {"level": "INFO", "msg": "Mock日志内容 - 时间戳: 2025-06-30T16:17:21.541373 - UUID: d0e4f9ac-5ffb-492c-8221-36638e1b3e28 这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。", "ide": "idea", "module": "module_ls", "clientTimestamp": "2025-06-30T16:17:21.541372", "traceId": "trace_d79d71c62820441a", "sessionId": "test_session_50_1", "requestId": "req_29b2f6629326", "env": {"userId": "test_user_1", "workspaceUri": "/workspace/test_project_1", "pluginVersion": "1.0.0", "os": "macOS"}}, {"level": "WARN", "msg": "Mock日志内容 - 时间戳: 2025-06-30T16:17:21.541385 - UUID: 4f1d9538-69e9-4f20-85a1-a311d8257661 这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。", "ide": "idea", "module": "module_plugin", "clientTimestamp": "2025-06-30T16:17:21.541384", "traceId": "trace_fa2b138e2eae44bf", "sessionId": "test_session_50_1", "requestId": "req_c0a43a577271", "env": {"userId": "test_user_1", "workspaceUri": "/workspace/test_project_1", "pluginVersion": "1.0.0", "os": "Windows"}}, {"level": "WARN", "msg": "Mock日志内容 - 时间戳: 2025-06-30T16:17:21.541399 - UUID: 760058cb-738e-41d6-b103-a4a25845b215 这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。", "ide": "idea", "module": "module_webview", "clientTimestamp": "2025-06-30T16:17:21.541398", "traceId": "trace_6ae4153bdc2540b9", "sessionId": "test_session_50_1", "requestId": "req_430063b3a993", "env": {"userId": "test_user_1", "workspaceUri": "/workspace/test_project_1", "pluginVersion": "1.0.0", "os": "Linux"}}, {"level": "INFO", "msg": "Mock日志内容 - 时间戳: 2025-06-30T16:17:21.541412 - UUID: ad59aa9d-d347-494c-a05c-70ca9cc3d6f7 这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。", "ide": "idea", "module": "module_ls", "clientTimestamp": "2025-06-30T16:17:21.541410", "traceId": "trace_0623268a91514c9c", "sessionId": "test_session_50_1", "requestId": "req_fc4c099d3060", "env": {"userId": "test_user_1", "workspaceUri": "/workspace/test_project_1", "pluginVersion": "1.0.0", "os": "Windows"}}, {"level": "WARN", "msg": "Mock日志内容 - 时间戳: 2025-06-30T16:17:21.541424 - UUID: 7b7db22b-6e41-4442-9b83-64f26af6e4d8 这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。", "ide": "idea", "module": "module_plugin", "clientTimestamp": "2025-06-30T16:17:21.541423", "traceId": "trace_f7d62ba192914391", "sessionId": "test_session_50_1", "requestId": "req_3cd5e3e8f308", "env": {"userId": "test_user_1", "workspaceUri": "/workspace/test_project_1", "pluginVersion": "1.0.0", "os": "macOS"}}, {"level": "WARN", "msg": "Mock日志内容 - 时间戳: 2025-06-30T16:17:21.541437 - UUID: 4301334e-24b3-4eee-8bbd-e28bf2b7f012 这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。", "ide": "idea", "module": "module_ls", "clientTimestamp": "2025-06-30T16:17:21.541436", "traceId": "trace_f707c7fe47f94a6e", "sessionId": "test_session_50_1", "requestId": "req_ed1c0c834bf9", "env": {"userId": "test_user_1", "workspaceUri": "/workspace/test_project_1", "pluginVersion": "1.0.0", "os": "Linux"}}, {"level": "WARN", "msg": "Mock日志内容 - 时间戳: 2025-06-30T16:17:21.541449 - UUID: 1e80dd7c-b08b-4bb9-8635-0cd31fe9bfb3 这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。", "ide": "idea", "module": "module_plugin", "clientTimestamp": "2025-06-30T16:17:21.541448", "traceId": "trace_efef95af75cc4f03", "sessionId": "test_session_50_1", "requestId": "req_d355403f1f07", "env": {"userId": "test_user_1", "workspaceUri": "/workspace/test_project_1", "pluginVersion": "1.0.0", "os": "macOS"}}, {"level": "WARN", "msg": "Mock日志内容 - 时间戳: 2025-06-30T16:17:21.541462 - UUID: c0500f06-a7c5-4fcd-8885-fb0c460bb73a 这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。", "ide": "idea", "module": "module_webview", "clientTimestamp": "2025-06-30T16:17:21.541461", "traceId": "trace_4d99d36fbfd6495c", "sessionId": "test_session_50_1", "requestId": "req_fa0119309c3b", "env": {"userId": "test_user_1", "workspaceUri": "/workspace/test_project_1", "pluginVersion": "1.0.0", "os": "macOS"}}, {"level": "INFO", "msg": "Mock日志内容 - 时间戳: 2025-06-30T16:17:21.541476 - UUID: 5086e4d9-34c0-4d08-b35b-9c4117d4a976 这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。", "ide": "idea", "module": "module_ls", "clientTimestamp": "2025-06-30T16:17:21.541474", "traceId": "trace_72cbefcc95734cc1", "sessionId": "test_session_50_1", "requestId": "req_2e9f2b6c5900", "env": {"userId": "test_user_1", "workspaceUri": "/workspace/test_project_1", "pluginVersion": "1.0.0", "os": "Windows"}}, {"level": "WARN", "msg": "Mock日志内容 - 时间戳: 2025-06-30T16:17:21.541487 - UUID: 6185cbc2-2c94-42d7-bd65-d8b3b5a8a65f 这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。", "ide": "idea", "module": "module_ls", "clientTimestamp": "2025-06-30T16:17:21.541486", "traceId": "trace_33bfff4e1da846a3", "sessionId": "test_session_50_1", "requestId": "req_8b510c471665", "env": {"userId": "test_user_1", "workspaceUri": "/workspace/test_project_1", "pluginVersion": "1.0.0", "os": "macOS"}}]}