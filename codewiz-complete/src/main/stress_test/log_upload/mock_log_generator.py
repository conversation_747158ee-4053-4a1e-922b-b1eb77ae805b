#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Mock日志请求生成器
用于生成符合接口格式的示例日志请求，可用于测试和调试
"""

import json
import uuid
import random
import gzip
from datetime import datetime
from typing import Dict, List
import argparse


class MockLogGenerator:
    """Mock日志数据生成器"""
    
    def __init__(self):
        self.log_levels = ["INFO", "WARN", "ERROR"]
        self.modules = ['webview', 'plugin', 'ls']
        self.os_types = ["Windows", "macOS", "Linux"]
        
    def generate_log_content(self, target_size_kb: int = 1) -> str:
        """生成指定大小的日志内容"""
        target_bytes = target_size_kb * 1024
        
        # 基础内容 - 包含时间戳和UUID确保唯一性
        base_content = f"Mock日志内容 - 时间戳: {datetime.now().isoformat()} - UUID: {uuid.uuid4()}"
        
        # 计算需要填充的字符数
        current_bytes = len(base_content.encode('utf-8'))
        remaining_bytes = max(0, target_bytes - current_bytes)
        
        # 生成填充内容
        if remaining_bytes > 0:
            filler = "这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。" * (remaining_bytes // 100 + 1)
            filler = filler[:remaining_bytes]
            base_content += " " + filler
            
        return base_content

    def create_log_entry(self, 
                        user_id: str = None, 
                        workspace_uri: str = None,
                        session_id: str = None,
                        log_size_kb: int = 1) -> Dict:
        """创建单条日志条目"""
        current_time = datetime.now().isoformat()
        
        # 使用默认值或随机生成
        if not user_id:
            user_id = f"user_{random.randint(1, 1000)}"
        if not workspace_uri:
            workspace_uri = f"/workspace/project_{random.randint(1, 100)}"
        if not session_id:
            session_id = f"session_{uuid.uuid4().hex[:8]}"
        
        return {
            "level": random.choice(self.log_levels),
            "msg": self.generate_log_content(log_size_kb),
            "ide": "idea",  # 固定为idea
            "module": f"module_{random.choice(self.modules)}",
            "clientTimestamp": current_time,
            "traceId": f"trace_{uuid.uuid4().hex[:16]}",
            "sessionId": session_id,
            "requestId": f"req_{uuid.uuid4().hex[:12]}",
            "env": {
                "userId": user_id,
                "workspaceUri": workspace_uri,
                "pluginVersion": "1.0.0",
                "os": random.choice(self.os_types)
            }
        }

    def create_batch_request(self, 
                           batch_size: int = 10,
                           user_id: str = None,
                           workspace_uri: str = None,
                           session_id: str = None,
                           log_size_kb: int = 1) -> Dict:
        """创建批量日志请求"""
        logs = []
        for i in range(batch_size):
            logs.append(self.create_log_entry(user_id, workspace_uri, session_id, log_size_kb))
        
        return {"logs": logs}

    def save_batch_to_file(self, request: Dict, filename: str):
        """保存单个批次请求到文件，同时生成JSON和GZ格式"""
        # 保存JSON文件
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(request, f, ensure_ascii=False, indent=2)
        print(f"已保存批次大小为 {len(request['logs'])} 的请求到文件: {filename}")
        
        # 保存GZ压缩文件
        gz_filename = filename + '.gz'
        with gzip.open(gz_filename, 'wt', encoding='utf-8') as f:
            json.dump(request, f, ensure_ascii=False, indent=2)
        print(f"已保存压缩文件: {gz_filename}")

    def generate_batch_files(self, batch_sizes: List[int] = [10, 20, 50], log_size_kb: int = 1):
        """为每个批次大小生成单独的文件"""
        for batch_size in batch_sizes:
            # 创建单个批次请求
            request = self.create_batch_request(
                batch_size=batch_size,
                user_id="test_user_1",
                workspace_uri="/workspace/test_project_1", 
                session_id=f"test_session_{batch_size}_1",
                log_size_kb=log_size_kb
            )
            
            # 保存到对应文件（JSON和GZ）
            filename = f"request_batch_{batch_size}.json"
            self.save_batch_to_file(request, filename)


def main():
    parser = argparse.ArgumentParser(description='Mock日志请求生成器')
    parser.add_argument('--batch-sizes', nargs='+', type=int, default=[10, 20, 50],
                        help='批次大小列表 (默认: 10 20 50)')
    parser.add_argument('--log-size', type=int, default=1,
                        help='单条日志大小 (KB) (默认: 1)')
    
    args = parser.parse_args()
    
    # 创建生成器并生成批次文件
    generator = MockLogGenerator()
    generator.generate_batch_files(
        batch_sizes=args.batch_sizes,
        log_size_kb=args.log_size
    )


if __name__ == "__main__":
    main() 