{"logs": [{"level": "ERROR", "msg": "Mock日志内容 - 时间戳: 2025-06-30T16:17:21.538440 - UUID: e84f05ab-1b0d-4144-bb6f-57d3cad6bb33 这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。", "ide": "idea", "module": "module_ls", "clientTimestamp": "2025-06-30T16:17:21.538437", "traceId": "trace_cb51ba829b7846d7", "sessionId": "test_session_20_1", "requestId": "req_11c5ef0c19af", "env": {"userId": "test_user_1", "workspaceUri": "/workspace/test_project_1", "pluginVersion": "1.0.0", "os": "Linux"}}, {"level": "ERROR", "msg": "Mock日志内容 - 时间戳: 2025-06-30T16:17:21.538463 - UUID: 6342ac8e-3ea8-4180-bac2-f22e4c015b4b 这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。", "ide": "idea", "module": "module_webview", "clientTimestamp": "2025-06-30T16:17:21.538462", "traceId": "trace_4ab3a3647fc447b7", "sessionId": "test_session_20_1", "requestId": "req_9e3f33bc7f44", "env": {"userId": "test_user_1", "workspaceUri": "/workspace/test_project_1", "pluginVersion": "1.0.0", "os": "Windows"}}, {"level": "ERROR", "msg": "Mock日志内容 - 时间戳: 2025-06-30T16:17:21.538476 - UUID: ed182d7d-2f81-4829-ab0c-2c8baf6204d2 这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。", "ide": "idea", "module": "module_ls", "clientTimestamp": "2025-06-30T16:17:21.538475", "traceId": "trace_f2602a6577ed4ade", "sessionId": "test_session_20_1", "requestId": "req_65af45b33bfe", "env": {"userId": "test_user_1", "workspaceUri": "/workspace/test_project_1", "pluginVersion": "1.0.0", "os": "Linux"}}, {"level": "ERROR", "msg": "Mock日志内容 - 时间戳: 2025-06-30T16:17:21.538489 - UUID: fa2adce2-b597-45ce-aa62-5428f01a621e 这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。", "ide": "idea", "module": "module_webview", "clientTimestamp": "2025-06-30T16:17:21.538488", "traceId": "trace_8955df9d4e134196", "sessionId": "test_session_20_1", "requestId": "req_3f01a8895b65", "env": {"userId": "test_user_1", "workspaceUri": "/workspace/test_project_1", "pluginVersion": "1.0.0", "os": "Windows"}}, {"level": "WARN", "msg": "Mock日志内容 - 时间戳: 2025-06-30T16:17:21.538502 - UUID: bb28fc12-1f93-462a-9977-32f4a9c6b336 这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。", "ide": "idea", "module": "module_webview", "clientTimestamp": "2025-06-30T16:17:21.538501", "traceId": "trace_957ba61bc9a74699", "sessionId": "test_session_20_1", "requestId": "req_48354cc120f2", "env": {"userId": "test_user_1", "workspaceUri": "/workspace/test_project_1", "pluginVersion": "1.0.0", "os": "Linux"}}, {"level": "ERROR", "msg": "Mock日志内容 - 时间戳: 2025-06-30T16:17:21.538515 - UUID: d902e919-926d-4b3d-944a-4ec459bfdc65 这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。", "ide": "idea", "module": "module_webview", "clientTimestamp": "2025-06-30T16:17:21.538514", "traceId": "trace_eed349948d4e423e", "sessionId": "test_session_20_1", "requestId": "req_c29a82fe8183", "env": {"userId": "test_user_1", "workspaceUri": "/workspace/test_project_1", "pluginVersion": "1.0.0", "os": "macOS"}}, {"level": "INFO", "msg": "Mock日志内容 - 时间戳: 2025-06-30T16:17:21.538529 - UUID: 895499ff-e8bd-4c5c-ab01-ab180172e76f 这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。", "ide": "idea", "module": "module_plugin", "clientTimestamp": "2025-06-30T16:17:21.538527", "traceId": "trace_17efc6f8a8714df9", "sessionId": "test_session_20_1", "requestId": "req_7741e2ee3b66", "env": {"userId": "test_user_1", "workspaceUri": "/workspace/test_project_1", "pluginVersion": "1.0.0", "os": "macOS"}}, {"level": "ERROR", "msg": "Mock日志内容 - 时间戳: 2025-06-30T16:17:21.538541 - UUID: 9ec1394d-a5de-4c14-b0c5-3fb8fdf5672d 这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。", "ide": "idea", "module": "module_ls", "clientTimestamp": "2025-06-30T16:17:21.538540", "traceId": "trace_3d61a312f09f4b08", "sessionId": "test_session_20_1", "requestId": "req_9e75a6e0ed27", "env": {"userId": "test_user_1", "workspaceUri": "/workspace/test_project_1", "pluginVersion": "1.0.0", "os": "Windows"}}, {"level": "INFO", "msg": "Mock日志内容 - 时间戳: 2025-06-30T16:17:21.538554 - UUID: 3c6fb576-11c0-42e1-b064-4e6f1d2a1f57 这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。", "ide": "idea", "module": "module_plugin", "clientTimestamp": "2025-06-30T16:17:21.538553", "traceId": "trace_cc1d48cf287a42f5", "sessionId": "test_session_20_1", "requestId": "req_a9c2a2ffbc28", "env": {"userId": "test_user_1", "workspaceUri": "/workspace/test_project_1", "pluginVersion": "1.0.0", "os": "macOS"}}, {"level": "WARN", "msg": "Mock日志内容 - 时间戳: 2025-06-30T16:17:21.538567 - UUID: d61e51a5-a5a7-4fd1-9e2b-81f80ac9249c 这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。", "ide": "idea", "module": "module_ls", "clientTimestamp": "2025-06-30T16:17:21.538566", "traceId": "trace_4aaaf3f59f0a4f9c", "sessionId": "test_session_20_1", "requestId": "req_061154180bf4", "env": {"userId": "test_user_1", "workspaceUri": "/workspace/test_project_1", "pluginVersion": "1.0.0", "os": "macOS"}}, {"level": "WARN", "msg": "Mock日志内容 - 时间戳: 2025-06-30T16:17:21.538580 - UUID: 92748da0-8e1d-4585-938d-87bffa63c593 这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。", "ide": "idea", "module": "module_webview", "clientTimestamp": "2025-06-30T16:17:21.538579", "traceId": "trace_b0f5ea10a35e4c96", "sessionId": "test_session_20_1", "requestId": "req_390a68e09bd3", "env": {"userId": "test_user_1", "workspaceUri": "/workspace/test_project_1", "pluginVersion": "1.0.0", "os": "Linux"}}, {"level": "INFO", "msg": "Mock日志内容 - 时间戳: 2025-06-30T16:17:21.538593 - UUID: bde5b7df-6beb-4cba-9b13-f441f0444c54 这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。", "ide": "idea", "module": "module_ls", "clientTimestamp": "2025-06-30T16:17:21.538592", "traceId": "trace_3f93c91b0db24d77", "sessionId": "test_session_20_1", "requestId": "req_687dfa692ff2", "env": {"userId": "test_user_1", "workspaceUri": "/workspace/test_project_1", "pluginVersion": "1.0.0", "os": "Windows"}}, {"level": "INFO", "msg": "Mock日志内容 - 时间戳: 2025-06-30T16:17:21.538606 - UUID: acf7796e-7771-4de6-b8cb-180e6fa6c0af 这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。", "ide": "idea", "module": "module_webview", "clientTimestamp": "2025-06-30T16:17:21.538605", "traceId": "trace_3fbf0c141afd47b9", "sessionId": "test_session_20_1", "requestId": "req_ff7fe21e95bb", "env": {"userId": "test_user_1", "workspaceUri": "/workspace/test_project_1", "pluginVersion": "1.0.0", "os": "Linux"}}, {"level": "WARN", "msg": "Mock日志内容 - 时间戳: 2025-06-30T16:17:21.538619 - UUID: ddb5f1f3-4f77-4495-a781-9e37c22bb963 这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。", "ide": "idea", "module": "module_webview", "clientTimestamp": "2025-06-30T16:17:21.538618", "traceId": "trace_ad9ac0c533a14c13", "sessionId": "test_session_20_1", "requestId": "req_edece0f7af1d", "env": {"userId": "test_user_1", "workspaceUri": "/workspace/test_project_1", "pluginVersion": "1.0.0", "os": "Linux"}}, {"level": "INFO", "msg": "Mock日志内容 - 时间戳: 2025-06-30T16:17:21.538632 - UUID: 6efea234-676d-46ff-a296-40dfa6ad6ff7 这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。", "ide": "idea", "module": "module_plugin", "clientTimestamp": "2025-06-30T16:17:21.538631", "traceId": "trace_d9a3215d978b4662", "sessionId": "test_session_20_1", "requestId": "req_bb0545c68983", "env": {"userId": "test_user_1", "workspaceUri": "/workspace/test_project_1", "pluginVersion": "1.0.0", "os": "Linux"}}, {"level": "INFO", "msg": "Mock日志内容 - 时间戳: 2025-06-30T16:17:21.538644 - UUID: 9b0e3f52-5d0b-45e9-a387-67e557b1f898 这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。", "ide": "idea", "module": "module_plugin", "clientTimestamp": "2025-06-30T16:17:21.538643", "traceId": "trace_68bd2abfbab846e3", "sessionId": "test_session_20_1", "requestId": "req_0a47a5625b1c", "env": {"userId": "test_user_1", "workspaceUri": "/workspace/test_project_1", "pluginVersion": "1.0.0", "os": "macOS"}}, {"level": "ERROR", "msg": "Mock日志内容 - 时间戳: 2025-06-30T16:17:21.538657 - UUID: 3d7dfc39-a12a-4472-93cd-d47116a94b69 这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。", "ide": "idea", "module": "module_plugin", "clientTimestamp": "2025-06-30T16:17:21.538656", "traceId": "trace_2f11dc62d96d40f1", "sessionId": "test_session_20_1", "requestId": "req_2c9532b02209", "env": {"userId": "test_user_1", "workspaceUri": "/workspace/test_project_1", "pluginVersion": "1.0.0", "os": "Windows"}}, {"level": "INFO", "msg": "Mock日志内容 - 时间戳: 2025-06-30T16:17:21.538672 - UUID: 535f7f6f-e150-4f42-bd1d-43866b77b838 这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。", "ide": "idea", "module": "module_ls", "clientTimestamp": "2025-06-30T16:17:21.538671", "traceId": "trace_027c4d4e116b48cd", "sessionId": "test_session_20_1", "requestId": "req_d03ec1cd3db0", "env": {"userId": "test_user_1", "workspaceUri": "/workspace/test_project_1", "pluginVersion": "1.0.0", "os": "Linux"}}, {"level": "INFO", "msg": "Mock日志内容 - 时间戳: 2025-06-30T16:17:21.538685 - UUID: e1ddae04-1a9a-487b-b17a-b5f4330b87fe 这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。", "ide": "idea", "module": "module_webview", "clientTimestamp": "2025-06-30T16:17:21.538684", "traceId": "trace_fc4e49956d344a89", "sessionId": "test_session_20_1", "requestId": "req_a680075d8f85", "env": {"userId": "test_user_1", "workspaceUri": "/workspace/test_project_1", "pluginVersion": "1.0.0", "os": "Windows"}}, {"level": "ERROR", "msg": "Mock日志内容 - 时间戳: 2025-06-30T16:17:21.538697 - UUID: 97afae73-daa4-4976-81f9-9968e97e5451 这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。这是填充内容用于达到指定KB大小。包含各种测试数据和模拟信息。", "ide": "idea", "module": "module_webview", "clientTimestamp": "2025-06-30T16:17:21.538696", "traceId": "trace_2ab2692e81244d2a", "sessionId": "test_session_20_1", "requestId": "req_5f1e2727a4a8", "env": {"userId": "test_user_1", "workspaceUri": "/workspace/test_project_1", "pluginVersion": "1.0.0", "os": "macOS"}}]}