package com.xiaohongshu.codewiz.complete.service.logupload.impl;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.xiaohongshu.codewiz.complete.dto.logdata.LogBatchUploadReq;
import com.xiaohongshu.codewiz.complete.dto.logdata.LogBatchUploadResp;
import com.xiaohongshu.codewiz.complete.dto.logdata.LogData;
import com.xiaohongshu.codewiz.complete.service.logupload.LogService;
import com.xiaohongshu.codewiz.complete.service.logupload.metrics.LogMetricsData;
import com.xiaohongshu.codewiz.complete.service.logupload.metrics.LogServiceMetrics;
import com.xiaohongshu.xray.logging.LogTags;

import static com.xiaohongshu.codewiz.complete.constant.MetricsConstants.STATUS_FAIL;
import static com.xiaohongshu.codewiz.complete.constant.MetricsConstants.STATUS_SUCCESS;
import lombok.extern.slf4j.Slf4j;

/**
 * 日志服务实现类
 * <p>
 * 核心逻辑：通过Logger输出客户端上报的日志数据，实现日志收集
 */
@Slf4j
@Service
public class LogServiceImpl implements LogService {

    // 日志消息最大长度限制
    private static final int MAX_LOG_MESSAGE_LENGTH = 1000;
    
    // 专门用于日志上报的logger，只输出到控制台
    private static final Logger CLIENT_LOG = LoggerFactory.getLogger("CLIENT_LOG");

    // 异步处理日志的线程池 (通过依赖注入获取)
    private final Executor logProcessorExecutor;

    // 监控指标收集器
    private final LogServiceMetrics metrics;

    public LogServiceImpl(@Qualifier("logUploadExecutor") Executor logProcessorExecutor,
                          LogServiceMetrics metrics) {
        this.logProcessorExecutor = logProcessorExecutor;
        this.metrics = metrics;
        log.info("日志上报服务初始化完成，使用专用线程池处理日志输出");
    }

    @Override
    public LogBatchUploadResp batchUpload(LogBatchUploadReq request) {
        // 生成批次ID用于追踪
        String batchId = UUID.randomUUID().toString();
        int logCount = request.getLogs().size();

        try {
            // 服务层面的接收信息记录
            log.info("接收到日志上报请求，batchId: {}, logCount: {}", batchId, logCount);

            // 异步处理日志输出，不阻塞请求线程
            CompletableFuture.runAsync(() -> processLogsAsync(request.getLogs(), batchId), logProcessorExecutor)
                    .exceptionally(throwable -> {
                        // 服务层面的异步处理异常记录
                        log.error("异步处理日志失败，batchId: {}", batchId, throwable);
                        return null;
                    });

            // 立即返回接受状态
            return LogBatchUploadResp.accepted(batchId, logCount);
        } catch (Exception e) {
            // 服务层面的同步阶段异常记录
            log.error("日志上报请求处理失败，batchId: {}", batchId, e);
            return LogBatchUploadResp.failure("请求处理失败: " + e.getMessage());
        }
    }

    /**
     * 异步处理日志列表
     */
    private void processLogsAsync(List<LogData> logs, String batchId) {
        long startTime = System.currentTimeMillis();
        long totalLogSizeBytes = 0;

        // 创建监控数据收集器，记录基本信息
        LogMetricsData metricsData = new LogMetricsData();
        metricsData.setBatchId(batchId);
        metricsData.setTotalLogCount(logs.size());

        try {
            // 服务层面的处理开始记录
            log.info("开始异步处理日志批次，batchId: {}, logCount: {}", batchId, logs.size());

            // 逐条输出日志并计算大小
            for (LogData logData : logs) {
                // 计算msg字节大小
                long msgSizeBytes = logData.getMsg().getBytes(StandardCharsets.UTF_8).length;
                totalLogSizeBytes += msgSizeBytes;

                // 收集单条日志监控数据
                metricsData.addLogEntry(logData.getLevel(), logData.getIde(), logData.getModule(), msgSizeBytes);

                outputClientLog(logData, batchId);
            }

            // 设置成功处理的监控数据
            metricsData.setStatus(STATUS_SUCCESS);

            // 服务层面的处理完成记录
            log.info("异步处理日志批次完成，batchId: {}, logCount: {}, 耗时: {}ms, 数据量: {} bytes",
                    batchId, logs.size(), System.currentTimeMillis() - startTime, totalLogSizeBytes);
        } catch (Exception e) {
            // 设置失败处理的监控数据
            metricsData.setStatus(STATUS_FAIL);

            // 服务层面的异步处理异常记录
            log.error("异步处理日志批次失败，batchId: {}, 耗时: {}ms", batchId, System.currentTimeMillis() - startTime, e);

        } finally {
            // 无论成功失败，都在finally中统一设置时间和大小数据，并上报监控指标
            long processingTime = System.currentTimeMillis() - startTime;
            metricsData.setTotalSizeBytes(totalLogSizeBytes);
            metricsData.setProcessingTimeMs(processingTime);

            // 上报监控指标
            metrics.emitMetrics(metricsData);
        }
    }


    /**
     * 输出客户端上报的日志数据
     * 核心实现：通过Logger输出就是日志上传的实现
     */
    private void outputClientLog(LogData logData, String batchId) {
        // 构建tag信息（不包含msg，msg作为日志消息）
        Map<String, Object> logTags = new HashMap<>();

        // 基本信息
        logTags.put("batchId", batchId);
        logTags.put("level", logData.getLevel());
        logTags.put("ide", logData.getIde());
        logTags.put("module", logData.getModule());
        logTags.put("clientTimestamp", logData.getClientTimestamp());

        // 环境信息
        logTags.put("userId", logData.getEnv().getUserId());
        logTags.put("workspaceUri", logData.getEnv().getWorkspaceUri());
        logTags.put("pluginVersion", logData.getEnv().getPluginVersion());

        // 可选字段
        if (logData.getEnv().getOs() != null) {
            logTags.put("os", logData.getEnv().getOs());
        }
        if (logData.getTraceId() != null) {
            logTags.put("clientTraceId", logData.getTraceId());
        }
        if (logData.getSessionId() != null) {
            logTags.put("sessionId", logData.getSessionId());
        }
        if (logData.getRequestId() != null) {
            logTags.put("requestId", logData.getRequestId());
        }

        // 使用客户端的msg作为日志消息，其他信息作为tags
        String clientMessage = logData.getMsg();
        
        // 截断过长的消息（保证不超过4KB）
        clientMessage = truncateMessageSafely(clientMessage);

        // 根据客户端日志级别输出到对应的服务端日志级别
        switch (logData.getLevel().toUpperCase()) {
            case "ERROR":
                CLIENT_LOG.error(LogTags.of(logTags), clientMessage);
                break;
            case "WARN":
            case "WARNING":
                CLIENT_LOG.warn(LogTags.of(logTags), clientMessage);
                break;
            case "DEBUG":
                CLIENT_LOG.debug(LogTags.of(logTags), clientMessage);
                break;
            case "INFO":
            default:
                CLIENT_LOG.info(LogTags.of(logTags), clientMessage);
                break;
        }
    }

    /**
     * 截断过长的消息
     * 
     * @param message 原始消息
     * @return 截断后的消息
     */
    private String truncateMessageSafely(String message) {
        if (message == null || message.length() <= MAX_LOG_MESSAGE_LENGTH) {
            return message;
        }
        
        return message.substring(0, MAX_LOG_MESSAGE_LENGTH - 3) + "...";
    }
} 