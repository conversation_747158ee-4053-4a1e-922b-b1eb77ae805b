package com.xiaohongshu.codewiz.complete.model.dependency;

import lombok.Getter;

/**
 * 依赖管理工具类型枚举
 */
@Getter
public enum DependencyType {

    MAVEN("Maven", "Apache Maven"),
    GRADLE("Gradle", "Gradle Build Tool"),
    NPM("NPM", "Node Package Manager"),
    YARN("Yarn", "Yarn Package Manager"),
    PIP("Pip", "Python Package Installer"),
    CARGO("Cargo", "Rust Package Manager"),
    GO_MODULES("Go Modules", "Go Module System"),
    CONAN("Conan", "C/C++ Package Manager"),
    COCOAPODS("CocoaPods", "Dependency Manager for Swift and Objective-C"),
    UNKNOWN("Unknown", "Unknown Dependency Manager");

    private final String name;
    private final String displayName;

    DependencyType(String name, String displayName) {
        this.name = name;
        this.displayName = displayName;
    }

    /**
     * 根据名称获取依赖管理工具类型
     */
    public static DependencyType fromName(String name) {
        if (name == null || name.trim().isEmpty()) {
            return UNKNOWN;
        }

        for (DependencyType type : values()) {
            if (type.name.equalsIgnoreCase(name.trim())) {
                return type;
            }
        }

        return UNKNOWN;
    }

    /**
     * 根据文件扩展名或特征推断依赖管理工具类型
     */
    public static DependencyType inferFromContext(String context) {
        if (context == null) {
            return UNKNOWN;
        }

        String lowerContext = context.toLowerCase();

        if (lowerContext.contains("pom.xml") || lowerContext.contains("maven")) {
            return MAVEN;
        } else if (lowerContext.contains("build.gradle") || lowerContext.contains("gradle")) {
            return GRADLE;
        } else if (lowerContext.contains("package.json") || lowerContext.contains("npm")) {
            return NPM;
        } else if (lowerContext.contains("yarn.lock") || lowerContext.contains("yarn")) {
            return YARN;
        } else if (lowerContext.contains("requirements.txt") || lowerContext.contains("pip")) {
            return PIP;
        } else if (lowerContext.contains("cargo.toml") || lowerContext.contains("cargo")) {
            return CARGO;
        } else if (lowerContext.contains("go.mod") || lowerContext.contains("go modules")) {
            return GO_MODULES;
        } else if (lowerContext.contains("conanfile") || lowerContext.contains("conan")) {
            return CONAN;
        }

        return UNKNOWN;
    }
} 