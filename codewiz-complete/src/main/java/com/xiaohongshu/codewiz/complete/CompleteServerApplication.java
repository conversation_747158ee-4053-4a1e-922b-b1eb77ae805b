package com.xiaohongshu.codewiz.complete;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication(scanBasePackages = {"com.xiaohongshu.infra", "com.xiaohongshu.codewiz"})
@EnableAsync
@EnableScheduling
public class CompleteServerApplication {

    public static void main(String[] args) {
        SpringApplication.run(CompleteServerApplication.class, args);
    }

}
