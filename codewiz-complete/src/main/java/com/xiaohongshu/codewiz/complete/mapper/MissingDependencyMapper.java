package com.xiaohongshu.codewiz.complete.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xiaohongshu.codewiz.complete.entity.MissingDependencyEntity;
import com.xiaohongshu.codewiz.complete.model.dependency.Dependency;

/**
 * 缺失依赖数据库映射接口
 */
@Mapper
public interface MissingDependencyMapper extends BaseMapper<MissingDependencyEntity> {

    /**
     * 批量插入缺失依赖记录
     * 使用 INSERT IGNORE 语法避免重复记录
     *
     * @param list 缺失依赖记录列表
     * @return 成功插入的记录数
     */
    @Insert("<script>" +
            "INSERT IGNORE INTO t_miss_dependency (" +
            "language, dependency_type, namespace, name, version, snapshot_version, " +
            "source_trace_id, created_at, updated_at) VALUES " +
            "<foreach collection='list' item='item' separator=','>" +
            "(#{item.language}, #{item.dependencyType}, #{item.namespace}, " +
            "#{item.name}, #{item.version}, #{item.snapshotVersion}, " +
            "#{item.sourceTraceId}, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)" +
            "</foreach>" +
            "</script>")
    int batchInsert(@Param("list") List<MissingDependencyEntity> list);

    /**
     * 根据依赖特征获取缺失依赖（去重）
     * 用于获取具体的依赖信息（不包含版本），然后解析所有版本
     *
     * @param limit 限制返回数量
     * @return 缺失依赖列表（去重后的）
     */
    @Select("SELECT language, dependency_type, namespace, name, version, " +
            "MIN(created_at) as created_at, MIN(id) as id " +
            "FROM t_miss_dependency " +
            "WHERE `skip` = false " +
            "GROUP BY language, dependency_type, namespace, name, version " +
            "ORDER BY MIN(created_at) ASC " +
            "LIMIT #{limit}")
    List<MissingDependencyEntity> selectUniquePendingDependencies(@Param("limit") int limit);

    /**
     * 根据依赖特征删除已处理的缺失依赖
     * 删除指定 namespace:name 的所有记录
     *
     * @param language       语言类型
     * @param dependencyType 依赖类型
     * @param namespace      命名空间
     * @param name           名称
     * @return 删除的记录数
     */
    @Delete("DELETE FROM t_miss_dependency " +
            "WHERE language = #{language} " +
            "AND dependency_type = #{dependencyType} " +
            "AND namespace = #{namespace} " +
            "AND name = #{name}")
    int deleteByDependencySignature(@Param("language") String language,
                                    @Param("dependencyType") String dependencyType,
                                    @Param("namespace") String namespace,
                                    @Param("name") String name);



    /**
     * 批量删除已处理的缺失依赖（直接使用Dependency对象）
     *
     * @param dependencies 依赖列表
     * @return 删除的记录数
     */
    @Delete("<script>" +
            "DELETE FROM t_miss_dependency WHERE " +
            "<foreach collection='dependencies' item='dep' separator=' OR '>" +
            "(language = #{dep.language.name} AND dependency_type = #{dep.dependencyType.name} " +
            "AND namespace = #{dep.namespace} AND name = #{dep.name})" +
            "</foreach>" +
            "</script>")
    int batchDeleteByDependencies(@Param("dependencies") List<Dependency> dependencies);

    /**
     * 批量更新缺失依赖为跳过状态
     * 如果Dependency中的version为null，则不限制版本；否则精确匹配版本
     *
     * @param dependencies 依赖列表
     * @param skipReason 跳过原因
     * @return 更新的记录数
     */
    @org.apache.ibatis.annotations.Update("<script>" +
            "UPDATE t_miss_dependency SET " +
            "`skip` = true, skip_reason = #{skipReason}, updated_at = CURRENT_TIMESTAMP " +
            "WHERE " +
            "<foreach collection='dependencies' item='dep' separator=' OR '>" +
            "(language = #{dep.language.name} AND dependency_type = #{dep.dependencyType.name} " +
            "AND namespace = #{dep.namespace} AND name = #{dep.name})" +
            "</foreach>" +
            "</script>")
    int batchUpdateSkipByDependenciesWithoutVersion(@Param("dependencies") List<Dependency> dependencies,
                                                    @Param("skipReason") String skipReason);

} 