package com.xiaohongshu.codewiz.complete.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xiaohongshu.codewiz.complete.entity.PackageContextEntity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 代码依赖上下文数据库映射接口
 */
@Mapper
public interface KnowledgeMapper extends BaseMapper<PackageContextEntity> {

    /**
     * 根据依赖信息查询知识
     * 使用自定义SQL确保class_signatures字段的类型处理器正确生效
     */
    @Select("SELECT id, language, dependency_type, namespace, name, version, snapshot_version, package_name, " +
            "class_signatures, content_hash, publish_time, is_latest, source_url, metadata_url, " +
            "created_at, updated_at, source_trace_id " +
            "FROM t_code_context " +
            "WHERE dependency_type = #{dependencyType} " +
            "AND language = #{language} " +
            "AND namespace = #{namespace} " +
            "AND name = #{name} " +
            "AND version = #{version}")
    @Results({
            @Result(property = "classSignatures", column = "class_signatures",
                    typeHandler = com.xiaohongshu.codewiz.complete.config.MapTypeHandler.class)
    })
    List<PackageContextEntity> selectByDependency(@Param("dependencyType") String dependencyType,
                                                  @Param("language") String language,
                                                  @Param("namespace") String namespace,
                                                  @Param("name") String name,
                                                  @Param("version") String version);

    /**
     * 检查依赖是否存在（性能优化版本，只返回count）
     */
    default boolean existsByDependency(String dependencyType, String language,
                                       String namespace, String name, String version) {

        LambdaQueryWrapper<PackageContextEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PackageContextEntity::getDependencyType, dependencyType)
                .eq(PackageContextEntity::getLanguage, language)
                .eq(PackageContextEntity::getNamespace, namespace)
                .eq(PackageContextEntity::getName, name)
                .eq(PackageContextEntity::getVersion, version)
                .last("LIMIT 1"); // 只需要知道是否存在，不需要返回所有记录

        return selectCount(wrapper) > 0;
    }

    /**
     * 批量插入或更新知识信息，使用MySQL的批量INSERT ... ON DUPLICATE KEY UPDATE
     * 基于唯一索引：UNIQUE KEY uq_pkg (dependency_type, language, namespace, name, version, package_name)
     *
     * @param entities 要插入或更新的知识实体列表
     * @return 影响的行数
     */
    @Insert({
            "<script>",
            "INSERT INTO t_code_context (dependency_type, language, namespace, name, version, snapshot_version, package_name, class_signatures, ",
            "content_hash, publish_time, is_latest, source_url, metadata_url, source_trace_id) VALUES ",
            "<foreach collection='entities' item='entity' separator=','>",
            "(#{entity.dependencyType}, #{entity.language}, #{entity.namespace}, #{entity.name}, #{entity.version}, #{entity.snapshotVersion}, #{entity.packageName}, ",
            "#{entity.classSignatures, typeHandler=com.xiaohongshu.codewiz.complete.config.MapTypeHandler}, ",
            "#{entity.contentHash}, #{entity.publishTime}, #{entity.isLatest}, #{entity.sourceUrl}, #{entity.metadataUrl}, #{entity.sourceTraceId})",
            "</foreach>",
            "ON DUPLICATE KEY UPDATE ",
            "class_signatures = VALUES(class_signatures), ",
            "content_hash = VALUES(content_hash), ",
            "publish_time = VALUES(publish_time), ",
            "is_latest = VALUES(is_latest), ",
            "source_url = VALUES(source_url), ",
            "metadata_url = VALUES(metadata_url), ",
            "source_trace_id = VALUES(source_trace_id), ",
            "snapshot_version = VALUES(snapshot_version), ",
            "updated_at = CURRENT_TIMESTAMP",
            "</script>"
    })
    int batchInsertOrUpdate(@Param("entities") List<PackageContextEntity> entities);

    /**
     * 将指定dependency的所有版本的is_latest字段设置为false
     * 用于在保存新版本前先清除旧版本的latest标记
     */
    @Update("UPDATE t_code_context SET is_latest = 0 " +
            "WHERE dependency_type = #{dependencyType} AND language = #{language} " +
            "AND namespace = #{namespace} AND name = #{name}")
    int updateIsLatestToFalse(@Param("dependencyType") String dependencyType,
                              @Param("language") String language,
                              @Param("namespace") String namespace,
                              @Param("name") String name);

    /**
     * 删除指定dependency version下的所有package记录
     * 用于在保存新版本前先清除旧版本的所有package数据
     */
    @Delete("DELETE FROM t_code_context " +
            "WHERE dependency_type = #{dependencyType} AND language = #{language} " +
            "AND namespace = #{namespace} AND name = #{name} AND version = #{version}")
    int deleteByDependencyVersion(@Param("dependencyType") String dependencyType,
                                  @Param("language") String language,
                                  @Param("namespace") String namespace,
                                  @Param("name") String name,
                                  @Param("version") String version);

    /**
     * 批量查询多个依赖的知识信息（性能优化版本）
     * 使用IN查询替代OR条件，提升查询性能
     *
     * @param dependencies 依赖查询参数列表
     * @return 知识信息列表
     */
    @Select({
            "<script>",
            "SELECT id, language, dependency_type, namespace, name, version, snapshot_version, package_name, ",
            "class_signatures, is_latest, source_url, metadata_url, ",
            "created_at, updated_at, source_trace_id ",
            "FROM t_code_context ",
            "WHERE (dependency_type, language, namespace, name, version) IN ",
            "<foreach collection='dependencies' item='dep' open='(' separator=',' close=')'>",
            "(#{dep.dependencyType}, #{dep.language}, #{dep.namespace}, #{dep.name}, #{dep.version})",
            "</foreach>",
            "</script>"
    })
    @Results({
            @Result(property = "classSignatures", column = "class_signatures",
                    typeHandler = com.xiaohongshu.codewiz.complete.config.MapTypeHandler.class)
    })
    List<PackageContextEntity> batchSelectByDependencies(@Param("dependencies") List<DependencyQueryParam> dependencies);

    /**
     * 查询指定依赖的最新版本号
     * 根据is_latest=1字段查询，返回版本字符串
     */
    @Select("SELECT version FROM t_code_context " +
            "WHERE dependency_type = #{dependencyType} " +
            "AND language = #{language} " +
            "AND namespace = #{namespace} " +
            "AND name = #{name} " +
            "AND is_latest = 1 " +
            "ORDER BY version DESC " +
            "LIMIT 1")
    String selectLatestVersion(@Param("dependencyType") String dependencyType,
                               @Param("language") String language,
                               @Param("namespace") String namespace,
                               @Param("name") String name);

    /**
     * 查询指定依赖的所有版本号
     * 返回去重的版本列表，按版本字符串排序
     */
    @Select("SELECT DISTINCT version FROM t_code_context " +
            "WHERE dependency_type = #{dependencyType} " +
            "AND language = #{language} " +
            "AND namespace = #{namespace} " +
            "AND name = #{name} " +
            "ORDER BY version")
    List<String> selectAllVersionsByDependency(@Param("dependencyType") String dependencyType,
                                               @Param("language") String language,
                                               @Param("namespace") String namespace,
                                               @Param("name") String name);

    /**
     * 批量检查多个依赖是否存在（性能优化版本）
     * 只返回存在的依赖的标识信息，避免返回大量数据
     */
    @Select({
            "<script>",
            "SELECT DISTINCT dependency_type, language, namespace, name, version ",
            "FROM t_code_context ",
            "WHERE ",
            "<foreach collection='dependencies' item='dep' separator=' OR '>",
            "(dependency_type = #{dep.dependencyType} AND language = #{dep.language} ",
            "AND namespace = #{dep.namespace} AND name = #{dep.name} AND version = #{dep.version})",
            "</foreach>",
            "</script>"
    })
    List<DependencyExistResult> batchCheckDependencyExist(@Param("dependencies") List<DependencyQueryParam> dependencies);

    /**
     * 依赖查询参数
     */
    class DependencyQueryParam {
        private final String dependencyType;
        private final String language;
        private final String namespace;
        private final String name;
        private final String version;
        private final String snapshotVersion;

        public DependencyQueryParam(String dependencyType, String language, String namespace, String name, String version,
                                    String snapshotVersion) {
            this.dependencyType = dependencyType;
            this.language = language;
            this.namespace = namespace;
            this.name = name;
            this.version = version;
            this.snapshotVersion = snapshotVersion;
        }

        // Getters
        public String getDependencyType() {
            return dependencyType;
        }

        public String getLanguage() {
            return language;
        }

        public String getNamespace() {
            return namespace;
        }

        public String getName() {
            return name;
        }

        public String getVersion() {
            return version;
        }

        public String getSnapshotVersion() {
            return snapshotVersion;
        }
    }

    /**
     * 依赖存在性检查结果
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    class DependencyExistResult {
        // Getters and Setters
        private String dependencyType;
        private String language;
        private String namespace;
        private String name;
        private String version;

        /**
         * 生成依赖键，用于匹配
         * 必须与Dependency.getDependencyKey()的格式保持一致
         */
        public String getDependencyKey() {
            return String.format("%s:%s:%s:%s:%s", dependencyType, language, namespace, name, version);
        }
    }
} 