package com.xiaohongshu.codewiz.complete.service;

import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.google.common.collect.Lists;
import com.xiaohongshu.codewiz.core.dao.config.LingmaUserTokenDao;
import com.xiaohongshu.codewiz.core.entity.common.SingleResponse;
import com.xiaohongshu.codewiz.core.entity.config.LingmaUserTokenDo;
import com.xiaohongshu.codewiz.core.entity.config.dto.CreateLingmaTokenRequest;
import com.xiaohongshu.codewiz.core.entity.config.dto.CreateLingmaTokenResponse;
import com.xiaohongshu.codewiz.core.entity.config.dto.GetMappingLoginTokenRes;
import com.xiaohongshu.codewiz.core.entity.config.dto.ProviderFreeAccountNotifyRequest;
import com.xiaohongshu.codewiz.core.entity.config.dto.ProviderFreeAccountNotifyRequest.NotifyInfo;
import com.xiaohongshu.codewiz.core.entity.config.dto.QueryLingmaUserResponse;
import com.xiaohongshu.codewiz.core.feign.LingmaRemoteClient;
import com.xiaohongshu.codewiz.core.feign.ProviderAccountNotifyClient;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.List;
import java.util.Set;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import redis.clients.jedis.Jedis;

/**
 * Author: liukunpeng Date: 2025-05-28 Description:
 */
@Slf4j
@Service
public class ProviderUserTokenService {
  @Autowired
  private LingmaUserTokenDao lingmaUserTokenDao;
  @Autowired
  private LingmaRemoteClient lingmaRemoteClient;
  @Autowired
  private ProviderAccountNotifyClient providerAccountNotifyClient;
  @Resource(name = "codewizJedis")
  private Jedis jedis;
  @Value("${lingma.x-yunxiao-token}")
  private String lingmaToken;
  @ApolloJsonValue("${completion.provider.notify.user: []}")
  private Set<String> notifyUsers;

  @Deprecated
  public SingleResponse<String> getLingmaUserToken(String userEmail) {
    try {
      LingmaUserTokenDo lingmaUserTokenDo = lingmaUserTokenDao.getByEmail(userEmail);
      if (null != lingmaUserTokenDo && StringUtils.isNotBlank(lingmaUserTokenDo.getLingmaToken())) {
        return SingleResponse.of(lingmaUserTokenDo.getLingmaToken());
      }
      return SingleResponse.of("");
      /*String username = userEmail.split("@")[0]; //取邮箱前缀
      QueryLingmaUserResponse userInfo = lingmaRemoteClient.queryUser(username, lingmaToken).getBody();
      if (null == userInfo) {
        log.error("{}不存在lingma用户信息", username);
        return SingleResponse.of("");
      }
      boolean add = null == lingmaUserTokenDo;
      lingmaUserTokenDo = new LingmaUserTokenDo();
      lingmaUserTokenDo.setEmail(userEmail);
      lingmaUserTokenDo.setLingmaUserId(userInfo.getId());
      lingmaUserTokenDo.setUserName(userInfo.getUsername());
      lingmaUserTokenDo.setName(userInfo.getName());
      lingmaUserTokenDo.setNickName(userInfo.getNickName());

      CreateLingmaTokenRequest create = new CreateLingmaTokenRequest();
      create.setDescription("lingma调用使用token");
      create.setName("username-lingma访问令牌");
      create.setPermissions(getUserPermissions());
      create.setUserId(userInfo.getId());
      create.setExpiredAt(OffsetDateTime.now(ZoneOffset.UTC).plusYears(10));
      CreateLingmaTokenResponse response = lingmaRemoteClient.createToken(create, lingmaToken).getBody();
      if (null == response || StringUtils.isBlank(response.getToken())) {
        log.error("创建lingma用户token失败");
      }
      lingmaUserTokenDo.setLingmaToken(response.getToken());
      lingmaUserTokenDo.setLingmaTokenId(response.getTokenId());

      if (add) {
        lingmaUserTokenDo.setCreatedTime(LocalDateTime.now());
        lingmaUserTokenDo.setUpdatedTime(LocalDateTime.now());
        lingmaUserTokenDao.save(lingmaUserTokenDo);
      } else {
        lingmaUserTokenDo.setUpdatedTime(LocalDateTime.now());
        lingmaUserTokenDao.updateById(lingmaUserTokenDo);
      }
      return SingleResponse.of(response.getToken());*/
    } catch (Exception e) {
      log.error("获取用户token失败", e);
      return SingleResponse.of("");
    }
  }
  public SingleResponse<Void> addUserToken(CreateLingmaTokenResponse response) {
    try {
      LingmaUserTokenDo lingmaUserTokenDo = lingmaUserTokenDao.getByEmail(response.getUserEmail());
      boolean add = null == lingmaUserTokenDo;
      if (add) {
        lingmaUserTokenDo = new LingmaUserTokenDo();
      }
      lingmaUserTokenDo.setEmail(response.getUserEmail());
      lingmaUserTokenDo.setLingmaUserId(response.getUserId());
      lingmaUserTokenDo.setUserName(response.getUsername());
      lingmaUserTokenDo.setName(response.getName());
      lingmaUserTokenDo.setNickName(response.getNickName());

      lingmaUserTokenDo.setLingmaToken(response.getToken());
      lingmaUserTokenDo.setLingmaTokenId(response.getTokenId());

      if (add) {
        lingmaUserTokenDo.setCreatedTime(LocalDateTime.now());
        lingmaUserTokenDo.setUpdatedTime(LocalDateTime.now());
        lingmaUserTokenDao.save(lingmaUserTokenDo);
      } else {
        lingmaUserTokenDo.setUpdatedTime(LocalDateTime.now());
        lingmaUserTokenDao.updateById(lingmaUserTokenDo);
      }
      return SingleResponse.ok();
    } catch (Exception e) {
      log.error("获取用户token失败", e);
      return SingleResponse.buildFailure("500", "添加用户token失败");
    }
  }

  public SingleResponse<Void> delUserToken(String userEmail) {
    LingmaUserTokenDo lingmaUserTokenDo = lingmaUserTokenDao.getByEmail(userEmail);
    if (null != lingmaUserTokenDo) {
      lingmaUserTokenDo.setIsDeleted(1);
      lingmaUserTokenDao.updateById(lingmaUserTokenDo);
    }
    return SingleResponse.ok();
  }

  public SingleResponse<GetMappingLoginTokenRes> getLingmaUserTokenV2(String userEmail) {
    GetMappingLoginTokenRes res = new GetMappingLoginTokenRes();
    LingmaUserTokenDo lingmaUserTokenDo = lingmaUserTokenDao.getByEmail(userEmail);
    //存在映射关系，直接返回该邮箱对应的lingma后台token
    if (null != lingmaUserTokenDo) {
      if (StringUtils.isNotBlank(lingmaUserTokenDo.getLingmaToken())) {
        res.setToken(lingmaUserTokenDo.getLingmaToken());
        return SingleResponse.of(res);
      }
      if (StringUtils.isNotBlank(lingmaUserTokenDo.getUserName())) {
        res.setUsername(lingmaUserTokenDo.getUserName());
        return SingleResponse.of(res);
      }
      //都不存在，说名记录存在问题
      lingmaUserTokenDo.setIsDeleted(1);
      lingmaUserTokenDao.updateById(lingmaUserTokenDo);
    }
    List<String> alreadyMapping = Lists.newArrayList();
    //不存在映射关系，尝试获取尚未分配的lingma用户
    while (true) {
      lingmaUserTokenDo = lingmaUserTokenDao.getFreeUser(alreadyMapping);
      if (null == lingmaUserTokenDo) {
        //查询不到可供分配的用户，那么直接返回(走个人版登陆)
        return SingleResponse.of(res);
      }
      //可以查询到，那么先获取锁，防止冲突
      String userMappingKey = "userMapping:" + lingmaUserTokenDo.getUserName();
      if ("OK".equals(jedis.set(userMappingKey, "", "NX", "EX", 60))) {
        //获取到锁，那么进行用户映射
        if (StringUtils.isNotBlank(lingmaUserTokenDo.getLingmaToken())) {
          //未分配但是存在token，说明属于释放账号，那么保存邮箱映射后，直接返回该token
          lingmaUserTokenDo.setEmail(userEmail);
          lingmaUserTokenDao.updateById(lingmaUserTokenDo);
          res.setToken(lingmaUserTokenDo.getLingmaToken());
          return SingleResponse.of(res);
        }
        if (StringUtils.isNotBlank(lingmaUserTokenDo.getUserName())) {
          //没有token，但是存在用户名称，那么进行保存邮箱映射，提供lingma账号名交给插件侧去申请token
          lingmaUserTokenDo.setEmail(userEmail);
          lingmaUserTokenDao.updateById(lingmaUserTokenDo);
          res.setUsername(lingmaUserTokenDo.getUserName());
          return SingleResponse.of(res);
        }
        //既没有token，也没有设置lingma的用户名称，属于异常数据，逻辑删除,继续查询
        lingmaUserTokenDo.setIsDeleted(1);
        lingmaUserTokenDao.updateById(lingmaUserTokenDo);
      } else {
        //获取锁失败，说明该用户正在分配流程中，那么直接跳过该用户，继续查询
        alreadyMapping.add(lingmaUserTokenDo.getUserName());
      }
    }
  }

  /**
   * 半小时执行一次检测
   */
  @Scheduled(fixedRate = 30 * 60 * 1000)
  public void freeAccountMonitor() {
    try {
      //分布式部署，这个简单任务没有必要走调度平台，直接redis自己控制吧
      String lockKey = "codeiwz-freeAccountMonitor";
      if (!"OK".equals(jedis.set(lockKey, "", "NX", "EX", 60*30))) {
        //保证30min仅触发一次
        return;
      }
      int allCount = lingmaUserTokenDao.countValid(false);
      int freeCount = lingmaUserTokenDao.countValid(true);
      log.info("freeAccountMonitor success, 总账号数量：{}，空闲账号数量：{}", allCount, freeCount);
      //可用账号数量小于总账号数量的10%，则认为账号池异常，触发告警
      boolean within10Percent = freeCount * 10 < allCount;
      String content = null;
      if (within10Percent) {
        content = "当前账号池可用账号不足，当前空闲账号小于10%\n";
      } else if (freeCount <= 10) {
        content = "当前账号池可用账号不足，当前空闲账号小于10个\n";
      }
      String globalSkipTimeKey = lockKey + "-globalSkipTime";
      String nowSkipTimeKey = lockKey + "-nowSkipTime";
      if (StringUtils.isNotBlank(content)) {
        //获取本次通知需要跳过的周期数
        long globalSkipTime = jedis.incrBy(globalSkipTimeKey, 0);
        //获取已跳过次数
        long nowSkipTime = jedis.incrBy(nowSkipTimeKey, 0);
        if (nowSkipTime < globalSkipTime) {
          //如果当前跳过次数小于周期跳过次数，那么直接返回
          log.info("freeAccountMonitor skip, globalSkipTime = {}, nowSkipTime = {}", globalSkipTime, nowSkipTime);
          jedis.incrBy(nowSkipTimeKey, 1); //递增
          return;
        }
        //比例不足或者空闲账号数量不足，触发告警
        content += "总账号数量：" + allCount + "，空闲账号数量：" + freeCount;
        ProviderFreeAccountNotifyRequest request = new ProviderFreeAccountNotifyRequest();
        request.setMsgtype("text");
        NotifyInfo text = new NotifyInfo();
        text.setContent(content);
        text.setMentioned_mobile_list(Lists.newArrayList("@all"));
        request.setText(text);
        providerAccountNotifyClient.freeAccountUnEnoughNotify(request);
        //通知完毕，周期+1，重置已跳过
        jedis.incrBy(globalSkipTimeKey, 1);
        jedis.set(nowSkipTimeKey, "0"); //重置已跳过次数
        return;
      }
      //对于不需要发通知，那么直接重置俩redis
      jedis.set(globalSkipTimeKey, "0");
      jedis.set(nowSkipTimeKey, "0");
    } catch (Exception e) {
      log.warn("freeAccountMonitor err, msg = ", e);
      //单独给开发者发送告警，提示去看日志
      if (CollectionUtils.isNotEmpty(notifyUsers)) {
        ProviderFreeAccountNotifyRequest request = new ProviderFreeAccountNotifyRequest();
        request.setMsgtype("text");
        NotifyInfo text = new NotifyInfo();
        text.setContent("服务商账户余额监控异常，msg = " + e
            .getMessage() + "\n详情请查看日志");
        text.setMentioned_mobile_list(Lists.newArrayList(notifyUsers));
        request.setText(text);
        providerAccountNotifyClient.freeAccountUnEnoughNotify(request);
      }
    }
  }
  private List<String> getUserPermissions() {
    return Lists.newArrayList(
        "lingma_developerusage_read",
        "lingma_departmentusage_read",
        "lingma_knowledgebase_read_write",
        "lingma_developermember_read_write",
        "lingma_kbmember_read_write",
        "lingma_kbfile_read_write");
  }
}
