package com.xiaohongshu.codewiz.complete.controller;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.xiaohongshu.codewiz.core.entity.common.SingleResponse;
import com.xiaohongshu.codewiz.core.entity.metrics.MetricsRequest;
import com.xiaohongshu.codewiz.core.service.metrics.MetricsService;

/**
 * <AUTHOR>
 * @date 2025/4/23 15:49
 */
@RestController
@RequestMapping("/metrics")
public class MetricsController {

    @Resource
    private MetricsService metricsService;

    @PostMapping("/v1")
    public SingleResponse<Void> metrics(@RequestBody MetricsRequest request) {
        metricsService.sendMetrics(request);
        return SingleResponse.ok();
    }
}
