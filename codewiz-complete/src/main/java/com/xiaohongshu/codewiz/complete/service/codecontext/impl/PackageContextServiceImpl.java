package com.xiaohongshu.codewiz.complete.service.codecontext.impl;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import org.slf4j.MDC;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.databind.ObjectMapper;

import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.xiaohongshu.codewiz.complete.config.PackageContextGrayControlConf;
import com.xiaohongshu.codewiz.complete.dto.packagecontext.DependencyContextDTO;
import com.xiaohongshu.codewiz.complete.dto.packagecontext.DependencyDTO;
import com.xiaohongshu.codewiz.complete.dto.packagecontext.PackageContextDTO;
import com.xiaohongshu.codewiz.complete.dto.packagecontext.PackageContextQueryReq;
import com.xiaohongshu.codewiz.complete.dto.packagecontext.PackageContextQueryResp;
import com.xiaohongshu.codewiz.complete.model.dpc.DependencyPackageContext;
import com.xiaohongshu.codewiz.complete.model.dpc.PackageContextItem;
import com.xiaohongshu.codewiz.complete.model.dependency.Dependency;
import com.xiaohongshu.codewiz.complete.model.dependency.DependencyType;
import com.xiaohongshu.codewiz.complete.model.lang.LanguageType;
import com.xiaohongshu.codewiz.complete.service.codecontext.PackageContextService;
import com.xiaohongshu.codewiz.complete.store.PackageContextStore;
import com.xiaohongshu.codewiz.core.annotation.LogExecutionTime;
import com.xiaohongshu.xray.logging.LogConstants;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 代码上下文服务实现类 - 简化版
 * 只负责查询和记录缺失依赖
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PackageContextServiceImpl implements PackageContextService {

    private final PackageContextStore packageContextStore;
    private final ObjectMapper objectMapper = new ObjectMapper();

    @ApolloJsonValue("${dependency_package_context.graycontrol.config}")
    private PackageContextGrayControlConf packageContextGrayControlConf;

    @Override
    @LogExecutionTime
    public PackageContextQueryResp queryContext(PackageContextQueryReq request) {
        if (!packageContextGrayControlConf.isOn()) {
            log.info("灰度关闭，不处理请求");
            return PackageContextQueryResp.buildSuccess(new ArrayList<>());
        }

        if (packageContextGrayControlConf.getUseDefault()) {
            log.info("忽略当前请求，使用默认请求");
            // 从default_req.json 导入
            request = loadDefaultRequest();
        }
        String xrayTraceId = MDC.get(LogConstants.XRAY_TRACE_ID);
        log.info("开始查询依赖上下文，依赖数量: {}, trace_id: {}", request.getDependencies().size(), xrayTraceId);

        // 参数验证
        PackageContextQueryResp validationResult = validateRequest(request);
        if (validationResult != null) {
            return validationResult;
        }

        // 1. 解析所有依赖
        List<Dependency> parsedDependencies = parseDependencies(request);
        if (parsedDependencies.isEmpty()) {
            log.warn("无有效的依赖信息");
            return PackageContextQueryResp.buildFailure("400", "无有效的依赖信息");
        }

        List<DependencyContextDTO> results = new ArrayList<>();
        List<Dependency> missingDependencies = new ArrayList<>();

        // 2. 找出未指定版本的依赖，尝试查询最新版本，若找不到最新版本，则记录到缺失依赖表中
        List<Dependency> latestVersionDependencies = parsedDependencies.stream().filter(Dependency::isLatest).collect(Collectors.toList());
        Map<String, String> latestVersionMap = packageContextStore.batchGetLatestVersion(latestVersionDependencies);

        for (Dependency dependency : latestVersionDependencies) {
            String latestVersion = latestVersionMap.get(dependency.getDependencySignature());
            if (latestVersion == null) {
                log.warn("依赖 {} 的最新版本未找到，记录到缺失依赖表中", dependency.getDependencySignature());
                missingDependencies.add(dependency);
                results.add(buildProcessingResult(dependency));
            } else {
                dependency.setVersion(latestVersion);
            }
        }


        // 3. 批量查询
        List<DependencyPackageContext> dependencyPackageContexts = packageContextStore.batchGetPackageContexts(parsedDependencies);

        // 4. 找到缺失依赖项，并构建结果
        Map<String, DependencyPackageContext> packageContextMap =
                dependencyPackageContexts.stream()
                        .collect(Collectors.toMap(DependencyPackageContext::getDependencyKey,
                                dependencyPackageContext -> dependencyPackageContext));
        for (Dependency dependency : parsedDependencies) {
            String key = dependency.getDependencyKey();
            DependencyPackageContext dependencyPackageContext = packageContextMap.get(key);
            if (dependencyPackageContext == null) {
                missingDependencies.add(dependency);
                results.add(buildProcessingResult(dependency));
            } else {
                log.info("{} -> {}个package", key, dependencyPackageContext.getPackageContextItems().size());
                results.add(buildSucessResult(dependency, dependencyPackageContext.getPackageContextItems()));
            }
        }
        log.info("依赖上下文查询完成，trace_id: {}", xrayTraceId);

        // 4. 异步记录缺失的依赖
        if (!missingDependencies.isEmpty()) {
            log.info("发现{}个依赖不在知识库中，记录到缺失依赖表", missingDependencies.size());
            packageContextStore.asyncRecordMissingDependencies(missingDependencies, xrayTraceId);
        }

        // 5. 统计结果
        int successCount = (int) results.stream().filter(DependencyContextDTO::getSuccess).count();
        int processingCount = (int) results.stream().filter(result ->
                !result.getSuccess() && result.getErrorMessage() != null && result.getErrorMessage().contains("正在后台构建中")).count();
        log.info("成功解析依赖数量: {}, 正在处理依赖数量: {}, 失败解析依赖数量: {}",
                successCount, processingCount, results.size() - successCount - processingCount);

        return PackageContextQueryResp.buildSuccess(results);
    }

    /**
     * 验证请求参数
     *
     * @param request 请求参数
     * @return 如果验证失败返回错误响应，验证成功返回null
     */
    private PackageContextQueryResp validateRequest(PackageContextQueryReq request) {
        if (request.getDependencies() == null || request.getDependencies().isEmpty()) {
            return PackageContextQueryResp.buildFailure("400", "依赖列表不能为空");
        }

        // 未设置依赖类型则返回失败
        if (request.getDependencyType() == null) {
            return PackageContextQueryResp.buildFailure("400", "依赖类型不能为空");
        }
        DependencyType dependencyTypeEnum = parseDependencyType(request.getDependencyType());
        if (dependencyTypeEnum == null) {
            return PackageContextQueryResp.buildFailure("400", "依赖类型不支持");
        }
        // 未设置语言类型则返回失败
        if (request.getLanguage() == null) {
            return PackageContextQueryResp.buildFailure("400", "语言类型不能为空");
        }

        return null; // 验证通过
    }

    /**
     * 解析依赖列表
     *
     * @param request 请求参数
     * @return 解析后的依赖列表
     */
    private List<Dependency> parseDependencies(PackageContextQueryReq request) {
        return request.getDependencies().stream()
                .map(item -> parseDependencyItem(item, request.getLanguage(), request.getDependencyType()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 解析依赖项
     */
    private Dependency parseDependencyItem(DependencyDTO item, String language, String dependencyType) {
        if (item == null || item.getNamespace() == null || item.getName() == null) {
            return null;
        }

        LanguageType languageType = parseLanguageType(language);
        DependencyType dependencyTypeEnum = parseDependencyType(dependencyType);
        Dependency dependency = Dependency.builder()
                .namespace(item.getNamespace())
                .name(item.getName())
                .language(languageType)
                .dependencyType(dependencyTypeEnum)
                .build();

        // 处理版本
        if (item.getVersion() == null || item.getVersion().isEmpty()) {
            // 指向latest version
            dependency.setLatest(true);
        } else {
            dependency.setVersion(item.getVersion());
        }

        return dependency;
    }

    /**
     * 解析语言类型
     */
    private LanguageType parseLanguageType(String languageType) {
        if (languageType == null) {
            return LanguageType.JAVA; // 默认Java
        }

        try {
            return LanguageType.valueOf(languageType.toUpperCase());
        } catch (IllegalArgumentException e) {
            log.warn("未知的语言类型: {}, 使用默认值JAVA", languageType);
            return LanguageType.JAVA;
        }
    }

    /**
     * 解析依赖类型
     */
    private DependencyType parseDependencyType(String dependencyType) {
        if (dependencyType == null) {
            return DependencyType.UNKNOWN; // 默认Java
        }

        try {
            return DependencyType.valueOf(dependencyType.toUpperCase());
        } catch (IllegalArgumentException e) {
            log.warn("未知的依赖类型: {}", dependencyType);
            return DependencyType.UNKNOWN;
        }
    }

    /**
     * 从多个Knowledge构建成功结果（一个dependency对应一个result）
     */
    private DependencyContextDTO buildSucessResult(Dependency dependency,
                                                   List<PackageContextItem> packageContextItemList) {
        List<PackageContextDTO> packageContextDTOList = new ArrayList<>();

        // 收集包级别的错误信息
        List<String> packageErrors = new ArrayList<>();

        // 合并所有knowledge的class signatures和package info
        for (PackageContextItem packageContextItem : packageContextItemList) {
            // 检查是否有错误信息
            boolean hasError = packageContextItem.getHasError() != null && packageContextItem.getHasError();
            if (hasError) {
                String errorInfo = String.format("包 %s: %s",
                        packageContextItem.getPackageName(),
                        packageContextItem.getErrorMessage());
                packageErrors.add(errorInfo);
            }

            PackageContextDTO packageContextDTOResult = PackageContextDTO.builder()
                    .packageName(packageContextItem.getPackageName())
                    .classSignatures(packageContextItem.getClassSignatures())
                    .classCount(packageContextItem.getClassSignatureCount())
                    .fileCount(packageContextItem.getClassSignatureCount())
                    .hasError(hasError)
                    .errorMessage(hasError ? packageContextItem.getErrorMessage() : null)
                    .errorType(hasError ? packageContextItem.getErrorType() : null)
                    .build();

            packageContextDTOList.add(packageContextDTOResult);
        }

        // 构建结果，包含错误信息
        DependencyContextDTO.DependencyContextDTOBuilder resultBuilder = DependencyContextDTO.builder()
                .namespace(dependency.getNamespace())
                .name(dependency.getName())
                .version(dependency.getVersion())
                .language(dependency.getLanguage() != null ? dependency.getLanguage().name() : "UNKNOWN")
                .dependencyType(dependency.getDependencyType() != null ? dependency.getDependencyType().name() : "UNKNOWN")
                .packages(packageContextDTOList)
                .success(true);

        // 如果有包级别的错误，添加到错误信息中
        if (!packageErrors.isEmpty()) {
            String errorMessage = "部分包构建有错误: " + String.join("; ", packageErrors);
            resultBuilder.errorMessage(errorMessage);
        }

        return resultBuilder.build();
    }

    /**
     * 构建正在处理中的结果
     */
    private DependencyContextDTO buildProcessingResult(Dependency dependency) {
        return DependencyContextDTO.builder()
                .namespace(dependency.getNamespace())
                .name(dependency.getName())
                .version(dependency.getVersion())
                .language(dependency.getLanguage() != null ? dependency.getLanguage().name() : "UNKNOWN")
                .dependencyType(dependency.getDependencyType() != null ? dependency.getDependencyType().name() : "UNKNOWN")
                .packages(Collections.emptyList())
                .success(false)
                .errorMessage("依赖知识正在后台构建中，请稍后再试")
                .build();
    }

    /**
     * 从resources目录下的default.json文件中加载默认请求配置
     */
    private PackageContextQueryReq loadDefaultRequest() {
        try (InputStream inputStream = getClass().getClassLoader().getResourceAsStream("default_req.json")) {
            if (inputStream == null) {
                log.warn("未找到default_req.json文件，使用空的默认请求");
                return new PackageContextQueryReq();
            }
            
            PackageContextQueryReq defaultRequest = objectMapper.readValue(inputStream, PackageContextQueryReq.class);
            log.info("成功加载default_req.json配置，依赖数量: {}, 内容: {}", 
                    defaultRequest.getDependencies() != null ? defaultRequest.getDependencies().size() : 0, defaultRequest);
            return defaultRequest;
        } catch (IOException e) {
            log.error("读取default_req.json文件失败", e);
            return new PackageContextQueryReq();
        }
    }
}