package com.xiaohongshu.codewiz.complete.dto.logdata;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 日志环境信息
 */
@Data
public class LogEnvironment {

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 所属工作空间/项目空间uri
     */
    private String workspaceUri;

    /**
     * 插件或SDK版本号
     */
    private String pluginVersion;

    /**
     * 所属OS平台 (可选)
     */
    private String os;
} 