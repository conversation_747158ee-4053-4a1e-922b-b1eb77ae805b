package com.xiaohongshu.codewiz.complete.store;

import java.util.List;
import java.util.Map;

import com.xiaohongshu.codewiz.complete.model.dpc.DependencyPackageContext;
import com.xiaohongshu.codewiz.complete.model.dependency.Dependency;

/**
 * 知识存储接口
 * 负责知识的读取和写入，底层接入MySQL
 */
public interface PackageContextStore {

    /**
     * 原子性批量保存知识信息
     * 确保所有知识项要么全部成功，要么全部失败（单个dependency级别的事务性）
     *
     * @return 成功保存的数量
     * @throws Exception 如果保存失败，抛出异常并回滚事务
     */
    int saveDependencyPackageContext(DependencyPackageContext dependencyPackageContext) throws Exception;

    /**
     * 批量获取知识信息
     * 对于snapshot版本，会使用snapshot_version进行查询
     * 对于null版本，会查询is_latest=true的记录
     *
     * @param dependencies 依赖列表
     * @return 知识信息Map，key为依赖键值，value为该依赖的所有knowledge列表
     */
    List<DependencyPackageContext> batchGetPackageContexts(List<Dependency> dependencies);

    /**
     * 批量检查多个依赖是否存在
     *
     * @param dependencies 依赖列表
     * @return 依赖存在性Map，key为依赖键值，value为是否存在
     */
    Map<String, Boolean> batchCheckDependencyExist(List<Dependency> dependencies);

    /**
     * 批量获取最新版本
     * 
     * @param dependencies 依赖列表（没有指定版本的依赖）
     * @return 版本信息Map，key为依赖键值（不包含版本），value为最新版本
     */
    Map<String, String> batchGetLatestVersion(List<Dependency> dependencies);

    /**
     * 异步记录缺失的依赖
     *
     * @param dependencies 缺失的依赖列表
     * @param traceId      跟踪ID
     */
    void asyncRecordMissingDependencies(List<Dependency> dependencies, String traceId);

    /**
     * 获取待处理的缺失依赖
     *
     * @param limit 限制数量
     * @return 待处理的依赖列表
     */
    List<Dependency> getMissingDependencies(int limit);

    /**
     * 标记缺失依赖为已处理（删除记录）
     *
     * @param dependencies 已处理的依赖列表
     * @return 删除的记录数
     */
    int markMissingDependenciesAsProcessed(List<Dependency> dependencies);

    /**
     * 标记缺失依赖为跳过状态
     *
     * @param dependencies 需要跳过的依赖列表
     * @param skipReason 跳过原因
     * @return 更新的记录数
     */
    int markMissingDependenciesAsSkipped(List<Dependency> dependencies, String skipReason);

    /**
     * 获取指定依赖的最新版本号
     * 根据is_latest=1查询数据库中的最新版本
     *
     * @param dependency 依赖信息
     * @return 最新版本号，如果不存在则返回null
     */
    String getLatestVersion(Dependency dependency);

    /**
     * 获取指定依赖的所有版本号
     * 查询数据库中该dependency的所有版本（去重）
     *
     * @param dependency 依赖信息（不包含版本）
     * @return 版本号列表，如果不存在则返回空列表
     */
    List<String> getAllVersions(Dependency dependency);
} 