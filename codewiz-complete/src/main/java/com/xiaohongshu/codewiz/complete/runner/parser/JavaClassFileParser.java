package com.xiaohongshu.codewiz.complete.runner.parser;

import com.xiaohongshu.codewiz.complete.model.lang.SourceFile;
import com.xiaohongshu.codewiz.core.annotation.LogExecutionTime;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import org.objectweb.asm.*;
import org.objectweb.asm.signature.SignatureReader;
import org.objectweb.asm.signature.SignatureVisitor;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 基于ASM的Java字节码文件解析器
 * 专门负责解析单个.class文件，使用ASM库深度解析Java字节码，
 * 提取完整的类、方法、字段等结构信息，能力对齐JavaSourceParser
 */
@Slf4j
@Component
public class JavaClassFileParser {

    /**
     * 解析Java字节码文件 - 主要解析方法，与JavaSourceParser接口对齐
     * @param fileName 文件名
     * @param classBytes 字节码数据
     * @return 解析后的SourceFile对象
     */
    @LogExecutionTime
    public static SourceFile parseJavaClassFile(String fileName, byte[] classBytes) {
        try {
            // 对特定问题文件进行预检查
            if (classBytes == null || classBytes.length == 0) {
                log.warn("Java字节码内容为空: {}", fileName);
                return null;
            }
            
            // 使用ASM解析字节码
            ClassReader classReader = new ClassReader(classBytes);
            ClassInfoCollector collector = new ClassInfoCollector();
            classReader.accept(collector, ClassReader.SKIP_DEBUG);
            
            return collector.buildSourceFile(fileName);
                    
        } catch (Exception e) {
            log.error("解析Java字节码失败: {}", fileName, e);
            return null;
        }
    }
    
    
    /**
     * ASM ClassVisitor实现，用于收集类信息
     */
    private static class ClassInfoCollector extends ClassVisitor {
        private String className;
        private String packageName;
        private String superClass;
        private List<String> interfaces = new ArrayList<>();
        private List<SourceFile.AnnotationInfo> classAnnotations = new ArrayList<>();
        private List<SourceFile.FieldInfo> fields = new ArrayList<>();
        private List<SourceFile.MethodInfo> methods = new ArrayList<>();
        private String classType = "class";
        private String visibility = "package-private";
        private List<String> modifiers = new ArrayList<>();
        private List<String> genericTypes = new ArrayList<>();
        private String signature;
        private int access;

        public ClassInfoCollector() {
            super(Opcodes.ASM9);
        }

        @Override
        public void visit(int version, int access, String name, String signature, String superName, String[] interfaces) {
            this.access = access;
            this.signature = signature;
            
            // 解析类名和包名
            String fullClassName = name.replace('/', '.');
            int lastDot = fullClassName.lastIndexOf('.');
            if (lastDot > 0) {
                this.packageName = fullClassName.substring(0, lastDot);
                this.className = fullClassName.substring(lastDot + 1);
            } else {
                this.className = fullClassName;
            }
            
            // 设置超类
            if (superName != null && !superName.equals("java/lang/Object")) {
                this.superClass = superName.replace('/', '.');
            }
            
            // 设置接口
            if (interfaces != null) {
                this.interfaces = Arrays.stream(interfaces)
                        .map(iface -> iface.replace('/', '.'))
                        .collect(Collectors.toList());
            }
            
            // 设置类型和修饰符
            this.classType = determineClassType(access);
            this.visibility = determineVisibility(access);
            this.modifiers = determineModifiers(access);
            
            // 解析泛型信息
            if (signature != null) {
                this.genericTypes = parseGenericSignature(signature);
            }
        }

        @Override
        public AnnotationVisitor visitAnnotation(String descriptor, boolean visible) {
            String annotationName = Type.getType(descriptor).getClassName();
            SourceFile.AnnotationInfo annotationInfo = SourceFile.AnnotationInfo.builder()
                    .annotationName(annotationName)
                    .attributes(new ArrayList<>())
                    .build();
            classAnnotations.add(annotationInfo);
            
            return new AnnotationVisitor(Opcodes.ASM9) {
                @Override
                public void visit(String name, Object value) {
                    SourceFile.AnnotationInfo.AnnotationAttribute attribute = 
                            SourceFile.AnnotationInfo.AnnotationAttribute.builder()
                                    .name(name != null ? name : "value")
                                    .value(value != null ? value.toString() : "")
                                    .build();
                    annotationInfo.getAttributes().add(attribute);
                }
            };
        }

        @Override
        public FieldVisitor visitField(int access, String name, String descriptor, String signature, Object value) {
            String fieldType = Type.getType(descriptor).getClassName();
            String fieldVisibility = determineVisibility(access);
            List<String> fieldModifiers = determineModifiers(access);
            String defaultValue = value != null ? value.toString() : null;
            
            List<SourceFile.AnnotationInfo> fieldAnnotations = new ArrayList<>();
            
            SourceFile.FieldInfo fieldInfo = SourceFile.FieldInfo.builder()
                    .fieldName(name)
                    .fieldType(fieldType)
                    .visibility(fieldVisibility)
                    .modifiers(fieldModifiers)
                    .defaultValue(defaultValue)
                    .annotations(fieldAnnotations)
                    .javadoc(null) // 字节码中没有JavaDoc
                    .lineNumber(0) // 字节码中没有行号
                    .build();
            
            fields.add(fieldInfo);
            
            return new FieldVisitor(Opcodes.ASM9) {
                @Override
                public AnnotationVisitor visitAnnotation(String descriptor, boolean visible) {
                    String annotationName = Type.getType(descriptor).getClassName();
                    SourceFile.AnnotationInfo annotationInfo = SourceFile.AnnotationInfo.builder()
                            .annotationName(annotationName)
                            .attributes(new ArrayList<>())
                            .build();
                    fieldAnnotations.add(annotationInfo);
                    return null;
                }
            };
        }

        @Override
        public MethodVisitor visitMethod(int access, String name, String descriptor, String signature, String[] exceptions) {
            Type methodType = Type.getType(descriptor);
            String returnType = methodType.getReturnType().getClassName();
            String methodVisibility = determineVisibility(access);
            List<String> methodModifiers = determineModifiers(access);
            boolean isConstructor = name.equals("<init>");
            
            // 解析参数
            List<SourceFile.ParameterInfo> parameters = new ArrayList<>();
            Type[] argumentTypes = methodType.getArgumentTypes();
            for (int i = 0; i < argumentTypes.length; i++) {
                String paramType = argumentTypes[i].getClassName();
                String paramName = "arg" + i; // 字节码中参数名可能不可用
                
                SourceFile.ParameterInfo paramInfo = SourceFile.ParameterInfo.builder()
                        .parameterName(paramName)
                        .parameterType(paramType)
                        .modifiers(new ArrayList<>())
                        .annotations(new ArrayList<>())
                        .build();
                parameters.add(paramInfo);
            }
            
            // 解析异常
            List<String> methodExceptions = exceptions != null ? 
                    Arrays.stream(exceptions)
                            .map(ex -> ex.replace('/', '.'))
                            .collect(Collectors.toList()) : 
                    new ArrayList<>();
            
            // 解析泛型信息
            List<String> methodGenericTypes = signature != null ? 
                    parseMethodGenericSignature(signature) : 
                    new ArrayList<>();
            
            List<SourceFile.AnnotationInfo> methodAnnotations = new ArrayList<>();
            
            SourceFile.MethodInfo methodInfo = SourceFile.MethodInfo.builder()
                    .methodName(isConstructor ? className : name)
                    .returnType(isConstructor ? null : returnType)
                    .visibility(methodVisibility)
                    .modifiers(methodModifiers)
                    .parameters(parameters)
                    .exceptions(methodExceptions)
                    .genericTypes(methodGenericTypes)
                    .annotations(methodAnnotations)
                    .isConstructor(isConstructor)
                    .javadoc(null) // 字节码中没有JavaDoc
                    .lineNumber(0) // 字节码中没有行号
                    .methodBody(null) // 字节码中没有方法体源码
                    .build();
            
            methods.add(methodInfo);
            
            return new MethodVisitor(Opcodes.ASM9) {
                @Override
                public AnnotationVisitor visitAnnotation(String descriptor, boolean visible) {
                    String annotationName = Type.getType(descriptor).getClassName();
                    SourceFile.AnnotationInfo annotationInfo = SourceFile.AnnotationInfo.builder()
                            .annotationName(annotationName)
                            .attributes(new ArrayList<>())
                            .build();
                    methodAnnotations.add(annotationInfo);
                    return null;
                }
            };
        }

        public SourceFile buildSourceFile(String fileName) {
            // 为enum类型提取enum常量
            List<SourceFile.EnumConstantInfo> enumConstants = new ArrayList<>();
            if ("enum".equals(classType)) {
                enumConstants = extractEnumConstants();
            }
            
            SourceFile.ClassInfo classInfo = SourceFile.ClassInfo.builder()
                    .className(className)
                    .classType(classType)
                    .visibility(visibility)
                    .modifiers(modifiers)
                    .extendsClass(superClass)
                    .implementsInterfaces(interfaces)
                    .genericTypes(genericTypes)
                    .annotations(classAnnotations)
                    .fields(fields)
                    .methods(methods)
                    .innerClasses(new ArrayList<>()) // 内部类需要单独处理
                    .enumConstants(enumConstants)
                    .javadoc(null) // 字节码中没有JavaDoc
                    .lineNumber(0) // 字节码中没有行号
                    .signature(buildClassSignature())
                    .build();
            
            return SourceFile.builder()
                    .fileName(fileName)
                    .content(null) // 字节码文件没有源码内容
                    .packageName(packageName)
                    .imports(new ArrayList<>()) // 字节码文件没有import信息
                    .classes(Collections.singletonList(classInfo))
                    .build();
        }

        private String buildClassSignature() {
            StringBuilder sb = new StringBuilder();
            sb.append(visibility);
            if (!modifiers.isEmpty()) {
                sb.append(" ").append(String.join(" ", modifiers));
            }
            sb.append(" ").append(classType).append(" ").append(className);
            if (superClass != null && !superClass.equals("java.lang.Object")) {
                sb.append(" extends ").append(superClass);
            }
            if (!interfaces.isEmpty()) {
                sb.append(" implements ").append(String.join(", ", interfaces));
            }
            return sb.toString();
        }

        private String determineClassType(int access) {
            if ((access & Opcodes.ACC_INTERFACE) != 0) {
                return "interface";
            } else if ((access & Opcodes.ACC_ENUM) != 0) {
                return "enum";
            } else if ((access & Opcodes.ACC_ANNOTATION) != 0) {
                return "annotation";
            }
            return "class";
        }

        private String determineVisibility(int access) {
            if ((access & Opcodes.ACC_PUBLIC) != 0) {
                return "public";
            } else if ((access & Opcodes.ACC_PROTECTED) != 0) {
                return "protected";
            } else if ((access & Opcodes.ACC_PRIVATE) != 0) {
                return "private";
            }
            return "package-private";
        }

        private List<String> determineModifiers(int access) {
            List<String> modifiers = new ArrayList<>();
            
            if ((access & Opcodes.ACC_STATIC) != 0) modifiers.add("static");
            if ((access & Opcodes.ACC_FINAL) != 0) modifiers.add("final");
            if ((access & Opcodes.ACC_ABSTRACT) != 0) modifiers.add("abstract");
            if ((access & Opcodes.ACC_SYNCHRONIZED) != 0) modifiers.add("synchronized");
            if ((access & Opcodes.ACC_NATIVE) != 0) modifiers.add("native");
            if ((access & Opcodes.ACC_STRICT) != 0) modifiers.add("strictfp");
            if ((access & Opcodes.ACC_VOLATILE) != 0) modifiers.add("volatile");
            if ((access & Opcodes.ACC_TRANSIENT) != 0) modifiers.add("transient");
            
            return modifiers;
        }

        private List<String> parseGenericSignature(String signature) {
            List<String> genericTypes = new ArrayList<>();
            try {
                SignatureReader signatureReader = new SignatureReader(signature);
                signatureReader.accept(new SignatureVisitor(Opcodes.ASM9) {
                    @Override
                    public void visitFormalTypeParameter(String name) {
                        genericTypes.add(name);
                    }
                });
            } catch (Exception e) {
                log.warn("解析泛型签名失败: {}", signature, e);
            }
            return genericTypes;
        }

        private List<String> parseMethodGenericSignature(String signature) {
            List<String> genericTypes = new ArrayList<>();
            try {
                SignatureReader signatureReader = new SignatureReader(signature);
                signatureReader.accept(new SignatureVisitor(Opcodes.ASM9) {
                    @Override
                    public void visitFormalTypeParameter(String name) {
                        genericTypes.add(name);
                    }
                });
            } catch (Exception e) {
                log.warn("解析方法泛型签名失败: {}", signature, e);
            }
            return genericTypes;
        }
        
        /**
         * 从字段中提取enum常量
         * 在字节码中，enum常量表现为public static final字段，且类型为enum类本身
         */
        private List<SourceFile.EnumConstantInfo> extractEnumConstants() {
            List<SourceFile.EnumConstantInfo> enumConstants = new ArrayList<>();
            
            for (SourceFile.FieldInfo field : fields) {
                // enum常量的特征：public static final，且类型为enum类本身
                if (field.getModifiers() != null 
                    && field.getModifiers().contains("static") 
                    && field.getModifiers().contains("final")
                    && "public".equals(field.getVisibility())
                    && field.getFieldType().equals(className)) {
                    
                    // 创建enum常量信息
                    SourceFile.EnumConstantInfo enumConstant = SourceFile.EnumConstantInfo.builder()
                            .constantName(field.getFieldName())
                            .arguments(new ArrayList<>()) // 字节码中无法获取构造函数参数
                            .annotations(field.getAnnotations() != null ? 
                                    new ArrayList<>(field.getAnnotations()) : new ArrayList<>())
                            .javadoc(field.getJavadoc()) // 字节码中通常没有JavaDoc
                            .lineNumber(field.getLineNumber())
                            .build();
                    
                    enumConstants.add(enumConstant);
                }
            }
            
            return enumConstants;
        }
    }
} 