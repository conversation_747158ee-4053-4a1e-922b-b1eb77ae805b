package com.xiaohongshu.codewiz.complete.dto.packagecontext;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

/**
 * 依赖项详细信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DependencyDTO {
    
    /**
     * 依赖命名空间/组织标识 (如Java的groupId、NPM的@scope等)
     */
    private String namespace;
    
    /**
     * 依赖名称 (如Java的artifactId、Python的package名等)
     */
    private String name;
    
    /**
     * 依赖版本（可选，为空时获取最新版本）
     */
    private String version;
} 