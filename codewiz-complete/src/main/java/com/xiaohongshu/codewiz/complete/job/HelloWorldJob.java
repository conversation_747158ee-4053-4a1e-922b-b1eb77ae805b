package com.xiaohongshu.codewiz.complete.job;

import org.springframework.stereotype.Component;

import com.xiaohongshu.infra.redschedule.api.Parameter;
import com.xiaohongshu.infra.redschedule.api.RedSchedule;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2025/3/2
 */
@Slf4j
@Component
public class HelloWorldJob {

    @RedSchedule(value = "complete.hellworld", autoFillAppid = true)
    public void demoJobTest(Parameter parameter) {
        // 直接Parameter打印,请勿json格式化再打印
        log.info("demoJobTest parameter:{}", parameter);
        // 获取某个key
        // 详细见 <a href="https://docs.xiaohongshu.com/doc/291b6d094e7853d2a6ffc335709e311b">周期参数说明</a>
        String key = parameter.getString("key");
        log.info("hello world key:{}", key);
    }
}
