package com.xiaohongshu.codewiz.complete.runner.model;

/**
 * 依赖生命周期状态枚举
 */
public enum LifecycleState {
    
    /**
     * 初始化
     */
    INITIALIZED("初始化", 0),
    
    /**
     * 解析中
     */
    PARSING("解析中", 1),
    
    /**
     * 解析完成
     */
    PARSED("解析完成", 2),
    
    /**
     * 版本解析中
     */
    RESOLVING_VERSIONS("版本解析中", 3),
    
    /**
     * 版本解析完成
     */
    VERSIONS_RESOLVED("版本解析完成", 4),
    
    /**
     * 获取制品中
     */
    FETCHING_ARTIFACT("获取制品中", 5),
    
    /**
     * 制品获取完成
     */
    ARTIFACT_FETCHED("制品获取完成", 6),
    
    /**
     * 构建知识中
     */
    BUILDING_KNOWLEDGE("构建知识中", 7),
    
    /**
     * 知识构建完成
     */
    KNOWLEDGE_BUILT("知识构建完成", 8),
    
    /**
     * 处理完成
     */
    COMPLETED("处理完成", 9),
    
    /**
     * 处理失败
     */
    FAILED("处理失败", -1),
    
    /**
     * 跳过处理
     */
    SKIPPED("跳过处理", -2);
    
    private final String displayName;
    private final int order;
    
    LifecycleState(String displayName, int order) {
        this.displayName = displayName;
        this.order = order;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    public int getOrder() {
        return order;
    }
    
    /**
     * 是否为终态
     */
    public boolean isTerminal() {
        return this == COMPLETED || this == FAILED || this == SKIPPED;
    }
    
    /**
     * 是否为成功状态
     */
    public boolean isSuccess() {
        return this == COMPLETED;
    }
} 