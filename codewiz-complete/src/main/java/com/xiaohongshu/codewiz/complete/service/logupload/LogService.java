package com.xiaohongshu.codewiz.complete.service.logupload;

import com.xiaohongshu.codewiz.complete.dto.logdata.LogBatchUploadReq;
import com.xiaohongshu.codewiz.complete.dto.logdata.LogBatchUploadResp;

/**
 * 日志服务接口
 */
public interface LogService {

    /**
     * 批量上报日志
     *
     * @param request 批量日志上报请求
     * @return 上报结果
     */
    LogBatchUploadResp batchUpload(LogBatchUploadReq request);
} 