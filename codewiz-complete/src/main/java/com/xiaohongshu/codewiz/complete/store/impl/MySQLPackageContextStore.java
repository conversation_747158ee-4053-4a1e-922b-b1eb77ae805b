package com.xiaohongshu.codewiz.complete.store.impl;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import com.xiaohongshu.codewiz.complete.entity.MissingDependencyEntity;
import com.xiaohongshu.codewiz.complete.entity.PackageContextEntity;
import com.xiaohongshu.codewiz.complete.mapper.KnowledgeMapper;
import com.xiaohongshu.codewiz.complete.mapper.MissingDependencyMapper;
import com.xiaohongshu.codewiz.complete.model.dependency.Dependency;
import com.xiaohongshu.codewiz.complete.model.dependency.DependencyType;
import com.xiaohongshu.codewiz.complete.model.dpc.DependencyPackageContext;
import com.xiaohongshu.codewiz.complete.model.dpc.PackageContextItem;
import com.xiaohongshu.codewiz.complete.model.lang.LanguageType;
import com.xiaohongshu.codewiz.complete.store.PackageContextStore;
import com.xiaohongshu.codewiz.core.annotation.LogExecutionTime;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * MySQL知识存储实现
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class MySQLPackageContextStore implements PackageContextStore {

    /**
     * 批量查询的批次大小
     * MySQL建议单次查询参数不超过200个，这里设置为200以保持安全边界
     * 可通过配置文件调整: codewiz.complete.batch.size
     */
    private static final int DEFAULT_BATCH_SIZE = 200;
    private final KnowledgeMapper knowledgeMapper;
    // 批量查询配置参数
    private final MissingDependencyMapper missingDependencyMapper;


    /**
     * 保存依赖包上下文
     *
     * @param dependencyPackageContext 依赖包上下文数据
     * @return 成功保存的数量
     * @throws Exception 如果保存失败
     */
    @LogExecutionTime
    @Transactional(rollbackFor = Exception.class)
    public int saveDependencyPackageContext(DependencyPackageContext dependencyPackageContext) throws Exception {
        if (dependencyPackageContext == null || !dependencyPackageContext.isValid()) {
            log.warn("依赖包上下文数据无效，跳过保存");
            return 0;
        }

        Dependency dependency = dependencyPackageContext.getDependency();
        boolean isLatest = dependencyPackageContext.isLatest();
        boolean isSnapshot = dependencyPackageContext.isSnapshot();

        log.info("开始保存依赖包上下文，dependency: {}:{}:{}, 包数量: {}, 是否最新: {}, 是否为snapshot: {}",
                dependency.getNamespace(), dependency.getName(), dependency.getVersion(),
                dependencyPackageContext.getPackageContextItems().size(), isLatest, isSnapshot);

        if (isLatest) {
            try {
                // 将同一dependency的所有版本的is_latest设置为false
                int updatedRows = knowledgeMapper.updateIsLatestToFalse(
                        dependency.getDependencyType().name(),
                        dependency.getLanguage().name(),
                        dependency.getNamespace(),
                        dependency.getName()
                );

                log.info("已将dependency {}:{}的所有版本is_latest设为false，影响行数: {}",
                        dependency.getNamespace(), dependency.getName(), updatedRows);
            } catch (Exception e) {
                log.error("更新is_latest失败，事务将回滚: {}:{}:{}",
                        dependency.getNamespace(), dependency.getName(), dependency.getVersion(), e);
                throw e;
            }
        }

        // 先删除现有的同一dependency version下的所有package记录
        try {
            int deletedRows = knowledgeMapper.deleteByDependencyVersion(
                    dependency.getDependencyType().name(),
                    dependency.getLanguage().name(),
                    dependency.getNamespace(),
                    dependency.getName(),
                    dependency.getVersion()
            );

            log.info("删除现有记录完成，dependency: {}:{}:{}, 删除行数: {}",
                    dependency.getNamespace(), dependency.getName(), dependency.getVersion(), deletedRows);
        } catch (Exception e) {
            log.error("删除现有记录失败，事务将回滚: {}:{}:{}",
                    dependency.getNamespace(), dependency.getName(), dependency.getVersion(), e);
            throw e;
        }

        // 转换为实体列表
        List<PackageContextEntity> validEntities = convertToEntities(dependencyPackageContext);

        if (validEntities.isEmpty()) {
            log.warn("没有有效的知识项需要保存");
            return 0;
        }

        // 在事务中进行批量插入（使用原有的batchInsertOrUpdate方法）
        try {
            log.info("执行批量插入，数量: {}", validEntities.size());
            int batchResult = knowledgeMapper.batchInsertOrUpdate(validEntities);
            log.info("批量插入成功，影响行数: {}", batchResult);

            log.info("保存依赖包上下文成功，dependency: {}:{}:{}, 插入: {}",
                    dependency.getNamespace(), dependency.getName(), dependency.getVersion(),
                    batchResult);
            return batchResult;

        } catch (Exception e) {
            log.error("批量插入失败，事务将回滚", e);
            throw new RuntimeException("保存依赖包上下文失败", e);
        }
    }

    @Override
    @LogExecutionTime
    public List<DependencyPackageContext> batchGetPackageContexts(List<Dependency> dependencies) {
        if (dependencies == null || dependencies.isEmpty()) {
            return new ArrayList<>();
        }

        log.info("开始批量获取依赖包上下文，数量: {}", dependencies.size());

        // 1. 过滤无效依赖
        List<Dependency> validDependencies = dependencies.stream()
                .filter(dependency -> {
                    if (dependency == null ||
                            (dependency.getVersion() == null)) {
                        log.warn("依赖版本为空，跳过查询: {}:{}",
                                dependency != null ? dependency.getNamespace() : "null",
                                dependency != null ? dependency.getName() : "null");
                        return false;
                    }
                    return true;
                })
                .collect(Collectors.toList());

        if (validDependencies.isEmpty()) {
            log.warn("没有有效的依赖需要查询");
            return new ArrayList<>();
        }
        return processBatchWithFunction(
                validDependencies,
                // 批处理器 - Lambda表达式
                (batchDeps, batchNum, totalBatches, context) ->
                        processBatchGetPackageContexts(batchDeps, batchNum, totalBatches),
                // 结果合并器 - 合并Map
                (result1, result2) -> {
                    result1.addAll(result2);
                    return result1;
                },
                // 初始结果
                new ArrayList<DependencyPackageContext>(),
                // 上下文（这里不需要）
                null);
    }

    /**
     * 将Map结果转换为DependencyPackageContext列表
     */
    private List<DependencyPackageContext> convertToDependencyPackageContextList(List<Dependency> dependencies,
                                                                                 Map<String, List<PackageContextItem>> resultsMap) {
        List<DependencyPackageContext> results = new ArrayList<>();

        for (Dependency dependency : dependencies) {
            String key = dependency.getDependencyKey();
            List<PackageContextItem> packageContextItems = resultsMap.get(key);

            if (packageContextItems != null && !packageContextItems.isEmpty()) {
                DependencyPackageContext dependencyPackageContext = DependencyPackageContext.builder()
                        .dependency(dependency)
                        .packageContextItems(packageContextItems)
                        .isLatest(dependency.isLatest())
                        .build();
                results.add(dependencyPackageContext);
            }
        }

        return results;
    }

    /**
     * 从Dependency和PackageContextItem构建数据库实体（新方法）
     */
    private PackageContextEntity buildPackageContextEntity(Dependency dependency, PackageContextItem packageContextItem, String traceId,
                                                           boolean isLatest) {
        if (dependency == null || packageContextItem == null) {
            return null;
        }

        DependencyType dependencyType = dependency.getDependencyType();
        if (dependencyType == null) {
            return null;
        }

        LanguageType languageType = dependency.getLanguage();
        if (languageType == null) {
            return null;
        }

        // 从Dependency获取准确的依赖信息
        String namespace = dependency.getNamespace();
        String name = dependency.getName();
        String version = dependency.getVersion();
        String packageName = packageContextItem.getPackageName();

        if (!StringUtils.hasText(namespace) || !StringUtils.hasText(name) || !StringUtils.hasText(version)) {
            return null;
        }

        // 检查class_signatures是否为空
        Map<String, String> classSignatures = packageContextItem.getClassSignatures();
        if (classSignatures == null || classSignatures.isEmpty()) {
            return null;
        }

        // 计算内容哈希
        String contentHash = calculateContentHash(classSignatures);

        // 构建实体
        return PackageContextEntity.builder()
                .dependencyType(dependencyType.name())
                .language(languageType.name())
                .namespace(namespace)
                .name(name)
                .version(version)
                .snapshotVersion(dependency.getSnapshotVersion())
                .packageName(packageName)
                .classSignatures(classSignatures)
                .contentHash(contentHash)
                .publishTime(null)
                .isLatest(isLatest)
                .sourceUrl(packageContextItem.getSourceUrl())
                .metadataUrl(packageContextItem.getMetadataUrl())
                .sourceTraceId(traceId)
                .build();
    }

    /**
     * 从数据库实体构建PackageContextItem对象
     */
    private PackageContextItem buildPackageContextItemFromEntity(PackageContextEntity entity) {

        // 直接获取classSignatures Map，不需要JSON解析
        Map<String, String> classSignatures = entity.getClassSignatures();
        if (classSignatures == null) {
            log.warn("实体 {} 的 classSignatures 为 null，可能是类型处理器未生效", entity.getPackageName());
            classSignatures = new HashMap<>();
        } else {
            log.debug("成功获取到 classSignatures，数量: {}, packageName: {}",
                    classSignatures.size(), entity.getPackageName());
        }

        // 构建简化的PackageContextItem
        return PackageContextItem.builder()
                .packageName(entity.getPackageName())
                .classSignatures(classSignatures)
                .classCount(classSignatures.size())
                .hasError(false)
                .build();
    }

    /**
     * 处理单批次的知识获取
     */
    private List<DependencyPackageContext> processBatchGetPackageContexts(List<Dependency> batchDependencies, int batchNumber,
                                                                          int totalBatches) {
        // 构建查询参数
        List<KnowledgeMapper.DependencyQueryParam> queryParams = batchDependencies.stream()
                .map(dependency -> new KnowledgeMapper.DependencyQueryParam(
                        dependency.getDependencyType().name(),
                        dependency.getLanguage().name(),
                        dependency.getNamespace(),
                        dependency.getName(),
                        dependency.getVersion(),
                        null
                ))
                .collect(Collectors.toList());

        // 批量查询
        long startTime = System.currentTimeMillis();

        // 构建并输出完整的SQL语句到debug.sql.log
        String debugSql = buildDebugSql(queryParams);
        log.debug("第{}/{}批查询SQL:\n{}\n-- 参数数量: {}, 预计返回结果用于分析性能",
                batchNumber, totalBatches, debugSql, queryParams.size());

        List<PackageContextEntity> allEntities = knowledgeMapper.batchSelectByDependencies(queryParams);
        long queryTime = System.currentTimeMillis() - startTime;

        log.info("第{}/{}批SQL查询完成，耗时: {}ms, 查询{}个依赖，返回{}条记录",
                batchNumber, totalBatches, queryTime, batchDependencies.size(), allEntities.size());

        // 如果查询时间过长，输出EXPLAIN建议
        if (queryTime > 1000) {
            log.warn("查询耗时过长({}ms)，建议执行EXPLAIN分析:\nEXPLAIN {};", queryTime, debugSql);
        }

        // 使用抽象的转换方法
        return convertEntitiesToDependencyPackageContexts(allEntities, batchDependencies, batchNumber, totalBatches);
    }

    /**
     * 构建完整的SQL语句用于调试
     */
    private String buildDebugSql(List<KnowledgeMapper.DependencyQueryParam> queryParams) {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT id, language, dependency_type, namespace, name, version, snapshot_version, package_name, ");
        sql.append("class_signatures, content_hash, publish_time, is_latest, source_url, metadata_url, ");
        sql.append("created_at, updated_at, source_trace_id ");
        sql.append("FROM t_code_context ");
        sql.append("WHERE (dependency_type, language, namespace, name, version) IN (");

        for (int i = 0; i < queryParams.size(); i++) {
            KnowledgeMapper.DependencyQueryParam param = queryParams.get(i);
            if (i > 0) {
                sql.append(", ");
            }
            sql.append("('").append(escapeSqlString(param.getDependencyType())).append("', ");
            sql.append("'").append(escapeSqlString(param.getLanguage())).append("', ");
            sql.append("'").append(escapeSqlString(param.getNamespace())).append("', ");
            sql.append("'").append(escapeSqlString(param.getName())).append("', ");
            sql.append("'").append(escapeSqlString(param.getVersion())).append("')");
        }

        sql.append(");");
        return sql.toString();
    }

    /**
     * 转义SQL字符串中的特殊字符
     */
    private String escapeSqlString(String str) {
        if (str == null) {
            return "NULL";
        }
        return str.replace("'", "''");
    }

    /**
     * 将查询结果按依赖分组
     */
    private Map<String, List<PackageContextEntity>> groupEntitiesByDependency(List<PackageContextEntity> entities) {
        Map<String, List<PackageContextEntity>> entityMap = new HashMap<>();

        for (PackageContextEntity entity : entities) {
            // 构建依赖键：dependencyType:language:namespace:name:version
            // 必须与Dependency.getDependencyKey()的格式保持一致
            String dependencyKey = String.format("%s:%s:%s:%s:%s",
                    entity.getDependencyType(),
                    entity.getLanguage(),
                    entity.getNamespace(),
                    entity.getName(),
                    entity.getVersion());

            entityMap.computeIfAbsent(dependencyKey, k -> new ArrayList<>()).add(entity);
        }

        return entityMap;
    }

    /**
     * 抽象的转换方法：将PackageContextEntity列表转换为DependencyPackageContext列表
     *
     * @param allEntities       查询到的所有实体
     * @param batchDependencies 批次依赖列表
     * @param batchNumber       批次号（用于日志）
     * @param totalBatches      总批次数（用于日志）
     * @return 转换后的DependencyPackageContext列表
     */
    private List<DependencyPackageContext> convertEntitiesToDependencyPackageContexts(
            List<PackageContextEntity> allEntities,
            List<Dependency> batchDependencies,
            int batchNumber,
            int totalBatches) {

        List<DependencyPackageContext> result = new ArrayList<>();

        // 按依赖分组处理结果
        Map<String, List<PackageContextEntity>> entityMap = groupEntitiesByDependency(allEntities);

        // 为每个原始依赖构建结果
        for (Dependency dependency : batchDependencies) {
            String dependencyKey = dependency.getDependencyKey();
            List<PackageContextEntity> entities = entityMap.get(dependencyKey);

            if (entities != null && !entities.isEmpty()) {
                // 将Entity转换为PackageContextItem
                List<PackageContextItem> packageContextItems = entities.stream()
                        .map(this::buildPackageContextItemFromEntity)
                        .collect(Collectors.toList());

                // 从任一Entity中读取is_latest信息（所有同一dependency的Entity的is_latest应该相同）
                boolean isLatest = entities.get(0).getIsLatest() != null ? entities.get(0).getIsLatest() : false;

                // 构建DependencyPackageContext
                DependencyPackageContext dependencyPackageContext = DependencyPackageContext.builder()
                        .dependency(dependency)
                        .packageContextItems(packageContextItems)
                        .isLatest(isLatest)
                        .build();

                result.add(dependencyPackageContext);
            }
        }

        return result;
    }

    @Override
    @LogExecutionTime
    public Map<String, Boolean> batchCheckDependencyExist(List<Dependency> dependencies) {
        if (dependencies == null || dependencies.isEmpty()) {
            return new HashMap<String, Boolean>();
        }

        log.info("开始批量检查依赖是否存在，数量: {}", dependencies.size());

        // 使用函数式编程重构后的代码
        return processBatchWithFunction(
                dependencies,
                // 批处理器 - Lambda表达式
                (batchDeps, batchNum, totalBatches, context) ->
                        processBatchCheckDependencyExist(batchDeps, batchNum, totalBatches),
                // 结果合并器 - 合并Map
                (result1, result2) -> {
                    Map<String, Boolean> merged = new HashMap<String, Boolean>(result1);
                    merged.putAll(result2);
                    return merged;
                },
                // 初始结果
                new HashMap<String, Boolean>(),
                // 上下文（这里不需要）
                null
        );
    }

    @Override
    @LogExecutionTime
    public Map<String, String> batchGetLatestVersion(List<Dependency> dependencies) {
        if (dependencies == null || dependencies.isEmpty()) {
            return new HashMap<String, String>();
        }

        log.info("开始批量获取最新版本，依赖数量: {}", dependencies.size());

        // 使用函数式编程处理批量查询
        return processBatchWithFunction(
                dependencies,
                // 批处理器 - Lambda表达式
                (batchDeps, batchNum, totalBatches, context) ->
                        processBatchGetLatestVersion(batchDeps, batchNum, totalBatches),
                // 结果合并器 - 合并Map
                (result1, result2) -> {
                    Map<String, String> merged = new HashMap<String, String>(result1);
                    merged.putAll(result2);
                    return merged;
                },
                // 初始结果
                new HashMap<String, String>(),
                // 上下文（这里不需要）
                null
        );
    }

    @Override
    @Async
    public void asyncRecordMissingDependencies(List<Dependency> dependencies, String traceId) {
        if (dependencies == null || dependencies.isEmpty()) {
            log.info("没有需要记录的缺失依赖");
            return;
        }

        log.info("开始异步记录缺失依赖到数据库，数量: {}, traceId: {}", dependencies.size(), traceId);

        // 分批处理，每批最多500个依赖
        final int BATCH_SIZE = 500;
        int totalBatches = (dependencies.size() + BATCH_SIZE - 1) / BATCH_SIZE;
        int totalSuccess = 0;

        for (int i = 0; i < dependencies.size(); i += BATCH_SIZE) {
            int endIndex = Math.min(i + BATCH_SIZE, dependencies.size());
            List<Dependency> batchDependencies = dependencies.subList(i, endIndex);
            int batchNumber = (i / BATCH_SIZE) + 1;

            try {
                // 构建缺失依赖记录列表
                List<MissingDependencyEntity> entities = batchDependencies.stream()
                        .map(dependency -> MissingDependencyEntity.builder()
                                .dependencyType(dependency.getDependencyType().name())
                                .language(dependency.getLanguage().name())
                                .namespace(dependency.getNamespace())
                                .name(dependency.getName())
                                .version(dependency.getVersion())
                                .snapshotVersion(dependency.getSnapshotVersion())
                                .sourceTraceId(traceId)
                                .build())
                        .collect(Collectors.toList());

                // 批量插入记录
                int affectedRows = missingDependencyMapper.batchInsert(entities);
                totalSuccess += affectedRows;
                log.info("第{}/{}批缺失依赖记录完成，成功插入: {}条记录, traceId: {}",
                        batchNumber, totalBatches, affectedRows, traceId);
            } catch (Exception e) {
                log.error("第{}/{}批缺失依赖记录失败, traceId: {}", batchNumber, totalBatches, traceId, e);
            }
        }

        log.info("所有批次缺失依赖记录完成，总共成功插入: {}条记录, traceId: {}", totalSuccess, traceId);
    }

    /**
     * 计算内容哈希
     */
    private String calculateContentHash(Map<String, String> classSignatures) {
        try {
            // 将class签名按类名排序后计算哈希
            StringBuilder content = new StringBuilder();
            if (classSignatures != null) {
                classSignatures.entrySet().stream()
                        .sorted(Map.Entry.comparingByKey())
                        .forEach(entry -> {
                            content.append(entry.getKey()).append(":").append(entry.getValue()).append(";");
                        });
            }

            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(content.toString().getBytes(StandardCharsets.UTF_8));

            // 转换为16进制字符串
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();

        } catch (Exception e) {
            log.error("计算内容哈希失败", e);
            return "";
        }
    }

    /**
     * 获取指定依赖的最新版本号
     */
    @Override
    @LogExecutionTime
    public String getLatestVersion(Dependency dependency) {
        if (dependency == null) {
            return null;
        }

        try {
            return knowledgeMapper.selectLatestVersion(
                    dependency.getDependencyType().name(),
                    dependency.getLanguage().name(),
                    dependency.getNamespace(),
                    dependency.getName()
            );
        } catch (Exception e) {
            log.error("查询最新版本失败: {}:{}, 错误: {}",
                    dependency.getNamespace(), dependency.getName(), e.getMessage());
            return null;
        }
    }

    /**
     * 获取指定依赖的所有版本号
     */
    @Override
    @LogExecutionTime
    public List<String> getAllVersions(Dependency dependency) {
        if (dependency == null) {
            return new ArrayList<>();
        }

        try {
            List<String> versions = knowledgeMapper.selectAllVersionsByDependency(
                    dependency.getDependencyType().name(),
                    dependency.getLanguage().name(),
                    dependency.getNamespace(),
                    dependency.getName()
            );

            log.info("查询所有版本成功: {}:{} -> {} 个版本",
                    dependency.getNamespace(), dependency.getName(), versions.size());

            return versions != null ? versions : new ArrayList<>();
        } catch (Exception e) {
            log.error("查询所有版本失败: {}:{}, 错误: {}",
                    dependency.getNamespace(), dependency.getName(), e.getMessage());
            return new ArrayList<>();
        }
    }

    @Override
    public List<Dependency> getMissingDependencies(int limit) {
        try {
            List<MissingDependencyEntity> entities = missingDependencyMapper.selectUniquePendingDependencies(limit);
            return entities.stream()
                    .map(this::convertToMissingDependency)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取待处理缺失依赖失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public int markMissingDependenciesAsProcessed(List<Dependency> dependencies) {
        if (dependencies == null || dependencies.isEmpty()) {
            return 0;
        }

        try {
            // 直接使用Dependency对象，无需转换
            return missingDependencyMapper.batchDeleteByDependencies(dependencies);
        } catch (Exception e) {
            log.error("标记缺失依赖为已处理失败", e);
            return 0;
        }
    }

    @Override
    public int markMissingDependenciesAsSkipped(List<Dependency> dependencies, String skipReason) {
        if (dependencies == null || dependencies.isEmpty()) {
            return 0;
        }

        try {
            // 直接使用Dependency对象, 忽略版本
            int updatedCount = missingDependencyMapper.batchUpdateSkipByDependenciesWithoutVersion(dependencies, skipReason);
            log.info("成功标记 {} 个依赖为跳过状态（精确到版本），跳过原因: {}", updatedCount, skipReason);
            return updatedCount;
        } catch (Exception e) {
            log.error("标记缺失依赖为跳过状态失败，跳过原因: {}", skipReason, e);
            return 0;
        }
    }


    /**
     * 转换MissingDependencyEntity到Dependency
     */
    private Dependency convertToMissingDependency(MissingDependencyEntity entity) {
        if (entity == null) {
            return null;
        }

        try {
            LanguageType language = LanguageType.valueOf(entity.getLanguage().toUpperCase());
            DependencyType dependencyType = DependencyType.valueOf(entity.getDependencyType().toUpperCase());

            return Dependency.builder()
                    .language(language)
                    .dependencyType(dependencyType)
                    .namespace(entity.getNamespace())
                    .name(entity.getName())
                    .version(entity.getVersion())
                    .build();
        } catch (Exception e) {
            log.warn("转换缺失依赖实体失败: {}:{}:{}", entity.getNamespace(), entity.getName(), entity.getVersion(), e);
            return null;
        }
    }

    /**
     * 获取实际使用的批次大小
     */
    private int getBatchSize() {
        // 后续可以从配置文件读取，现在使用默认值
        return DEFAULT_BATCH_SIZE;
    }

    /**
     * 将DependencyPackageContext转换为PackageContextEntity列表
     *
     * @param dependencyPackageContext 依赖包上下文
     * @return 转换后的实体列表，包含转换结果信息
     */
    private List<PackageContextEntity> convertToEntities(DependencyPackageContext dependencyPackageContext) {
        if (dependencyPackageContext == null || !dependencyPackageContext.isValid()) {
            return Collections.emptyList();
        }

        Dependency dependency = dependencyPackageContext.getDependency();
        List<PackageContextItem> packageContextItems = dependencyPackageContext.getPackageContextItems();
        boolean isLatest = dependencyPackageContext.isLatest();
        String traceId = isLatest ? "save-dependency-context-latest" : "save-dependency-context";

        List<PackageContextEntity> validEntities = new ArrayList<>();
        int skippedCount = 0;

        for (PackageContextItem packageContextItem : packageContextItems) {
            // 检查class_signatures是否为空，如果为空则跳过保存
            if (!packageContextItem.hasValidClassSignatures()) {
                log.info("包的class_signatures为空，跳过保存: packageName={}",
                        packageContextItem.getPackageName());
                skippedCount++;
                continue;
            }

            try {
                PackageContextEntity entity = buildPackageContextEntity(dependency, packageContextItem, traceId, isLatest);
                if (entity != null) {
                    validEntities.add(entity);
                }
            } catch (Exception e) {
                String packageKey = String.format("%s:%s:%s:%s",
                        dependency.getNamespace(), dependency.getName(), dependency.getVersion(),
                        packageContextItem.getPackageName());
                log.error("构建实体失败: {}", packageKey, e);
                throw new RuntimeException("转换实体失败，dependency版本: " +
                        dependency.getNamespace() + ":" + dependency.getName() + ":" + dependency.getVersion(), e);
            }
        }

        return validEntities;
    }

    /**
     * 通用批处理方法
     *
     * @param dependencies  依赖列表
     * @param processor     批处理器
     * @param merger        结果合并器
     * @param initialResult 初始结果
     * @param context       上下文对象
     * @param <T>           上下文类型
     * @param <R>           结果类型
     * @return 合并后的结果
     */
    private <T, R> R processBatchWithFunction(
            List<Dependency> dependencies,
            BatchProcessor<T, R> processor,
            ResultMerger<R> merger,
            R initialResult,
            T context) {

        if (dependencies == null || dependencies.isEmpty()) {
            return initialResult;
        }

        final int BATCH_SIZE = getBatchSize();

        R result = initialResult;

        log.info("依赖数量: {}, 将分{}批处理，每批最多{}个",
                dependencies.size(),
                (dependencies.size() + BATCH_SIZE - 1) / BATCH_SIZE, BATCH_SIZE);

        // 2. 分批处理
        for (int i = 0; i < dependencies.size(); i += BATCH_SIZE) {
            int endIndex = Math.min(i + BATCH_SIZE, dependencies.size());
            List<Dependency> batchDependencies = dependencies.subList(i, endIndex);
            int batchNumber = (i / BATCH_SIZE) + 1;
            int totalBatches = (dependencies.size() + BATCH_SIZE - 1) / BATCH_SIZE;

            log.info("开始处理第{}/{}批，依赖数量: {}", batchNumber, totalBatches, batchDependencies.size());

            try {
                R batchResult = processor.process(batchDependencies, batchNumber, totalBatches, context);
                result = merger.merge(result, batchResult);
            } catch (Exception e) {
                log.error("第{}/{}批处理失败，跳过该批次", batchNumber, totalBatches, e);
                // 不使用回退机制，直接跳过失败的批次
            }
        }

        return result;
    }

    /**
     * 处理单批次的依赖存在性检查
     */
    private Map<String, Boolean> processBatchCheckDependencyExist(List<Dependency> batchDependencies, int batchNumber, int totalBatches) {
        Map<String, Boolean> result = new HashMap<>();

        // 构建查询参数
        List<KnowledgeMapper.DependencyQueryParam> queryParams = batchDependencies.stream()
                .map(dependency -> new KnowledgeMapper.DependencyQueryParam(
                        dependency.getDependencyType().name(),
                        dependency.getLanguage().name(),
                        dependency.getNamespace(),
                        dependency.getName(),
                        dependency.getVersion(),
                        null
                ))
                .collect(Collectors.toList());

        // 批量查询依赖存在性
        long startTime = System.currentTimeMillis();
        List<KnowledgeMapper.DependencyExistResult> existingDeps = knowledgeMapper.batchCheckDependencyExist(queryParams);
        long queryTime = System.currentTimeMillis() - startTime;

        log.info("第{}/{}批存在性检查SQL查询完成，耗时: {}ms, 检查{}个依赖，找到{}个存在",
                batchNumber, totalBatches, queryTime, batchDependencies.size(), existingDeps.size());

        // 构建存在依赖的键集合，用于快速查找
        Set<String> existingKeys = existingDeps.stream()
                .map(KnowledgeMapper.DependencyExistResult::getDependencyKey)
                .collect(Collectors.toSet());

        // 为每个依赖设置存在性结果
        for (Dependency dependency : batchDependencies) {
            String dependencyKey = dependency.getDependencyKey();
            boolean exists = existingKeys.contains(dependencyKey);
            result.put(dependencyKey, exists);

            log.debug("第{}/{}批依赖存在性检查: {} -> {}", batchNumber, totalBatches, dependencyKey, exists);
        }

        return result;
    }

    /**
     * 处理单批次的最新版本查询
     */
    private Map<String, String> processBatchGetLatestVersion(List<Dependency> batchDependencies, int batchNumber, int totalBatches) {
        Map<String, String> result = new HashMap<>();

        // 为每个依赖查询最新版本
        for (Dependency dependency : batchDependencies) {
            try {
                String latestVersion = knowledgeMapper.selectLatestVersion(
                        dependency.getDependencyType().name(),
                        dependency.getLanguage().name(),
                        dependency.getNamespace(),
                        dependency.getName()
                );

                // 使用getDependencySignature()作为key，因为我们是在查找最新版本（不包含版本信息）
                String dependencySignature = dependency.getDependencySignature();

                if (latestVersion != null) {
                    result.put(dependencySignature, latestVersion);
                    log.debug("第{}/{}批最新版本查询: {} -> {}", batchNumber, totalBatches, dependencySignature, latestVersion);
                } else {
                    log.debug("第{}/{}批最新版本查询: {} -> 未找到", batchNumber, totalBatches, dependencySignature);
                }
            } catch (Exception e) {
                log.error("查询最新版本失败: {}:{}, 错误: {}",
                        dependency.getNamespace(), dependency.getName(), e.getMessage());
            }
        }

        log.info("第{}/{}批最新版本查询完成，查询{}个依赖，找到{}个版本",
                batchNumber, totalBatches, batchDependencies.size(), result.size());

        return result;
    }

    /**
     * 函数式接口：批处理处理器
     */
    @FunctionalInterface
    public interface BatchProcessor<T, R> {
        R process(List<Dependency> batchDependencies, int batchNumber, int totalBatches, T context) throws Exception;
    }

    /**
     * 函数式接口：结果合并器
     */
    @FunctionalInterface
    public interface ResultMerger<R> {
        R merge(R result1, R result2);
    }
} 