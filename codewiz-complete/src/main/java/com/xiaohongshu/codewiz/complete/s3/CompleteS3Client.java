package com.xiaohongshu.codewiz.complete.s3;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.UUID;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.amazonaws.services.s3.model.S3Object;
import com.fasterxml.jackson.databind.JsonNode;
import com.xiaohongshu.codewiz.core.utils.GzipUtil;
import com.xiaohongshu.codewiz.core.utils.JsonMapperUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * Created on 2025/4/2
 */
@Slf4j
@Service
public class CompleteS3Client {
    private static final String KEY_XHS_FEATURE_SNIPPET = "complete/xhs_feature_snippet.json";
    private static final String FEEDBACK_LOG_KEY_PATTERN = "feedback/logs/%s";

    @Value("${ros.bucket}")
    private String bucket;

    @Resource
    private AmazonS3 amazonS3;

    public JsonNode getXhsFeatureSnippet() {
        try (S3Object s3Object = amazonS3.getObject(bucket, KEY_XHS_FEATURE_SNIPPET)) {
            try (InputStream inputStream = s3Object.getObjectContent()) {
                return JsonMapperUtils.mapper().readTree(inputStream);
            } catch (Exception e) {
                log.error("readXhsFeatureSnippetError", e);
            }
        } catch (Exception e) {
            log.error("getXhsFeatureSnippetError", e);
        }
        return null;
    }

    /**
     * 上传反馈日志文件到S3
     * 注意：logContent是gzip压缩的base64字符串，需要先解压再上传
     *
     * @param logContent gzip压缩的日志内容
     * @return S3 key
     */
    public String uploadFeedbackLog(String logContent) {
        if (StringUtils.isBlank(logContent)) {
            log.warn("uploadFeedbackLog, logContent is blank");
            return null;
        }

        String key = String.format(FEEDBACK_LOG_KEY_PATTERN, UUID.randomUUID().toString());
        try {
            // 1. 先解压gzip压缩的日志内容
            String decompressedContent;
            try {
                decompressedContent = GzipUtil.decompress(logContent);
                if (decompressedContent == null) {
                    log.error("Failed to decompress log content, treating as plain text");
                    decompressedContent = logContent; // 如果解压失败，当作普通文本处理
                }
                log.info("Successfully decompressed log content, original size: {}, decompressed size: {}", 
                    logContent.length(), decompressedContent.length());
            } catch (IOException e) {
                log.error("Failed to decompress log content, treating as plain text", e);
                decompressedContent = logContent; // 如果解压失败，当作普通文本处理
            }

            // 2. 上传解压后的内容到S3
            byte[] contentBytes = decompressedContent.getBytes();
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentLength(contentBytes.length);
            metadata.setContentType("text/plain");
            
            amazonS3.putObject(bucket, key, new ByteArrayInputStream(contentBytes), metadata);
            log.info("uploadFeedbackLog success, key: {}, size: {}", key, contentBytes.length);
            return key;
        } catch (Exception e) {
            log.error("uploadFeedbackLog error, key: {}", key, e);
            return null;
        }
    }

    /**
     * 从S3下载反馈日志文件
     *
     * @param key S3 key
     * @return 日志内容
     */
    public String downloadFeedbackLog(String key) {
        if (StringUtils.isBlank(key)) {
            log.warn("downloadFeedbackLog, key is blank");
            return null;
        }

        try (S3Object s3Object = amazonS3.getObject(bucket, key)) {
            try (InputStream inputStream = s3Object.getObjectContent()) {
                String content = new String(inputStream.readAllBytes());
                log.info("downloadFeedbackLog success, key: {}, size: {}", key, content.length());
                return content;
            } catch (Exception e) {
                log.error("readFeedbackLogError, key: {}", key, e);
            }
        } catch (Exception e) {
            log.error("getFeedbackLogError, key: {}", key, e);
        }
        return null;
    }
}
