package com.xiaohongshu.codewiz.complete.entity;

import java.time.LocalDateTime;
import java.util.Map;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 代码依赖上下文摘要表实体
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("t_code_context")
public class PackageContextEntity {

    @TableId(type = IdType.AUTO)
    private Long id;

    @TableField("language")
    private String language;

    @TableField("dependency_type")
    private String dependencyType;

    @TableField("namespace")
    private String namespace;

    @TableField("name")
    private String name;

    @TableField("version")
    private String version;

    @TableField("snapshot_version")
    private String snapshotVersion;

    @TableField("package_name")
    private String packageName;

    @TableField(value = "class_signatures", typeHandler = com.xiaohongshu.codewiz.complete.config.MapTypeHandler.class)
    private Map<String, String> classSignatures;

    @TableField("content_hash")
    private String contentHash;

    @TableField("publish_time")
    private LocalDateTime publishTime;

    @TableField("is_latest")
    private Boolean isLatest;

    @TableField("source_url")
    private String sourceUrl;

    @TableField("metadata_url")
    private String metadataUrl;

    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    @TableField("source_trace_id")
    private String sourceTraceId;
} 