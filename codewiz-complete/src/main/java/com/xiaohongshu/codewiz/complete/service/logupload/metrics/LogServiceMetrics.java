package com.xiaohongshu.codewiz.complete.service.logupload.metrics;

import java.util.concurrent.TimeUnit;

import org.springframework.stereotype.Component;

import com.dianping.cat.Cat;

import static com.xiaohongshu.codewiz.complete.constant.MetricsConstants.CNT_LOG_UPLOAD_BATCH_REQUESTS;
import static com.xiaohongshu.codewiz.complete.constant.MetricsConstants.CNT_LOG_UPLOAD_THROUGHPUT;
import static com.xiaohongshu.codewiz.complete.constant.MetricsConstants.CommonTags.STATUS;
import static com.xiaohongshu.codewiz.complete.constant.MetricsConstants.LogTags.IDE;
import static com.xiaohongshu.codewiz.complete.constant.MetricsConstants.LogTags.LEVEL;
import static com.xiaohongshu.codewiz.complete.constant.MetricsConstants.LogTags.MODULE;
import static com.xiaohongshu.codewiz.complete.constant.MetricsConstants.STATUS_SUCCESS;
import static com.xiaohongshu.codewiz.complete.constant.MetricsConstants.SUM_LOG_SIZE;
import static com.xiaohongshu.codewiz.complete.constant.MetricsConstants.TIMER_LOG_UPLOAD_COST;

/**
 * 日志服务相关的监控指标收集器
 * 统一管理所有日志上传相关的metrics打点
 */
@Component
public class LogServiceMetrics {

    /**
     * 统一上报所有监控指标
     *
     * @param metricsData 收集的监控数据
     */
    public void emitMetrics(LogMetricsData metricsData) {
        // 如果处理完成，上报处理相关指标
        if (metricsData.getStatus() != null) {
            // 上报批请求
            Cat.counter(CNT_LOG_UPLOAD_BATCH_REQUESTS.getName(), CNT_LOG_UPLOAD_BATCH_REQUESTS.getDescription())
                    .addTag(STATUS, metricsData.getStatus())
                    .increment();

            // 上报处理耗时
            Cat.timer(TIMER_LOG_UPLOAD_COST.getName(), TIMER_LOG_UPLOAD_COST.getDescription())
                    .addTag(STATUS, metricsData.getStatus())
                    .record(metricsData.getProcessingTimeMs(), TimeUnit.MILLISECONDS);

            // 成功时才上报的指标
            if (metricsData.getStatus().equals(STATUS_SUCCESS)) {
                // 上报throughput
                Cat.counter(CNT_LOG_UPLOAD_THROUGHPUT.getName(), CNT_LOG_UPLOAD_THROUGHPUT.getDescription())
                        .increment(metricsData.getTotalSizeBytes());
                // 上报单条日志大小
                for (LogMetricsData.LogEntryMetrics entry : metricsData.getLogEntries()) {
                    Cat.summary(SUM_LOG_SIZE.getName(), SUM_LOG_SIZE.getDescription())
                            .addTag(LEVEL, entry.getLevel())
                            .addTag(IDE, entry.getIde())
                            .addTag(MODULE, entry.getModule())
                            .record(entry.getSizeBytes());
                }
            }
        }
    }
} 