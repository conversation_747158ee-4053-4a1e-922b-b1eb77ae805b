package com.xiaohongshu.codewiz.complete.runner.model;

import lombok.Data;

/**
 * 步骤执行结果
 */
@Data
public class StepResult {
    
    /**
     * 是否成功
     */
    private boolean success;
    
    /**
     * 错误消息
     */
    private String errorMessage;
    
    /**
     * 结果数据
     */
    private Object data;
    
    private StepResult(boolean success, String errorMessage, Object data) {
        this.success = success;
        this.errorMessage = errorMessage;
        this.data = data;
    }
    
    /**
     * 创建成功结果
     */
    public static StepResult success() {
        return new StepResult(true, null, null);
    }
    
    /**
     * 创建成功结果并携带数据
     */
    public static StepResult success(Object data) {
        return new StepResult(true, null, data);
    }
    
    /**
     * 创建失败结果
     */
    public static StepResult failure(String errorMessage) {
        return new StepResult(false, errorMessage, null);
    }
    
    /**
     * 创建失败结果并携带数据
     */
    public static StepResult failure(String errorMessage, Object data) {
        return new StepResult(false, errorMessage, data);
    }
} 