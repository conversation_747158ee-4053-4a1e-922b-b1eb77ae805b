package com.xiaohongshu.codewiz.complete.model.dependency;

import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 依赖制品信息 - 多语言通用模型
 * <p>
 * 支持不同语言的依赖管理系统：
 * - Java/Maven: namespace=groupId, name=artifactId, version=version
 * - Python/PyPI: namespace=null, name=package_name, version=version
 * - JavaScript/NPM: namespace=@scope, name=package_name, version=version
 * - Go: namespace=domain, name=module_path, version=version
 * - C++/Conan: namespace=user, name=package_name, version=version
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Artifact {

    /**
     * 基础依赖信息
     */
    private Dependency dependency;

    /**
     * 依赖描述信息
     */
    private String description;

    /**
     * 扩展属性，存储语言特定的元数据
     */
    private Map<String, Object> properties;

    /**
     * 源码内容 (如sources.jar、源码包等的二进制内容)
     */
    private byte[] sourceContent;

    /**
     * 源码下载URL
     */
    private String sourceUrl;

    /**
     * 元数据下载URL
     */
    private String metadataUrl;

    /**
     * 制品总大小
     */
    private long size;

    /**
     * 制品校验和
     */
    private String checksum;
} 