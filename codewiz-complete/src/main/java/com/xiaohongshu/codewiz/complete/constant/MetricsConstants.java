package com.xiaohongshu.codewiz.complete.constant;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * 监控指标常量定义
 * 命名规范：CNT_(Counter)、SUM_(Summary)、TIMER_(Timer)、GAUGE_(Gauge) + 具体指标名
 */
public class MetricsConstants {
    
    // Counter类型指标 - CNT_前缀
    public static final MetricInfo CNT_LOG_UPLOAD_BATCH_REQUESTS = new MetricInfo("log_upload_batch_requests", "日志上报batch接口调用次数");
    public static final MetricInfo CNT_LOG_UPLOAD_THROUGHPUT = new MetricInfo("log_upload_throughput", "日志上报吞吐");


    // Summary类型指标 - SUM_前缀
    public static final MetricInfo SUM_LOG_SIZE = new MetricInfo("log_size", "日志上报条数和大小统计");

    // Timer类型指标 - TIMER_前缀
    public static final MetricInfo TIMER_LOG_UPLOAD_COST = new MetricInfo("log_upload_cost", "批次处理耗时");

    // Gauge类型指标 - GAUGE_前缀
    public static final MetricInfo GAUGE_THREAD_POOL_QUEUE_SIZE = new MetricInfo("thread_pool_queue_size", "线程池队列任务数");

    // 常用值
    public static final String STATUS_SUCCESS = "success";
    public static final String STATUS_FAIL = "fail";
    
    // 公共标签常量
    public static final class CommonTags {
        // 通用状态标签
        public static final String STATUS = "status";
    }
    
    // 日志服务标签常量
    public static final class LogTags {
        // 日志级别
        public static final String LEVEL = "level";
        
        // IDE类型
        public static final String IDE = "ide";
        
        // 模块名
        public static final String MODULE = "module";
    }

    // 线程池监控标签常量
    public static final class ThreadPoolTags {
        // 线程池名称
        public static final String POOL_NAME = "pool_name";
    }
    
    // 指标信息类
    @Data
    @AllArgsConstructor
    public static class MetricInfo {
        private final String name;
        private final String description;
    }
} 