package com.xiaohongshu.codewiz.complete.runner.builder.impl;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.jar.JarEntry;
import java.util.jar.JarInputStream;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.xiaohongshu.codewiz.complete.model.dependency.Artifact;
import com.xiaohongshu.codewiz.complete.model.dependency.Dependency;
import com.xiaohongshu.codewiz.complete.model.dpc.PackageContextItem;
import com.xiaohongshu.codewiz.complete.model.lang.LanguageType;
import com.xiaohongshu.codewiz.complete.model.lang.SourceFile;
import com.xiaohongshu.codewiz.complete.model.task.PackageContextBuildResult;
import com.xiaohongshu.codewiz.complete.runner.builder.PackageContextBuilder;
import com.xiaohongshu.codewiz.complete.runner.parser.JavaSourceParser;
import com.xiaohongshu.codewiz.core.annotation.LogExecutionTime;

import lombok.extern.slf4j.Slf4j;

/**
 * JAR包知识构建器实现
 * 负责解析JAR包中的源码并构建知识信息
 */
@Slf4j
@Component
public class JarPackageContextBuilder implements PackageContextBuilder {
    // 将PackageInfo转换为Knowledge列表
    int totalFiles = 0;
    int totalClasses = 0;
    // 构建最终结果
    PackageContextBuildResult result;

    @Autowired
    private ExecutorService javaParserExecutor;

    @Override
    @LogExecutionTime
    public PackageContextBuildResult buildPackageContextWithDetails(Artifact artifact) {
        String key = artifact.getDependency().getDependencyKey();
        log.info("开始构建知识: {}", key);


        // 解析JAR包中的源码文件
        Map<String, List<SourceFile>> packageSourceFiles;
        try {
            packageSourceFiles = parseJarFile(artifact.getSourceContent());
        } catch (Exception e) {
            String errorMsg = String.format("JAR包解析失败: %s", e.getMessage());
            log.error(errorMsg, e);

            PackageContextBuildResult.BuildError error = PackageContextBuildResult.BuildError.builder()
                    .errorType(PackageContextBuildResult.ErrorType.JAR_PARSE_ERROR)
                    .errorMessage(errorMsg)
                    .context("JAR文件: " + key)
                    .stackTrace(e.toString())
                    .build();

            return PackageContextBuildResult.createFailure(key, List.of(error));
        }

        // 如果解析失败或没有找到任何源码文件
        if (packageSourceFiles.isEmpty()) {
            String errorMsg = String.format("JAR包解析失败或未找到任何Java源码文件: %s", key);
            log.error(errorMsg);

            PackageContextBuildResult.BuildError error = PackageContextBuildResult.BuildError.builder()
                    .errorType(PackageContextBuildResult.ErrorType.JAR_PARSE_ERROR)
                    .errorMessage("未找到任何Java源码文件")
                    .context("JAR文件: " + key)
                    .build();

            return PackageContextBuildResult.createFailure(key, List.of(error));
        }

        log.info("成功解析JAR包，包含{}个包的源码文件", packageSourceFiles.size());

        // 构建包信息列表 (中间处理)
        List<PackageContextItem> packageContextItems = new ArrayList<>();
        List<PackageContextBuildResult.BuildError> buildErrors = new ArrayList<>();

        for (Map.Entry<String, List<SourceFile>> entry : packageSourceFiles.entrySet()) {
            String packageName = entry.getKey();
            List<SourceFile> sourceFiles = entry.getValue();
            Dependency dependency = artifact.getDependency();

            try {
                // 生成package中class的摘要
                Map<String, String> classSignatures = new HashMap<>();
                for (SourceFile sourceFile : sourceFiles) {
                    if (sourceFile.getClasses() != null) {
                        for (SourceFile.ClassInfo classInfo : sourceFile.getClasses()) {
                            try {
                                String signature = generateClassSummary(sourceFile, classInfo);
                                if (signature.isEmpty()) {
                                    log.warn("类签名生成为空: {}", classInfo.getClassName());
                                    continue;
                                }
                                classSignatures.put(classInfo.getClassName(), signature);
                            } catch (Exception e) {
                                String errorMsg = String.format("生成类签名失败: %s", e.getMessage());
                                log.error("生成类签名失败: {} - {}", classInfo.getClassName(), e.getMessage(), e);

                                PackageContextBuildResult.BuildError error = PackageContextBuildResult.BuildError.builder()
                                        .errorType(PackageContextBuildResult.ErrorType.CLASS_SIGNATURE_GENERATION_ERROR)
                                        .errorMessage(errorMsg)
                                        .context("类名: " + classInfo.getClassName() + ", 包名: " + packageName)
                                        .stackTrace(e.toString())
                                        .build();
                                buildErrors.add(error);
                            }
                        }
                    }
                }

                // 如果classSignatures为空，记录日志但不创建Knowledge
                if (classSignatures.isEmpty()) {
                    log.info("package {} 的classSignatures为空，跳过创建知识", packageName);

                    PackageContextBuildResult.BuildError error = PackageContextBuildResult.BuildError.builder()
                            .errorType(PackageContextBuildResult.ErrorType.PACKAGE_INFO_BUILD_ERROR)
                            .errorMessage("包中没有可解析的类签名")
                            .context("包名: " + packageName)
                            .build();
                    buildErrors.add(error);
                    continue;
                }
                // 构建包信息
                PackageContextItem packageContextItem = PackageContextItem.builder()
                        .packageName(packageName)
                        .fileCount(sourceFiles.size())
                        .classCount(classSignatures.size())
                        .classSignatures(classSignatures)
                        .sourceUrl(artifact.getSourceUrl())
                        .metadataUrl(artifact.getMetadataUrl())
                        .build();

                packageContextItems.add(packageContextItem);

                // 统计信息
                totalFiles += packageContextItem.getFileCount();
                totalClasses += packageContextItem.getClassCount();
            } catch (Exception e) {
                String errorMsg = String.format("转换包信息为知识失败: %s", e.getMessage());
                log.error("转换包信息为知识失败: {}", packageName, e);

                PackageContextBuildResult.BuildError error = PackageContextBuildResult.BuildError.builder()
                        .errorType(PackageContextBuildResult.ErrorType.PACKAGE_INFO_BUILD_ERROR)
                        .errorMessage(errorMsg)
                        .context("包名: " + packageName)
                        .stackTrace(e.toString())
                        .build();
                buildErrors.add(error);
            }
        }
        return PackageContextBuildResult.createMixed(key, packageContextItems, buildErrors);
    }

    /**
     * 解析JAR文件中的源码
     */
    private Map<String, List<SourceFile>> parseJarFile(byte[] jarContent) throws IOException {
        // 验证输入参数
        if (jarContent == null || jarContent.length == 0) {
            throw new IllegalArgumentException("JAR文件内容为空或null");
        }

        // 使用ConcurrentHashMap支持并发操作
        Map<String, List<SourceFile>> packageSourceFiles = new ConcurrentHashMap<>();

        // 第一步：先遍历JAR文件，收集所有的.java文件信息
        List<JavaFileInfo> javaFiles = new ArrayList<>();
        try (JarInputStream jarStream = new JarInputStream(new ByteArrayInputStream(jarContent))) {
            JarEntry entry;

            while ((entry = jarStream.getNextJarEntry()) != null) {
                String fileName = entry.getName();

                // 只处理.java源码文件
                if (fileName.endsWith(".java") && !entry.isDirectory()) {
                    try {
                        String packageName = extractPackageName(fileName);
                        String content = readFileContent(jarStream);

                        if (content != null && !content.trim().isEmpty()) {
                            javaFiles.add(new JavaFileInfo(fileName, packageName, content));
                            log.debug("收集Java源码文件: {}", fileName);
                        }
                    } catch (Exception e) {
                        log.error("读取Java源码文件失败: {}", fileName, e);
                        throw new RuntimeException("读取Java源码文件失败: " + fileName, e);
                    }
                }
                jarStream.closeEntry();
            }
        } catch (IOException e) {
            log.error("读取JAR文件流时发生IO异常", e);
            throw new RuntimeException("JAR文件格式损坏或读取失败", e);
        } catch (Exception e) {
            log.error("解析JAR文件时发生未知异常", e);
            throw new RuntimeException("JAR文件解析失败", e);
        }

        if (javaFiles.isEmpty()) {
            log.warn("JAR包中所有Java源码文件都读取失败");
            return packageSourceFiles;
        }

        log.info("开始并行解析{}个Java源码文件", javaFiles.size());

        // 第二步：使用线程池并行解析Java源码文件
        List<CompletableFuture<ParseResult>> futures = new ArrayList<>();

        for (JavaFileInfo javaFile : javaFiles) {
            CompletableFuture<ParseResult> future = CompletableFuture.supplyAsync(() -> {
                try {
                    // 使用JavaSourceParser解析源码文件
                    SourceFile sourceFile = JavaSourceParser.parseJavaSource(javaFile.fileName, javaFile.content);

                    if (sourceFile != null) {
                        return new ParseResult(javaFile.fileName, javaFile.packageName, sourceFile, true, null);
                    } else {
                        String errorMsg = "解析源码文件返回null: " + javaFile.fileName;
                        log.error(errorMsg);
                        return new ParseResult(javaFile.fileName, javaFile.packageName, null, false, errorMsg);
                    }
                } catch (Exception e) {
                    String errorMsg = "解析源码文件失败: " + javaFile.fileName;
                    log.error(errorMsg, e);
                    return new ParseResult(javaFile.fileName, javaFile.packageName, null, false, errorMsg + " " + e.getMessage());
                }
            }, javaParserExecutor);

            futures.add(future);
        }

        // 等待所有解析任务完成并统计结果
        List<ParseResult> results;
        try {
            results = futures.stream()
                    .map(CompletableFuture::join)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("并行解析过程中发生异常", e);
            throw new RuntimeException("并行解析JAR文件失败", e);
        }

        // 统计解析结果
        int successCount = 0;
        int failureCount = 0;
        List<String> failureMessages = new ArrayList<>();

        for (ParseResult result : results) {
            if (result.success) {
                successCount++;
                // 使用ConcurrentHashMap的computeIfAbsent方法进行线程安全的添加
                packageSourceFiles.computeIfAbsent(result.packageName, k -> Collections.synchronizedList(new ArrayList<>()))
                        .add(result.sourceFile);
                log.debug("成功解析源码文件: {}", result.fileName);
            } else {
                failureCount++;
                failureMessages.add(result.errorMessage);
            }
        }

        // 严格检查：任何文件解析失败都不允许
        if (failureCount > 0) {
            String errorMsg = String.format("源码解析存在失败: %d/%d 个文件解析失败，失败原因: %s",
                    failureCount, javaFiles.size(),
                    String.join("; ", failureMessages.subList(0, Math.min(5, failureMessages.size()))));
            log.error(errorMsg);
            throw new RuntimeException(errorMsg);
        }

        log.info("并行解析完成，成功解析{}个包的源码文件", successCount);
        return packageSourceFiles;
    }

    /**
     * 生成类的结构化摘要信息
     * 过滤 @Deprecated 注解：类有 @Deprecated 返回空，方法有 @Deprecated 则忽略
     */
    private String generateClassSummary(SourceFile sourceFile, SourceFile.ClassInfo classInfo) {
        // 检查类是否有 @Deprecated 注解，如果有则返回空字符串
        if (hasDeprecatedAnnotation(classInfo.getAnnotations())) {
            return "";
        }

        // 检查是否为Thrift生成的代码
        if (isThriftGenerated(sourceFile)) {
            // 如果是Thrift Service类，使用Service摘要
            if (isThriftService(classInfo)) {
                return generateThriftServiceSummary(classInfo);
            }
            // 如果是Thrift DTO类，使用DTO摘要
            else if (isThriftDto(classInfo)) {
                return generateThriftDtoSummary(classInfo);
            }
        }

        StringBuilder summary = new StringBuilder();

        // 类声明头部
        if (classInfo.getVisibility() != null && !classInfo.getVisibility().equals("package-private")) {
            summary.append(classInfo.getVisibility()).append(" ");
        }

        if (classInfo.getModifiers() != null && !classInfo.getModifiers().isEmpty()) {
            summary.append(String.join(" ", classInfo.getModifiers())).append(" ");
        }

        summary.append(classInfo.getClassType()).append(" ").append(classInfo.getClassName());

        // 泛型参数
        if (classInfo.getGenericTypes() != null && !classInfo.getGenericTypes().isEmpty()) {
            summary.append("<").append(String.join(", ", classInfo.getGenericTypes())).append(">");
        }

        // 继承和实现
        if (classInfo.getExtendsClass() != null) {
            summary.append(" extends ").append(simplifyTypeName(classInfo.getExtendsClass()));
        }

        if (classInfo.getImplementsInterfaces() != null && !classInfo.getImplementsInterfaces().isEmpty()) {
            // 简化实现接口的类型名称
            List<String> simplifiedInterfaces = classInfo.getImplementsInterfaces().stream()
                    .map(this::simplifyTypeName)
                    .collect(Collectors.toList());
            summary.append(" implements ").append(String.join(", ", simplifiedInterfaces));
        }

        summary.append(" {\n");

        // enum常量摘要（仅对enum类型）
        if ("enum".equals(classInfo.getClassType()) && classInfo.getEnumConstants() != null && !classInfo.getEnumConstants().isEmpty()) {
            for (SourceFile.EnumConstantInfo enumConstant : classInfo.getEnumConstants()) {
                summary.append("  ").append(enumConstant.getConstantName());

                // 添加构造函数参数
                if (enumConstant.getArguments() != null && !enumConstant.getArguments().isEmpty()) {
                    summary.append("(").append(String.join(", ", enumConstant.getArguments())).append(")");
                }

                summary.append(",\n");
            }
            summary.append("\n");
        }

        // 字段摘要（忽略private字段）
        if (classInfo.getFields() != null && !classInfo.getFields().isEmpty()) {
            boolean hasNonPrivateFields = false;
            for (SourceFile.FieldInfo field : classInfo.getFields()) {
                // 跳过private字段
                if ("private".equals(field.getVisibility())) {
                    continue;
                }

                hasNonPrivateFields = true;
                summary.append("  ");
                if (field.getVisibility() != null && !field.getVisibility().equals("package-private")) {
                    summary.append(field.getVisibility()).append(" ");
                }
                if (field.getModifiers() != null && !field.getModifiers().isEmpty()) {
                    summary.append(String.join(" ", field.getModifiers())).append(" ");
                }
                // 简化字段类型名称
                summary.append(simplifyTypeName(field.getFieldType())).append(" ").append(field.getFieldName()).append(";\n");
            }
            if (hasNonPrivateFields && classInfo.getMethods() != null && !classInfo.getMethods().isEmpty()
                    && classInfo.getMethods().stream()
                    .anyMatch(method -> !"private".equals(method.getVisibility()) && !hasDeprecatedAnnotation(method.getAnnotations()))) {
                summary.append("\n");
            }
        }

        // 方法摘要（只显示签名，不显示方法体，忽略private方法和@Deprecated方法）
        if (classInfo.getMethods() != null && !classInfo.getMethods().isEmpty()) {
            for (SourceFile.MethodInfo method : classInfo.getMethods()) {
                // 跳过private方法
                if ("private".equals(method.getVisibility())) {
                    continue;
                }

                // 跳过有 @Deprecated 注解的方法
                if (hasDeprecatedAnnotation(method.getAnnotations())) {
                    continue;
                }

                summary.append("  ");
                if (method.getVisibility() != null && !method.getVisibility().equals("package-private")) {
                    summary.append(method.getVisibility()).append(" ");
                }
                if (method.getModifiers() != null && !method.getModifiers().isEmpty()) {
                    summary.append(String.join(" ", method.getModifiers())).append(" ");
                }
                if (method.getReturnType() != null) {
                    // 简化返回类型名称
                    summary.append(simplifyTypeName(method.getReturnType())).append(" ");
                }
                summary.append(method.getMethodName()).append("(");

                // 参数
                if (method.getParameters() != null && !method.getParameters().isEmpty()) {
                    for (int i = 0; i < method.getParameters().size(); i++) {
                        SourceFile.ParameterInfo param = method.getParameters().get(i);
                        if (i > 0) {
                            summary.append(", ");
                        }
                        // 简化参数类型名称
                        summary.append(simplifyTypeName(param.getParameterType())).append(" ").append(param.getParameterName());
                    }
                }

                summary.append(")");

                // 异常 - 过滤掉不需要的异常（如TException）
                List<String> filteredExceptions = filterExceptions(method.getExceptions());
                if (filteredExceptions != null && !filteredExceptions.isEmpty()) {
                    summary.append(" throws ").append(String.join(", ", filteredExceptions));
                }

                summary.append(";\n");
            }
        }

        // 内部类摘要（仅处理白名单中配置的内部类）
        if (classInfo.getInnerClasses() != null && !classInfo.getInnerClasses().isEmpty()) {
            List<SourceFile.ClassInfo> innerClasses = classInfo.getInnerClasses();


            boolean hasNonInnerClassContent = (classInfo.getMethods() != null &&
                    classInfo.getMethods().stream().anyMatch(method ->
                            !"private".equals(method.getVisibility()) &&
                                    !hasDeprecatedAnnotation(method.getAnnotations()))) ||
                    (classInfo.getFields() != null &&
                            classInfo.getFields().stream().anyMatch(field ->
                                    !"private".equals(field.getVisibility())));

            if (hasNonInnerClassContent) {
                summary.append("\n");
            }

            for (SourceFile.ClassInfo innerClass : innerClasses) {
                // 跳过有 @Deprecated 注解的内部类
                if (hasDeprecatedAnnotation(innerClass.getAnnotations())) {
                    continue;
                }

                // 递归生成内部类的摘要，但添加缩进
                String innerClassSummary = generateClassSummary(sourceFile, innerClass);
                if (!innerClassSummary.isEmpty()) {
                    // 为内部类添加适当的缩进
                    String indentedInnerClass = innerClassSummary.lines()
                            .map(line -> "  " + line)
                            .collect(Collectors.joining("\n"));
                    summary.append(indentedInnerClass).append("\n");
                }
            }
        }


        summary.append("}");
        return summary.toString();
    }

    /**
     * 检查注解列表中是否包含 @Deprecated 注解
     *
     * @param annotations 注解列表
     * @return 如果包含 @Deprecated 注解返回 true，否则返回 false
     */
    private boolean hasDeprecatedAnnotation(List<SourceFile.AnnotationInfo> annotations) {
        if (annotations == null || annotations.isEmpty()) {
            return false;
        }

        return annotations.stream()
                .anyMatch(annotation -> "Deprecated".equals(annotation.getAnnotationName())
                        || "java.lang.Deprecated".equals(annotation.getAnnotationName()));
    }

    /**
     * 简化类型名称，去掉包名前缀
     * 主要用于简化Thrift生成的接口中的冗长包名
     *
     * @param fullTypeName 完整的类型名称
     * @return 简化后的类型名称
     */
    private String simplifyTypeName(String fullTypeName) {
        if (fullTypeName == null || fullTypeName.trim().isEmpty()) {
            return fullTypeName;
        }

        // 处理泛型类型，如 List<com.example.Type> -> List<Type>
        if (fullTypeName.contains("<") && fullTypeName.contains(">")) {
            int genericStart = fullTypeName.indexOf('<');
            int genericEnd = fullTypeName.lastIndexOf('>');

            if (genericStart > 0 && genericEnd > genericStart) {
                String baseType = fullTypeName.substring(0, genericStart);
                String genericPart = fullTypeName.substring(genericStart + 1, genericEnd);
                String suffix = fullTypeName.substring(genericEnd);

                // 递归处理泛型内的类型
                String[] genericTypes = genericPart.split(",");
                for (int i = 0; i < genericTypes.length; i++) {
                    genericTypes[i] = simplifyTypeName(genericTypes[i].trim());
                }

                return simplifyTypeName(baseType) + "<" + String.join(", ", genericTypes) + ">" + suffix;
            }
        }

        // 提取简单类名
        int lastDot = fullTypeName.lastIndexOf('.');
        if (lastDot > 0 && lastDot < fullTypeName.length() - 1) {
            return fullTypeName.substring(lastDot + 1);
        }

        return fullTypeName;
    }

    /**
     * 过滤异常列表，移除不需要显示的异常
     * 主要用于过滤掉Thrift生成的TException
     *
     * @param exceptions 原始异常列表
     * @return 过滤后的异常列表
     */
    private List<String> filterExceptions(List<String> exceptions) {
        if (exceptions == null || exceptions.isEmpty()) {
            return exceptions;
        }

        return exceptions.stream()
                .filter(exception -> !exception.equals("org.apache.thrift.TException")
                        && !exception.equals("TException"))
                .collect(Collectors.toList());
    }

    /**
     * 从文件路径提取包名
     */
    private String extractPackageName(String fileName) {
        // 去除文件名，只保留包路径
        int lastSlash = fileName.lastIndexOf('/');
        if (lastSlash > 0) {
            return fileName.substring(0, lastSlash).replace('/', '.');
        }
        return ""; // 根包
    }

    /**
     * 读取文件内容
     */
    private String readFileContent(JarInputStream jarStream) throws IOException {
        byte[] buffer = new byte[1024];
        StringBuilder content = new StringBuilder();
        int bytesRead;

        while ((bytesRead = jarStream.read(buffer)) != -1) {
            content.append(new String(buffer, 0, bytesRead));
        }

        return content.toString();
    }

    @Override
    public boolean supports(LanguageType languageType) {
        return LanguageType.JAVA.equals(languageType);
    }

    @Override
    public List<LanguageType> getSupportedLanguages() {
        return List.of(LanguageType.JAVA);
    }

    /**
     * 检查源文件是否为Thrift自动生成的代码
     * 通过源码注释"Autogenerated by Thrift Compiler"判断
     */
    private boolean isThriftGenerated(SourceFile sourceFile) {
        if (sourceFile.getContent() == null) {
            return false;
        }

        String content = sourceFile.getContent().toLowerCase();
        return content.contains("autogenerated by thrift compiler") ||
                content.contains("auto-generated by thrift compiler");
    }

    /**
     * 检查是否为Thrift生成的Service类
     * 通过是否包含Iface内部类来判断
     */
    private boolean isThriftService(SourceFile.ClassInfo classInfo) {
        if (classInfo.getInnerClasses() == null || classInfo.getInnerClasses().isEmpty()) {
            return false;
        }

        // 检查是否有名为"Iface"的内部类
        return classInfo.getInnerClasses().stream()
                .anyMatch(innerClass -> "Iface".equals(innerClass.getClassName()));
    }

    /**
     * 检查是否为Thrift生成的DTO类
     * 通过是否实现TBase接口来判断
     */
    private boolean isThriftDto(SourceFile.ClassInfo classInfo) {
        if (classInfo.getImplementsInterfaces() == null) {
            return false;
        }

        // 检查是否实现了 org.apache.thrift.TBase 接口
        return classInfo.getImplementsInterfaces().stream()
                .anyMatch(interfaceName ->
                        interfaceName.contains("org.apache.thrift.TBase") ||
                                interfaceName.contains("TBase")
                );
    }

    /**
     * 为Thrift Service类生成摘要
     */
    private String generateThriftServiceSummary(SourceFile.ClassInfo classInfo) {
        StringBuilder summary = new StringBuilder();

        // 添加Thrift Service标识
        summary.append("// thrift::service\n");

        // 类声明头部
        if (classInfo.getVisibility() != null && !classInfo.getVisibility().equals("package-private")) {
            summary.append(classInfo.getVisibility()).append(" ");
        }

        if (classInfo.getModifiers() != null && !classInfo.getModifiers().isEmpty()) {
            summary.append(String.join(" ", classInfo.getModifiers())).append(" ");
        }

        summary.append(classInfo.getClassType()).append(" ").append(classInfo.getClassName());

        // 泛型参数
        if (classInfo.getGenericTypes() != null && !classInfo.getGenericTypes().isEmpty()) {
            summary.append("<").append(String.join(", ", classInfo.getGenericTypes())).append(">");
        }

        summary.append(" {\n");

        // 只显示Iface接口的定义
        if (classInfo.getInnerClasses() != null && !classInfo.getInnerClasses().isEmpty()) {
            for (SourceFile.ClassInfo innerClass : classInfo.getInnerClasses()) {
                if ("Iface".equals(innerClass.getClassName())) {
                    summary.append("  public interface Iface {\n");

                    // 显示接口中的方法签名
                    if (innerClass.getMethods() != null && !innerClass.getMethods().isEmpty()) {
                        for (SourceFile.MethodInfo method : innerClass.getMethods()) {
                            // 跳过private方法和构造函数
                            if ("private".equals(method.getVisibility()) ||
                                    "<init>".equals(method.getMethodName())) {
                                continue;
                            }

                            summary.append("    ");
                            if (method.getReturnType() != null) {
                                summary.append(simplifyTypeName(method.getReturnType())).append(" ");
                            }
                            summary.append(method.getMethodName()).append("(");

                            // 参数
                            if (method.getParameters() != null && !method.getParameters().isEmpty()) {
                                for (int i = 0; i < method.getParameters().size(); i++) {
                                    SourceFile.ParameterInfo param = method.getParameters().get(i);
                                    if (i > 0) {
                                        summary.append(", ");
                                    }
                                    summary.append(simplifyTypeName(param.getParameterType())).append(" ").append(param.getParameterName());
                                }
                            }

                            summary.append(")");

                            // 异常
                            List<String> filteredExceptions = filterExceptions(method.getExceptions());
                            if (filteredExceptions != null && !filteredExceptions.isEmpty()) {
                                summary.append(" throws ").append(String.join(", ", filteredExceptions));
                            }

                            summary.append(";\n");
                        }
                    }

                    summary.append("  }\n");
                    break;
                }
            }
        }

        summary.append("}");
        return summary.toString();
    }

    /**
     * 为Thrift DTO类生成简化的Lombok风格摘要
     */
    private String generateThriftDtoSummary(SourceFile.ClassInfo classInfo) {
        StringBuilder summary = new StringBuilder();

        // 添加Thrift DTO标识
        summary.append("// thrift::dto\n");

        // Lombok注解
        summary.append("@Data\n");
        summary.append("@NoArgsConstructor\n");
        summary.append("@Accessors(chain = true)\n");

        // 类声明头部
        if (classInfo.getVisibility() != null && !classInfo.getVisibility().equals("package-private")) {
            summary.append(classInfo.getVisibility()).append(" ");
        }

        if (classInfo.getModifiers() != null && !classInfo.getModifiers().isEmpty()) {
            summary.append(String.join(" ", classInfo.getModifiers())).append(" ");
        }

        summary.append(classInfo.getClassType()).append(" ").append(classInfo.getClassName());

        // 泛型参数
        if (classInfo.getGenericTypes() != null && !classInfo.getGenericTypes().isEmpty()) {
            summary.append("<").append(String.join(", ", classInfo.getGenericTypes())).append(">");
        }

        // Thrift DTO类跳过继承和接口实现，保持简洁

        summary.append(" {\n");

        // 字段摘要（只保留public字段，这些是DTO的核心数据）
        if (classInfo.getFields() != null && !classInfo.getFields().isEmpty()) {
            for (SourceFile.FieldInfo field : classInfo.getFields()) {
                // 跳过private字段
                if ("private".equals(field.getVisibility())) {
                    continue;
                }

                // 跳过Thrift元数据字段
                if ("metaDataMap".equals(field.getFieldName()) ||
                        field.getFieldName().contains("__")) {
                    continue;
                }

                summary.append("  ");
                if (field.getVisibility() != null && !field.getVisibility().equals("package-private")) {
                    summary.append(field.getVisibility()).append(" ");
                }
                if (field.getModifiers() != null && !field.getModifiers().isEmpty()) {
                    summary.append(String.join(" ", field.getModifiers())).append(" ");
                }
                summary.append(simplifyTypeName(field.getFieldType())).append(" ").append(field.getFieldName()).append(";\n");
            }
        }

        // Thrift DTO类的所有方法都是样板代码，由Lombok注解提供，无需显示

        // Thrift DTO类忽略所有内部类（如_Fields枚举），这些都是框架生成的样板代码

        summary.append("}");
        return summary.toString();
    }

    /**
     * 解析结果内部类
     */
    private static class ParseResult {
        final String fileName;
        final String packageName;
        final SourceFile sourceFile;
        final boolean success;
        final String errorMessage;

        ParseResult(String fileName, String packageName, SourceFile sourceFile, boolean success, String errorMessage) {
            this.fileName = fileName;
            this.packageName = packageName;
            this.sourceFile = sourceFile;
            this.success = success;
            this.errorMessage = errorMessage;
        }
    }

    /**
     * Java文件信息内部类
     */
    private static class JavaFileInfo {
        final String fileName;
        final String packageName;
        final String content;

        JavaFileInfo(String fileName, String packageName, String content) {
            this.fileName = fileName;
            this.packageName = packageName;
            this.content = content;
        }
    }


} 