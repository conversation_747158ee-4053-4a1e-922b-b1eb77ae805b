package com.xiaohongshu.codewiz.complete.model.dpc;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.util.Map;
import java.util.HashMap;

/**
 * 包上下文项，对应一个package级别的信息
 * 注意：依赖信息现在由 DependencyPackageContext 统一管理，这里只保存包级别的数据
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PackageContextItem {
    
    /**
     * 包名称
     */
    private String packageName;

    /**
     * 包文件数量（冗余字段，便于快速获取）
     */
    private Integer fileCount;
    
    /**
     * 类级别的签名信息，key为类名，value为类签名
     */
    private Map<String, String> classSignatures; 
    
    /**
     * 类数量（冗余字段，便于快速获取）
     */
    private Integer classCount; 
    
    /**
     * 是否有错误
     */
    private Boolean hasError;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 错误类型：SOURCE_PARSE_ERROR, FILE_READ_ERROR, etc.
     */
    private String errorType;

    private String sourceUrl;

    private String metadataUrl;
    
    /**
     * 简化的构造函数
     */
    public PackageContextItem(String packageName) {
        this.packageName = packageName;
        this.classSignatures = new HashMap<>();
        this.classCount = 0;
        this.hasError = false;
    }
    
    /**
     * 创建错误结果的静态方法
     */
    public static PackageContextItem createErrorResult(String packageName, String errorMessage, String errorType) {
        return PackageContextItem.builder()
                .packageName(packageName)
                .classSignatures(new HashMap<>())
                .classCount(0)
                .hasError(true)
                .errorMessage(errorMessage)
                .errorType(errorType)
                .build();
    }
    
    /**
     * 创建成功结果的静态方法
     */
    public static PackageContextItem createSuccessResult(String packageName, Map<String, String> classSignatures) {
        return PackageContextItem.builder()
                .packageName(packageName)
                .classSignatures(classSignatures != null ? classSignatures : new HashMap<>())
                .classCount(classSignatures != null ? classSignatures.size() : 0)
                .hasError(false)
                .build();
    }
    
    /**
     * 检查是否有有效的类签名
     */
    public boolean hasValidClassSignatures() {
        return classSignatures != null && !classSignatures.isEmpty();
    }
    
    /**
     * 获取实际的类数量
     */
    public int getClassSignatureCount() {
        return classSignatures != null ? classSignatures.size() : 0;
    }
} 