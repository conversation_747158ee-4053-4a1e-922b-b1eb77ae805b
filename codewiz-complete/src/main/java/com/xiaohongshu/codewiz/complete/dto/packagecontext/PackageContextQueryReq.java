package com.xiaohongshu.codewiz.complete.dto.packagecontext;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.util.List;

/**
 * 代码上下文查询请求DTO
 * 支持单个和批量查询
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PackageContextQueryReq {
    
    /**
     * 依赖列表 - 支持单个或多个依赖查询
     */
    private List<DependencyDTO> dependencies;

    /**
     * 依赖管理类型
     */
    private String dependencyType;

    /**
     * 语言类型
     */
    private String language;
} 