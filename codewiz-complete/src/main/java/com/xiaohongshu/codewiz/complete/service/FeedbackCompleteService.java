package com.xiaohongshu.codewiz.complete.service;

import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.xiaohongshu.codewiz.complete.s3.CompleteS3Client;
import com.xiaohongshu.codewiz.core.entity.common.SingleResponse;
import com.xiaohongshu.codewiz.core.entity.feedback.dto.FeedbackQueryResponse;
import com.xiaohongshu.codewiz.core.entity.feedback.dto.FeedbackReportRequest;
import com.xiaohongshu.codewiz.core.service.feedback.FeedbackService;
import com.xiaohongshu.codewiz.core.utils.JsonMapperUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 反馈完整服务
 *
 * <AUTHOR>
 * @since 2024-05-24
 */
@Slf4j
@Service
public class FeedbackCompleteService {

    @Autowired
    private FeedbackService feedbackService;

    @Autowired
    private CompleteS3Client completeS3Client;

    /**
     * 提交反馈完整流程
     *
     * @param request 反馈请求
     * @return 提交结果
     */
    public SingleResponse<Void> reportFeedback(FeedbackReportRequest request) {
        try {
            // 1. 上传日志文件到S3
            String logFileKey = null;
            if (request.getFeedback() != null && StringUtils.isNotBlank(request.getFeedback().get("pluginLogFile").toString())) {
                logFileKey = completeS3Client.uploadFeedbackLog(request.getFeedback().get("pluginLogFile").toString());
                if (logFileKey == null) {
                    log.error("Failed to upload log file for feedback: {}", request.getFeedbackId());
                    return SingleResponse.buildFailure("500", "Failed to upload log file");
                }
                log.info("Uploaded log file to S3, key: {}", logFileKey);
            }

            // 2. 调用核心服务保存反馈
            return feedbackService.reportFeedback(request, logFileKey);

        } catch (Exception e) {
            log.error("Report feedback complete service error, feedbackId: {}",
                    request.getFeedbackId(), e);
            return SingleResponse.buildFailure("500", "Report feedback error: " + e.getMessage());
        }
    }

    /**
     * 查询反馈完整流程
     *
     * @param feedbackId 反馈ID
     * @return 查询结果
     */
    public SingleResponse<FeedbackQueryResponse> queryFeedback(String feedbackId) {
        try {
            // 1. 从数据库查询反馈基础信息
            SingleResponse<FeedbackQueryResponse> response = feedbackService.queryFeedback(feedbackId);
            if (!response.isSuccess()) {
                return response;
            }

            FeedbackQueryResponse feedbackResponse = response.getData();

            // 2. 从S3下载并解压日志文件内容
            if (StringUtils.isNotBlank(feedbackResponse.getLogFileKey())) {
                try {
                    // 取出s3 key
                    String s3Key = feedbackResponse.getLogFileKey();

                    if (StringUtils.isNotBlank(s3Key)) {
                        // 从S3下载日志内容
                        String logContent = completeS3Client.downloadFeedbackLog(s3Key);
                        if (logContent != null) {
                            // 设置日志内容到响应中（S3中存储的已经是解压后的内容）
                            feedbackResponse.setPluginLogFileContent(logContent);
                            log.info("Downloaded and set log content for feedback: {}, content size: {}",
                                    feedbackId, logContent.length());
                        } else {
                            log.warn("Failed to download log content from S3, key: {}", s3Key);
                        }
                    }
                } catch (Exception e) {
                    log.error("Failed to process log file for feedback: {}", feedbackId, e);
                    // 不影响主流程，继续返回其他信息
                }
            }

            log.info("queryFeedback complete success, feedbackId: {}", feedbackId);
            return SingleResponse.of(feedbackResponse);

        } catch (Exception e) {
            log.error("Query feedback complete service error, feedbackId: {}", feedbackId, e);
            return SingleResponse.buildFailure("500", "Query feedback error: " + e.getMessage());
        }
    }
}