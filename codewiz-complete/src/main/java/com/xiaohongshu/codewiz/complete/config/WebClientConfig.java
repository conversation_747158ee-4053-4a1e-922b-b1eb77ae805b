package com.xiaohongshu.codewiz.complete.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.function.client.WebClient;

/**
 * WebClient配置
 */
@Configuration
public class WebClientConfig {

    @Bean
    public WebClient webClient() {


        return WebClient.builder()
                .build();
    }
} 