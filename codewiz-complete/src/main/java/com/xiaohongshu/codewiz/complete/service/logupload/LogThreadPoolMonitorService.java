package com.xiaohongshu.codewiz.complete.service.logupload;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;

import javax.annotation.Resource;

import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import com.dianping.cat.Cat;

import lombok.extern.slf4j.Slf4j;

/**
 * 日志上报线程池监控服务
 * 定时监控logUploadExecutor的队列任务数
 *
 * <AUTHOR>
 * @date 2025/6/30
 */
@Slf4j
@Service
public class LogThreadPoolMonitorService {

    @Resource
    private ExecutorService logUploadExecutor;

    /**
     * 定时监控线程池队列任务数
     * 每30秒执行一次，上报队列任务数到Cat监控系统
     */
    @Scheduled(fixedRate = 30000)
    public void monitorLogUploadQueue() {
        try {
            if (!(logUploadExecutor instanceof ThreadPoolExecutor)) {
                log.warn("logUploadExecutor不是ThreadPoolExecutor类型，无法监控队列指标");
                return;
            }

            ThreadPoolExecutor threadPool = (ThreadPoolExecutor) logUploadExecutor;

            // 获取队列任务数
            int queueSize = threadPool.getQueue().size();

            // 上报队列任务数到Cat
            Cat.gauge("thread_pool_queue_size", "日志上报线程池队列任务数")
                    .addTag("pool_name", "logUploadExecutor")
                    .setValue(queueSize);

            // 记录日志
            log.info("日志上报线程池监控 - 队列任务数: {}", queueSize);

        } catch (Exception e) {
            log.error("线程池队列监控异常", e);
        }
    }
} 