package com.xiaohongshu.codewiz.complete.model.dpc;

import java.util.List;

import com.xiaohongshu.codewiz.complete.model.dependency.Dependency;

import lombok.Builder;
import lombok.Data;

/**
 * 依赖包上下文数据结构
 * 用于封装特定依赖版本及其对应的所有包上下文项
 */
@Data
@Builder
public class DependencyPackageContext {
    /**
     * 依赖信息
     */
    private Dependency dependency;
    
    /**
     * 该依赖版本对应的所有包上下文项
     */
    private List<PackageContextItem> packageContextItems;
    
    /**
     * 是否为最新版本
     */
    private boolean isLatest;

    /**
     * 是否为snapshot版本
     */
    private boolean isSnapshot;
    
    /**
     * 验证数据有效性
     */
    public boolean isValid() {
        return dependency != null 
            && packageContextItems != null 
            && !packageContextItems.isEmpty()
            && packageContextItems.stream().allMatch(item -> 
                item.getClassSignatures() != null 
                && !item.getClassSignatures().isEmpty());
    }
    
    /**
     * 获取包上下文项数量
     */
    public int getPackageCount() {
        return packageContextItems != null ? packageContextItems.size() : 0;
    }
    
    /**
     * 获取总的类签名数量
     */
    public int getTotalClassCount() {
        if (packageContextItems == null) {
            return 0;
        }
        return packageContextItems.stream()
            .mapToInt(item -> item.getClassSignatures() != null ? item.getClassSignatures().size() : 0)
            .sum();
    }

    public String getDependencyKey() {
        if (dependency == null) {
            return "";
        }
        return dependency.getDependencyKey();
    }
} 