package com.xiaohongshu.codewiz.complete.model.task;

import lombok.Data;

/**
 * 失败信息
 */
@Data
public class FailureInfo {
    private final FailureType type;
    private final String reason;
    private final Exception exception;

    public FailureInfo(FailureType type, String reason) {
        this.type = type;
        this.reason = reason;
        this.exception = null;
    }

    public FailureInfo(FailureType type, String reason, Exception exception) {
        this.type = type;
        this.reason = reason;
        this.exception = exception;
    }
} 