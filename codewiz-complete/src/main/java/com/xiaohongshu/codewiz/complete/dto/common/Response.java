package com.xiaohongshu.codewiz.complete.dto.common;

import org.slf4j.MDC;
import com.xiaohongshu.xray.logging.LogConstants;
import lombok.Data;

/**
 * 通用响应基类
 */
@Data
public class Response {

    private boolean success;

    private String code;

    private String msg;

    private String xrayTraceId;

    public Response() {
        // 配合com.xiaohongshu.infra.gatewaystarter.HttpInterceptor进行请求日志链路追踪
        this.xrayTraceId = MDC.get(LogConstants.XRAY_TRACE_ID);
    }
} 