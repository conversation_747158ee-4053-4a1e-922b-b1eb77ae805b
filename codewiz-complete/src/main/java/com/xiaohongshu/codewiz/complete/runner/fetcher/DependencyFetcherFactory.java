package com.xiaohongshu.codewiz.complete.runner.fetcher;

import com.xiaohongshu.codewiz.complete.model.dependency.DependencyType;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

/**
 * 依赖获取器工厂
 * 根据编程语言选择合适的fetcher实现
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DependencyFetcherFactory {
    
    private final List<DependencyFetcher> fetchers;
    
    /**
     * 根据语言获取合适的fetcher
     * @param dependencyType 依赖类型
     * @return fetcher实现
     */
    public Optional<DependencyFetcher> getFetcher(DependencyType dependencyType) {
        log.debug("寻找支持依赖类型 {} 的fetcher", dependencyType.getDisplayName());
        
        for (DependencyFetcher fetcher : fetchers) {
            if (fetcher.supports(dependencyType)) {
                log.debug("找到支持依赖类型 {} 的fetcher: {}", dependencyType.getDisplayName(), fetcher.getClass().getSimpleName());
                return Optional.of(fetcher);
            }
        }
        
        log.warn("未找到支持依赖类型 {} 的fetcher", dependencyType.getDisplayName());
        return Optional.empty();
    }
} 