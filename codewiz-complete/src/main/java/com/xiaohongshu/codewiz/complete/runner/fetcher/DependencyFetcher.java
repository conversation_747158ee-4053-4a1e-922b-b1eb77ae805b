package com.xiaohongshu.codewiz.complete.runner.fetcher;

import java.util.List;
import java.util.Map;
import java.util.Optional;

import com.xiaohongshu.codewiz.complete.model.dependency.Dependency;
import com.xiaohongshu.codewiz.complete.model.dependency.Artifact;
import com.xiaohongshu.codewiz.complete.model.dependency.DependencyType;

/**
 * 依赖获取器接口
 * 负责依赖的获取，对接不同的制品库
 */
public interface DependencyFetcher {

    /**
     * 判断是否支持指定语言
     *
     * @param dependencyType 依赖类型
     * @return 是否支持
     */
    boolean supports(DependencyType dependencyType);

    /**
     * 获取依赖制品信息
     *
     * @param dependency 解析后的依赖信息
     * @return 制品信息
     */
    Optional<Artifact> fetchArtifact(Dependency dependency);

    /**
     * 批量获取依赖制品信息
     * <p>
     * 设计理念：
     * 1. 优先使用原生批量API（如NPM批量查询、PyPI批量接口等）
     * 2. 对于不支持批量的服务，退化到并发调用单个接口
     * 3. 不同语言/仓库类型应该有不同的批量实现策略
     *
     * @param dependencies 依赖列表
     * @return 成功获取的依赖制品信息，key为依赖键值
     */
    default List<Artifact> batchFetcherArtifact(List<Dependency> dependencies) {
        // 默认实现：不支持批量获取
        throw new UnsupportedOperationException("该实现不支持批量获取功能");
    }

    /**
     * 解析依赖的真实版本
     * 用于处理空version、SNAPSHOT等特殊版本标识
     *
     * @param dependencies 依赖列表
     * @return 解析后的依赖列表（version已更新为真实版本）
     */
    default List<Dependency> resolveVersions(List<Dependency> dependencies) {
        // 默认实现：直接返回原依赖列表，不做版本解析
        return dependencies;
    }

    default Dependency resolveVersion(Dependency dependency) {
        // 默认实现：直接返回原依赖列表，不做版本解析
        return dependency;
    }

    /**
     * 解析依赖的所有可用release版本（新版本）
     * 用于依赖刷新场景，获取制品库中该依赖的所有版本号
     * 
     * @param dependencies 依赖列表（不包含version或version为空）
     * @return Map，key为依赖签名（namespace:name），value为版本列表（按从新到旧排序）
     */
    default Map<String, List<String>> getAllReleaseVersions(List<Dependency> dependencies) {
        // 默认实现：不支持获取所有版本
        throw new UnsupportedOperationException("该实现不支持获取所有版本功能");
    }
} 