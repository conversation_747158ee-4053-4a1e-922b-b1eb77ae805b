package com.xiaohongshu.codewiz.complete.model.lang;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;

/**
 * 源码文件模型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SourceFile {
    
    private String fileName;
    private String content;
    
    // 包信息
    private String packageName;
    
    // 导入信息
    private List<String> imports;
    
    // 解析后的结构化信息
    private List<ClassInfo> classes;
    
    /**
     * 类信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ClassInfo {
        private String className;
        private String classType; // class, interface, enum, annotation
        private String visibility; // public, private, protected, package-private
        private List<String> modifiers; // static, final, abstract等
        private String extendsClass;
        private List<String> implementsInterfaces;
        private List<String> genericTypes; // 泛型参数
        private List<AnnotationInfo> annotations; // 注解信息
        private List<FieldInfo> fields;
        private List<MethodInfo> methods;
        private List<ClassInfo> innerClasses;
        private String javadoc; // JavaDoc注释
        private int lineNumber; // 类在文件中的行号
        
        // 新增：enum常量信息（仅当classType为enum时使用）
        private List<EnumConstantInfo> enumConstants;
        
        // 新增：类的签名信息，用于AI理解和检索
        private String signature; // 类的结构化签名摘要
    }
    
    /**
     * Enum常量信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class EnumConstantInfo {
        private String constantName;
        private List<String> arguments; // 构造函数参数
        private List<AnnotationInfo> annotations; // 常量注解
        private String javadoc; // JavaDoc注释
        private int lineNumber; // 常量在文件中的行号
    }
    
    /**
     * 字段信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FieldInfo {
        private String fieldName;
        private String fieldType;
        private String visibility;
        private List<String> modifiers;
        private String defaultValue; // 如果有初始值
        private List<AnnotationInfo> annotations; // 字段注解
        private String javadoc; // JavaDoc注释
        private int lineNumber; // 字段在文件中的行号
    }
    
    /**
     * 方法信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MethodInfo {
        private String methodName;
        private String returnType;
        private String visibility;
        private List<String> modifiers;
        private List<ParameterInfo> parameters;
        private List<String> exceptions;
        private List<String> genericTypes; // 方法泛型参数
        private List<AnnotationInfo> annotations; // 方法注解
        private boolean isConstructor;
        private String javadoc; // JavaDoc注释
        private int lineNumber; // 方法在文件中的行号
        private String methodBody; // 方法体概要（可选，用于AI分析）
    }
    
    /**
     * 参数信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ParameterInfo {
        private String parameterName;
        private String parameterType;
        private List<String> modifiers; // final等
        private List<AnnotationInfo> annotations; // 参数注解
    }
    
    /**
     * 注解信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AnnotationInfo {
        private String annotationName;
        private List<AnnotationAttribute> attributes; // 注解属性
        
        /**
         * 注解属性
         */
        @Data
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        public static class AnnotationAttribute {
            private String name;
            private String value;
        }
    }
} 