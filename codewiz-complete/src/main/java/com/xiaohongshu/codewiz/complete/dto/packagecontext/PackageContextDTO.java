package com.xiaohongshu.codewiz.complete.dto.packagecontext;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * Package级别的信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PackageContextDTO {
    
    /**
     * 包名
     */
    private String packageName;
    
    /**
     * 该包下的类签名信息 (className -> signature)
     */
    private Map<String, String> classSignatures;
    
    /**
     * 该包下的类数量
     */
    private Integer classCount;
    
    /**
     * 源文件数量
     */
    private Integer fileCount;
    
    /**
     * 发布时间
     */
    private LocalDateTime publishTime;
    
    /**
     * 是否有错误
     */
    private Boolean hasError;
    
    /**
     * 错误信息（如果有的话）
     */
    private String errorMessage;
    
    /**
     * 错误类型（如果有的话）
     */
    private String errorType;
} 