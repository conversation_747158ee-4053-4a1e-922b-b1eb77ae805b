package com.xiaohongshu.codewiz.complete.dto.logdata;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import lombok.Data;

/**
 * 日志条目
 */
@Data
public class LogData {

    /**
     * 日志级别
     */
    @NotBlank(message = "日志级别不能为空")
    private String level;

    /**
     * 日志内容
     */
    @NotBlank(message = "日志内容不能为空")
    private String msg;

    /**
     * 插件所在IDE
     */
    @NotBlank(message = "IDE信息不能为空")
    private String ide;

    /**
     * 日志所属模块
     */
    @NotBlank(message = "模块信息不能为空")
    private String module;

    /**
     * 日志在客户端生成时间
     */
    @NotBlank(message = "客户端时间戳不能为空")
    private String clientTimestamp;

    /**
     * 链路追踪ID (可选)
     */
    private String traceId;

    /**
     * 会话ID (可选)
     */
    private String sessionId;

    /**
     * 请求ID，用于请求级别追踪 (可选)
     */
    private String requestId;

    /**
     * 环境信息
     */
    @Valid
    private LogEnvironment env;
} 