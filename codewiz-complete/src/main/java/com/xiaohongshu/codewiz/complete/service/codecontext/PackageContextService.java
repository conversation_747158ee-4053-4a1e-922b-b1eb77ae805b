package com.xiaohongshu.codewiz.complete.service.codecontext;

import com.xiaohongshu.codewiz.complete.dto.packagecontext.PackageContextQueryReq;
import com.xiaohongshu.codewiz.complete.dto.packagecontext.PackageContextQueryResp;

/**
 * 代码上下文服务接口
 * 负责主流程控制，触发上下文构建流程，协调各组件
 */
public interface PackageContextService {
    
    /**
     * 查询依赖的上下文知识
     * @param request 查询请求，包含groupId、artifactId、version等信息
     * @return 上下文查询响应
     */
    PackageContextQueryResp queryContext(PackageContextQueryReq request);
} 