package com.xiaohongshu.codewiz.complete.controller;

import com.xiaohongshu.codewiz.complete.dto.logdata.LogBatchUploadReq;
import com.xiaohongshu.codewiz.complete.dto.logdata.LogBatchUploadResp;
import com.xiaohongshu.codewiz.complete.service.logupload.LogService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

/**
 * 日志上报控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/logs")
@RequiredArgsConstructor
@Validated
public class LogController {

    private final LogService logService;

    /**
     * 批量上报日志（异步处理）
     *
     * @param request 批量日志上报请求
     * @return 接受结果，实际处理异步进行
     */
    @PostMapping("/batch")
    public LogBatchUploadResp batchUpload(@Valid @RequestBody LogBatchUploadReq request, 
                                          HttpServletRequest httpRequest) {
        return logService.batchUpload(request);
    }
} 