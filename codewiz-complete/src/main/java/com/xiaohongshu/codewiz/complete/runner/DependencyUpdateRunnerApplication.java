package com.xiaohongshu.codewiz.complete.runner;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * 依赖更新 Runner 独立启动类
 * 
 * 专门用于运行依赖更新任务，不启动 HTTP 服务器
 * 
 * 使用方式:
 * 1. IntelliJ IDEA: 直接运行此类的 main 方法
 * 2. 命令行: java -jar xxx.jar --spring.main.sources=com.xiaohongshu.codewiz.complete.runner.DependencyUpdateRunnerApplication
 * 3. Maven: mvn spring-boot:run -Dspring-boot.run.mainClass=com.xiaohongshu.codewiz.complete.runner.DependencyUpdateRunnerApplication
 */
@SpringBootApplication(scanBasePackages = {"com.xiaohongshu.infra", "com.xiaohongshu.codewiz"})
@EnableAsync
public class DependencyUpdateRunnerApplication {

    public static void main(String[] args) {
        
        // 禁用 Web 环境，避免启动 HTTP 服务器
        System.setProperty("spring.main.web-application-type", "none");
        
        // 启动 Spring Boot 应用
        SpringApplication app = new SpringApplication(DependencyUpdateRunnerApplication.class);
        app.setWebApplicationType(org.springframework.boot.WebApplicationType.NONE);
        app.run(args);
    }
} 