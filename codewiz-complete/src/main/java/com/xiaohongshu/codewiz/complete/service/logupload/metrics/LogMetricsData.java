package com.xiaohongshu.codewiz.complete.service.logupload.metrics;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 日志服务监控数据收集器
 * 用于收集一次日志处理过程中的所有监控数据，最后统一上报
 */
@Data
public class LogMetricsData {
    
    // 批次信息
    private String batchId;
    private int totalLogCount;
    private long totalSizeBytes;
    private long processingTimeMs;
    
    // 处理状态
    private String status; // "success" 或 "failed"
    
    // 单条日志信息（用于记录每条日志的详细信息）
    private List<LogEntryMetrics> logEntries = new ArrayList<>();
    
    @Data
    public static class LogEntryMetrics {
        private String level;
        private String ide; 
        private String module;
        private long sizeBytes;
    }
    
    public void addLogEntry(String level, String ide, String module, long sizeBytes) {
        LogEntryMetrics entry = new LogEntryMetrics();
        entry.setLevel(level);
        entry.setIde(ide);
        entry.setModule(module);
        entry.setSizeBytes(sizeBytes);
        logEntries.add(entry);
    }
} 