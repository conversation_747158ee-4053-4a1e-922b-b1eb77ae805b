package com.xiaohongshu.codewiz.complete.runner.builder;

import java.util.List;

import com.xiaohongshu.codewiz.complete.model.task.PackageContextBuildResult;
import com.xiaohongshu.codewiz.complete.model.dependency.Artifact;
import com.xiaohongshu.codewiz.complete.model.lang.LanguageType;

/**
 * 知识构建器接口
 * 负责将制品信息组装成业务需要的知识
 */
public interface PackageContextBuilder {

    /**
     * 构建知识信息（带详细错误信息）
     * 返回详细的构建结果，包括成功的项目和详细的错误信息
     *
     * @param artifact 依赖制品信息
     * @return 包含成功结果和错误信息的构建结果
     */
    PackageContextBuildResult buildPackageContextWithDetails(Artifact artifact);

    /**
     * 判断是否支持指定的语言类型
     *
     * @param languageType 编程语言类型
     * @return 是否支持
     */
    boolean supports(LanguageType languageType);

    /**
     * 获取支持的语言类型列表
     *
     * @return 支持的语言类型列表
     */
    List<LanguageType> getSupportedLanguages();
} 