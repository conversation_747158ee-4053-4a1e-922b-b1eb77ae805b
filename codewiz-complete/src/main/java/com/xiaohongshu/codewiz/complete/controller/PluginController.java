package com.xiaohongshu.codewiz.complete.controller;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.databind.JsonNode;
import com.xiaohongshu.codewiz.complete.s3.CompleteS3Client;

/**
 * <AUTHOR>
 * Created on 2025/4/2
 */
@RestController
@RequestMapping("/api/plugin")
public class PluginController {
    @Resource
    private CompleteS3Client completeS3Client;

    @GetMapping("featureSnippets")
    public JsonNode xhsFeatureSnippetList() {
        return completeS3Client.getXhsFeatureSnippet();
    }
}
