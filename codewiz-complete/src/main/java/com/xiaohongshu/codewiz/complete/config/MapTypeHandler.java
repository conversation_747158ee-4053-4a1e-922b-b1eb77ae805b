package com.xiaohongshu.codewiz.complete.config;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;

import lombok.extern.slf4j.Slf4j;

/**
 * 自定义Map<String, String>类型处理器
 * 使用FastJSON2进行高性能JSON序列化和反序列化
 */
@Slf4j
@MappedTypes({Map.class})
@MappedJdbcTypes({JdbcType.VARCHAR, JdbcType.LONGVARCHAR})
public class MapTypeHandler extends BaseTypeHandler<Map<String, String>> {

    // FastJSON2类型引用
    private static final TypeReference<Map<String, String>> MAP_TYPE_REFERENCE =
            new TypeReference<Map<String, String>>() {
            };

    // 缓存空Map，避免重复创建
    private static final Map<String, String> EMPTY_MAP = new HashMap<>();

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, Map<String, String> parameter, JdbcType jdbcType)
            throws SQLException {
        try {
            if (parameter == null || parameter.isEmpty()) {
                ps.setString(i, "{}");
            } else {
                String json = JSON.toJSONString(parameter);
                ps.setString(i, json);
            }
        } catch (Exception e) {
            log.error("序列化Map到JSON失败", e);
            ps.setString(i, "{}");
        }
    }

    @Override
    public Map<String, String> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        return parseJson(rs.getString(columnName));
    }

    @Override
    public Map<String, String> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        return parseJson(rs.getString(columnIndex));
    }

    @Override
    public Map<String, String> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        return parseJson(cs.getString(columnIndex));
    }

    private Map<String, String> parseJson(String json) {
        // 快速路径：处理空值情况
        if (json == null || json.isEmpty() || "null".equals(json)) {
            return new HashMap<>(EMPTY_MAP);
        }

        // 快速路径：处理空JSON对象
        String trimmed = json.trim();
        if ("{}".equals(trimmed)) {
            return new HashMap<>(EMPTY_MAP);
        }

        try {
            // 使用FastJSON2解析，性能比Jackson高30-50%
            Map<String, String> result = JSON.parseObject(trimmed, MAP_TYPE_REFERENCE);
            return result != null ? result : new HashMap<>(EMPTY_MAP);
        } catch (Exception e) {
            log.warn("FastJSON解析JSON到Map失败，使用空Map，JSON长度: {}", json.length());
            return new HashMap<>(EMPTY_MAP);
        }

    }
} 