package com.xiaohongshu.codewiz.complete.model.parser;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import lombok.Data;

import java.util.List;

/**
 * Maven元数据模型
 * 用于解析maven-metadata.xml文件
 * 支持RELEASE和SNAPSHOT版本
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JacksonXmlRootElement(localName = "metadata")
public class MavenMetadata {
    
    @JacksonXmlProperty(localName = "groupId")
    private String groupId;
    
    @JacksonXmlProperty(localName = "artifactId")
    private String artifactId;
    
    @JacksonXmlProperty(localName = "version")
    private String version;
    
    @JacksonXmlProperty(localName = "versioning")
    private Versioning versioning;
    
    @Data
    public static class Versioning {
        @JacksonXmlProperty(localName = "latest")
        private String latest;
        
        @JacksonXmlProperty(localName = "release")
        private String release;
        
        @JacksonXmlProperty(localName = "lastUpdated")
        private String lastUpdated;
        
        @JacksonXmlProperty(localName = "versions")
        private Versions versions;
        
        @JacksonXmlProperty(localName = "snapshot")
        private Snapshot snapshot;
        
        @JacksonXmlProperty(localName = "snapshotVersions")
        private SnapshotVersions snapshotVersions;
    }
    
    @Data
    public static class Versions {
        @JacksonXmlElementWrapper(useWrapping = false)
        @JacksonXmlProperty(localName = "version")
        private List<String> version;
    }
    
    @Data
    public static class Snapshot {
        @JacksonXmlProperty(localName = "timestamp")
        private String timestamp;
        
        @JacksonXmlProperty(localName = "buildNumber")
        private String buildNumber;
    }
    
    @Data
    public static class SnapshotVersions {
        @JacksonXmlElementWrapper(useWrapping = false)
        @JacksonXmlProperty(localName = "snapshotVersion")
        private List<SnapshotVersion> snapshotVersion;
    }
    
    @Data
    public static class SnapshotVersion {
        @JacksonXmlProperty(localName = "classifier")
        private String classifier;
        
        @JacksonXmlProperty(localName = "extension")
        private String extension;
        
        @JacksonXmlProperty(localName = "value")
        private String value;
        
        @JacksonXmlProperty(localName = "updated")
        private String updated;
    }
} 