package com.xiaohongshu.codewiz.complete.dto.logdata;

import com.xiaohongshu.codewiz.complete.dto.common.Response;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 批量日志上报响应（异步处理）
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LogBatchUploadResp extends Response {

    /**
     * 本次上报的批次ID，用于问题追踪
     */
    private String batchId;

    /**
     * 接收到的日志数量
     */
    private Integer receivedCount;

    /**
     * 创建成功响应（异步接受）
     */
    public static LogBatchUploadResp accepted(String batchId, Integer receivedCount) {
        LogBatchUploadResp resp = new LogBatchUploadResp();
        resp.setSuccess(true);
        resp.setCode("200");
        resp.setMsg("日志上报请求已接受，正在异步处理");
        resp.setBatchId(batchId);
        resp.setReceivedCount(receivedCount);
        return resp;
    }

    /**
     * 创建失败响应
     */
    public static LogBatchUploadResp failure(String errorMsg) {
        LogBatchUploadResp resp = new LogBatchUploadResp();
        resp.setSuccess(false);
        resp.setCode("500");
        resp.setMsg(errorMsg);
        return resp;
    }
} 