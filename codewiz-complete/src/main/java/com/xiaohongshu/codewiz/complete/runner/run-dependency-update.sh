#!/bin/bash

# 依赖更新 Runner 启动脚本
# 支持定时触发和手动执行

set -e

# 配置参数
APP_NAME="codewiz-server"
JAR_PATH="../../../../../../target/codewiz-complete-1.0.0.jar"
CONFIG_FILE="./dependency-update-config.json"
LOG_DIR="./logs/dependency-update"
PROFILE="prod"

# JVM 参数
JVM_OPTS="-Xms512m -Xmx2g -XX:+UseG1GC"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查运行环境..."
    
    # 检查 Java
    if ! command -v java &> /dev/null; then
        log_error "Java 未安装或不在 PATH 中"
        exit 1
    fi
    
    # 检查 JAR 包
    if [ ! -f "$JAR_PATH" ]; then
        log_error "JAR 包不存在: $JAR_PATH"
        log_info "请先编译项目: mvn clean package -DskipTests"
        exit 1
    fi
    
    # 检查配置文件
    if [ ! -f "$CONFIG_FILE" ]; then
        log_error "配置文件不存在: $CONFIG_FILE"
        log_info "请参考 dependency-update-config.json 示例创建配置文件"
        exit 1
    fi
    
    # 创建日志目录
    mkdir -p "$LOG_DIR"
    
    log_info "环境检查通过"
}

# 运行依赖更新
run_dependency_update() {
    local force_refresh=${1:-false}
    local batch_size=${2:-20}
    
    log_info "开始执行依赖更新..."
    log_info "强制刷新: $force_refresh"
    log_info "批处理大小: $batch_size"
    
    # 构建运行参数 - 大部分参数都有默认值，只需要设置核心参数
    local java_args="$JVM_OPTS"
    java_args="$java_args -Dspring.profiles.active=$PROFILE"
    java_args="$java_args -Dcodewiz.dependency-update.runner.enabled=true"
    
    # 可选参数，只在非默认值时设置
    if [ "$force_refresh" != "false" ]; then
        java_args="$java_args -Dcodewiz.dependency-update.runner.force-refresh=$force_refresh"
    fi
    
    if [ "$batch_size" != "20" ]; then
        java_args="$java_args -Dcodewiz.dependency-update.runner.batch-size=$batch_size"
    fi
    
    # 如果指定了自定义配置文件，才设置该参数
    if [ "$CONFIG_FILE" != "./dependency-update-config.json" ]; then
        local config_abs_path=$(realpath "$CONFIG_FILE")
        java_args="$java_args -Dcodewiz.dependency-update.runner.config-file=$config_abs_path"
    fi
    
    # 执行应用
    local start_time=$(date +%s)
    log_info "启动应用..."
    log_info "使用参数: $java_args"
    
    if java $java_args -jar "$JAR_PATH"; then
        local end_time=$(date +%s)
        local duration=$((end_time - start_time))
        log_info "依赖更新执行成功，耗时: ${duration}秒"
        
        # 显示最新的报告文件
        local latest_report=$(find "./logs/dependency-update" -name "final_report.txt" -type f -printf '%T@ %p\n' 2>/dev/null | sort -n | tail -1 | cut -d' ' -f2-)
        if [ -n "$latest_report" ]; then
            log_info "最新报告文件: $latest_report"
            log_info "报告摘要:"
            echo "----------------------------------------"
            head -20 "$latest_report" 2>/dev/null || echo "无法读取报告文件"
            echo "----------------------------------------"
        fi
        
        return 0
    else
        local end_time=$(date +%s)
        local duration=$((end_time - start_time))
        log_error "依赖更新执行失败，耗时: ${duration}秒"
        return 1
    fi
}

# 显示帮助信息
show_help() {
    echo "依赖更新 Runner 启动脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help              显示帮助信息"
    echo "  -f, --force-refresh     强制刷新所有依赖（忽略已存在的依赖）"
    echo "  -b, --batch-size SIZE   设置批处理大小（默认: 20）"
    echo "  -c, --config FILE       指定配置文件路径（默认: 使用内置配置）"
    echo "  -p, --profile PROFILE   指定 Spring Profile（默认: $PROFILE）"
    echo "  --dry-run              只检查环境，不执行更新"
    echo ""
    echo "默认配置说明:"
    echo "  - 配置文件: 使用内置的 classpath:dependency-update-config.json"
    echo "  - 日志目录: ./logs/dependency-update"
    echo "  - 批处理大小: 20"
    echo "  - 强制刷新: false（增量更新模式）"
    echo "  - 处理缺失依赖: true"
    echo "  - 缺失依赖批次大小: 100"
    echo "  - 冷启动版本数量: 5"
    echo ""
    echo "示例:"
    echo "  $0                      # 使用所有默认配置执行"
    echo "  $0 -f                   # 强制刷新所有依赖"
    echo "  $0 -b 50                # 设置批处理大小为 50"
    echo "  $0 -f -b 10             # 强制刷新 + 小批处理"
    echo "  $0 -c custom-config.json # 使用自定义配置文件"
    echo ""
    echo "注意:"
    echo "  - 请确保已编译项目: mvn clean package -DskipTests"
    echo "  - 内置配置包含常用依赖示例，可直接运行测试"
    echo "  - 自定义配置文件格式请参考内置的 dependency-update-config.json"
    echo "  - 执行日志和报告将保存在 ./logs/dependency-update 目录下"
    echo ""
}

# 主函数
main() {
    # 默认参数
    local force_refresh=false
    local batch_size=20
    local dry_run=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -f|--force-refresh)
                force_refresh=true
                shift
                ;;
            -b|--batch-size)
                batch_size="$2"
                shift 2
                ;;
            -c|--config)
                CONFIG_FILE="$2"
                shift 2
                ;;
            -p|--profile)
                PROFILE="$2"
                shift 2
                ;;
            --dry-run)
                dry_run=true
                shift
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 检查环境
    check_dependencies
    
    if [ "$dry_run" = true ]; then
        log_info "Dry run 模式，环境检查完成，退出"
        exit 0
    fi
    
    # 执行依赖更新
    run_dependency_update "$force_refresh" "$batch_size"
}

# 如果脚本被直接执行
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi 