package com.xiaohongshu.codewiz.complete.runner.model;

import java.util.ArrayList;
import java.util.List;

import com.xiaohongshu.codewiz.complete.model.dependency.Dependency;

import lombok.Data;

/**
 * 依赖组处理结果
 */
@Data
public class GroupProcessResult {
    private final String groupName;
    private final List<Dependency> successfulDependencies;
    private final List<Dependency> failedDependencies;
    private final int totalProcessed;
    private final boolean success;
    private final String errorMessage;

    public GroupProcessResult(String groupName, List<Dependency> successfulDependencies,
                              List<Dependency> failedDependencies, int totalProcessed) {
        this.groupName = groupName;
        this.successfulDependencies = successfulDependencies != null ? successfulDependencies : new ArrayList<>();
        this.failedDependencies = failedDependencies != null ? failedDependencies : new ArrayList<>();
        this.totalProcessed = totalProcessed;
        this.success = !this.successfulDependencies.isEmpty();
        this.errorMessage = null;
    }

    public GroupProcessResult(String groupName, String errorMessage) {
        this.groupName = groupName;
        this.successfulDependencies = new ArrayList<>();
        this.failedDependencies = new ArrayList<>();
        this.totalProcessed = 0;
        this.success = false;
        this.errorMessage = errorMessage;
    }

    public int getSuccessCount() {
        return successfulDependencies.size();
    }

    public int getFailedCount() {
        return failedDependencies.size();
    }
} 