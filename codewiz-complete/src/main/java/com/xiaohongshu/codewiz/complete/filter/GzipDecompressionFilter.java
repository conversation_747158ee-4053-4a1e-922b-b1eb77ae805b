package com.xiaohongshu.codewiz.complete.filter;

import com.dianping.cat.Cat;
import com.dianping.cat.metrics.prometheus.builder.GaugeBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.zip.GZIPInputStream;

/**
 * 智能gzip解压Filter - 专门用于日志上报接口
 * 支持自动检测gzip格式，同时兼容gzip和非gzip请求
 */
@Slf4j
@Component
@Order(1)
public class GzipDecompressionFilter implements Filter {

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        
        // 只处理日志上报接口
        if (!isLogUploadRequest(httpRequest)) {
            chain.doFilter(request, response);
            return;
        }
        
        try {
            // 读取原始数据
            byte[] originalData = readRequestBody(httpRequest.getInputStream());
            
            // 智能检测是否为gzip数据
            boolean isActuallyGzip = isGzipData(originalData);
            boolean hasGzipHeader = isGzipRequest(httpRequest);
            
            if (isActuallyGzip) {
                // 数据确实是gzip格式，进行解压
                handleGzipRequest(httpRequest, response, chain, originalData, hasGzipHeader);
            } else {
                // 数据不是gzip格式
                if (hasGzipHeader) {
                    // 客户端声明了gzip但数据不是gzip格式，记录警告但继续处理
                    log.warn("客户端设置Content-Encoding: gzip但数据不是gzip格式，按非压缩数据处理");
                }
                // 直接按非压缩数据处理
                handleNonGzipRequest(httpRequest, response, chain, originalData);
            }
            
        } catch (Exception e) {
            log.error("请求数据处理失败: {}", e.getMessage(), e);
            throw new ServletException("请求数据处理失败", e);
        }
    }
    
    /**
     * 处理gzip压缩的请求
     */
    private void handleGzipRequest(HttpServletRequest httpRequest, ServletResponse response, 
                                 FilterChain chain, byte[] compressedData, boolean hasGzipHeader) 
            throws IOException, ServletException {
        
        String originalContentLength = httpRequest.getHeader("Content-Length");
        long compressedSize = originalContentLength != null ? Long.parseLong(originalContentLength) : compressedData.length;
        
        // 解压gzip数据
        byte[] decompressedData = decompressGzip(compressedData);
        long decompressedSize = decompressedData.length;
        
        // 计算压缩率
        double compressionRatio = compressedSize > 0 ? (1.0 - (double) compressedSize / decompressedSize) * 100 : 0;
        
        // 使用Cat记录gzip压缩率metrics
        GaugeBuilder compressionGauge = Cat.gauge("gzip_compression_ratio", "Gzip压缩率统计")
                .addTag("endpoint", "/api/v1/logs/batch")
                .addTag("has_header", String.valueOf(hasGzipHeader));
        compressionGauge.setValue(compressionRatio);
        
        log.debug("gzip数据解压成功，压缩前: {} bytes, 解压后: {} bytes, 压缩率: {:.2f}%", 
                compressedSize, decompressedSize, compressionRatio);
        
        // 创建包装请求
        ServletRequest wrappedRequest = new GzipRequestWrapper(httpRequest, decompressedData, true);
        chain.doFilter(wrappedRequest, response);
    }
    
    /**
     * 处理非gzip的请求
     */
    private void handleNonGzipRequest(HttpServletRequest httpRequest, ServletResponse response, 
                                    FilterChain chain, byte[] originalData) 
            throws IOException, ServletException {
        
        log.debug("处理非gzip数据，大小: {} bytes", originalData.length);
        
        // 创建包装请求
        ServletRequest wrappedRequest = new GzipRequestWrapper(httpRequest, originalData, false);
        chain.doFilter(wrappedRequest, response);
    }
    
    /**
     * 读取请求体数据
     */
    private byte[] readRequestBody(InputStream inputStream) throws IOException {
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            byte[] buffer = new byte[1024];
            int length;
            while ((length = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, length);
            }
            return outputStream.toByteArray();
        }
    }
    
    /**
     * 检测数据是否为gzip格式
     * gzip文件以魔数0x1f 0x8b开头
     */
    private boolean isGzipData(byte[] data) {
        if (data == null || data.length < 2) {
            return false;
        }
        // gzip魔数：0x1f 0x8b
        return (data[0] & 0xff) == 0x1f && (data[1] & 0xff) == 0x8b;
    }
    
    private boolean isLogUploadRequest(HttpServletRequest request) {
        return request.getRequestURI().startsWith("/api/v1/logs");
    }
    
    private boolean isGzipRequest(HttpServletRequest request) {
        return "gzip".equalsIgnoreCase(request.getHeader("Content-Encoding"));
    }
    
    private byte[] decompressGzip(byte[] compressedData) throws IOException {
        try (ByteArrayInputStream inputStream = new ByteArrayInputStream(compressedData);
             GZIPInputStream gzipStream = new GZIPInputStream(inputStream);
             ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            
            byte[] buffer = new byte[1024];
            int length;
            while ((length = gzipStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, length);
            }
            return outputStream.toByteArray();
        }
    }
    
    /**
     * 增强的请求包装器，支持gzip和非gzip数据
     */
    private static class GzipRequestWrapper extends HttpServletRequestWrapper {
        private final byte[] data;
        private final boolean wasGzipped;
        
        public GzipRequestWrapper(HttpServletRequest request, byte[] data, boolean wasGzipped) {
            super(request);
            this.data = data;
            this.wasGzipped = wasGzipped;
        }
        
        @Override
        public ServletInputStream getInputStream() throws IOException {
            return new ServletInputStream() {
                private final ByteArrayInputStream stream = new ByteArrayInputStream(data);
                
                @Override
                public int read() throws IOException {
                    return stream.read();
                }
                
                @Override
                public boolean isFinished() {
                    return stream.available() == 0;
                }
                
                @Override
                public boolean isReady() {
                    return true;
                }
                
                @Override
                public void setReadListener(ReadListener readListener) {
                    // 不需要实现
                }
            };
        }
        
        @Override
        public String getHeader(String name) {
            // 添加处理信息的自定义header
            if ("X-Decompressed-Size".equalsIgnoreCase(name)) {
                return String.valueOf(data.length);
            }
            if ("X-Was-Gzipped".equalsIgnoreCase(name)) {
                return String.valueOf(wasGzipped);
            }
            // 如果原始数据被解压，移除Content-Encoding头
            if (wasGzipped && "Content-Encoding".equalsIgnoreCase(name)) {
                return null;
            }
            // 保留其他原始头部
            return super.getHeader(name);
        }
        
        @Override
        public int getContentLength() {
            return data.length;
        }
        
        @Override
        public long getContentLengthLong() {
            return data.length;
        }
    }
} 