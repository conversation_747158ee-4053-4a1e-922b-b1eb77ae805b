package com.xiaohongshu.codewiz.complete.runner.builder;

import com.xiaohongshu.codewiz.complete.model.lang.LanguageType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

/**
 * 知识构建器工厂
 * 根据编程语言选择合适的builder实现
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PackageContextBuilderFactory {
    
    private final List<PackageContextBuilder> builders;
    
    /**
     * 根据语言类型获取合适的builder
     * @param languageType 编程语言类型
     * @return builder实现
     */
    public Optional<PackageContextBuilder> getBuilder(LanguageType languageType) {
        log.debug("寻找支持语言类型 {} 的builder", languageType.getDisplayName());
        
        for (PackageContextBuilder builder : builders) {
            if (builder.supports(languageType)) {
                log.debug("找到支持语言类型 {} 的builder: {}", languageType.getDisplayName(), builder.getClass().getSimpleName());
                return Optional.of(builder);
            }
        }
        
        log.warn("未找到支持语言类型 {} 的builder", languageType.getDisplayName());
        return Optional.empty();
    }
} 