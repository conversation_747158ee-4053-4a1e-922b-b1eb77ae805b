package com.xiaohongshu.codewiz.complete.controller;

import java.sql.Connection;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;

import javax.annotation.Resource;
import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Event;
import com.dianping.cat.message.Transaction;
import com.xiaohongshu.xray.logging.LogTags;
import com.xiaohongshu.xray.logging.MDCUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2025/2/25 17:55
 */
@Slf4j
@RestController
@RequestMapping("/api/health")
public class Health {
    @Resource
    private ExecutorService codewizExecutor;
    @Value("${test.key:}")
    private String testKey;
    @Value("${test.key1:}")
    private String testKey1;
    @Resource
    private DataSource dataSource;

    @GetMapping
    public String health() {
        Transaction t = Cat.newTransaction("URL", "pageName");

        try {
            Cat.logEvent("URL.Server", "serverIp", Event.SUCCESS, "ip=${serverIp}");
            Connection connection = dataSource.getConnection();
            log.info("数据源连接获取成功");
        } catch (Exception e) {
            t.setStatus(e);
            Cat.logError(e);
        } finally {
            t.complete();
        }
        MDCUtils.put("codewiz", "complete");
        log.info("health");
        log.info(LogTags.of("tag", "health"), "aa");
        log.error("health error");
        CompletableFuture.runAsync(() -> {
            log.info(LogTags.of("executor", "codewizExecutor"), "codewizExecutor");
        }, codewizExecutor);
        log.info("testKey1: {}", testKey1);
        return testKey;
    }

    @GetMapping("/log/info")
    public String log1() {
        log.info("log1");
        return "log1";
    }

    @GetMapping("/log/err")
    public String log2() {
        log.error("log2");
        return "log1";
    }

    @GetMapping("/exception")
    public String exception() {
        log.info("trigger exception");
        throw new RuntimeException("trigger exception");
    }
}
