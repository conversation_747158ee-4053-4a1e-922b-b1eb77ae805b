package com.xiaohongshu.codewiz.complete.entity;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 缺失依赖记录表实体
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("t_miss_dependency")
public class MissingDependencyEntity {

    @TableId(type = IdType.AUTO)
    private Long id;

    @TableField("language")
    private String language;

    @TableField("dependency_type")
    private String dependencyType;

    @TableField("namespace")
    private String namespace;

    @TableField("name")
    private String name;

    @TableField("version")
    private String version;

    @TableField("snapshot_version")
    private String snapshotVersion;

    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    @TableField("source_trace_id")
    private String sourceTraceId;

    @TableField("skip")
    private boolean skip;

    @TableField("skip_reason")
    private String skipReason;
} 