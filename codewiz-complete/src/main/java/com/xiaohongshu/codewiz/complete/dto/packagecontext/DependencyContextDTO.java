package com.xiaohongshu.codewiz.complete.dto.packagecontext;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 单个依赖的查询结果
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DependencyContextDTO {

    /**
     * 依赖命名空间/组织标识 (如Java的groupId、NPM的@scope、Go的domain等)
     */
    private String namespace;

    /**
     * 依赖名称 (如Java的artifactId、Python的package名、NPM的package名等)
     */
    private String name;

    /**
     * 依赖版本
     */
    private String version;

    /**
     * 编程语言类型
     */
    private String language;

    /**
     * 依赖类型
     */
    private String dependencyType;

    /**
     * 按package组织的查询结果
     */
    private List<PackageContextDTO> packages;

    /**
     * 该依赖是否查询成功
     */
    private Boolean success;

    /**
     * 错误信息（该依赖查询失败时）
     */
    private String errorMessage;
} 