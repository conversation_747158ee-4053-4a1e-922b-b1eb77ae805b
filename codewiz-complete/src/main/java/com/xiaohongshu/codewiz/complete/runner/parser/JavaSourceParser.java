package com.xiaohongshu.codewiz.complete.runner.parser;

import com.github.javaparser.JavaParser;
import com.github.javaparser.ParseResult;
import com.github.javaparser.Problem;
import com.github.javaparser.ast.CompilationUnit;
import com.github.javaparser.ast.ImportDeclaration;
import com.github.javaparser.ast.Modifier;
import com.github.javaparser.ast.NodeList;
import com.github.javaparser.ast.PackageDeclaration;
import com.github.javaparser.ast.body.*;
import com.github.javaparser.ast.expr.AnnotationExpr;
import com.github.javaparser.ast.expr.NormalAnnotationExpr;
import com.github.javaparser.ast.expr.SingleMemberAnnotationExpr;
import com.github.javaparser.ast.type.*;
import com.github.javaparser.javadoc.Javadoc;
import com.xiaohongshu.codewiz.complete.model.lang.SourceFile;
import com.xiaohongshu.codewiz.core.annotation.LogExecutionTime;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 基于JavaParser的Java源码解析器
 * 使用JavaParser SDK深度解析Java源码，提取完整的类、方法、字段等结构信息
 */
@Slf4j
@Component
public class JavaSourceParser {
    /**
     * 解析Java源码文件
     */
    @LogExecutionTime
    public static SourceFile parseJavaSource(String fileName, String content) {
        JavaParser javaParser = new JavaParser();
        try {
            // 对特定问题文件进行预检查
            if (content == null || content.trim().isEmpty()) {
                log.warn("Java源码内容为空: {}", fileName);
                return null;
            }
            
            ParseResult<CompilationUnit> parseResult = javaParser.parse(content);
            
            if (!parseResult.isSuccessful()) {
                String error = parseResult.getProblems().stream()
                        .map(Problem::getMessage)
                        .collect(Collectors.joining("; "));
                log.warn("解析Java源码时遇到问题: {}, 错误: {}", fileName, error);
                return null;
            }
            
            Optional<CompilationUnit> cuOpt = parseResult.getResult();
            if (cuOpt.isEmpty()) {
                log.error("无法解析Java源码: {}", fileName);
                return null;
            }
            
            CompilationUnit cu = cuOpt.get();
            
            
            // 分别尝试解析各个部分，如果解析失败则抛出异常
            List<SourceFile.ClassInfo> classes = parseClasses(cu);
            String packageName = parsePackageName(cu);
            List<String> imports = parseImports(cu);
            
            return SourceFile.builder()
                    .fileName(fileName)
                    .content(content)
                    .packageName(packageName)
                    .imports(imports)
                    .classes(classes)
                    .build();
                    
        } catch (Exception e) {
            log.error("解析Java源码失败: {}", fileName, e);
            return null;
        }
    }
    
    /**
     * 解析包名
     */
    private static String parsePackageName(CompilationUnit cu) {
        return cu.getPackageDeclaration()
                .map(PackageDeclaration::getNameAsString)
                .orElse(null);
    }
    
    /**
     * 解析导入语句
     */
    private static List<String> parseImports(CompilationUnit cu) {
        return cu.getImports().stream()
                .map(ImportDeclaration::getNameAsString)
                .collect(Collectors.toList());
    }
    
    /**
     * 解析编译单元中的所有类
     */
    private static List<SourceFile.ClassInfo> parseClasses(CompilationUnit cu) {
        List<SourceFile.ClassInfo> classes = new ArrayList<>();
        
        // 解析顶级类、接口、枚举、注解
        cu.getTypes().forEach(typeDeclaration -> {
            SourceFile.ClassInfo classInfo = parseTypeDeclaration(typeDeclaration);
            if (classInfo != null) {
                classes.add(classInfo);
            }
        });
        
        return classes;
    }
    
    /**
     * 解析类型声明（类、接口、枚举、注解）
     */
    private static SourceFile.ClassInfo parseTypeDeclaration(TypeDeclaration<?> typeDeclaration) {
        try {
            String className = typeDeclaration.getNameAsString();
            String classType = getClassType(typeDeclaration);
            String visibility = getVisibility(typeDeclaration.getModifiers());
            List<String> modifiers = getModifiers(typeDeclaration.getModifiers());
            List<String> genericTypes = parseGenericTypes(typeDeclaration);
            List<SourceFile.AnnotationInfo> annotations = parseAnnotations(typeDeclaration.getAnnotations());
            String javadoc = parseJavadoc(typeDeclaration.getJavadoc());
            
            // 安全获取行号，避免位置信息异常
            int lineNumber = safeGetLineNumber(typeDeclaration, className);
            
            String extendsClass = null;
            List<String> implementsInterfaces = new ArrayList<>();
            
            // 处理类的继承和实现
            if (typeDeclaration instanceof ClassOrInterfaceDeclaration) {
                ClassOrInterfaceDeclaration classDecl = (ClassOrInterfaceDeclaration) typeDeclaration;
                
                // 获取继承的类
                if (!classDecl.getExtendedTypes().isEmpty()) {
                    try {
                        extendsClass = classDecl.getExtendedTypes().get(0).getNameAsString();
                    } catch (Exception e) {
                        log.warn("获取类继承信息失败: {}", className, e);
                    }
                }
                
                // 获取实现的接口
                try {
                    implementsInterfaces = classDecl.getImplementedTypes().stream()
                            .map(Type::asString)
                            .collect(Collectors.toList());
                } catch (Exception e) {
                    log.warn("获取类实现接口信息失败: {}", className, e);
                    implementsInterfaces = new ArrayList<>();
                }
            }
            
            // 解析字段
            List<SourceFile.FieldInfo> fields = parseFields(typeDeclaration);
            
            // 解析方法
            List<SourceFile.MethodInfo> methods = parseMethods(typeDeclaration);
            
            // 解析内部类
            List<SourceFile.ClassInfo> innerClasses = parseInnerClasses(typeDeclaration);
            
            // 解析enum常量（仅针对enum类型）
            List<SourceFile.EnumConstantInfo> enumConstants = new ArrayList<>();
            if (typeDeclaration instanceof EnumDeclaration) {
                enumConstants = parseEnumConstants((EnumDeclaration) typeDeclaration);
            }
            
            return SourceFile.ClassInfo.builder()
                    .className(className)
                    .classType(classType)
                    .visibility(visibility)
                    .modifiers(modifiers)
                    .extendsClass(extendsClass)
                    .implementsInterfaces(implementsInterfaces)
                    .genericTypes(genericTypes)
                    .annotations(annotations)
                    .fields(fields)
                    .methods(methods)
                    .innerClasses(innerClasses)
                    .enumConstants(enumConstants)
                    .javadoc(javadoc)
                    .lineNumber(lineNumber)
                    .build();
                    
        } catch (Exception e) {
            String className = "unknown";
            try {
                className = typeDeclaration.getNameAsString();
            } catch (Exception ignored) {
                // 如果连类名都获取不到，使用默认值
            }
            log.warn("解析类型声明失败: {}", className, e);
            return null;
        }
    }
    
    /**
     * 安全获取行号，避免位置信息异常
     */
    private static int safeGetLineNumber(TypeDeclaration<?> typeDeclaration, String className) {
        try {
            return typeDeclaration.getBegin()
                    .map(pos -> {
                        int line = pos.line;
                        // 检查行号是否异常
                        if (line < 0 || line == Integer.MAX_VALUE) {
                            log.warn("类 {} 的行号异常: {}", className, line);
                            return 0;
                        }
                        return line;
                    })
                    .orElse(0);
        } catch (Exception e) {
            log.warn("获取类 {} 行号失败", className, e);
            return 0;
        }
    }
    
    /**
     * 安全获取方法行号，避免位置信息异常
     */
    private static int safeGetMethodLineNumber(MethodDeclaration methodDeclaration, String methodName) {
        try {
            return methodDeclaration.getBegin()
                    .map(pos -> {
                        int line = pos.line;
                        // 检查行号是否异常
                        if (line < 0 || line == Integer.MAX_VALUE) {
                            log.warn("方法 {} 的行号异常: {}", methodName, line);
                            return 0;
                        }
                        return line;
                    })
                    .orElse(0);
        } catch (Exception e) {
            log.warn("获取方法 {} 行号失败", methodName, e);
            return 0;
        }
    }
    
    /**
     * 安全获取构造函数行号，避免位置信息异常
     */
    private static int safeGetMethodLineNumber(ConstructorDeclaration constructorDeclaration, String methodName) {
        try {
            return constructorDeclaration.getBegin()
                    .map(pos -> {
                        int line = pos.line;
                        // 检查行号是否异常
                        if (line < 0 || line == Integer.MAX_VALUE) {
                            log.warn("构造函数 {} 的行号异常: {}", methodName, line);
                            return 0;
                        }
                        return line;
                    })
                    .orElse(0);
        } catch (Exception e) {
            log.warn("获取构造函数 {} 行号失败", methodName, e);
            return 0;
        }
    }
    
    /**
     * 获取类型（class, interface, enum, annotation）
     */
    private static String getClassType(TypeDeclaration<?> typeDeclaration) {
        if (typeDeclaration instanceof ClassOrInterfaceDeclaration) {
            ClassOrInterfaceDeclaration classDecl = (ClassOrInterfaceDeclaration) typeDeclaration;
            return classDecl.isInterface() ? "interface" : "class";
        } else if (typeDeclaration instanceof EnumDeclaration) {
            return "enum";
        } else if (typeDeclaration instanceof AnnotationDeclaration) {
            return "annotation";
        }
        return "class";
    }
    
    /**
     * 获取可见性
     */
    private static String getVisibility(NodeList<Modifier> modifiers) {
        for (Modifier modifier : modifiers) {
            switch (modifier.getKeyword()) {
                case PUBLIC:
                    return "public";
                case PRIVATE:
                    return "private";
                case PROTECTED:
                    return "protected";
            }
        }
        return "package-private";
    }
    
    /**
     * 获取修饰符列表
     */
    private static List<String> getModifiers(NodeList<Modifier> modifiers) {
        return modifiers.stream()
                .map(modifier -> modifier.getKeyword().asString())
                .filter(mod -> !Arrays.asList("public", "private", "protected").contains(mod))
                .collect(Collectors.toList());
    }
    
    /**
     * 解析字段
     */
    private static List<SourceFile.FieldInfo> parseFields(TypeDeclaration<?> typeDeclaration) {
        List<SourceFile.FieldInfo> fields = new ArrayList<>();
        
        typeDeclaration.getFields().forEach(fieldDeclaration -> {
            String visibility = getVisibility(fieldDeclaration.getModifiers());
            List<String> modifiers = getModifiers(fieldDeclaration.getModifiers());
            String fieldType = fieldDeclaration.getCommonType().asString();
            List<SourceFile.AnnotationInfo> annotations = parseAnnotations(fieldDeclaration.getAnnotations());
            String javadoc = parseJavadoc(fieldDeclaration.getJavadoc());
            int lineNumber = fieldDeclaration.getBegin().map(pos -> pos.line).orElse(0);
            
            // 处理多个变量声明
            fieldDeclaration.getVariables().forEach(variableDeclarator -> {
                String fieldName = variableDeclarator.getNameAsString();
                String defaultValue = variableDeclarator.getInitializer()
                        .map(expression -> expression.toString())
                        .orElse(null);
                
                fields.add(SourceFile.FieldInfo.builder()
                        .fieldName(fieldName)
                        .fieldType(fieldType)
                        .visibility(visibility)
                        .modifiers(new ArrayList<>(modifiers))
                        .defaultValue(defaultValue)
                        .annotations(annotations)
                        .javadoc(javadoc)
                        .lineNumber(lineNumber)
                        .build());
            });
        });
        
        return fields;
    }
    
    /**
     * 解析方法
     */
    private static List<SourceFile.MethodInfo> parseMethods(TypeDeclaration<?> typeDeclaration) {
        List<SourceFile.MethodInfo> methods = new ArrayList<>();
        
        // 解析普通方法
        typeDeclaration.getMethods().forEach(methodDeclaration -> {
            String methodName = methodDeclaration.getNameAsString();
            String returnType = methodDeclaration.getType().asString();
            String visibility = getVisibility(methodDeclaration.getModifiers());
            List<String> modifiers = getModifiers(methodDeclaration.getModifiers());
            List<SourceFile.ParameterInfo> parameters = parseParameters(methodDeclaration.getParameters());
            List<String> exceptions = parseExceptions(methodDeclaration.getThrownExceptions());
            List<String> genericTypes = parseMethodGenericTypes(methodDeclaration);
            List<SourceFile.AnnotationInfo> annotations = parseAnnotations(methodDeclaration.getAnnotations());
            String javadoc = parseJavadoc(methodDeclaration.getJavadoc());
            int lineNumber = safeGetMethodLineNumber(methodDeclaration, methodName);
            String methodBody = extractMethodBodySummary(methodDeclaration);
            
            methods.add(SourceFile.MethodInfo.builder()
                    .methodName(methodName)
                    .returnType(returnType)
                    .visibility(visibility)
                    .modifiers(modifiers)
                    .parameters(parameters)
                    .exceptions(exceptions)
                    .genericTypes(genericTypes)
                    .annotations(annotations)
                    .isConstructor(false)
                    .javadoc(javadoc)
                    .lineNumber(lineNumber)
                    .methodBody(methodBody)
                    .build());
        });
        
        // 解析构造函数
        typeDeclaration.getConstructors().forEach(constructorDeclaration -> {
            String methodName = constructorDeclaration.getNameAsString();
            String visibility = getVisibility(constructorDeclaration.getModifiers());
            List<String> modifiers = getModifiers(constructorDeclaration.getModifiers());
            List<SourceFile.ParameterInfo> parameters = parseParameters(constructorDeclaration.getParameters());
            List<String> exceptions = parseExceptions(constructorDeclaration.getThrownExceptions());
            List<SourceFile.AnnotationInfo> annotations = parseAnnotations(constructorDeclaration.getAnnotations());
            String javadoc = parseJavadoc(constructorDeclaration.getJavadoc());
            int lineNumber = safeGetMethodLineNumber(constructorDeclaration, methodName);
            String methodBody = extractConstructorBodySummary(constructorDeclaration);
            
            methods.add(SourceFile.MethodInfo.builder()
                    .methodName(methodName)
                    .returnType(null)
                    .visibility(visibility)
                    .modifiers(modifiers)
                    .parameters(parameters)
                    .exceptions(exceptions)
                    .genericTypes(new ArrayList<>()) // 构造函数无泛型
                    .annotations(annotations)
                    .isConstructor(true)
                    .javadoc(javadoc)
                    .lineNumber(lineNumber)
                    .methodBody(methodBody)
                    .build());
        });
        
        return methods;
    }
    
    /**
     * 解析方法参数
     */
    private static List<SourceFile.ParameterInfo> parseParameters(NodeList<Parameter> parameters) {
        return parameters.stream()
                .map(parameter -> {
                    String parameterName = parameter.getNameAsString();
                    String parameterType = parameter.getType().asString();
                    List<String> modifiers = getModifiers(parameter.getModifiers());
                    List<SourceFile.AnnotationInfo> annotations = parseAnnotations(parameter.getAnnotations());
                    
                    return SourceFile.ParameterInfo.builder()
                            .parameterName(parameterName)
                            .parameterType(parameterType)
                            .modifiers(modifiers)
                            .annotations(annotations)
                            .build();
                })
                .collect(Collectors.toList());
    }
    
    /**
     * 提取方法体概要（用于AI分析）
     */
    private static String extractMethodBodySummary(MethodDeclaration methodDeclaration) {
        try {
            return methodDeclaration.getBody()
                    .map(body -> {
                        try {
                            String bodyStr = body.toString();
                            // 安全的字符串截取，避免索引越界
                            return safeSubstring(bodyStr, 500);
                        } catch (Exception e) {
                            log.warn("提取方法体时出错: {}", methodDeclaration.getNameAsString(), e);
                            return null;
                        }
                    })
                    .orElse(null);
        } catch (Exception e) {
            log.warn("提取方法体概要失败: {}", methodDeclaration.getNameAsString(), e);
            return null;
        }
    }
    
    /**
     * 提取构造函数体概要（用于AI分析）
     */
    private static String extractConstructorBodySummary(ConstructorDeclaration constructorDeclaration) {
        try {
            String bodyStr = constructorDeclaration.getBody().toString();
            // 安全的字符串截取，避免索引越界
            return safeSubstring(bodyStr, 500);
        } catch (Exception e) {
            log.warn("提取构造函数体概要失败: {}", constructorDeclaration.getNameAsString(), e);
            return null;
        }
    }
    
    /**
     * 安全的字符串截取方法
     * 避免IndexOutOfBoundsException和Integer.MAX_VALUE溢出问题
     */
    private static String safeSubstring(String str, int maxLength) {
        if (str == null) {
            return null;
        }
        
        try {
            int length = str.length();
            // 检查长度是否有效（避免Integer.MAX_VALUE等异常值）
            if (length <= 0 || length == Integer.MAX_VALUE) {
                log.warn("字符串长度异常: {}", length);
                return str; // 返回原字符串，不进行截取
            }
            
            if (length <= maxLength) {
                return str;
            }
            
            // 安全截取，确保索引不会越界
            int endIndex = Math.min(maxLength, length);
            if (endIndex < 0 || endIndex > length) {
                log.warn("计算的截取索引异常: endIndex={}, length={}", endIndex, length);
                return str; // 返回原字符串
            }
            
            return str.substring(0, endIndex) + "...";
            
        } catch (StringIndexOutOfBoundsException e) {
            log.error("字符串截取索引越界: str.length()={}, maxLength={}", 
                str.length(), maxLength, e);
            return str; // 发生异常时返回原字符串
        } catch (Exception e) {
            log.error("字符串截取时发生未知异常", e);
            return str; // 发生异常时返回原字符串
        }
    }
    
    /**
     * 解析异常声明
     */
    private static List<String> parseExceptions(NodeList<ReferenceType> thrownExceptions) {
        return thrownExceptions.stream()
                .map(Type::asString)
                .collect(Collectors.toList());
    }
    
    /**
     * 解析内部类
     */
    private static List<SourceFile.ClassInfo> parseInnerClasses(TypeDeclaration<?> typeDeclaration) {
        List<SourceFile.ClassInfo> innerClasses = new ArrayList<>();
        
        // 获取所有成员类型（内部类、内部接口等）
        typeDeclaration.getMembers().forEach(bodyDeclaration -> {
            if (bodyDeclaration instanceof TypeDeclaration) {
                TypeDeclaration<?> innerTypeDeclaration = (TypeDeclaration<?>) bodyDeclaration;
                SourceFile.ClassInfo innerClassInfo = parseTypeDeclaration(innerTypeDeclaration);
                if (innerClassInfo != null) {
                    innerClasses.add(innerClassInfo);
                }
            }
        });
        
        return innerClasses;
    }
    
    /**
     * 解析泛型类型参数
     */
    private static List<String> parseGenericTypes(TypeDeclaration<?> typeDeclaration) {
        List<String> genericTypes = new ArrayList<>();
        
        if (typeDeclaration instanceof ClassOrInterfaceDeclaration) {
            ClassOrInterfaceDeclaration classDecl = (ClassOrInterfaceDeclaration) typeDeclaration;
            if (classDecl.getTypeParameters().isNonEmpty()) {
                genericTypes = classDecl.getTypeParameters().stream()
                        .map(TypeParameter::getNameAsString)
                        .collect(Collectors.toList());
            }
        }
        
        return genericTypes;
    }
    
    /**
     * 解析方法泛型类型参数
     */
    private static List<String> parseMethodGenericTypes(MethodDeclaration methodDeclaration) {
        return methodDeclaration.getTypeParameters().stream()
                .map(TypeParameter::getNameAsString)
                .collect(Collectors.toList());
    }
    
    /**
     * 解析注解信息
     */
    private static List<SourceFile.AnnotationInfo> parseAnnotations(NodeList<AnnotationExpr> annotations) {
        return annotations.stream()
                .map(JavaSourceParser::parseAnnotation)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
    
    /**
     * 解析单个注解
     */
    private static SourceFile.AnnotationInfo parseAnnotation(AnnotationExpr annotationExpr) {
        try {
            String annotationName = annotationExpr.getNameAsString();
            List<SourceFile.AnnotationInfo.AnnotationAttribute> attributes = new ArrayList<>();
            
            if (annotationExpr instanceof NormalAnnotationExpr) {
                NormalAnnotationExpr normalAnnotation = (NormalAnnotationExpr) annotationExpr;
                attributes = normalAnnotation.getPairs().stream()
                        .map(pair -> SourceFile.AnnotationInfo.AnnotationAttribute.builder()
                                .name(pair.getNameAsString())
                                .value(pair.getValue().toString())
                                .build())
                        .collect(Collectors.toList());
            } else if (annotationExpr instanceof SingleMemberAnnotationExpr) {
                SingleMemberAnnotationExpr singleMemberAnnotation = (SingleMemberAnnotationExpr) annotationExpr;
                attributes.add(SourceFile.AnnotationInfo.AnnotationAttribute.builder()
                        .name("value")
                        .value(singleMemberAnnotation.getMemberValue().toString())
                        .build());
            }
            
            return SourceFile.AnnotationInfo.builder()
                    .annotationName(annotationName)
                    .attributes(attributes)
                    .build();
        } catch (Exception e) {
            log.warn("解析注解失败: {}", annotationExpr.toString(), e);
            return null;
        }
    }
    
    /**
     * 解析JavaDoc注释
     */
    private static String parseJavadoc(Optional<Javadoc> javadocOpt) {
        return javadocOpt
                .map(Javadoc::getDescription)
                .map(description -> description.toText())
                .orElse(null);
    }
    
    /**
     * 解析enum常量
     */
    private static List<SourceFile.EnumConstantInfo> parseEnumConstants(EnumDeclaration enumDeclaration) {
        return enumDeclaration.getEntries().stream()
                .map(JavaSourceParser::parseEnumConstant)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
    
    /**
     * 解析单个enum常量
     */
    private static SourceFile.EnumConstantInfo parseEnumConstant(EnumConstantDeclaration enumConstant) {
        try {
            String constantName = enumConstant.getNameAsString();
            
            // 解析构造函数参数
            List<String> arguments = enumConstant.getArguments().stream()
                    .map(expression -> expression.toString())
                    .collect(Collectors.toList());
            
            // 解析注解信息
            List<SourceFile.AnnotationInfo> annotations = parseAnnotations(enumConstant.getAnnotations());
            
            // 解析JavaDoc注释
            String javadoc = parseJavadoc(enumConstant.getJavadoc());
            
            // 获取行号
            int lineNumber = enumConstant.getBegin().map(pos -> pos.line).orElse(0);
            
            return SourceFile.EnumConstantInfo.builder()
                    .constantName(constantName)
                    .arguments(arguments)
                    .annotations(annotations)
                    .javadoc(javadoc)
                    .lineNumber(lineNumber)
                    .build();
                    
        } catch (Exception e) {
            String constantName = "unknown";
            try {
                constantName = enumConstant.getNameAsString();
            } catch (Exception ignored) {
                // 如果连常量名都获取不到，使用默认值
            }
            log.warn("解析enum常量失败: {}", constantName, e);
            return null;
        }
    }
} 