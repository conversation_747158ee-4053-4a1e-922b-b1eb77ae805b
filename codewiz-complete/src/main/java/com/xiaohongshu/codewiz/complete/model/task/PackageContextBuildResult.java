package com.xiaohongshu.codewiz.complete.model.task;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.util.List;
import java.util.ArrayList;

import com.xiaohongshu.codewiz.complete.model.dpc.PackageContextItem;

/**
 * 包上下文构建结果
 * 封装构建过程中的成功结果和错误信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PackageContextBuildResult {
    
    /**
     * 成功构建的包上下文项列表
     */
    private List<PackageContextItem> successItems;
    
    /**
     * 构建过程中的错误信息列表
     */
    private List<BuildError> errors;
    
    /**
     * 是否有任何成功结果
     */
    private Boolean hasSuccessItems;
    
    /**
     * 是否有错误
     */
    private Boolean hasErrors;
    
    /**
     * 依赖键值
     */
    private String dependencyKey;
    
    /**
     * 构建错误信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BuildError {
        /**
         * 错误类型
         */
        private ErrorType errorType;
        
        /**
         * 错误消息
         */
        private String errorMessage;
        
        /**
         * 错误发生的上下文（如文件名、包名等）
         */
        private String context;
        
        /**
         * 异常堆栈信息（可选）
         */
        private String stackTrace;
    }
    
    /**
     * 错误类型枚举
     */
    public enum ErrorType {
        JAR_PARSE_ERROR("JAR包解析失败"),
        SOURCE_FILE_PARSE_ERROR("源文件解析失败"),
        FILE_READ_ERROR("文件读取失败"),
        CLASS_SIGNATURE_GENERATION_ERROR("类签名生成失败"),
        PACKAGE_INFO_BUILD_ERROR("包信息构建失败"),
        UNKNOWN_ERROR("未知错误");
        
        private final String description;
        
        ErrorType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 创建成功结果
     */
    public static PackageContextBuildResult createSuccess(String dependencyKey, List<PackageContextItem> items) {
        List<PackageContextItem> safeItems = items != null ? items : new ArrayList<>();
        return PackageContextBuildResult.builder()
                .dependencyKey(dependencyKey)
                .successItems(safeItems)
                .errors(new ArrayList<>())
                .hasSuccessItems(!safeItems.isEmpty())
                .hasErrors(false)
                .build();
    }
    
    /**
     * 创建失败结果
     */
    public static PackageContextBuildResult createFailure(String dependencyKey, List<BuildError> errors) {
        return PackageContextBuildResult.builder()
                .dependencyKey(dependencyKey)
                .successItems(new ArrayList<>())
                .errors(errors != null ? errors : new ArrayList<>())
                .hasSuccessItems(false)
                .hasErrors(true)
                .build();
    }
    
    /**
     * 创建混合结果（部分成功，部分失败）
     */
    public static PackageContextBuildResult createMixed(String dependencyKey, 
                                                       List<PackageContextItem> items, 
                                                       List<BuildError> errors) {
        return PackageContextBuildResult.builder()
                .dependencyKey(dependencyKey)
                .successItems(items != null ? items : new ArrayList<>())
                .errors(errors != null ? errors : new ArrayList<>())
                .hasSuccessItems(items != null && !items.isEmpty())
                .hasErrors(errors != null && !errors.isEmpty())
                .build();
    }
    
    /**
     * 添加错误
     */
    public void addError(ErrorType errorType, String errorMessage, String context) {
        if (this.errors == null) {
            this.errors = new ArrayList<>();
        }
        
        BuildError error = BuildError.builder()
                .errorType(errorType)
                .errorMessage(errorMessage)
                .context(context)
                .build();
                
        this.errors.add(error);
        this.hasErrors = true;
    }
    
    /**
     * 添加错误（带异常信息）
     */
    public void addError(ErrorType errorType, String errorMessage, String context, Throwable throwable) {
        if (this.errors == null) {
            this.errors = new ArrayList<>();
        }
        
        BuildError error = BuildError.builder()
                .errorType(errorType)
                .errorMessage(errorMessage)
                .context(context)
                .stackTrace(throwable != null ? throwable.toString() : null)
                .build();
                
        this.errors.add(error);
        this.hasErrors = true;
    }
    
    /**
     * 获取所有错误的摘要信息
     */
    public String getErrorSummary() {
        if (errors == null || errors.isEmpty()) {
            return "无错误";
        }
        
        StringBuilder summary = new StringBuilder();
        summary.append("共").append(errors.size()).append("个错误：\n");
        
        for (int i = 0; i < errors.size(); i++) {
            BuildError error = errors.get(i);
            summary.append(i + 1).append(". ")
                   .append(error.getErrorType().getDescription())
                   .append("：").append(error.getErrorMessage());
            if (error.getContext() != null) {
                summary.append(" (").append(error.getContext()).append(")");
            }
            if (i < errors.size() - 1) {
                summary.append("\n");
            }
        }
        
        return summary.toString();
    }
} 