package com.xiaohongshu.codewiz.complete.runner.fetcher;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import com.xiaohongshu.codewiz.complete.model.task.FailureInfo;
import com.xiaohongshu.codewiz.complete.model.task.FailureType;
import com.xiaohongshu.codewiz.complete.model.dependency.Dependency;

import lombok.Data;

/**
 * Fetch上下文，用于存储获取制品时的失败信息
 */
@Data
public class FetchContext {
    /**
     * 失败信息映射，key为dependency的key，value为失败详情
     */
    private final Map<String, FailureInfo> failures = new ConcurrentHashMap<>();
    
    /**
     * 失败的依赖对象映射，key为dependency的key，value为依赖对象
     */
    private final Map<String, Dependency> failedDependencies = new ConcurrentHashMap<>();
    
    /**
     * 添加失败信息
     * @param dependencyKey 依赖的key
     * @param type 失败类型
     * @param reason 失败原因
     */
    public void addFailure(String dependencyKey, FailureType type, String reason) {
        failures.put(dependencyKey, new FailureInfo(type, reason));
    }
    
    /**
     * 添加失败信息和详细错误
     * @param dependencyKey 依赖的key
     * @param type 失败类型
     * @param reason 失败原因
     * @param error 详细错误
     */
    public void addFailure(String dependencyKey, FailureType type, String reason, Exception error) {
        failures.put(dependencyKey, new FailureInfo(type, reason, error));
    }

    /**
     * 添加失败的依赖对象
     * @param dependency 失败的依赖对象
     * @param type 失败类型
     * @param reason 失败原因
     */
    public void addFailure(Dependency dependency, FailureType type, String reason) {
        String dependencyKey = dependency.getDependencyKey();
        failures.put(dependencyKey, new FailureInfo(type, reason));
        failedDependencies.put(dependencyKey, dependency);
    }

    /**
     * 添加失败的依赖对象和详细错误
     * @param dependency 失败的依赖对象
     * @param type 失败类型
     * @param reason 失败原因
     * @param error 详细错误
     */
    public void addFailure(Dependency dependency, FailureType type, String reason, Exception error) {
        String dependencyKey = dependency.getDependencyKey();
        failures.put(dependencyKey, new FailureInfo(type, reason, error));
        failedDependencies.put(dependencyKey, dependency);
    }

    /**
     * 兼容旧版本的添加失败信息方法
     * @param dependencyKey 依赖的key
     * @param reason 失败原因
     */
    public void addFailure(String dependencyKey, String reason) {
        failures.put(dependencyKey, new FailureInfo(FailureType.OTHER, reason));
    }

    /**
     * 兼容旧版本的添加失败信息和详细错误方法
     * @param dependencyKey 依赖的key
     * @param reason 失败原因
     * @param error 详细错误
     */
    public void addFailure(String dependencyKey, String reason, Exception error) {
        failures.put(dependencyKey, new FailureInfo(FailureType.OTHER, reason, error));
    }
    
    /**
     * 获取失败信息
     * @param dependencyKey 依赖的key
     * @return 失败信息，如果没有失败则返回null
     */
    public FailureInfo getFailureInfo(String dependencyKey) {
        return failures.get(dependencyKey);
    }

    /**
     * 获取失败原因（兼容旧版本）
     * @param dependencyKey 依赖的key
     * @return 失败原因，如果没有失败则返回null
     */
    public String getFailureReason(String dependencyKey) {
        FailureInfo info = failures.get(dependencyKey);
        return info != null ? info.getReason() : null;
    }
    
    /**
     * 获取详细错误（兼容旧版本）
     * @param dependencyKey 依赖的key
     * @return 详细错误，如果没有错误则返回null
     */
    public Exception getDetailError(String dependencyKey) {
        FailureInfo info = failures.get(dependencyKey);
        return info != null ? info.getException() : null;
    }
    
    /**
     * 检查是否有失败的依赖
     * @return 是否有失败
     */
    public boolean hasFailures() {
        return !failures.isEmpty();
    }
    
    /**
     * 获取所有失败的依赖key
     * @return 失败的依赖key列表
     */
    public Set<String> getFailedDependencyKeys() {
        return failures.keySet();
    }
    
    /**
     * 根据失败类型获取失败的依赖key
     * @param type 失败类型
     * @return 指定类型失败的依赖key列表
     */
    public Set<String> getFailedDependencyKeysByType(FailureType type) {
        return failures.entrySet().stream()
                .filter(entry -> entry.getValue().getType() == type)
                .map(Map.Entry::getKey)
                .collect(Collectors.toSet());
    }
    
    /**
     * 获取失败数量
     * @return 失败数量
     */
    public int getFailureCount() {
        return failures.size();
    }

    /**
     * 根据失败类型获取失败数量
     * @param type 失败类型
     * @return 指定类型的失败数量
     */
    public int getFailureCountByType(FailureType type) {
        return (int) failures.values().stream()
                .filter(info -> info.getType() == type)
                .count();
    }

    /**
     * 获取失败的依赖对象
     * @param dependencyKey 依赖的key
     * @return 失败的依赖对象，如果没有则返回null
     */
    public Dependency getFailedDependency(String dependencyKey) {
        return failedDependencies.get(dependencyKey);
    }

    /**
     * 获取所有失败的依赖对象
     * @return 失败的依赖对象列表
     */
    public List<Dependency> getFailedDependencies() {
        return new ArrayList<>(failedDependencies.values());
    }

    /**
     * 根据失败类型获取失败的依赖对象
     * @param type 失败类型
     * @return 指定类型失败的依赖对象列表
     */
    public List<Dependency> getFailedDependenciesByType(FailureType type) {
        return failedDependencies.entrySet().stream()
                .filter(entry -> {
                    FailureInfo info = failures.get(entry.getKey());
                    return info != null && info.getType() == type;
                })
                .map(Map.Entry::getValue)
                .collect(Collectors.toList());
    }
} 