package com.xiaohongshu.codewiz.complete.model.lang;

/**
 * 编程语言类型枚举
 */
public enum LanguageType {
    
    JAVA("Java"),
    GOLANG("Go"),
    JAVASCRIPT("JavaScript"),
    PYTHON("Python"),
    CPP("C++"),
    KOTLIN("Kotlin"),
    TYPESCRIPT("TypeScript");
    
    private final String displayName;
    
    LanguageType(String displayName) {
        this.displayName = displayName;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    /**
     * 根据显示名称获取枚举
     */
    public static LanguageType fromDisplayName(String displayName) {
        for (LanguageType type : values()) {
            if (type.displayName.equalsIgnoreCase(displayName)) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知的语言类型: " + displayName);
    }
    
    /**
     * 根据名称获取枚举（兼容旧版本）
     */
    public static LanguageType fromName(String name) {
        try {
            return valueOf(name.toUpperCase());
        } catch (IllegalArgumentException e) {
            // 尝试通过显示名称匹配
            return fromDisplayName(name);
        }
    }
} 