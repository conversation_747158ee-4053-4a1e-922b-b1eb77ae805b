package com.xiaohongshu.codewiz.complete.runner.fetcher.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;

import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import com.xiaohongshu.codewiz.complete.model.dependency.Artifact;
import com.xiaohongshu.codewiz.complete.model.dependency.Dependency;
import com.xiaohongshu.codewiz.complete.model.dependency.DependencyType;
import com.xiaohongshu.codewiz.complete.model.parser.MavenMetadata;
import com.xiaohongshu.codewiz.complete.runner.fetcher.DependencyFetcher;
import com.xiaohongshu.codewiz.core.annotation.LogExecutionTime;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * Maven依赖获取器实现
 * 支持从JFrog仓库获取Maven依赖
 * 支持RELEASE和SNAPSHOT版本
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class MavenFetcher implements DependencyFetcher {

    private static final String JFROG_BASE_URL = "https://artifactory.devops.xiaohongshu.com";
    private static final String JFROG_DOWNLOAD_API = "artifactory";
    private static final String MAVEN_RELEASES_REPO = "maven-releases";
    private static final String MAVEN_SNAPSHOTS_REPO = "maven-snapshots";



    /**
     * 版本截断数量：保留最新的版本数量
     */
    private static final int MAX_VERSIONS_LIMIT = 5;

    private final WebClient webClient;
    private final XmlMapper xmlMapper = new XmlMapper();

    /**
     * 批量获取专用线程池 - 从ExecutorConfig注入
     */
    @Autowired
    private ExecutorService mavenBatchFetchExecutor;

    @Override
    public boolean supports(DependencyType dependencyType) {
        return DependencyType.MAVEN == dependencyType;
    }

    @Override
    public Optional<Artifact> fetchArtifact(Dependency dependency) {
        log.info("开始获取Maven依赖: {}", dependency.getDependencyKey());

        if (!supports(dependency.getDependencyType())) {
            log.warn("不支持的依赖类型: {}", dependency.getDependencyType().getDisplayName());
            return Optional.empty();
        }

        // 直接调用单个依赖获取逻辑
        return downloadArtifact(dependency);
    }

    @Override
    @LogExecutionTime
    public List<Artifact> batchFetcherArtifact(List<Dependency> dependencies) {
        if (dependencies == null || dependencies.isEmpty()) {
            return new ArrayList<>();
        }

        // 去重：基于dependencyKey去重，避免重复获取相同的依赖
        Map<String, Dependency> uniqueDependencies = dependencies.stream()
                .collect(Collectors.toMap(
                        Dependency::getDependencyKey,
                        dependency -> dependency,
                        (existing, replacement) -> existing
                ));

        int originalSize = dependencies.size();
        int uniqueSize = uniqueDependencies.size();
        if (originalSize != uniqueSize) {
            log.info("Maven依赖列表去重：原始{}个 -> 去重后{}个", originalSize, uniqueSize);
        }

        log.info("开始批量获取{}个Maven依赖", uniqueSize);

        try {
            // 并发处理每个依赖 - 每个Future返回自己的结果
            List<CompletableFuture<Optional<Artifact>>> futures = uniqueDependencies.values().stream()
                    .map(dependency -> CompletableFuture.supplyAsync(() -> {
                        long startTime = System.currentTimeMillis();
                        String threadName = Thread.currentThread().getName();
                        log.info("线程 {} 开始处理依赖: {}", threadName, dependency.getDependencyKey());
                        
                        try {
                            Optional<Artifact> artifact = downloadArtifact(dependency);
                            long duration = System.currentTimeMillis() - startTime;
                            if (artifact.isPresent()) {
                                log.info("线程 {} 成功获取Maven依赖: {}, 耗时: {}ms", threadName, dependency.getDependencyKey(), duration);
                            } else {
                                log.warn("线程 {} 获取Maven依赖失败: {}, 耗时: {}ms", threadName, dependency.getDependencyKey(), duration);
                            }
                            return artifact;
                        } catch (Exception e) {
                            long duration = System.currentTimeMillis() - startTime;
                            log.error("线程 {} 处理Maven依赖{}时发生异常, 耗时: {}ms", threadName, dependency.getDependencyKey(), duration, e);
                            return Optional.<Artifact>empty();
                        }
                    }, mavenBatchFetchExecutor))
                    .collect(Collectors.toList());

            // 等待所有任务完成并收集结果
            List<Artifact> artifactResults = futures.stream()
                    .map(CompletableFuture::join)
                    .filter(Optional::isPresent)
                    .map(Optional::get)
                    .collect(Collectors.toList());

            log.info("Maven依赖批量获取完成，成功获取{}个依赖，总共{}个", artifactResults.size(), uniqueSize);
            return artifactResults;

        } catch (Exception e) {
            log.error("批量获取Maven依赖失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public Dependency resolveVersion(Dependency dependency) {
        String originalVersion = dependency.getVersion();

        // 如果版本为null、空字符串或"LATEST"，则需要获取最新release版本
        if (originalVersion == null || originalVersion.trim().isEmpty()) {
            log.info("版本为空或LATEST，需要获取最新版本: {}", dependency.getDependencySignature());
            String latestVersion = fetchLatestReleaseVersion(dependency);
            if (latestVersion == null) {
                log.warn("无法获取最新版本: " + dependency.getDependencyKey());
                return null;
            }
            dependency.setVersion(latestVersion);
        } else if (isSnapshotVersion(originalVersion)) {
            String resolvedVersion = resolveSnapshotVersion(dependency);
            if (resolvedVersion == null) {
                log.warn("无法解析SNAPSHOT版本的实际版本号: " + dependency.getDependencyKey());
                return null;
            }
            // 塞入snapshot version中
            dependency.setSnapshotVersion(resolvedVersion);
        }
        return dependency;
    }

    @Override
    public List<Dependency> resolveVersions(List<Dependency> dependencies) {
        if (dependencies == null || dependencies.isEmpty()) {
            return new ArrayList<>();
        }

        log.info("开始解析Maven依赖的真实版本，数量: {}", dependencies.size());

        List<Dependency> resolvedDependencies = new ArrayList<>();

        for (Dependency dependency : dependencies) {
            Dependency resolved = resolveVersion(dependency);
            if (resolved == null) {
                log.warn("解析版本失败: {}", dependency.getDependencyKey());
                continue;
            }
            log.info("解析版本成功: {} -> {}", dependency.getDependencyKey(), resolved.getDependencyKey());
            // 将处理后的依赖添加到结果列表中
            resolvedDependencies.add(resolved);
        }

        return resolvedDependencies;
    }

    @Override
    public Map<String, List<String>> getAllReleaseVersions(List<Dependency> dependencies) {
        if (dependencies == null || dependencies.isEmpty()) {
            return new HashMap<>();
        }

        log.info("开始解析Maven依赖的所有版本（Map格式），数量: {}", dependencies.size());

        Map<String, List<String>> result = new HashMap<>();

        for (Dependency dependency : dependencies) {
            String dependencySignature = dependency.getDependencySignature();
            log.info("解析依赖的所有版本: {}", dependencySignature);

            try {
                // 如果依赖已经指定了具体版本号，直接使用该版本，不需要获取所有版本
                String specifiedVersion = dependency.getVersion();
                if (specifiedVersion != null && !specifiedVersion.trim().isEmpty()) {
                    log.info("依赖已指定版本号: {} -> {}, 跳过获取所有版本",
                            dependencySignature, specifiedVersion);

                    result.put(dependencySignature, List.of(specifiedVersion));
                    continue;
                }

                // 获取该依赖的所有版本（已按从新到旧排序）
                List<String> allVersions = fetchAllVersions(dependency);

                if (allVersions.isEmpty()) {
                    log.warn("未找到任何版本: {}", dependencySignature);
                    result.put(dependencySignature, new ArrayList<>());
                    continue;
                }
                List<String> versionToDisplay = allVersions.subList(0, Math.min(10, allVersions.size()));

                result.put(dependencySignature, allVersions);
                log.info("找到{}个版本: {} -> {}(仅展示{}个)", allVersions.size(), dependencySignature, versionToDisplay, versionToDisplay.size());

            } catch (Exception e) {
                log.error("解析依赖所有版本失败: {}, 错误: {}",
                        dependencySignature, e.getMessage(), e);
                // 继续处理下一个依赖，将其标记为空列表
                result.put(dependencySignature, new ArrayList<>());
            }
        }

        log.info("Maven依赖所有版本解析完成（Map格式），处理了{}个依赖", dependencies.size());
        return result;
    }

    /**
     * 获取依赖的所有版本
     */
    private List<String> fetchAllVersions(Dependency dependency) {
        try {
            String metadataUrl = buildVersionMetadataUrl(dependency);
            log.info("Maven版本元数据URL: {}", metadataUrl);

            String metadataXml = downloadMetadata(metadataUrl);
            if (metadataXml == null) {
                log.warn("无法下载Maven版本元数据: {}", metadataUrl);
                return new ArrayList<>();
            }

            MavenMetadata metadata = xmlMapper.readValue(metadataXml, MavenMetadata.class);
            List<String> allVersions = extractAllVersions(metadata);

            if (allVersions.isEmpty()) {
                log.warn("无法从元数据中解析版本列表: {}", dependency.getDependencyKey());
                return new ArrayList<>();
            } else {
                // 截断版本列表用于日志显示，最多显示10个版本
                List<String> displayVersions = allVersions.size() > 10 ? 
                    allVersions.subList(0, 10) : allVersions;
                String versionDisplay = allVersions.size() > 10 ? 
                    displayVersions + "..." : displayVersions.toString();
                log.info("成功解析到{}个版本: {} -> {}", allVersions.size(), dependency.getDependencyKey(), versionDisplay);
                return allVersions;
            }

        } catch (Exception e) {
            log.error("获取所有版本失败: {}", dependency.getDependencyKey(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 从Maven元数据中提取所有版本
     * 返回按从新到旧排序的版本列表
     */
    private List<String> extractAllVersions(MavenMetadata metadata) {
        if (metadata == null || metadata.getVersioning() == null) {
            return new ArrayList<>();
        }

        MavenMetadata.Versioning versioning = metadata.getVersioning();

        // 从versions列表中获取所有版本
        if (versioning.getVersions() != null && versioning.getVersions().getVersion() != null) {
            List<String> versions = versioning.getVersions().getVersion();
            log.info("从maven-metadata.xml中提取到{}个版本", versions.size());

            // Maven元数据中版本是按时间顺序排列的，最后的是最新的
            // 反转列表，使最新版本在前（从新到旧排序）
            List<String> sortedVersions = new ArrayList<>(versions);
            java.util.Collections.reverse(sortedVersions);

            return sortedVersions;
        }

        log.warn("maven-metadata.xml中未找到versions节点");
        return new ArrayList<>();
    }

    /**
     * 获取单个Maven依赖
     */
    private Optional<Artifact> downloadArtifact(Dependency dependency) {
        try {
            // 当原始版本为空时，将解析后的版本设置到dependency对象上，确保后续逻辑一致性
            String version = dependency.getVersion();
            if (version == null || version.trim().isEmpty()) {
                // 报错
                log.warn("版本为空，无法获取Maven依赖: {}", dependency.getDependencyKey());
                return Optional.empty();
            }

            // 构建下载URL（使用已解析的版本号）
            String sourceJarUrl = buildSourceJarUrl(dependency);
            String pomUrl = buildPomUrl(dependency);

            byte[] jarContent = downloadJar(sourceJarUrl);

            if (jarContent == null) {
                return Optional.empty();
            }

            // 构建DependencyArtifact
            Artifact artifact = Artifact.builder()
                    .dependency(dependency)
                    .sourceContent(jarContent)
                    .sourceUrl(sourceJarUrl)
                    .metadataUrl(pomUrl)
                    .size(jarContent.length)
                    .build();

            // 日志显示：如果是SNAPSHOT，显示用户版本信息
            String displayVersion = dependency.isSnapshot() ?
                    String.format("%s (实际版本: %s)", dependency.getVersion(), dependency.getSnapshotVersion()) : dependency.getVersion();
            log.info("成功获取Maven依赖: {}:{} 版本: {}",
                    dependency.getNamespace(), dependency.getName(), displayVersion);
            return Optional.of(artifact);

        } catch (Exception e) {
            log.error("获取Maven依赖失败: {}", dependency.getDependencyKey(), e);
            return Optional.empty();
        }
    }

    /**
     * 从maven-metadata.xml获取Maven依赖的最新版本（带缓存）
     */
    private String fetchLatestReleaseVersion(Dependency dependency) {
        try {
            String metadataUrl = buildVersionMetadataUrl(dependency);
            log.info("Maven版本元数据URL: {}", metadataUrl);

            String metadataXml = downloadMetadata(metadataUrl);
            if (metadataXml == null) {
                log.warn("无法下载Maven版本元数据: {}", metadataUrl);
                return null;
            }

            MavenMetadata metadata = xmlMapper.readValue(metadataXml, MavenMetadata.class);
            String latestVersion = extractLatestVersion(metadata);

            if (latestVersion != null) {
                return latestVersion;
            } else {
                log.warn("无法从元数据中解析最新版本: {}", dependency.getDependencyKey());
                return null;
            }

        } catch (Exception e) {
            log.error("解析最新版本失败: {}", dependency.getDependencyKey(), e);
            return null;
        }
    }

    /**
     * 从Maven元数据中提取最新版本
     */
    private String extractLatestVersion(MavenMetadata metadata) {
        if (metadata == null || metadata.getVersioning() == null) {
            return null;
        }

        MavenMetadata.Versioning versioning = metadata.getVersioning();

        // 优先使用latest字段
        if (StringUtils.hasText(versioning.getLatest())) {
            return versioning.getLatest();
        }

        // 其次使用release字段
        if (StringUtils.hasText(versioning.getRelease())) {
            return versioning.getRelease();
        }

        // 最后从versions列表中取最后一个
        if (versioning.getVersions() != null && versioning.getVersions().getVersion() != null) {
            var versions = versioning.getVersions().getVersion();
            if (!versions.isEmpty()) {
                return versions.get(versions.size() - 1);
            }
        }

        return null;
    }

    /**
     * 检测版本是否为SNAPSHOT版本
     */
    private boolean isSnapshotVersion(String version) {
        return version != null && version.endsWith("-SNAPSHOT");
    }

    /**
     * 构建版本元数据URL（用于获取最新版本信息）
     */
    private String buildVersionMetadataUrl(Dependency dependency) {
        String groupPath = dependency.getNamespace().replace(".", "/");
        String filePath = String.format("%s/%s/maven-metadata.xml", groupPath, dependency.getName());
        return String.format("%s/%s/%s/%s",
                JFROG_BASE_URL,
                JFROG_DOWNLOAD_API,
                MAVEN_RELEASES_REPO,
                filePath);
    }

    /**
     * 构建SNAPSHOT版本元数据URL（用于获取SNAPSHOT的实际版本号）
     */
    private String buildSnapshotMetadataUrl(Dependency dependency) {
        String groupPath = dependency.getNamespace().replace(".", "/");
        String filePath = String.format("%s/%s/%s/maven-metadata.xml",
                groupPath, dependency.getName(), dependency.getVersion());
        return String.format("%s/%s/%s/%s",
                JFROG_BASE_URL,
                JFROG_DOWNLOAD_API,
                MAVEN_SNAPSHOTS_REPO,
                filePath);
    }

    /**
     * 解析SNAPSHOT版本的实际版本号（带缓存）
     *
     * @param dependency 依赖信息
     */
    private String resolveSnapshotVersion(Dependency dependency) {
        // 缓存未命中，从远程获取
        try {
            String snapshotMetadataUrl = buildSnapshotMetadataUrl(dependency);
            log.info("SNAPSHOT版本元数据URL: {}", snapshotMetadataUrl);

            String metadataXml = downloadMetadata(snapshotMetadataUrl);
            if (metadataXml == null) {
                log.warn("无法下载SNAPSHOT版本元数据: {}", snapshotMetadataUrl);
                return null;
            }

            MavenMetadata metadata = xmlMapper.readValue(metadataXml, MavenMetadata.class);
            String actualVersion = extractSnapshotVersion(metadata);

            if (actualVersion != null) {
                return actualVersion;
            } else {
                log.warn("无法从SNAPSHOT元数据中解析实际版本: {}", dependency.getDependencyKey());
                return null;
            }

        } catch (Exception e) {
            log.error("解析SNAPSHOT版本失败: {}", dependency.getDependencyKey(), e);
            return null;
        }
    }

    /**
     * 从SNAPSHOT元数据中提取实际版本号
     */
    private String extractSnapshotVersion(MavenMetadata metadata) {
        if (metadata == null || metadata.getVersioning() == null) {
            return null;
        }

        MavenMetadata.Versioning versioning = metadata.getVersioning();
        if (versioning.getSnapshotVersions() == null ||
                versioning.getSnapshotVersions().getSnapshotVersion() == null) {
            return null;
        }

        // 查找extension为"jar"且没有classifier的版本
        for (MavenMetadata.SnapshotVersion snapshotVersion : versioning.getSnapshotVersions().getSnapshotVersion()) {
            if ("jar".equals(snapshotVersion.getExtension()) && snapshotVersion.getClassifier() == null) {
                return snapshotVersion.getValue();
            }
        }

        log.warn("未找到SNAPSHOT版本");
        return null;
    }

    /**
     * 下载文件内容
     */
    private String downloadMetadata(String url) {
        try {
            Mono<String> response = webClient.get()
                    .uri(url)
                    .retrieve()
                    .bodyToMono(String.class);

            String result = response.block();
            return result;
        } catch (WebClientResponseException e) {
            if (e.getStatusCode().value() == 404) {
                log.warn("下载元数据失败: {} - 文件不存在(404)", url);
            } else {
                log.warn("下载元数据失败: {} - HTTP错误: {}", url, e.getStatusCode());
            }
            return null;
        } catch (Exception e) {
            log.warn("下载元数据失败: {} - {}", url, e.getMessage());
            return null;
        }
    }

    /**
     * 下载JAR文件
     */
    private byte[] downloadJar(String url) {
        try {
            long startTime = System.currentTimeMillis();
            String threadName = Thread.currentThread().getName();
            log.info("线程 {} 开始下载JAR: {}, 时间: {}", threadName, url, startTime);
            
            Mono<byte[]> response = webClient.get()
                    .uri(url)
                    .retrieve()
                    .bodyToMono(byte[].class);

            byte[] result = response.block();
            long duration = System.currentTimeMillis() - startTime;
            log.info("线程 {} JAR下载完成: {}, 耗时: {}ms, 大小: {}字节", threadName, url, duration, result != null ? result.length : 0);
            return result;
        } catch (WebClientResponseException e) {
            if (e.getStatusCode().value() == 404) {
                log.warn("下载JAR文件失败: {} - 文件不存在(404)", url);
            } else {
                log.warn("下载JAR文件失败: {} - HTTP错误: {}", url, e.getStatusCode());
            }
            return null;
        } catch (Exception e) {
            log.warn("下载JAR文件失败: {} - {}", url, e.getMessage());
            return null;
        }
    }

    /**
     * 构建源码JAR文件下载URL
     */
    private String buildSourceJarUrl(Dependency dependency) {
        // 判断是否为SNAPSHOT：优先检查snapshotVersion字段，其次检查原始version
        boolean isSnapshot = dependency.isSnapshot();
        String repoName = isSnapshot ? MAVEN_SNAPSHOTS_REPO : MAVEN_RELEASES_REPO;
        String directoryVersion = isSnapshot ? dependency.getSnapshotVersion() : dependency.getVersion();

        // 构建文件名
        String fileName = String.format("%s-%s-sources.jar", dependency.getName(), directoryVersion);

        // 构建完整URL - 目录版本使用原始版本（SNAPSHOT版本用snapshotVersion，其他用version）
        return String.format("%s/%s/%s/%s/%s/%s/%s",
                JFROG_BASE_URL,
                JFROG_DOWNLOAD_API,
                repoName,
                dependency.getNamespace().replace(".", "/"),
                dependency.getName(),
                dependency.getVersion(),
                fileName);
    }

    /**
     * 构建POM文件下载URL
     */
    private String buildPomUrl(Dependency dependency) {
        // 判断是否为SNAPSHOT：优先检查snapshotVersion字段，其次检查原始version
        boolean isSnapshot = dependency.isSnapshot() || isSnapshotVersion(dependency.getVersion());
        String repoName = isSnapshot ? MAVEN_SNAPSHOTS_REPO : MAVEN_RELEASES_REPO;
        String directoryVersion = isSnapshot ? dependency.getSnapshotVersion() : dependency.getVersion();

        // 构建文件名 - POM文件应该是 .pom 扩展名
        String fileName = String.format("%s-%s.pom", dependency.getName(), directoryVersion);

        // 构建完整URL - 目录版本使用原始版本（SNAPSHOT版本用snapshotVersion，其他用version）
        return String.format("%s/%s/%s/%s/%s/%s/%s",
                JFROG_BASE_URL,
                JFROG_DOWNLOAD_API,
                repoName,
                dependency.getNamespace().replace(".", "/"),
                dependency.getName(),
                dependency.getVersion(),
                fileName);
    }
} 