package com.xiaohongshu.codewiz.complete.controller;

import com.xiaohongshu.codewiz.complete.service.ProviderUserTokenService;
import com.xiaohongshu.codewiz.core.entity.config.dto.CreateLingmaTokenResponse;
import com.xiaohongshu.codewiz.core.entity.config.dto.GetMappingLoginTokenRes;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.xiaohongshu.codewiz.core.entity.common.SingleResponse;
import com.xiaohongshu.codewiz.core.entity.config.dto.ConfigQueryRequest;
import com.xiaohongshu.codewiz.core.entity.config.dto.ConfigQueryResponse;
import com.xiaohongshu.codewiz.core.entity.config.dto.ConfigUpdateRequest;
import com.xiaohongshu.codewiz.core.entity.config.dto.ConfigUpgradeRequest;
import com.xiaohongshu.codewiz.core.service.config.IDEConfigService;

import lombok.extern.slf4j.Slf4j;

/**
 * IDE配置控制器
 *
 * <AUTHOR>
 * @since 2025-04-21
 */
@Slf4j
@RestController
@RequestMapping("/config")
public class IDEConfigController {

    @Autowired
    private IDEConfigService ideConfigService;
    @Autowired
    private ProviderUserTokenService providerUserTokenService;

    /**
     * 查询配置
     *
     * @param userEmail     用户邮箱
     * @param pluginVersion 插件版本
     * @param type          配置类型
     * @return 配置信息
     */
    @GetMapping("/query")
    public SingleResponse<ConfigQueryResponse> queryConfig(@RequestParam(required = false) String userEmail,
                                                           @RequestParam String pluginVersion,
                                                           @RequestParam(required = false) Integer type,
                                                           @RequestParam(required = false) String idePlatform) {
        log.info("queryConfig, userEmail: {}, pluginVersion: {}, type: {}", userEmail, pluginVersion, type);
        ConfigQueryRequest request = new ConfigQueryRequest();
        request.setUserEmail(userEmail);
        request.setPluginVersion(pluginVersion);
        request.setType(type);
        request.setIdePlatform(idePlatform);
        return ideConfigService.queryConfig(request);
    }

    /**
     * 更新配置
     *
     * @param request 更新请求
     * @return 更新结果
     */
    @PostMapping("/update")
    public SingleResponse<Void> updateConfig(@RequestBody ConfigUpdateRequest request) {
        log.info("updateConfig, request: {}", request);
        return ideConfigService.updateConfig(request);
    }

    /**
     * 升级配置
     *
     * @param request 升级请求
     * @return 升级结果
     */
    @PostMapping("/upgrade")
    public SingleResponse<Void> upgradeConfig(@RequestBody ConfigUpgradeRequest request) {
        log.info("upgradeConfig, request: {}", request);
        return ideConfigService.upgradeConfig(request);
    }
    @Deprecated
    @GetMapping("/user/token")
    public SingleResponse<String> getUserToken(@RequestParam(required = true) String userEmail) {
        log.info("getUserToken, request: {}", userEmail);
        return providerUserTokenService.getLingmaUserToken(userEmail);
    }
    @GetMapping("/user/tokenV2")
    public SingleResponse<GetMappingLoginTokenRes> getUserTokenV2(@RequestParam String userEmail) {
        log.info("getUserTokenV2, request: {}", userEmail);
        return providerUserTokenService.getLingmaUserTokenV2(userEmail);
    }

    @PostMapping("/user/addToken")
    public SingleResponse<Void> addUserToken(@RequestBody CreateLingmaTokenResponse response) {
        log.info("addUserToken, request: {}", response);
        return providerUserTokenService.addUserToken(response);
    }
    @GetMapping("/user/delToken")
    public SingleResponse<Void> delToken(@RequestParam String userEmail) {
        log.info("delToken, request: {}", userEmail);
        return providerUserTokenService.delUserToken(userEmail);
    }
}
