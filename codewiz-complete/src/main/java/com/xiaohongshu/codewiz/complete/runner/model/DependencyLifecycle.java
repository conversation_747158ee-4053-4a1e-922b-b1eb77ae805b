package com.xiaohongshu.codewiz.complete.runner.model;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;

/**
 * 依赖生命周期跟踪
 */
@Data
public class DependencyLifecycle {
    
    /**
     * 依赖标识 (namespace:name)
     */
    private String dependencyKey;
    
    /**
     * 命名空间
     */
    private String namespace;
    
    /**
     * 名称
     */
    private String name;
    
    /**
     * 语言
     */
    private String language;
    
    /**
     * 依赖类型
     */
    private String dependencyType;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    
    /**
     * 当前状态
     */
    private LifecycleState currentState = LifecycleState.INITIALIZED;
    
    /**
     * 当前状态消息
     */
    private String currentMessage;
    
    /**
     * 最后更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastUpdateTime;
    
    /**
     * 状态历史记录
     */
    private List<StateTransition> stateHistory = new ArrayList<>();
    
    /**
     * 处理的版本数量
     */
    private int versionCount = 0;
    
    /**
     * 生成的知识数量
     */
    private int knowledgeCount = 0;
    
    public DependencyLifecycle(String dependencyKey, String namespace, String name, 
                              String language, String dependencyType, LocalDateTime createTime) {
        this.dependencyKey = dependencyKey;
        this.namespace = namespace;
        this.name = name;
        this.language = language;
        this.dependencyType = dependencyType;
        this.createTime = createTime;
        this.lastUpdateTime = createTime;
        
        // 记录初始状态
        this.stateHistory.add(new StateTransition(LifecycleState.INITIALIZED, "依赖初始化", createTime));
    }
    
    /**
     * 更新状态
     */
    public void updateState(LifecycleState newState, String message) {
        LifecycleState previousState = this.currentState;
        this.currentState = newState;
        this.currentMessage = message;
        this.lastUpdateTime = LocalDateTime.now();
        
        // 记录状态转换
        this.stateHistory.add(new StateTransition(newState, message, this.lastUpdateTime));
    }
    
    /**
     * 获取状态持续时间（分钟）
     */
    public long getStateDurationMinutes() {
        if (stateHistory.size() < 2) {
            return 0;
        }
        
        StateTransition current = stateHistory.get(stateHistory.size() - 1);
        StateTransition previous = stateHistory.get(stateHistory.size() - 2);
        
        return java.time.Duration.between(previous.getTimestamp(), current.getTimestamp()).toMinutes();
    }
    
    /**
     * 获取总处理时间（分钟）
     */
    public long getTotalProcessingMinutes() {
        if (stateHistory.isEmpty()) {
            return 0;
        }
        
        StateTransition first = stateHistory.get(0);
        StateTransition last = stateHistory.get(stateHistory.size() - 1);
        
        return java.time.Duration.between(first.getTimestamp(), last.getTimestamp()).toMinutes();
    }
    
    /**
     * 是否处理完成
     */
    public boolean isCompleted() {
        return currentState == LifecycleState.COMPLETED || 
               currentState == LifecycleState.FAILED || 
               currentState == LifecycleState.SKIPPED;
    }
    
    /**
     * 是否处理成功
     */
    public boolean isSuccess() {
        return currentState == LifecycleState.COMPLETED;
    }
    
    /**
     * 状态转换记录
     */
    @Data
    public static class StateTransition {
        private LifecycleState state;
        private String message;
        
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime timestamp;
        
        public StateTransition(LifecycleState state, String message, LocalDateTime timestamp) {
            this.state = state;
            this.message = message;
            this.timestamp = timestamp;
        }
    }
} 