package com.xiaohongshu.codewiz.complete.controller;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.xiaohongshu.codewiz.complete.dto.packagecontext.PackageContextQueryReq;
import com.xiaohongshu.codewiz.complete.dto.packagecontext.PackageContextQueryResp;
import com.xiaohongshu.codewiz.complete.service.codecontext.PackageContextService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 代码上下文查询控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/context")
@RequiredArgsConstructor
public class PackageContextController {

    private final PackageContextService packageContextService;
    /**
     * 查询依赖的上下文知识
     *
     * @return 知识信息
     */
    @PostMapping("/query")
    public PackageContextQueryResp queryPackageContext(@RequestBody PackageContextQueryReq request) {
        log.info("接收到上下文查询请求: {}", request);

        PackageContextQueryResp response = packageContextService.queryContext(request);

        return response;
    }
} 