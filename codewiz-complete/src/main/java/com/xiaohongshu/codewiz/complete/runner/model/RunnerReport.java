package com.xiaohongshu.codewiz.complete.runner.model;

import java.time.LocalDateTime;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Builder;
import lombok.Data;

/**
 * Runner 执行报告
 */
@Data
@Builder
public class RunnerReport {
    
    /**
     * 运行ID
     */
    private String runId;
    
    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;
    
    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;
    
    /**
     * 运行时长（分钟）
     */
    private long durationMinutes;
    
    /**
     * 总依赖数
     */
    private int totalDependencies;
    
    /**
     * 已处理数量
     */
    private int processedCount;
    
    /**
     * 成功数量
     */
    private int successCount;
    
    /**
     * 失败数量
     */
    private int failedCount;
    
    /**
     * 跳过数量
     */
    private int skippedCount;
    
    /**
     * 配置文件路径
     */
    private String configFile;
    
    /**
     * 是否强制刷新
     */
    private boolean forceRefresh;

    
    /**
     * 依赖生命周期列表
     */
    private List<DependencyLifecycle> dependencyLifecycles;
    
    /**
     * 获取成功率
     */
    public double getSuccessRate() {
        return totalDependencies > 0 ? (double) successCount / totalDependencies * 100 : 0.0;
    }
    
    /**
     * 获取失败率
     */
    public double getFailureRate() {
        return totalDependencies > 0 ? (double) failedCount / totalDependencies * 100 : 0.0;
    }
    
    /**
     * 获取跳过率
     */
    public double getSkipRate() {
        return totalDependencies > 0 ? (double) skippedCount / totalDependencies * 100 : 0.0;
    }
} 