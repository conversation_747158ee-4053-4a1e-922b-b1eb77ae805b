package com.xiaohongshu.codewiz.complete.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import com.xiaohongshu.codewiz.complete.service.FeedbackCompleteService;
import com.xiaohongshu.codewiz.core.entity.common.SingleResponse;
import com.xiaohongshu.codewiz.core.entity.feedback.dto.FeedbackReportRequest;
import com.xiaohongshu.codewiz.core.entity.feedback.dto.FeedbackQueryResponse;

import lombok.extern.slf4j.Slf4j;

/**
 * 反馈控制器
 *
 * <AUTHOR>
 * @since 2024-05-24
 */
@Slf4j
@RestController
@RequestMapping("/issue")
public class FeedbackController {

    @Autowired
    private FeedbackCompleteService feedbackCompleteService;

    /**
     * 提交反馈
     *
     * @param request 反馈请求
     * @return 提交结果
     */
    @PostMapping("/report/v1")
    public SingleResponse<Void> reportFeedback(@RequestBody FeedbackReportRequest request) {
        log.info("reportFeedback, request: {}", request);
        return feedbackCompleteService.reportFeedback(request);
    }

    /**
     * 查询反馈
     *
     * @param feedbackId 反馈ID
     * @return 查询结果
     */
    @GetMapping("/query/v1")
    public SingleResponse<FeedbackQueryResponse> queryFeedback(@RequestParam("feedbackId") String feedbackId) {
        log.info("queryFeedback, feedbackId: {}", feedbackId);
        return feedbackCompleteService.queryFeedback(feedbackId);
    }
}