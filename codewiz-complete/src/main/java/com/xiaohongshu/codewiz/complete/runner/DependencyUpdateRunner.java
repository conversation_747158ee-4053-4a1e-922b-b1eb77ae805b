package com.xiaohongshu.codewiz.complete.runner;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.xiaohongshu.codewiz.complete.model.dpc.DependencyPackageContext;
import com.xiaohongshu.codewiz.complete.model.task.PackageContextBuildResult;
import com.xiaohongshu.codewiz.complete.model.dpc.PackageContextItem;
import com.xiaohongshu.codewiz.complete.model.dependency.Artifact;
import com.xiaohongshu.codewiz.complete.model.dependency.Dependency;
import com.xiaohongshu.codewiz.complete.model.dependency.DependencyType;
import com.xiaohongshu.codewiz.complete.model.lang.LanguageType;
import com.xiaohongshu.codewiz.complete.runner.config.DependencyUpdateConfig;
import com.xiaohongshu.codewiz.complete.runner.model.DependencyLifecycle;
import com.xiaohongshu.codewiz.complete.runner.model.GroupProcessResult;
import com.xiaohongshu.codewiz.complete.runner.model.LifecycleState;
import com.xiaohongshu.codewiz.complete.runner.model.RunnerReport;
import com.xiaohongshu.codewiz.complete.runner.model.StepResult;
import com.xiaohongshu.codewiz.complete.runner.model.VersionInfo;
import com.xiaohongshu.codewiz.complete.runner.builder.PackageContextBuilder;
import com.xiaohongshu.codewiz.complete.runner.builder.PackageContextBuilderFactory;
import com.xiaohongshu.codewiz.complete.runner.fetcher.DependencyFetcher;
import com.xiaohongshu.codewiz.complete.runner.fetcher.DependencyFetcherFactory;
import com.xiaohongshu.codewiz.complete.store.PackageContextStore;

import lombok.Data;
import lombok.RequiredArgsConstructor;
import java.util.concurrent.ConcurrentHashMap;

import org.springframework.boot.SpringApplication;
import org.springframework.context.ApplicationContext;
import com.xiaohongshu.codewiz.complete.model.task.FailureInfo;
import com.xiaohongshu.codewiz.complete.model.task.FailureType;
import com.xiaohongshu.codewiz.complete.runner.fetcher.FetchContext;

/**
 * 依赖更新 Runner
 * <p>
 * 通过配置文件驱动的批量依赖更新工具，支持:
 * - 配置文件驱动的依赖列表
 * - 完整的生命周期跟踪
 * - 详细的日志记录（通过标准日志框架输出）
 * - 最终报告生成
 * - 可配置的批处理大小和并发控制
 * - 缺失依赖实时清理（在成功保存后立即删除missing表记录）
 * <p>
 * 使用方式：
 * 通过 DependencyUpdateRunnerApplication 启动
 * <p>
 * 日志配置：
 * 所有日志通过标准的 SLF4J + Logback 输出，可通过 logback-spring.xml 配置文件输出到控制台和文件
 * 推荐配置独立的 appender 将 DependencyUpdateRunner 的日志输出到专门的文件
 */
@Component
@ConditionalOnProperty(prefix = "codewiz.dependency-update.runner", name = "enabled", havingValue = "true")
@RequiredArgsConstructor
public class DependencyUpdateRunner implements CommandLineRunner {

    private static final Logger log = LoggerFactory.getLogger(DependencyUpdateRunner.class);

    // 配置常量
    private static final int DEFAULT_BATCH_SIZE = 25;
    private static final DateTimeFormatter DATETIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static final DateTimeFormatter FILE_DATETIME_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss");

    // 依赖注入
    private final DependencyUpdateConfig config;
    private final PackageContextStore packageContextStore;
    private final DependencyFetcherFactory dependencyFetcherFactory;
    private final PackageContextBuilderFactory packageContextBuilderFactory;
    private final ApplicationContext applicationContext;
    private final ObjectMapper objectMapper = new ObjectMapper();
    private final Map<String, DependencyLifecycle> dependencyLifecycles = new HashMap<>();
    private final AtomicInteger processedCount = new AtomicInteger(0);
    private final AtomicInteger successCount = new AtomicInteger(0);
    private final AtomicInteger failedCount = new AtomicInteger(0);
    private final AtomicInteger skippedCount = new AtomicInteger(0);
    // 运行时状态
    private String runId;
    private LocalDateTime runStartTime;
    private String logDir;

    /**
     * 组处理上下文，用于收集批次处理过程中的失败信息
     */
    @Data
    private static class GroupProcessContext {
        private final Map<String, FailureInfo> failureInfoMap = new ConcurrentHashMap<>();
        private final List<Dependency> missingSourceJarDeps = new ArrayList<>();
        private final List<Dependency> otherFailedDeps = new ArrayList<>();
        
        public void addFailure(String dependencyKey, FailureType type, String reason) {
            failureInfoMap.put(dependencyKey, new FailureInfo(type, reason, null));
        }
        
        public void addMissingSourceJarDep(Dependency dep) {
            synchronized (missingSourceJarDeps) {
                missingSourceJarDeps.add(dep);
            }
        }
        
        public void addOtherFailedDep(Dependency dep) {
            synchronized (otherFailedDeps) {
                otherFailedDeps.add(dep);
            }
        }
        
        public boolean hasFailures() {
            return !failureInfoMap.isEmpty();
        }
        
        public List<Dependency> getMissingSourceJarDeps() {
            synchronized (missingSourceJarDeps) {
                return new ArrayList<>(missingSourceJarDeps);
            }
        }
        
        public List<Dependency> getOtherFailedDeps() {
            synchronized (otherFailedDeps) {
                return new ArrayList<>(otherFailedDeps);
            }
        }

        /**
         * 收集获取制品失败的依赖信息
         */
        public void collectFetchFailures(FetchContext fetchContext) {
            if (!fetchContext.hasFailures()) {
                return;
            }

            // 直接从 FetchContext 获取失败的依赖对象，不需要额外的 batch 参数
            List<Dependency> missingSourceJarDeps = fetchContext.getFailedDependenciesByType(FailureType.MISSING_SOURCE_JAR);
            List<Dependency> otherFailedDeps = fetchContext.getFailedDependenciesByType(FailureType.OTHER);

            // 收集缺失 source.jar 的依赖
            for (Dependency dep : missingSourceJarDeps) {
                String depKey = dep.getDependencyKey();
                FailureInfo failureInfo = fetchContext.getFailureInfo(depKey);
                addFailure(depKey, failureInfo.getType(), failureInfo.getReason());
                addMissingSourceJarDep(dep);
                log.info("依赖 {} 因找不到source.jar，收集到失败列表", depKey);
            }

            // 收集其他类型的失败依赖
            for (Dependency dep : otherFailedDeps) {
                String depKey = dep.getDependencyKey();
                FailureInfo failureInfo = fetchContext.getFailureInfo(depKey);
                addFailure(depKey, failureInfo.getType(), failureInfo.getReason());
                addOtherFailedDep(dep);
                log.warn("依赖 {} 获取制品失败，类型: {}, 原因: {}", depKey, failureInfo.getType(), failureInfo.getReason());
            }
        }
    }

    @Override
    public void run(String... args) throws Exception {
        // 初始化运行环境
        initializeRun();

        try {
            log.info("========== 依赖更新 Runner 开始执行 ==========");
            log.info("运行ID: {}", runId);
            log.info("开始时间: {}", runStartTime.format(DATETIME_FORMATTER));
            log.info("配置文件: {}", config.getConfigFile());
            log.info("强制刷新: {}", config.isForceRefresh());

            // 执行更新流程
            executeUpdate();

            log.info("========== 依赖更新 Runner 执行完成 ==========");

        } catch (Exception e) {
            log.error("依赖更新 Runner 执行失败", e);
            throw e;
        } finally {
            // 生成最终报告
            generateFinalReport();
            
            // 确保应用程序退出
            log.info("依赖更新 Runner 执行完成，准备退出应用程序...");
            System.exit(SpringApplication.exit(applicationContext, () -> 0));
        }
    }

    /**
     * 初始化运行环境
     */
    private void initializeRun() {
        runId = "run_" + System.currentTimeMillis();
        runStartTime = LocalDateTime.now();

        // 创建日志目录（用于报告文件）
        logDir = config.getLogDir() + "/" + runStartTime.format(FILE_DATETIME_FORMATTER) + "_" + runId;
        File logDirFile = new File(logDir);
        if (!logDirFile.exists()) {
            logDirFile.mkdirs();
        }

        log.info("依赖更新 Runner 初始化完成，运行ID: {}, 日志目录: {}", runId, logDir);
    }

    /**
     * 执行更新流程
     */
    private void executeUpdate() throws Exception {
        // 1. 读取更新dependency list
        StepResult loadConfigResult = loadDependencyConfig();
        if (!loadConfigResult.isSuccess()) {
            log.info("跳过配置文件处理: " + loadConfigResult.getErrorMessage());
            return;
        }

        @SuppressWarnings("unchecked")
        List<DependencyUpdateConfig.DependencyGroup> dependencyGroups =
                (List<DependencyUpdateConfig.DependencyGroup>) loadConfigResult.getData();


        // 打印本次更新依赖的详情
        log.info("本次更新依赖的详情: {}", dependencyGroups);

        // 开始优先更新文件中的依赖
        for (DependencyUpdateConfig.DependencyGroup group : dependencyGroups) {
            processGroup(group, false);
        }

        // 如果开启了从缺失依赖表注入依赖，在这一步进行处理
        if (config.isProcessMissingDependencies()) {
            processMissingDepGroups();
        }

        log.info(String.format("所有依赖处理完成，总计: 处理=%d, 成功=%d, 失败=%d, 跳过=%d",
                processedCount.get(), successCount.get(), failedCount.get(), skippedCount.get()));
    }

    /**
     * 加载依赖配置文件
     */
    private StepResult loadDependencyConfig() {
        try {
            log.info("开始加载依赖配置文件: {}", config.getConfigFile());

            String configContent = readConfigFile(config.getConfigFile());
            if (configContent == null || configContent.trim().isEmpty()) {
                log.warn("配置文件为空或不存在: {}", config.getConfigFile());
                return StepResult.success(new ArrayList<>());
            }

            // 解析JSON配置
            DependencyUpdateConfig.RunnerConfig runnerConfig = objectMapper.readValue(
                    configContent, DependencyUpdateConfig.RunnerConfig.class);

            List<DependencyUpdateConfig.DependencyGroup> dependencyGroups =
                    runnerConfig.getDependencyGroups() != null ?
                            runnerConfig.getDependencyGroups() : new ArrayList<>();

            log.info("依赖配置文件加载完成，依赖组数量: {}", dependencyGroups.size());
            for (DependencyUpdateConfig.DependencyGroup group : dependencyGroups) {
                log.info("  - 组名: {}, 语言: {}, 类型: {}, 依赖数: {}",
                        group.getName(), group.getLanguage(), group.getDependencyType(),
                        group.getDependencies() != null ? group.getDependencies().size() : 0);
            }

            return StepResult.success(dependencyGroups);

        } catch (Exception e) {
            log.error("加载依赖配置文件失败: " + e.getMessage(), e);
            return StepResult.failure("加载配置文件失败: " + e.getMessage());
        }
    }

    /**
     * 读取配置文件内容
     */
    private String readConfigFile(String configPath) throws IOException {
        if (configPath.startsWith("classpath:")) {
            // 从classpath读取
            String resourcePath = configPath.substring("classpath:".length());
            Resource resource = new ClassPathResource(resourcePath);

            if (!resource.exists()) {
                log.warn("Classpath资源不存在: {}", resourcePath);
                return null;
            }

            try (InputStream inputStream = resource.getInputStream()) {
                return new String(inputStream.readAllBytes(), StandardCharsets.UTF_8);
            }
        } else {
            // 从文件系统读取
            File file = new File(configPath);
            if (!file.exists()) {
                log.warn("配置文件不存在: {}", configPath);
                return null;
            }

            return Files.readString(file.toPath(), StandardCharsets.UTF_8);
        }
    }

    /**
     * 创建缺失依赖处理组
     */
    private List<DependencyUpdateConfig.DependencyGroup> createMissingDepGroups() {
        log.info("开始创建缺失依赖处理组");

        // 1. 获取缺失依赖统计信息
        List<Dependency> allMissingDependencies = packageContextStore.getMissingDependencies(5000);

        log.info(String.format("发现缺失依赖: %d个", allMissingDependencies.size()));

        if (allMissingDependencies.isEmpty()) {
            log.info("缺失依赖表为空，跳过创建处理组");
            return new ArrayList<>();
        }

        log.info(String.format("获取到所有缺失依赖，总数量: %d", allMissingDependencies.size()));

        // 3. 检查哪些依赖已经在t_code_context表中存在，过滤掉已存在的依赖
        log.info("开始检查缺失依赖是否已在t_code_context表中存在...");
        List<Dependency> filteredMissingDependencies = filterExistingDependencies(allMissingDependencies);

        // 计算已存在的依赖（即被过滤掉的依赖）
        int existingCount = allMissingDependencies.size() - filteredMissingDependencies.size();
        log.info(String.format("依赖存在性检查完成: 总数=%d, 需要处理=%d, 已存在=%d",
                allMissingDependencies.size(), filteredMissingDependencies.size(), existingCount));

        // 对于已经存在的依赖，立即从missing表中清理
        if (existingCount > 0) {
            Set<String> filteredKeys = filteredMissingDependencies.stream()
                    .map(Dependency::getDependencyKey)
                    .collect(Collectors.toSet());

            List<Dependency> existingDependencies = allMissingDependencies.stream()
                    .filter(dep -> !filteredKeys.contains(dep.getDependencyKey()))
                    .collect(Collectors.toList());

            log.info(String.format("识别出已存在的依赖数量: %d", existingDependencies.size()));

            if (!existingDependencies.isEmpty()) {
                // 输出一些样例依赖信息用于调试
                if (existingDependencies.size() <= 5) {
                    existingDependencies.forEach(dep ->
                            log.info("已存在依赖样例: {}:{}:{}:{}", dep.getLanguage(), dep.getNamespace(), dep.getName(),
                                    dep.getVersion()));
                } else {
                    existingDependencies.stream().limit(3).forEach(dep ->
                            log.info("已存在依赖样例: {}:{}:{}:{}", dep.getLanguage(), dep.getNamespace(), dep.getName(),
                                    dep.getVersion()));
                    log.info("... 还有 {} 个已存在依赖", existingDependencies.size() - 3);
                }

                try {
                    int deletedCount = packageContextStore.markMissingDependenciesAsProcessed(existingDependencies);
                    log.info(String.format("成功删除已存在的missing dependency记录: %d 条", deletedCount));
                } catch (Exception e) {
                    log.error("删除已存在的missing dependency记录失败: " + e.getMessage(), e);
                }
            } else {
                log.warn("计算显示有 {} 个已存在依赖，但实际识别为0，可能存在逻辑问题", existingCount);
            }
        } else {
            log.info("所有缺失依赖都需要处理，没有已存在的依赖需要删除");
        }

        if (filteredMissingDependencies.isEmpty()) {
            log.info("所有缺失依赖都已在t_code_context表中存在，无需处理");
            return new ArrayList<>();
        }

        log.info(String.format("缺失依赖过滤完成: 总数=%d, 已存在=%d, 需要处理=%d",
                allMissingDependencies.size(), existingCount, filteredMissingDependencies.size()));

        // 4. 按语言和依赖类型分组，然后按200个依赖为一批创建多个DependencyGroup
        List<DependencyUpdateConfig.DependencyGroup> missingDepGroups = new ArrayList<>();
        final int MISSING_DEP_BATCH_SIZE = 200;

        Map<String, List<Dependency>> groupedDeps = filteredMissingDependencies.stream()
                .collect(Collectors.groupingBy(dep ->
                        dep.getLanguage().name() + ":" + dep.getDependencyType().name()));

        for (Map.Entry<String, List<Dependency>> entry : groupedDeps.entrySet()) {
            String groupKey = entry.getKey();
            List<Dependency> groupDeps = entry.getValue();

            log.info(String.format("创建缺失依赖组: %s, 依赖数量: %d", groupKey, groupDeps.size()));

            // 将依赖按批次大小分割，每200个创建一个组
            List<List<Dependency>> batches = partitionList(groupDeps, MISSING_DEP_BATCH_SIZE);

            for (int i = 0; i < batches.size(); i++) {
                List<Dependency> batchDeps = batches.get(i);
                DependencyUpdateConfig.DependencyGroup missingDepGroup = createVirtualGroupForMissingDependencies(batchDeps, i + 1);
                missingDepGroups.add(missingDepGroup);

                log.info(String.format("创建缺失依赖子组: %s, 批次: %d/%d, 依赖数量: %d",
                        groupKey, i + 1, batches.size(), batchDeps.size()));
            }
        }

        log.info(String.format("缺失依赖处理组创建完成，共创建 %d 个组", missingDepGroups.size()));
        return missingDepGroups;
    }

    /**
     * 为缺失依赖创建虚拟依赖组
     */
    private DependencyUpdateConfig.DependencyGroup createVirtualGroupForMissingDependencies(List<Dependency> dependencies, int batchNum) {
        if (dependencies.isEmpty()) {
            throw new IllegalArgumentException("依赖列表不能为空");
        }

        Dependency firstDep = dependencies.get(0);
        String groupName = String.format("MissingDep_Batch%d_%s_%s",
                batchNum,
                firstDep.getLanguage().name(),
                firstDep.getDependencyType().name());

        // 转换为DependencyItem列表
        List<DependencyUpdateConfig.DependencyItem> dependencyItems = dependencies.stream()
                .map(dep -> {
                    DependencyUpdateConfig.DependencyItem item = new DependencyUpdateConfig.DependencyItem();
                    item.setNamespace(dep.getNamespace());
                    item.setName(dep.getName());
                    item.setVersion(dep.getVersion());
                    return item;
                })
                .collect(Collectors.toList());

        DependencyUpdateConfig.DependencyGroup group = new DependencyUpdateConfig.DependencyGroup();
        group.setName(groupName);
        group.setLanguage(firstDep.getLanguage().name());
        group.setDependencyType(firstDep.getDependencyType().name());
        group.setDescription("自动生成的缺失依赖处理组 - 批次" + batchNum);
        group.setDependencies(dependencyItems);
        return group;
    }

    /**
     * 处理缺失依赖组列表
     * 复用processGroup方法，但在处理完成后立即清理missing表中的成功记录
     */
    private void processMissingDepGroups() {

        List<DependencyUpdateConfig.DependencyGroup> missingDepGroups = createMissingDepGroups();
        if (missingDepGroups.isEmpty()) {
            log.info("没有缺失依赖组需要处理");
            return;
        }

        log.info(String.format("开始处理缺失依赖组，总数: %d", missingDepGroups.size()));

        int processedGroups = 0;
        int successfulGroups = 0;
        int failedGroups = 0;

        for (DependencyUpdateConfig.DependencyGroup group : missingDepGroups) {
            try {
                log.info(String.format("开始处理缺失依赖组 %d/%d: %s",
                        processedGroups + 1, missingDepGroups.size(), group.getName()));

                // 调用processGroup方法处理缺失依赖组
                GroupProcessResult result = processGroup(group, true);
                successfulGroups++;
                log.info(String.format("缺失依赖组 %s 处理完成，成功: %d, 失败: %d",
                        group.getName(), result.getSuccessCount(), result.getFailedCount()));

            } catch (Exception e) {
                failedGroups++;
                log.error(String.format("缺失依赖组 %s 处理失败: %s", group.getName(), e.getMessage()), e);
                // 继续处理下一个组，不中断整个流程
            } finally {
                processedGroups++;
            }
        }

        log.info(String.format("缺失依赖组处理完成，总计: %d, 成功: %d, 失败: %d",
                processedGroups, successfulGroups, failedGroups));
    }


    /**
     * 处理依赖组 - 主要业务流程
     */
    private GroupProcessResult processGroup(DependencyUpdateConfig.DependencyGroup group, boolean isMissingDepGroup) throws Exception {
        long groupStartTime = System.currentTimeMillis();
        String groupPrefix = String.format("[DEP-GROUP-%s]", group.getName());
        
        log.info(String.format("开始处理依赖组: %s, 语言: %s, 类型: %s, 依赖数量: %d, 是否缺失依赖组: %s",
                group.getName(), group.getLanguage(), group.getDependencyType(),
                group.getDependencies() != null ? group.getDependencies().size() : 0, isMissingDepGroup));

        if (group.getDependencies() == null || group.getDependencies().isEmpty()) {
            log.info("依赖组 " + group.getName() + " 中没有依赖项，跳过处理");
            return new GroupProcessResult(group.getName(), "依赖组中没有依赖项");
        }

        // 解析语言和依赖类型
        LanguageType languageType = parseLanguageType(group.getLanguage());
        DependencyType dependencyType = parseDependencyType(group.getDependencyType());

        // 1. 读取更新dependency list - 解析依赖配置
        long stepStartTime = System.currentTimeMillis();
        List<Dependency> baseDependencies = parseDependenciesFromConfig(group.getDependencies(), languageType, dependencyType);
        long stepEndTime = System.currentTimeMillis();
        log.info("{} 步骤1-解析依赖配置 耗时: {}ms, 解析出{}个依赖", 
                groupPrefix, stepEndTime - stepStartTime, baseDependencies.size());
        
        if (baseDependencies.isEmpty()) {
            log.info("依赖组 " + group.getName() + " 解析后没有有效依赖，跳过处理");
            return new GroupProcessResult(group.getName(), "解析后没有有效依赖");
        }

        // 2. 分析要更新的依赖
        stepStartTime = System.currentTimeMillis();
        List<Dependency> dependenciesToProcess = determineVersionsToUpdate(baseDependencies);
        stepEndTime = System.currentTimeMillis();
        log.info("{} 步骤2-分析要更新的依赖 耗时: {}ms, 需要处理{}个依赖", 
                groupPrefix, stepEndTime - stepStartTime, dependenciesToProcess.size());

        // 初始化依赖生命周期跟踪
        stepStartTime = System.currentTimeMillis();
        initializeDependencyLifecycles(group, dependenciesToProcess);
        stepEndTime = System.currentTimeMillis();
        log.info("{} 步骤3-初始化生命周期跟踪 耗时: {}ms", groupPrefix, stepEndTime - stepStartTime);

        // 3. 创建组处理上下文，用于收集失败信息
        GroupProcessContext groupContext = new GroupProcessContext();

        // 4. 批量处理基础依赖
        stepStartTime = System.currentTimeMillis();
        processBaseDependenciesInBatches(dependenciesToProcess, dependencyType, groupContext);
        stepEndTime = System.currentTimeMillis();
        log.info("{} 步骤4-批量处理依赖 耗时: {}ms", groupPrefix, stepEndTime - stepStartTime);

        // 5. 根据生命周期状态确定成功和失败的依赖
        stepStartTime = System.currentTimeMillis();
        List<Dependency> successfulDeps = new ArrayList<>();
        List<Dependency> failedDeps = new ArrayList<>();

        for (Dependency dep : dependenciesToProcess) {
            String depKey = dep.getDependencyKey();
            DependencyLifecycle lifecycle = dependencyLifecycles.get(depKey);
            if (lifecycle != null) {
                if (lifecycle.getCurrentState() == LifecycleState.COMPLETED) {
                    successfulDeps.add(dep);
                } else if (lifecycle.getCurrentState() == LifecycleState.FAILED) {
                    failedDeps.add(dep);
                }
            }
        }
        stepEndTime = System.currentTimeMillis();
        log.info("{} 步骤5-统计处理结果 耗时: {}ms, 成功{}个, 失败{}个", 
                groupPrefix, stepEndTime - stepStartTime, successfulDeps.size(), failedDeps.size());
        
        // 6. 如果是缺失依赖组，统一处理所有missing dependency记录
        if (isMissingDepGroup) {
            stepStartTime = System.currentTimeMillis();
            handleMissingGroupAfterUpdate(groupContext, successfulDeps, failedDeps);
            stepEndTime = System.currentTimeMillis();
            log.info("{} 步骤6-处理缺失依赖记录 耗时: {}ms", groupPrefix, stepEndTime - stepStartTime);
        }

        GroupProcessResult result = new GroupProcessResult(group.getName(), successfulDeps, failedDeps, dependenciesToProcess.size());

        long groupEndTime = System.currentTimeMillis();
        log.info("{} 依赖组处理完成，总耗时: {}ms, 成功: {}, 失败: {}, 总计: {}",
                groupPrefix, groupEndTime - groupStartTime, successfulDeps.size(), failedDeps.size(), dependenciesToProcess.size());

        return result;
    }

    /**
     * 从配置解析依赖列表
     */
    private List<Dependency> parseDependenciesFromConfig(List<DependencyUpdateConfig.DependencyItem> items,
                                                         LanguageType language, DependencyType dependencyType) {
        log.info("开始从配置解析依赖，数量: " + items.size());

        List<Dependency> dependencies = items.stream()
                .map(item -> {
                    if (item.getNamespace() == null || item.getName() == null) {
                        log.info("依赖信息不完整，跳过: " + item.getNamespace() + ":" + item.getName());
                        return null;
                    }

                    return Dependency.builder()
                            .namespace(item.getNamespace())
                            .name(item.getName())
                            .version(item.getVersion()) // 可能为null，表示获取所有版本
                            .language(language)
                            .dependencyType(dependencyType)
                            .build();
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        log.info("配置解析完成，有效依赖数量: " + dependencies.size());
        return dependencies;
    }

    /**
     * 初始化依赖生命周期跟踪（基于版本粒度）
     * 修正：使用 getDependencyKey() 管理版本级别的生命周期状态
     */
    private void initializeDependencyLifecycles(DependencyUpdateConfig.DependencyGroup group,
                                                List<Dependency> dependencies) {
        for (Dependency dep : dependencies) {
            // 使用包含版本的完整key来跟踪每个版本的生命周期
            String versionedDependencyKey = dep.getDependencyKey();
            dependencyLifecycles.put(versionedDependencyKey, new DependencyLifecycle(
                    versionedDependencyKey, dep.getNamespace(), dep.getName(), group.getLanguage(),
                    group.getDependencyType(), LocalDateTime.now()));
        }
    }

    /**
     * 确定需要更新的版本，所有返回的版本都覆盖更新
     * 1. snapshot版本：获得SNAPSHOT对应最新版本号，塞入snapshot version中，默认强制更新
     * 2. 指定版本: 默认强制更新
     * 3. 空版本: 强制更新时，获取所有数据库中的版本，以及远端版本的最近N个，都更新。
     * 增量更新时，获取数据库最新版本，更新与远端版本的增量更新。
     */
    private List<Dependency> determineVersionsToUpdate(List<Dependency> baseDependencies) throws Exception {
        long methodStartTime = System.currentTimeMillis();
        log.info("[VERSION-DETERMINE] 开始确定需要更新的版本，基础依赖数量: " + baseDependencies.size());

        Map<String, Dependency> allDependenciesToProcess = new HashMap<>();
        
        int generalCount = 0;
        int specifiedCount = 0;

        for (Dependency dependency : baseDependencies) {
            long stepStartTime = System.currentTimeMillis();
            // 未指定版本
            if (dependency.isGeneral()) {
                List<Dependency> dependencyList = handleGeneralDependency(dependency);
                dependencyList.stream().filter(Objects::nonNull).forEach(dep -> allDependenciesToProcess.put(dep.getDependencyKey(), dep));
                generalCount++;
                long stepEndTime = System.currentTimeMillis();
                log.info("[VERSION-DETERMINE] 处理未指定版本依赖[{}:{}] 耗时: {}ms, 生成{}个版本", 
                        dependency.getNamespace(), dependency.getName(), stepEndTime - stepStartTime, dependencyList.size());
            } else {
                Dependency dep = handleSpecifiedVersionDependency(dependency);
                if (dep != null) {
                    allDependenciesToProcess.put(dep.getDependencyKey(), dep);
                }
                specifiedCount++;
                long stepEndTime = System.currentTimeMillis();
                log.info("[VERSION-DETERMINE] 处理指定版本依赖[{}:{}:{}] 耗时: {}ms", 
                        dependency.getNamespace(), dependency.getName(), dependency.getVersion(), stepEndTime - stepStartTime);
            }
        }

        long methodEndTime = System.currentTimeMillis();
        log.info("[VERSION-DETERMINE] 版本确定完成，总耗时: {}ms, 处理了{}个未指定版本依赖和{}个指定版本依赖，最终需要处理的依赖版本数量: {}", 
                methodEndTime - methodStartTime, generalCount, specifiedCount, allDependenciesToProcess.size());
        return new ArrayList<>(allDependenciesToProcess.values());
    }

    /**
     * 处理指定版本的依赖, 包括snapshot依赖
     */
    private Dependency handleSpecifiedVersionDependency(Dependency dep) {
        log.info("开始处理指定版本依赖");

        String key = dep.getDependencyKey();
        updateLifecycleState(key, LifecycleState.RESOLVING_VERSIONS, "处理指定版本: " + dep.getVersion());


        // 对指定版本的依赖，直接解析版本（处理SNAPSHOT等特殊版本）
        Dependency dependency = resolveSpecifiedVersion(dep);
        return dependency;
    }

    /**
     * 强制更新时，获取所有数据库中的版本，以及远端版本的最近N个，都更新。
     * 增量更新时，获取数据库最新版本，更新与远端版本的增量更新。
     * 修正：只对具体版本创建生命周期管理，不对通用依赖创建
     */
    private List<Dependency> handleGeneralDependency(Dependency generalDep) {
        log.info("开始处理未指定版本依赖，根据forceRefresh参数判断更新策略: " +
                (config.isForceRefresh() ? "强制刷新" : "增量更新"));

        List<Dependency> allDeps = new ArrayList<>();
        
        // 注意：这里不为通用依赖创建生命周期，只在确定具体版本后创建

        try {
            // 2.2 从远端获取所有可用版本和最新版本信息
            VersionInfo versionInfo = getAllReleaseVersions(generalDep);
            List<String> allRemoteVersions = versionInfo.getVersions();
            String remoteLatestVersion = versionInfo.getLatestVersion();

            if (allRemoteVersions.isEmpty()) {
                log.info("无法获取远端版本列表: {}:{}", generalDep.getNamespace(), generalDep.getName());
                return new ArrayList<>();
            }

            List<String> versionsToUpdate;

            if (config.isForceRefresh()) {
                // 强制刷新：获取最近N个版本, 和数据库所有版本合并
                int coldStartVersionCount = config.getColdStartVersionCount() > 0 ? config.getColdStartVersionCount() : 1;

                // 获取远端最近N个版本
                List<String> remoteRecentVersions = allRemoteVersions.stream()
                        .limit(coldStartVersionCount)
                        .collect(Collectors.toList());

                // 获取数据库中的所有版本
//                List<String> databaseAllVersions = packageContextStore.getAllVersions(generalDep);
                List<String> databaseAllVersions = new ArrayList<>();
                // 合并去重：使用LinkedHashSet保持顺序，优先保留远端版本的顺序
                Set<String> mergedVersions = new LinkedHashSet<>();
                mergedVersions.addAll(remoteRecentVersions); // 先添加远端版本
                mergedVersions.addAll(databaseAllVersions);   // 再添加数据库版本（自动去重）

                versionsToUpdate = new ArrayList<>(mergedVersions);

                log.info(String.format(
                        "强制刷新: %s, 远端最近 %d 个版本: %s, 数据库已有 %d 个版本: %s, 合并后共 %d 个版本需要更新: %s",
                        generalDep.getDependencySignature(), coldStartVersionCount, remoteRecentVersions, databaseAllVersions.size(),
                        databaseAllVersions, versionsToUpdate.size(), versionsToUpdate));
            } else {
                // 增量更新：从数据库最新版本开始，获取gap版本
                String latestVersionInDB = getLatestVersionFromDatabase(generalDep);
                versionsToUpdate = determineIncrementalVersionsToUpdate(generalDep, latestVersionInDB, allRemoteVersions);
            }

            if (versionsToUpdate.isEmpty()) {
                log.info("没有需要更新的版本: {}:{}", generalDep.getNamespace(), generalDep.getName());
                return new ArrayList<>();
            }

            // 创建具体版本的依赖对象，并设置isLatest标记
            // 使用远程仓库确定的最新版本，而不是字符串比较
            List<Dependency> versionedDependencies = versionsToUpdate.stream()
                    .map(version -> Dependency.builder()
                            .namespace(generalDep.getNamespace())
                            .name(generalDep.getName())
                            .version(version)
                            .language(generalDep.getLanguage())
                            .dependencyType(generalDep.getDependencyType())
                            .isLatest(version.equals(remoteLatestVersion)) // 使用远程确定的最新版本
                            .build())
                    .collect(Collectors.toList());

            allDeps.addAll(versionedDependencies);

        } catch (Exception e) {
            log.error("处理依赖失败: {}:{}, 错误: {}", generalDep.getNamespace(), generalDep.getName(), e.getMessage());
        }

        log.info("未指定版本依赖处理完成，需要处理的版本数量: " + allDeps.size());
        return allDeps;
    }

    /**
     * 确定增量更新的版本列表
     *
     * @param baseDep           基础依赖信息
     * @param latestVersionInDB 数据库中的最新版本，可能为null
     * @param allRemoteVersions 远端所有版本列表（已按从新到旧排序）
     * @return 需要更新的版本列表
     */
    private List<String> determineIncrementalVersionsToUpdate(Dependency baseDep, String latestVersionInDB,
                                                              List<String> allRemoteVersions) {
        String key = baseDep.getDependencyKey();

        if (allRemoteVersions.isEmpty()) {
            log.info("远端版本列表为空: " + key);
            return new ArrayList<>();
        }

        // 远端版本列表已经是降序排列（从新到旧），无需再次排序
        List<String> versionsToUpdate;
        int coldStartVersionCount = config.getColdStartVersionCount() > 0 ? config.getColdStartVersionCount() : 1;

        if (latestVersionInDB == null) {
            // 数据库中没有该依赖，但不是强制刷新模式，获取最近N个版本（冷启动）
            versionsToUpdate = allRemoteVersions.stream()
                    .limit(coldStartVersionCount)
                    .collect(Collectors.toList());

            log.info(String.format("增量更新模式（数据库无记录）: %s, 获取最近 %d 个版本: %s",
                    key, coldStartVersionCount, versionsToUpdate));
        } else {
            // 增量更新：从数据库最新版本开始，获取更新的版本
            int latestIndex = allRemoteVersions.indexOf(latestVersionInDB);

            if (latestIndex == -1) {
                // 数据库中的版本在远端版本列表中找不到，可能是远端版本被删除了
                // 这种情况下，获取最新几个远端版本
                log.info(String.format("数据库版本在远端找不到: %s, 版本: %s, 将获取最%d个远端版本",
                        key, latestVersionInDB, coldStartVersionCount));
                versionsToUpdate = allRemoteVersions.stream().limit(coldStartVersionCount).collect(Collectors.toList());
            } else if (latestIndex == 0) {
                // 数据库已经是最新版本
                versionsToUpdate = new ArrayList<>();
                log.info(String.format("数据库已是最新版本: %s, 版本: %s", key, latestVersionInDB));
            } else {
                // 获取比数据库版本更新的所有版本
                versionsToUpdate = allRemoteVersions.subList(0, latestIndex);
                log.info(String.format("增量更新模式: %s, 数据库版本: %s, 需要更新 %d 个版本: %s",
                        key, latestVersionInDB, versionsToUpdate.size(), versionsToUpdate));
            }
        }

        return versionsToUpdate;
    }

    /**
     * 从数据库获取依赖的最新版本
     */
    private String getLatestVersionFromDatabase(Dependency dependency) {
        try {
            log.info(String.format("查询数据库中的最新版本: %s:%s", dependency.getNamespace(), dependency.getName()));

            String latestVersion = packageContextStore.getLatestVersion(dependency);

            if (latestVersion != null) {
                log.info(String.format("数据库中的最新版本: %s:%s -> %s",
                        dependency.getNamespace(), dependency.getName(), latestVersion));
                return latestVersion;
            } else {
                log.info(String.format("数据库中未找到依赖: %s:%s", dependency.getNamespace(), dependency.getName()));
                return null;
            }
        } catch (Exception e) {
            log.error(String.format("查询数据库最新版本失败: %s:%s, 错误: %s",
                    dependency.getNamespace(), dependency.getName(), e.getMessage()));
            return null;
        }
    }

    /**
     * 解析指定版本的依赖（处理SNAPSHOT等特殊版本）
     */
    private Dependency resolveSpecifiedVersion(Dependency dep) {

        DependencyFetcher fetcher = dependencyFetcherFactory.getFetcher(dep.getDependencyType()).orElse(null);
        if (fetcher == null) {
            log.info("未找到支持依赖类型 " + dep.getDependencyType() + " 的 fetcher");
            return null;
        }
        return fetcher.resolveVersion(dep);
    }

    /**
     * 过滤已存在的依赖，只保留不存在的依赖
     */
    private List<Dependency> filterExistingDependencies(List<Dependency> allDependencies) {
        if (allDependencies.isEmpty()) {
            return allDependencies;
        }

        log.info("开始检查依赖存在性，依赖数量: " + allDependencies.size());

        // 批量检查依赖是否存在
        Map<String, Boolean> existenceMap = packageContextStore.batchCheckDependencyExist(allDependencies);

        // 过滤出不存在的依赖
        List<Dependency> filteredDeps = allDependencies.stream()
                .filter(dep -> !existenceMap.getOrDefault(dep.getDependencyKey(), false))
                .collect(Collectors.toList());

        int existingCount = allDependencies.size() - filteredDeps.size();
        log.info("依赖过滤完成，已存在: {}, 需要处理: {}", existingCount, filteredDeps.size());
        return filteredDeps;
    }



    /**
     * 缺失依赖组更新后的统一处理
     * 负责处理所有缺失依赖组的missing dependency记录更新
     */
    private void handleMissingGroupAfterUpdate(GroupProcessContext groupContext, 
                                             List<Dependency> successfulDeps, 
                                             List<Dependency> failedDeps) {
        log.info("开始处理缺失依赖组更新后的记录，成功: {}, 失败: {}, 上下文失败信息: {}", 
                successfulDeps.size(), failedDeps.size(), groupContext.hasFailures());

        // 1. 处理成功处理的依赖 - 删除missing记录
        if (!successfulDeps.isEmpty()) {
            try {
                int deletedCount = packageContextStore.markMissingDependenciesAsProcessed(successfulDeps);
                log.info("缺失依赖组：删除成功处理的missing记录 {} 个", deletedCount);
            } catch (Exception e) {
                log.error("删除成功处理的missing记录失败: {}", e.getMessage(), e);
            }
        }

        // 2. 处理缺失source.jar的依赖 - 标记为skip但保留记录
        List<Dependency> missingSourceJarDeps = groupContext.getMissingSourceJarDeps();
        if (!missingSourceJarDeps.isEmpty()) {
            // 更新生命周期状态
            for (Dependency dep : missingSourceJarDeps) {
                updateLifecycleState(dep.getDependencyKey(), LifecycleState.SKIPPED, "跳过: 找不到source.jar");
            }
            
            // 在missing表中标记为跳过
            try {
                int updatedCount = packageContextStore.markMissingDependenciesAsSkipped(
                        missingSourceJarDeps, "找不到source.jar文件");
                log.info("缺失依赖组：标记 {} 个依赖为跳过（精确到版本），更新missing记录: {} 条",
                        missingSourceJarDeps.size(), updatedCount);
            } catch (Exception e) {
                log.error("标记missing dependency为跳过失败: {}", e.getMessage(), e);
            }
        }

        // 3. 处理其他类型失败的依赖 - 不做处理
        List<Dependency> otherFailedDeps = groupContext.getOtherFailedDeps();
        if (!otherFailedDeps.isEmpty()) {
            // 更新生命周期状态
            for (Dependency dep : otherFailedDeps) {
                updateLifecycleState(dep.getDependencyKey(), LifecycleState.FAILED, "获取制品失败");
            }
            
            // 不做处理
        }
    }

    /**
     * 批量处理基础依赖（版本解析在批次内进行）
     * 按指定的批次大小处理基础依赖，每个批次内进行版本解析
     */
    private void processBaseDependenciesInBatches(List<Dependency> baseDependencies, DependencyType dependencyType, GroupProcessContext groupContext) {
        if (baseDependencies.isEmpty()) {
            log.info("没有需要处理的基础依赖，跳过批量处理");
            return;
        }

        List<List<Dependency>> batches = partitionList(baseDependencies, DEFAULT_BATCH_SIZE);

        log.info(String.format("开始批量处理基础依赖，总批次数: %d, 批处理大小: %d", batches.size(), DEFAULT_BATCH_SIZE));

        for (int i = 0; i < batches.size(); i++) {
            List<Dependency> baseBatch = batches.get(i);
            log.info(String.format("开始处理第 %d/%d 批次，本批次基础依赖数量: %d", i + 1, batches.size(), baseBatch.size()));

            try {
                // 处理这批依赖
                processBatch(baseBatch, dependencyType, i + 1, batches.size(), groupContext);

            } catch (Exception e) {
                log.error(String.format("批次 %d/%d 处理失败: %s", i + 1, batches.size(), e.getMessage()), e);
                // 继续处理下一批
            }

            log.info(String.format("完成第 %d/%d 批次处理", i + 1, batches.size()));
        }

        log.info("所有批次处理完成");
    }

    /**
     * 处理单个批次
     */
    private void processBatch(List<Dependency> batch, DependencyType dependencyType,
                              int currentBatch, int totalBatches, GroupProcessContext groupContext) throws Exception {
        String batchPrefix = String.format("[BATCH-%d/%d]", currentBatch, totalBatches);
        long batchStartTime = System.currentTimeMillis();

        // 创建FetchContext来收集失败信息
        FetchContext fetchContext = new FetchContext();

        // 1. 获取制品
        long stepStartTime = System.currentTimeMillis();
        List<Artifact> artifacts = fetchArtifacts(batch, dependencyType, fetchContext);
        long stepEndTime = System.currentTimeMillis();
        log.info("{} 子步骤1-获取制品 耗时: {}ms, 输入{}个依赖, 获取{}个制品", 
                batchPrefix, stepEndTime - stepStartTime, batch.size(), artifacts.size());

        // 处理失败信息，转换到groupContext中
        stepStartTime = System.currentTimeMillis();
        groupContext.collectFetchFailures(fetchContext);
        stepEndTime = System.currentTimeMillis();
        log.info("{} 子步骤2-收集失败信息 耗时: {}ms", batchPrefix, stepEndTime - stepStartTime);

        // 3. 构建知识
        stepStartTime = System.currentTimeMillis();
        List<DependencyPackageContext> dependencyPackageContexts = buildDependencyContext(artifacts);
        stepEndTime = System.currentTimeMillis();
        log.info("{} 子步骤3-构建知识上下文 耗时: {}ms, 输入{}个制品, 构建{}个上下文", 
                batchPrefix, stepEndTime - stepStartTime, artifacts.size(), dependencyPackageContexts.size());

        // 4. 保存依赖包上下文
        stepStartTime = System.currentTimeMillis();
        saveAllDependencyPackageContexts(dependencyPackageContexts, currentBatch, totalBatches);
        stepEndTime = System.currentTimeMillis();
        log.info("{} 子步骤4-保存依赖包上下文 耗时: {}ms", batchPrefix, stepEndTime - stepStartTime);

        // 5. 更新生命周期状态和统计
        stepStartTime = System.currentTimeMillis();
        updateBatchResults(batch, dependencyPackageContexts);
        stepEndTime = System.currentTimeMillis();
        log.info("{} 子步骤5-更新生命周期状态 耗时: {}ms", batchPrefix, stepEndTime - stepStartTime);

        long batchEndTime = System.currentTimeMillis();
        log.info("{} 批次处理完成，总耗时: {}ms", batchPrefix, batchEndTime - batchStartTime);
    }

    /**
     * 保存所有依赖包上下文
     */
    private void saveAllDependencyPackageContexts(List<DependencyPackageContext> dependencyPackageContexts, 
                                                  int currentBatch, int totalBatches) {
        for (DependencyPackageContext dependencyPackageContext : dependencyPackageContexts) {
            String dependencyKey = dependencyPackageContext.getDependency().getDependencyKey();
            List<PackageContextItem> packageContextItems = dependencyPackageContext.getPackageContextItems();

            if (packageContextItems != null && !packageContextItems.isEmpty()) {
                log.info("批次 {}/{}: 开始保存 {} 的知识，数量: {}",
                        currentBatch, totalBatches, dependencyKey, packageContextItems.size());

                boolean saveResult = saveDependencyPackageContext(dependencyPackageContext);

                if (saveResult) {
                    log.info("批次 {}/{}: {} 保存成功", currentBatch, totalBatches, dependencyKey);
                } else {
                    log.warn("批次 {}/{}: {} 保存失败", currentBatch, totalBatches, dependencyKey);
                }
            } else {
                log.info("批次 {}/{}: {} 没有生成知识", currentBatch, totalBatches, dependencyKey);
            }
        }
    }

    /**
     * 原子性保存知识并更新is_latest标记
     * 确保单个dependency版本的所有知识项要么全部成功，要么全部失败
     * 同时确保新版本的is_latest=1，其他版本的is_latest=0
     *
     * @return 是否保存成功
     */
    private boolean saveDependencyPackageContext(DependencyPackageContext dependencyPackageContext) {
        try {
            // 使用事务性方法保存知识并更新is_latest
            int savedCount = packageContextStore.saveDependencyPackageContext(dependencyPackageContext);

            if (savedCount > 0) {
                log.info("原子性保存成功: 知识 {} 条，已更新is_latest字段", savedCount);
                return true;
            } else {
                log.info("原子性保存失败: 没有知识项被保存");
                return false;
            }

        } catch (Exception e) {
            log.error("原子性保存知识并更新is_latest失败: " + e.getMessage());
            return false;
        }
    }

    /**
     * 更新批次结果
     */
    private void updateBatchResults(List<Dependency> batch,
                                    List<DependencyPackageContext> dependencyPackageContexts) {
        Map<String, DependencyPackageContext> dependencyPackageContextMap = dependencyPackageContexts.stream()
                .collect(Collectors.toMap(
                        context -> context.getDependency().getDependencyKey(),
                        Function.identity()
                ));

        // 检查每个batch是否正确生成了dependencyPackageContexts
        for (Dependency dep : batch) {
            String depKey = dep.getDependencyKey();
            processedCount.incrementAndGet();

            DependencyPackageContext dependencyPackageContext = dependencyPackageContextMap.get(depKey);
            if (dependencyPackageContext != null) {
                successCount.incrementAndGet();
                updateLifecycleState(depKey, LifecycleState.COMPLETED,
                        String.format("版本 %s 处理完成，生成 %d 个知识项", dependencyPackageContext.getDependency().getVersion(),
                                dependencyPackageContext.getPackageContextItems().size()));
            } else {
                DependencyLifecycle lifecycle = dependencyLifecycles.get(depKey);
                if (lifecycle != null && lifecycle.getCurrentState() == LifecycleState.SKIPPED) {
                    skippedCount.incrementAndGet();
                } else if (lifecycle != null && lifecycle.getCurrentState() == LifecycleState.FAILED) {
                    failedCount.incrementAndGet();
                } else {
                    // 其他情况，标记为失败（如果生命周期状态还没有被设置）
                    failedCount.incrementAndGet();
                    if (lifecycle != null) {
                        updateLifecycleState(depKey, LifecycleState.FAILED, "最终处理失败");
                    }
                }
            }
        }
    }

    /**
     * 更新依赖生命周期状态
     */
    private void updateLifecycleState(String dependencyKey, LifecycleState state, String message) {
        DependencyLifecycle lifecycle = dependencyLifecycles.get(dependencyKey);
        if (lifecycle != null) {
            lifecycle.updateState(state, message);
        }
    }

    /**
     * 生成最终报告
     */
    private void generateFinalReport() {
        try {
            LocalDateTime endTime = LocalDateTime.now();
            long durationMinutes = java.time.Duration.between(runStartTime, endTime).toMinutes();

            // 创建报告对象
            RunnerReport report = RunnerReport.builder()
                    .runId(runId)
                    .startTime(runStartTime)
                    .endTime(endTime)
                    .durationMinutes(durationMinutes)
                    .totalDependencies(dependencyLifecycles.size())
                    .processedCount(processedCount.get())
                    .successCount(successCount.get())
                    .failedCount(failedCount.get())
                    .skippedCount(skippedCount.get())
                    .configFile(config.getConfigFile())
                    .forceRefresh(config.isForceRefresh())
                    .dependencyLifecycles(new ArrayList<>(dependencyLifecycles.values()))
                    .build();

            // 生成 JSON 报告
            String reportFile = logDir + "/final_report.json";
            objectMapper.writerWithDefaultPrettyPrinter().writeValue(new File(reportFile), report);

            // 生成简化的文本报告
            generateTextReport(report);

            log.info("最终报告已生成: {}", reportFile);
            log.info("运行时长: " + durationMinutes + " 分钟");
            log.info(String.format("处理统计: 总计=%d, 成功=%d, 失败=%d, 跳过=%d",
                    processedCount.get(), successCount.get(), failedCount.get(), skippedCount.get()));
            log.info("报告文件: " + reportFile);

        } catch (Exception e) {
            log.error("生成最终报告失败", e);
        }
    }

    /**
     * 生成文本格式报告
     */
    private void generateTextReport(RunnerReport report) throws IOException {
        String reportFile = logDir + "/final_report.txt";

        try (FileWriter writer = new FileWriter(reportFile)) {
            writer.write("========== 依赖更新 Runner 执行报告 ==========\n");
            writer.write(String.format("运行ID: %s\n", report.getRunId()));
            writer.write(String.format("开始时间: %s\n", report.getStartTime().format(DATETIME_FORMATTER)));
            writer.write(String.format("结束时间: %s\n", report.getEndTime().format(DATETIME_FORMATTER)));
            writer.write(String.format("运行时长: %d 分钟\n", report.getDurationMinutes()));
            writer.write(String.format("配置文件: %s\n", report.getConfigFile()));
            writer.write(String.format("强制刷新: %s\n", report.isForceRefresh()));
            writer.write("\n");

            writer.write("========== 处理统计 ==========\n");
            writer.write(String.format("总依赖数: %d\n", report.getTotalDependencies()));
            writer.write(String.format("已处理: %d\n", report.getProcessedCount()));
            writer.write(String.format("成功: %d\n", report.getSuccessCount()));
            writer.write(String.format("失败: %d\n", report.getFailedCount()));
            writer.write(String.format("跳过: %d\n", report.getSkippedCount()));
            writer.write(String.format("成功率: %.2f%%\n", report.getSuccessRate()));
            writer.write("\n");

            // 按状态分组输出依赖列表
            Map<LifecycleState, List<DependencyLifecycle>> groupedByState =
                    report.getDependencyLifecycles().stream()
                            .collect(Collectors.groupingBy(DependencyLifecycle::getCurrentState));

            for (LifecycleState state : LifecycleState.values()) {
                List<DependencyLifecycle> deps = groupedByState.get(state);
                if (deps != null && !deps.isEmpty()) {
                    writer.write(String.format("========== %s (%d) ==========\n", state.getDisplayName(), deps.size()));
                    for (DependencyLifecycle dep : deps) {
                        writer.write(String.format("%s - %s\n", dep.getDependencyKey(), dep.getCurrentMessage()));
                    }
                    writer.write("\n");
                }
            }
        }
    }

    // ============ 工具方法 ============

    private LanguageType parseLanguageType(String language) {
        try {
            return LanguageType.valueOf(language.toUpperCase());
        } catch (Exception e) {
            log.info("未知的语言类型: " + language + ", 使用默认值 JAVA");
            return LanguageType.JAVA;
        }
    }

    private DependencyType parseDependencyType(String type) {
        try {
            return DependencyType.valueOf(type.toUpperCase());
        } catch (Exception e) {
            log.info("未知的依赖类型: " + type + ", 使用默认值 UNKNOWN");
            return DependencyType.UNKNOWN;
        }
    }

    private <T> List<List<T>> partitionList(List<T> list, int batchSize) {
        List<List<T>> batches = new ArrayList<>();
        for (int i = 0; i < list.size(); i += batchSize) {
            batches.add(list.subList(i, Math.min(i + batchSize, list.size())));
        }
        return batches;
    }

    /**
     * 从远端获取所有可用版本，并确定最新版本
     */
    private VersionInfo getAllReleaseVersions(Dependency dependency) throws Exception {
        log.info(String.format("获取远端所有版本: %s", dependency.getDependencySignature()));

        DependencyFetcher fetcher = dependencyFetcherFactory.getFetcher(dependency.getDependencyType()).orElse(null);
        if (fetcher == null) {
            throw new RuntimeException("未找到支持的DependencyFetcher: " + dependency.getDependencyType());
        }

        try {
            Map<String, List<String>> versionsMap = fetcher.getAllReleaseVersions(List.of(dependency));
            String signature = dependency.getDependencySignature();
            List<String> versions = versionsMap.get(signature);

            if (versions == null || versions.isEmpty()) {
                log.info(String.format("远端版本列表为空: %s:%s", dependency.getNamespace(), dependency.getName()));
                return new VersionInfo(new ArrayList<>(), null);
            }

            // DependencyFetcher返回的版本列表已经是按照发布时间排序的（从新到旧）
            // 第一个版本就是最新版本
            String latestVersion = versions.get(0);

            log.info(String.format("远端版本获取成功: %s:%s -> %d 个版本，最新版本: %s",
                    dependency.getNamespace(), dependency.getName(), versions.size(), latestVersion));

            return new VersionInfo(versions, latestVersion);
        } catch (Exception e) {
            log.error(String.format("获取远端版本失败: %s:%s, 错误: %s",
                    dependency.getNamespace(), dependency.getName(), e.getMessage()));
            throw e;
        }
    }

    /**
     * 获取依赖制品
     * 
     * @param dependencies 依赖列表
     * @param dependencyType 依赖类型
     * @param fetchContext 用于存储失败信息的上下文，可为null
     * @return 成功获取的制品列表
     * @throws Exception 当无法找到合适的fetcher时抛出异常
     */
    private List<Artifact> fetchArtifacts(List<Dependency> dependencies, DependencyType dependencyType, 
                                        FetchContext fetchContext) throws Exception {
        log.info("开始获取制品，依赖数量: {}", dependencies.size());

        Optional<DependencyFetcher> fetcherOpt = dependencyFetcherFactory.getFetcher(dependencyType);
        if (fetcherOpt.isEmpty()) {
            String errorMsg = "未找到支持的DependencyFetcher: " + dependencyType.name();
            // 记录所有依赖都失败了
            if (fetchContext != null) {
                dependencies.forEach(dep -> 
                    fetchContext.addFailure(dep.getDependencyKey(), errorMsg));
            }
            throw new RuntimeException(errorMsg);
        }

        DependencyFetcher fetcher = fetcherOpt.get();
        List<Artifact> successfulArtifacts = new ArrayList<>();
        
        try {
            // 调用批量获取制品
            List<Artifact> artifacts = fetcher.batchFetcherArtifact(dependencies);
            successfulArtifacts.addAll(artifacts);
            
            // 检查哪些依赖没有成功获取制品，记录失败信息
            if (fetchContext != null) {
                Set<String> successfulDependencyKeys = artifacts.stream()
                    .map(artifact -> artifact.getDependency().getDependencyKey())
                    .collect(Collectors.toSet());
                
                dependencies.stream()
                    .filter(dep -> !successfulDependencyKeys.contains(dep.getDependencyKey()))
                    .forEach(failedDep -> {
                        // 根据异常信息判断是否是找不到source jar
                        String reason = "制品获取失败，可能原因：source.jar文件不存在或下载失败";
                        FailureType failureType = FailureType.MISSING_SOURCE_JAR;
                        
                        fetchContext.addFailure(failedDep, failureType, reason);
                        log.warn("依赖制品获取失败: {}, 类型: {}, 原因: {}", 
                            failedDep.getDependencyKey(), failureType, reason);
                    });
            }

            log.info("制品获取完成，请求: {} 个，成功获取: {} 个，失败: {} 个", 
                dependencies.size(), successfulArtifacts.size(), 
                dependencies.size() - successfulArtifacts.size());
            
            return successfulArtifacts;

        } catch (Exception e) {
            log.error("批量获取制品过程中发生异常: " + e.getMessage(), e);
            
            // 记录所有依赖都失败了
            if (fetchContext != null) {
                dependencies.forEach(dep -> 
                    fetchContext.addFailure(dep, FailureType.OTHER, "批量获取制品异常: " + e.getMessage(), e));
            }
            
            throw e;
        }
    }

    /**
     * 构建依赖包上下文
     */
    private List<DependencyPackageContext> buildDependencyContext(List<Artifact> artifacts) throws Exception {
        log.info("开始构建依赖包上下文，制品数量: {}", artifacts.size());

        List<DependencyPackageContext> results = new ArrayList<>();

        for (Artifact artifact : artifacts) {
            try {
                Optional<PackageContextBuilder> builderOpt =
                        packageContextBuilderFactory.getBuilder(artifact.getDependency().getLanguage());
                if (builderOpt.isEmpty()) {
                    log.warn("未找到支持语言 {} 的 PackageContextBuilder", artifact.getDependency().getLanguage());
                    continue;
                }

                PackageContextBuilder builder = builderOpt.get();
                PackageContextBuildResult buildResult = builder.buildPackageContextWithDetails(artifact);

                if (buildResult.getHasSuccessItems()) {
                    DependencyPackageContext dependencyPackageContext = DependencyPackageContext.builder()
                            .dependency(artifact.getDependency())
                            .packageContextItems(buildResult.getSuccessItems())
                            .isLatest(artifact.getDependency().isLatest())
                            .build();

                    results.add(dependencyPackageContext);
                    log.info("成功构建依赖包上下文: {}, 知识项数量: {}",
                            artifact.getDependency().getDependencyKey(), buildResult.getSuccessItems().size());
                } else {
                    log.warn("构建依赖包上下文失败，无有效知识项: {}", artifact.getDependency().getDependencyKey());
                }

            } catch (Exception e) {
                log.error("构建依赖包上下文异常: {}, 错误: {}", artifact.getDependency().getDependencyKey(), e.getMessage());
            }
        }

        log.info("依赖包上下文构建完成，成功构建: {} 个", results.size());
        return results;
    }

} 