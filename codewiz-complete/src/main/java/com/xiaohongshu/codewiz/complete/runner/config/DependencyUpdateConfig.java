package com.xiaohongshu.codewiz.complete.runner.config;

import java.util.List;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import lombok.Data;

/**
 * 依赖更新 Runner 配置
 */
@Data
@Component
@ConfigurationProperties(prefix = "codewiz.dependency-update.runner")
public class DependencyUpdateConfig {
    
    /**
     * 是否启用依赖更新 Runner
     * 默认为 false，避免在启动主应用时自动执行
     */
    private boolean enabled = false;
    
    /**
     * 配置文件路径
     * 默认使用 classpath 下的配置文件
     */
    private String configFile = "classpath:dependency-update-config.json";
    
    /**
     * 日志输出目录
     * 默认使用项目根目录下的 logs 文件夹
     */
    private String logDir = "./logs/dependency-update";
    
    /**
     * 是否强制刷新
     * false: 增量更新模式，从数据库最新版本开始更新
     * true: 存量更新模式，获取最近N个版本重新处理
     */
    private boolean forceRefresh = false;
    
    /**
     * 是否处理缺失依赖表
     * true: 优先处理缺失依赖表中的依赖
     * false: 只处理配置文件中的依赖
     */
    private boolean processMissingDependencies = true;
    
    /**
     * 每次从缺失依赖表读取的数量
     * 控制缺失依赖的批次大小
     */
    private int missingDependencyLimit = 100;
    
    /**
     * 冷启动时更新的版本数量（当数据库中没有该依赖时）
     * 在增量更新模式下，如果数据库中没有该依赖记录，会获取最近N个版本
     * 在存量更新模式下，直接获取最近N个版本
     */
    // todo 调整一下
    private int coldStartVersionCount = 1;
    
    /**
     * Runner 配置文件结构
     */
    @Data
    public static class RunnerConfig {
        /**
         * 配置版本
         */
        private String version = "1.0";
        
        /**
         * 配置描述
         */
        private String description;
        
        /**
         * 依赖组列表
         */
        private List<DependencyGroup> dependencyGroups;
    }
    
    /**
     * 依赖组配置
     */
    @Data
    public static class DependencyGroup {
        /**
         * 组名称
         */
        private String name;
        
        /**
         * 语言类型 (JAVA, JAVASCRIPT, PYTHON 等)
         */
        private String language;
        
        /**
         * 依赖类型 (MAVEN, NPM, PYPI 等)
         */
        private String dependencyType;
        
        /**
         * 描述
         */
        private String description;
        
        /**
         * 依赖列表
         */
        private List<DependencyItem> dependencies;
    }
    
    /**
     * 依赖项配置
     */
    @Data
    public static class DependencyItem {
        /**
         * 命名空间 (如 Maven 的 groupId, NPM 的 scope)
         */
        private String namespace;
        
        /**
         * 名称 (如 Maven 的 artifactId, NPM 的 package name)
         */
        private String name;
        
        /**
         * 版本（可选，为空时获取所有版本）
         * 如果指定版本，只处理该版本
         * 如果不指定，根据 forceRefresh 参数决定更新策略
         */
        private String version;
        
        /**
         * 描述
         */
        private String description;
    }
} 