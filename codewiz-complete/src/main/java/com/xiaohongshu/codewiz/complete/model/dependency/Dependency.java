package com.xiaohongshu.codewiz.complete.model.dependency;

import com.xiaohongshu.codewiz.complete.model.lang.LanguageType;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 解析后的依赖信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Dependency {

    private String namespace;
    private String name;
    private String version;
    private LanguageType language;
    private DependencyType dependencyType;

    /**
     * 原始SNAPSHOT版本号（如1.0-SNAPSHOT）
     * 当version是解析后的真实版本时，此字段存储用户输入的原始SNAPSHOT版本
     */
    private String snapshotVersion;

    /**
     * 是否为最新版本
     * 用于标记在同一个dependency的多个版本中，哪个是最新的
     */
    @Builder.Default
    private boolean isLatest = false;

    public String getDependencyKey() {
        return String.format("%s:%s:%s:%s:%s", dependencyType, language, namespace, name, version);
    }

    public String getDependencySignature() {
        return String.format("%s:%s:%s:%s", dependencyType, language, namespace, name);
    }

    /**
     * 判断是否为SNAPSHOT版本
     */
    public boolean isSnapshot() {
        if (version == null) {
            return false;
        }
        switch (language) {
            case JAVA:
                return version.endsWith("-SNAPSHOT");
            default:
                return false;
        }
    }

    /**
     * 判断是否为不指定版本的依赖
     */
    public boolean isGeneral() {
        return version == null || version.trim().isEmpty();
    }
}