package com.xiaohongshu.codewiz.complete.dto.packagecontext;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.util.Collections;
import java.util.List;

import com.xiaohongshu.codewiz.complete.dto.common.Response;

/**
 * 代码上下文查询响应DTO
 * 支持单个和批量查询结果
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PackageContextQueryResp extends Response {

    /**
     * 依赖查询结果列表
     */
    private List<DependencyContextDTO> results;

    public static PackageContextQueryResp buildSuccess(List<DependencyContextDTO> results) {
        PackageContextQueryResp response = new PackageContextQueryResp();
        response.setSuccess(true);
        response.setCode("200");
        response.setMsg("success");
        response.setResults(results);
        return response;
    }

    public static PackageContextQueryResp buildFailure(String code, String msg) {
        PackageContextQueryResp response = new PackageContextQueryResp();
        response.setSuccess(false);
        response.setCode(code);
        response.setMsg(msg);
        response.setResults(Collections.emptyList());
        return response;
    }
} 