recall支持多种策略

RagQueryCrCaseService类的recall方法，其中有

```
// 代码向量召回
        String codeSnippetFileName = RagCaseKnowledgeEnum.CODE_SNIPPET.getFileName();
        String code = context.getAnalysisQuery().getOrDefault(codeSnippetFileName, StringUtils.EMPTY);
        CompletableFuture<List<FewShotCase>> codeRecallFuture = CompletableFuture.supplyAsync(() ->
                        milvusVectorRecall(code, codeSnippetFileName, collectionName, topKey, filterFiled, scoreThreshold),
                ragAddDataExecutor);

        // 代码含义向量召回
        String codeExplainFileName = RagCaseKnowledgeEnum.CODE_EXPLAIN.getFileName();
        String explain = context.getAnalysisQuery().getOrDefault(codeExplainFileName, StringUtils.EMPTY);
        CompletableFuture<List<FewShotCase>> explainRecallFuture = CompletableFuture.supplyAsync(() ->
                        milvusVectorRecall(explain, codeExplainFileName, collectionName, topKey, filterFiled, scoreThreshold),
                ragAddDataExecutor);

        // 代码+含义混合文本向量召回
        String hybridFileName = RagCaseKnowledgeEnum.HYBRID_KNOWLEDGE.getFileName();
        String hybridKnowledge = context.getAnalysisQuery().getOrDefault(hybridFileName, StringUtils.EMPTY);
        CompletableFuture<List<FewShotCase>> hybridRecallFuture = CompletableFuture.supplyAsync(() ->
                        milvusVectorRecall(hybridKnowledge, hybridFileName, collectionName, topKey, filterFiled, scoreThreshold),
                ragAddDataExecutor);
```

这三种召回方式

我想要把这三种召回方式封装成策略设计模式，并且支持多种策略，例如：

1. 代码向量召回
2. 代码含义向量召回
3. 代码+含义+混合文本向量召回
   等等
   所以每一种召回都是一个策略，并且策略之间还可以组合
   所有的策略都实现接口 `IRagRecallStrategy`，并且实现类都放在 `com.xiaohongshu.codewiz.core.service.rag.strategy` 包下

## 实现过程

1. 创建抽象类 `AbstractRagRecallStrategy`，实现接口 `IRagRecallStrategy`，定义公共方法
2. 创建具体策略类，继承抽象类 `AbstractRagRecallStrategy`，实现具体策略
3. 在 `RagQueryCrCaseService` 类中，使用策略模式，根据不同的策略进行召回
4. 创建枚举类 `RagRecallStrategyEnum`，枚举值为具体策略类
5. 策略可以实现多种组合，例如：
    - 代码向量召回 + 代码含义向量召回
    - 代码向量召回 + 代码含义向量召回 + 混合文本向量召回
