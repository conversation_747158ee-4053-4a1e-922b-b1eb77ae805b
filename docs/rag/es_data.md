## 需求说明

### 修改/rag/data/add接口

1. 增加一个scene场景，"负向评论召回",scene = 6
2. 存入es的数据格式
3. 增加es的链接方式
4. 增加es创建索引的方式
5. 索引的中的核心字段是cmt_rule，会用来文本检索，需要设置text类型，其他字段设置为keyword类型
6. 把数据插入到es中
7. 整个链路要跟之前保持一致

## es 配置

用户名：elastic
密码：
prod环境:G7m$Xy2@qL!9
dev环境:7x#L9@qZ2!

地址
dev环境：https://es-0v9e5d9t.public.tencentelasticsearch.com:9200
prod环境：http://10.13.107.172:9200

版本：8.13

配置的一些代码

```
<dependency>
                <groupId>co.elastic.clients</groupId>
                <artifactId>elasticsearch-java</artifactId>
                <version>8.13.3</version>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch.client</groupId>
                <artifactId>elasticsearch-rest-client</artifactId>
                <version>8.13.3</version>
            </dependency>
```

```
package com.xiaohongshu.codewiz.core.config;

import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.elasticsearch.client.RestClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.json.jackson.JacksonJsonpMapper;
import co.elastic.clients.transport.ElasticsearchTransport;
import co.elastic.clients.transport.rest_client.RestClientTransport;

/**
 * <AUTHOR>
 * Created on 2025/3/21
 */
@Configuration
public class ElasticSearchConfiguration {
    @Value("${elasticsearch.url}")
    private String url;
    @Value("${elasticsearch.username}")
    private String username;
    @Value("${elasticsearch.password}")
    private String password;

    @Bean
    public ElasticsearchClient elasticsearchClient() {
        // 1. 创建凭证提供器
        CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
        credentialsProvider.setCredentials(AuthScope.ANY, new UsernamePasswordCredentials(username, password));

        // 2. 创建 RestClient
        RestClient restClient = RestClient.builder(HttpHost.create(url))
                .setHttpClientConfigCallback(httpClientBuilder -> httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider))
                .build();

        // 3. 创建 Transport 和 Client
        ElasticsearchTransport transport = new RestClientTransport(restClient, new JacksonJsonpMapper());
        return new ElasticsearchClient(transport);
    }
}

```

## 数据格式

1. cmt_rule，string
2. create_at，long
3. language，string
4. meta，object
    - tag，string
    - cmt_id，string
    - mr_id，string
    - repo_id，string