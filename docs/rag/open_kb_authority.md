# CodeWiz RAG 系统权限设计

## 概述

本文档描述了CodeWiz RAG系统的权限设计，主要包括业务线Token管理和权限控制策略。

## 权限模型

### 业务线（Biz）管理

- 每个业务线（biz）拥有唯一标识biz_id
- 每个业务线可以分配多个访问token
- Token分为两类：管理员token和普通用户token
- 管理员token可以创建、修改和删除知识库
- 普通用户token只能查询知识库

### 集合（Collection）权限

- 根据文档设计，每个biz分配1个公共知识库和10个私有知识库collection
- 每个私有知识库分配10个collection，通过user_id+project_id路由
- 权限控制精确到collection级别

### 操作权限

1. **读取权限**：允许检索知识库内容
2. **写入权限**：允许添加、更新文档
3. **删除权限**：允许删除文档和知识库
4. **管理权限**：允许创建知识库和管理权限

## 鉴权流程

1. 请求携带biz_id和token
2. 验证token有效性
3. 检查token对应的biz_id是否匹配
4. 根据token类型和请求操作类型进行权限验证
5. 对于私有知识库操作，额外验证user_id

## 数据库设计

### MySQL表设计

```sql
-- 业务线表
CREATE TABLE `biz` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `biz_id` VARCHAR(64) NOT NULL COMMENT '业务线ID',
  `biz_name` VARCHAR(255) NOT NULL COMMENT '业务线名称',
  `description` TEXT COMMENT '业务线描述',
  `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_biz_id` (`biz_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='业务线表';

-- 业务线Token表
CREATE TABLE `biz_token` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `biz_id` VARCHAR(64) NOT NULL COMMENT '业务线ID',
  `token` VARCHAR(128) NOT NULL COMMENT '访问Token',
  `token_name` VARCHAR(255) NOT NULL COMMENT 'Token名称',
  `token_type` TINYINT NOT NULL COMMENT 'Token类型：1-管理员，2-普通用户',
  `expires_at` TIMESTAMP NULL COMMENT '过期时间，NULL表示永不过期',
  `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_token` (`token`),
  KEY `idx_biz_id` (`biz_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='业务线Token表';

-- Collection表
CREATE TABLE `biz_collection` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `biz_id` VARCHAR(64) NOT NULL COMMENT '业务线ID',
  `collection_name` VARCHAR(128) NOT NULL COMMENT 'Milvus集合名称',
  `collection_type` TINYINT NOT NULL COMMENT '集合类型：1-公共知识库，2-私有知识库',
  `description` VARCHAR(255) DEFAULT NULL COMMENT '集合描述',
  `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_biz_collection` (`biz_id`, `collection_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='业务线Collection表';

-- Token权限表
CREATE TABLE `biz_token_authority` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `token_id` BIGINT NOT NULL COMMENT 'Token ID',
  `collection_id` BIGINT NOT NULL COMMENT 'Collection ID',
  `can_read` TINYINT NOT NULL DEFAULT 1 COMMENT '读权限：1-有，0-无',
  `can_write` TINYINT NOT NULL DEFAULT 0 COMMENT '写权限：1-有，0-无',
  `can_delete` TINYINT NOT NULL DEFAULT 0 COMMENT '删除权限：1-有，0-无',
  `can_manage` TINYINT NOT NULL DEFAULT 0 COMMENT '管理权限：1-有，0-无',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_token_collection` (`token_id`, `collection_id`),
  KEY `idx_collection_id` (`collection_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Token权限表';

-- 知识库表
CREATE TABLE `knowledge_base` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `kb_id` VARCHAR(64) NOT NULL COMMENT '知识库ID',
  `biz_id` VARCHAR(64) NOT NULL COMMENT '业务线ID',
  `collection_id` BIGINT NOT NULL COMMENT 'Collection ID',
  `kb_name` VARCHAR(255) NOT NULL COMMENT '知识库名称',
  `kb_type` TINYINT NOT NULL COMMENT '知识库类型：1-公共，2-私有',
  `user_id` VARCHAR(64) DEFAULT NULL COMMENT '用户ID，私有知识库必填',
  `project_id` VARCHAR(64) DEFAULT NULL COMMENT '项目ID，私有知识库必填',
  `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_kb_id` (`kb_id`),
  KEY `idx_biz_id` (`biz_id`),
  KEY `idx_user_project` (`user_id`, `project_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='知识库表';

-- 操作日志表
CREATE TABLE `operation_log` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `biz_id` VARCHAR(64) NOT NULL COMMENT '业务线ID',
  `token` VARCHAR(128) NOT NULL COMMENT '使用的Token',
  `user_id` VARCHAR(64) DEFAULT NULL COMMENT '用户ID',
  `operation_type` VARCHAR(32) NOT NULL COMMENT '操作类型',
  `resource_type` VARCHAR(32) NOT NULL COMMENT '资源类型',
  `resource_id` VARCHAR(64) NOT NULL COMMENT '资源ID',
  `request_data` TEXT COMMENT '请求数据',
  `status` TINYINT NOT NULL COMMENT '状态：1-成功，0-失败',
  `error_message` TEXT COMMENT '错误信息',
  `ip_address` VARCHAR(64) DEFAULT NULL COMMENT 'IP地址',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_biz_id` (`biz_id`),
  KEY `idx_token` (`token`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='操作日志表';
```

## 权限控制策略

### 1. Token生成与管理

- 采用UUID+密钥生成机制创建不可预测的token
- 支持token的定期轮换机制
- 支持token的紧急吊销

### 2. 公共知识库权限

- 默认所有token均可读取公共知识库
- 只有管理员token可以向公共知识库写入和删除内容
- 公共知识库的创建需要超级管理员权限

### 3. 私有知识库权限

- 普通token只能读取指定user_id的私有知识库
- 管理员token可以读取、写入、删除所有私有知识库
- 私有知识库的所有操作均需验证user_id+project_id

### 4. 操作审计

- 所有关键操作均记录到操作日志表
- 记录操作类型、资源、请求数据、状态等信息
- 支持按业务线、token、时间等维度查询审计日志

## 实现注意事项

1. Token传输采用HTTPS加密通道
2. 敏感操作增加额外的身份验证
3. 对失败的鉴权请求进行频率限制
4. 定期审计权限配置，确保最小权限原则
5. 为避免权限泄露，不同环境（开发、测试、生产）使用不同的token
