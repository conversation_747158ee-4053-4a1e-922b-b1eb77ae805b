## 公共知识库

```python
from pymilvus import Collection, DataType, FieldSchema, CollectionSchema

# 公共知识库集合schema
public_fields = [
    FieldSchema(name="chunk_id", dtype=DataType.VARCHAR, max_length=1024, is_primary=True),
    FieldSchema(name="kb_id", dtype=DataType.VARCHAR, max_length=1024),
    FieldSchema(name="doc_id", dtype=DataType.VARCHAR, max_length=1024),
    FieldSchema(name="doc_type", dtype=DataType.VARCHAR, max_length=1024),
    FieldSchema(name="content", dtype=DataType.VARCHAR, max_length=65535),
    FieldSchema(name="file_path", dtype=DataType.VARCHAR, max_length=1024),
    FieldSchema(name="file_type", dtype=DataType.VARCHAR, max_length=1024),
    FieldSchema(name="index", dtype=DataType.INT64),
    FieldSchema(name="tag", dtype=DataType.VARCHAR, max_length=1024),
    FieldSchema(name="extra", dtype=DataType.VARCHAR, max_length=65535),
    FieldSchema(name="created_at", dtype=DataType.INT64),  # 时间戳
    FieldSchema(name="updated_at", dtype=DataType.INT64),  # 时间戳
    FieldSchema(name="embedding", dtype=DataType.FLOAT_VECTOR, dim=1024)
]

public_schema = CollectionSchema(fields=public_fields, description="公共知识库")
public_collection = Collection(name="biz_id_public", schema=public_schema)

# 创建索引
public_collection.create_index(field_name="embedding", index_params={
    "index_type": "HNSW",
    "metric_type": "COSINE",
    "params": {"M": 16, "efConstruction": 500}
})

# 为标量字段创建倒排索引
public_collection.create_index(field_name="doc_id", index_name="doc_id_index", index_params={"index_type": "INVERTED"})
public_collection.create_index(field_name="chunk_id", index_name="chunk_id_index", index_params={"index_type": "INVERTED"})

```

## 私有知识库

```python

from pymilvus import Collection, DataType, FieldSchema, CollectionSchema

# 个人知识库集合schema
private_fields = [
    FieldSchema(name="chunk_id", dtype=DataType.VARCHAR, max_length=1024, is_primary=True),
    FieldSchema(name="user_project_id", dtype=DataType.VARCHAR, max_length=1024),
    FieldSchema(name="kb_id", dtype=DataType.VARCHAR, max_length=1024),
    FieldSchema(name="doc_id", dtype=DataType.VARCHAR, max_length=1024),
    FieldSchema(name="doc_type", dtype=DataType.VARCHAR, max_length=1024),
    FieldSchema(name="content", dtype=DataType.VARCHAR, max_length=65535),
    FieldSchema(name="file_path", dtype=DataType.VARCHAR, max_length=1024),
    FieldSchema(name="file_type", dtype=DataType.VARCHAR, max_length=1024),
    FieldSchema(name="index", dtype=DataType.INT64),
    FieldSchema(name="tag", dtype=DataType.VARCHAR, max_length=1024),
    FieldSchema(name="extra", dtype=DataType.VARCHAR, max_length=65535),
    FieldSchema(name="created_at", dtype=DataType.INT64),  # 时间戳
    FieldSchema(name="updated_at", dtype=DataType.INT64),  # 时间戳
    FieldSchema(name="embedding", dtype=DataType.FLOAT_VECTOR, dim=1024)
]

private_schema = CollectionSchema(fields=private_fields, description="个人知识库")
private_collection = Collection(name="biz_id_private_0", schema=private_schema)

# 创建向量索引
private_collection.create_index(field_name="embedding", index_params={
    "index_type": "HNSW",
    "metric_type": "COSINE",
    "params": {"M": 16, "efConstruction": 500}
})

# 为标量字段创建索引
private_collection.create_index(field_name="user_project_id", index_name="user_project_id_index", index_params={"index_type": "INVERTED"})
private_collection.create_index(field_name="doc_id", index_name="doc_id_index", index_params={"index_type": "INVERTED"})
private_collection.create_index(field_name="chunk_id", index_name="chunk_id_index", index_params={"index_type": "INVERTED"})

```


