# CodeWiz RAG 系统API文档

## 概述

CodeWiz RAG系统提供了代码仓库索引构建和查询能力，支持公共文档和个人开发仓库的存储、检索和管理。

## 数据模型

- 公共文档：单独存储在一个collection中
- 个人开发仓库：所有用户共用一个collection，通过标量过滤进行隔离

## 接口列表

### 1. 知识库创建（空知识库）

**接口**：`POST /rag/data/open/knowledgebase/create`

**请求参数**：

```json
{
  "biz_id": "string",          // 业务ID
  "user_id": "string",         // 用户ID
  "project_id": "string",      // 项目ID
  "kb_type": "enum"           // 知识库类型: PUBLIC/PRIVATE
}
```

**响应**：

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "kb_id": "string"          // 知识库ID
  }
}
```

**实现过程**：

1. 验证用户权限
2. 如果是public知识库
    1. 知识库命名方式，biz_id_public,其中biz_id为业务线ID
    2. 判断知识库是否存在，如果存在直接返回，否则在milvus中创建collection，名称为biz_id_public
    3. 公共知识库的schema

```
# 公共知识库集合schema
public_fields = [
    FieldSchema(name="chunk_id", dtype=DataType.VARCHAR, max_length=1024, is_primary=True),
    FieldSchema(name="kb_id", dtype=DataType.VARCHAR, max_length=1024),
    FieldSchema(name="doc_id", dtype=DataType.VARCHAR, max_length=1024),
    FieldSchema(name="content", dtype=DataType.VARCHAR, max_length=65535),
    FieldSchema(name="file_path", dtype=DataType.VARCHAR, max_length=1024),
    FieldSchema(name="file_type", dtype=DataType.VARCHAR, max_length=1024),
    FieldSchema(name="index", dtype=DataType.INT64),
    FieldSchema(name="tag", dtype=DataType.VARCHAR, max_length=1024),
    FieldSchema(name="extra", dtype=DataType.VARCHAR, max_length=65535),
    FieldSchema(name="created_at", dtype=DataType.INT64),  # 时间戳
    FieldSchema(name="updated_at", dtype=DataType.INT64),  # 时间戳
    FieldSchema(name="embedding", dtype=DataType.FLOAT_VECTOR, dim=1024)
]

public_schema = CollectionSchema(fields=public_fields, description="公共知识库")
public_collection = Collection(name="biz_id_public", schema=public_schema)

# 创建索引
public_collection.create_index(field_name="embedding", index_params={
    "index_type": "HNSW",
    "metric_type": "COSINE",
    "params": {"M": 16, "efConstruction": 500}
})

# 为标量字段创建倒排索引
public_collection.create_index(field_name="doc_id", index_name="doc_id_index", index_params={"index_type": "INVERTED"})
public_collection.create_index(field_name="chunk_id", index_name="chunk_id_index", index_params={"index_type": "INVERTED"})
```

3. 如果是private知识库
    1. 知识库命令方式，biz_id_private_0,1,2,,9 其中biz_id为业务线ID，0,1,2为私有知识库的序号，知识库的序号根据user_id+project_id进行hash得到，默认私人知识库最多创建10个
    2. 判断知识库是否存在，如果存在直接返回，否则在milvus中创建collection，名称为biz_id_private_0,1,2,,9
    3. 私有知识库的schema

```
# 个人知识库集合schema
private_fields = [
    FieldSchema(name="chunk_id", dtype=DataType.VARCHAR, max_length=1024, is_primary=True),
    FieldSchema(name="user_project_id", dtype=DataType.VARCHAR, max_length=1024),
    FieldSchema(name="kb_id", dtype=DataType.VARCHAR, max_length=1024),
    FieldSchema(name="doc_id", dtype=DataType.VARCHAR, max_length=1024),
    FieldSchema(name="content", dtype=DataType.VARCHAR, max_length=65535),
    FieldSchema(name="file_path", dtype=DataType.VARCHAR, max_length=1024),
    FieldSchema(name="file_type", dtype=DataType.VARCHAR, max_length=1024),
    FieldSchema(name="index", dtype=DataType.INT64),
    FieldSchema(name="tag", dtype=DataType.VARCHAR, max_length=1024),
    FieldSchema(name="extra", dtype=DataType.VARCHAR, max_length=65535),
    FieldSchema(name="created_at", dtype=DataType.INT64),  # 时间戳
    FieldSchema(name="updated_at", dtype=DataType.INT64),  # 时间戳
    FieldSchema(name="embedding", dtype=DataType.FLOAT_VECTOR, dim=1024)
]

private_schema = CollectionSchema(fields=private_fields, description="个人知识库")
private_collection = Collection(name="biz_id_private_0", schema=private_schema)

# 创建向量索引
private_collection.create_index(field_name="embedding", index_params={
    "index_type": "HNSW",
    "metric_type": "COSINE",
    "params": {"M": 16, "efConstruction": 500}
})

# 为标量字段创建索引
private_collection.create_index(field_name="user_project_id", index_name="user_project_id_index", index_params={"index_type": "INVERTED"})
private_collection.create_index(field_name="doc_id", index_name="doc_id_index", index_params={"index_type": "INVERTED"})
private_collection.create_index(field_name="chunk_id", index_name="chunk_id_index", index_params={"index_type": "INVERTED"})
```

### 2. 知识库删除

**接口**：`POST /rag/data/open/knowledgebase/delete`

**请求参数**：

```json
{
  "biz_id": "string",          // 业务ID
  "user_id": "string",         // 用户ID
  "project_id": "string",      // 项目ID
  "kb_type": "enum",           // 知识库类型: PUBLIC/PRIVATE
  "kb_id": "string",           // 知识库ID
}
```

**响应**：

```json
{
  "code": 0,
  "message": "success"
}
```

**实现过程**：

1. 验证用户权限
2. 从Milvus中删除该知识库相关的所有向量
    - 对于公共知识库，不支持删除知识库
    - 对于私有知识库，先根据user_id+project_id找到指定的私有知识库，然后删除指定user_id+project_id的所有记录

### 3. 知识库文档上传（新增）

**接口**：`POST /rag/data/open/document/upload`

**请求参数**：

```json
{
  "biz_id": "string",          // 业务ID
  "user_id": "string",         // 用户ID
  "project_id": "string",      // 项目ID
  "kb_id": "string",           // 知识库ID
  "kb_type": "enum",           // 知识库类型: PUBLIC/PRIVATE
  "file_path": "string",       // 文件路径
  "file_type": "string",       // 文件类型 (markdown/js/css/xhsml等)
  "content": "string"         // 文件内容
}
```

**响应**：

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "doc_id": "string",        // 文档ID
    "chunk_ids": ["string"]    // 分块ID列表
  }
}
```

**实现过程**：

1. 验证用户权限
2. 根据文件类型选择合适的切分策略进行文档分块，使用策略设计模式，根据文件类型选择合适的切分策略
    1. 基础分块策略是按照行数区分，200行一个chunk
    2. 对于markdown文件，使用markdown的语法进行分块，每个标题是一个chunk
    3. 对于代码文件，使用代码的语法进行分块，每个函数是一个chunk
3. 为每个分块生成向量嵌入，复用目前已有的获取向量的接口
4. 将向量及元数据插入Milvus，相关的向量和标量根据collection的schema进行确定
    - 公共知识库存入公共collection
    - 私有知识库存入私有collection
5. 一个文件的插入过程为
    1. 首先生成doc_id，doc_id为uuid
    2. 生成文件本身记录，doc_id和chunk_id相同，均为doc_id
    3. 根据文件类型调用指定的分块策略进行分块，生成多个chunk，每一个chunk使用uuid作为chunk_id
    4. 生成多个chunk的记录，每个chunk的doc_id为文件的doc_id，chunk_id为uuid，记录chunk的顺序，chunk的顺序从0开始，记录为index属性
    5. 将文件本身记录和chunk记录写入milvus
    6. 假设一个文件分为10个chunk，那么一个文件的最终会有11条记录，分别是1个文件本身记录和10个chunk记录

### 4. 知识库文档上传（更新）

**接口**：`POST /rag/data/open/document/update`

**请求参数**：

```json
{
  "biz_id": "string",          // 业务ID
  "user_id": "string",         // 用户ID
  "project_id": "string",      // 项目ID
  "kb_id": "string",           // 知识库ID
  "kb_type": "enum",           // 知识库类型: PUBLIC/PRIVATE
  "doc_id": "string",          // 文档ID
  "chunk_id": "string",         // 分块ID
  "file_path": "string",       // 文件路径
  "content": "string",         // 更新后的文件内容
  "file_type": "string"        // 文件类型 (markdown/js/css/xhsml等)
}
```

**响应**：

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "doc_id": "string",        // 文档ID
    "chunk_ids": ["string"]    // 更新后的分块ID列表
  }
}
```

**实现过程**：

1. 验证用户权限
2. 根据kd_id区分知识库
3. 如果chunk_id为空，则认为更新的是文件本身，否则认为更新的是指定chunk
4. 如果更新的是文件本身，此时chunk id为空，先把文件及其下面的chunk删除，然后重新插入文件本身和chunk，插入逻辑跟upload一致
5. 如果更新的是指定chunk，此时chunk id不为空，则更新指定chunk的记录

### 5. 知识库文档删除

**接口**：`POST /rag/data/open/document/delete`

**请求参数**：

```json
{
  "biz_id": "string",          // 业务ID
  "user_id": "string",         // 用户ID
  "project_id": "string",      // 项目ID
  "kb_id": "string",           // 知识库ID
  "kb_type": "enum",           // 知识库类型: PUBLIC/PRIVATE
  "doc_id": "string",          // 文档ID
  "chunk_id": "string"         // 分块ID
}
```

**响应**：

```json
{
  "code": 0,
  "message": "success"
}
```

**实现过程**：

1. 验证用户权限
2. 根据kd_id区分知识库
3. 如果chunk_id为空，则认为删除的是文件本身，否则认为删除的是指定chunk
4. 如果删除的是文件本身，此时chunk id为空，则根据doc_id删除文件及其chunk记录
5. 如果删除的是指定chunk，此时chunk id不为空，则根据chunk_id删除指定chunk的记录

### 6. 知识库获取文档列表

**接口**：`GET /rag/data/open/document/list`

**请求参数 （URL param参数，如 ?bizId=xx&userId=tt）**：

```
"biz_id": "string",          // 业务ID
"user_id": "string",         // 用户ID
"project_id": "string",      // 项目ID
"kb_id": "string",           // 知识库ID
"kb_type": "enum",           // 知识库类型: PUBLIC/PRIVATE
"page": "integer",           // 页码（从1开始）
"page_size": "integer"       // 每页数量,最多500
```

**响应**：

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "total": "integer",        // 总文档数
    "documents": [
      {
        "doc_id": "string",    // 文档ID
        "file_path": "string", // 文件路径
        "file_type": "string", // 文件类型
        "created_at": "string", // 创建时间
        "updated_at": "string"  // 更新时间
      }
    ]
  }
}
```

**实现过程**：

1. 验证用户权限
2. 根据kd_id区分知识库
3. 公共知识库：从公共collection中查询
    1. 分页查询
    2. 返回所有doc_type是doc的文档
4. 私有知识库：从私有collection中查询
    1. 根据user_project_id进行分页查询
    2. 返回所有doc_type是doc的文档
5. 分页返回结果

### 7. 查询文档的所有分段

**接口**：`GET /rag/data/open/document/chunks`

**请求参数 （URL param参数，如 ?bizId=xx&userId=tt）**：

```
"biz_id": "string",          // 业务ID
"user_id": "string",         // 用户ID
"project_id": "string",      // 项目ID
"kb_id": "string",           // 知识库ID
"kb_type": "enum",           // 知识库类型: PUBLIC/PRIVATE
"doc_id": "string"           // 文档ID
```

**响应**：

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "chunks": [
      {
        "chunk_id": "string",  // 分块ID
        "content": "string",   // 分块内容
        "index": "integer"     // 分块序号
      }
    ]
  }
}
```

**实现过程**：

1. 验证用户权限
2. 根据doc_id查询所有的doc_type是chunk的记录

### 8. 知识库检索

**接口**：`POST /rag/data/open/search`

**请求参数**：

```json
{
  "biz_id": "string",          // 业务ID
  "user_id": "string",         // 用户ID
  // "project_id": "string",  // 私有知识库专用。废弃，原用于私有知识库的分片计算
  "project_ids": [            // 项目ID列表，查询公共和私有知识库时使用
     "string1", "string2"
  ],
  "search_type": "enum",      // 1-公共知识库，2-私有知识库，3-所有知识库
  "query": "string",           // 查询文本
  "top_k": "integer",          // 返回结果数量
  "filter": {// 过滤条件, Map类型
     "chunk_id": "CHUNK_xxx",
     "doc_id": "DOC_xx",
     "index": 1,
     "file_path": "xxx/x/x",
     "file_type": "js",
     "tag": "tag1",
     "extra": "extra1"
  }                 
}
```

**响应**：

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "results": [
      {
        "kb_id": "string", // 知识库ID
        "doc_id": "string", // 文档ID
        "chunk_id": "string", // 分块ID
        "content": "string", // 分块内容
        "file_path": "string", // 文件路径
        "file_type": "string", // 文件类型
        "score": "float" // 相似度分数
      }
    ]
  }
}
```

**实现过程**：

1. 验证用户权限
2. 对查询文本生成向量嵌入
3. 分两路检索：
    - 公共知识库：在公共collection中检索（from_public为true）
    - 私有知识库：在私有collection中使用user_project_id作为标量过滤条件进行检索
4. 合并两路结果，按分数排序
5. 返回Top-K结果

## 实现注意事项

### 分片设计

随着个人开发仓库数据增多，可采用以下分片策略：
按照biz创建collection，每个biz分配1个公共知识库和10个私有知识库，每个私有知识库分配10个collection，根据user_id+project_id作哈希路由

1. **按用户分片**：根据user_id取模，将不同用户的数据分散到不同的collection
2. **时间衰减策略**：对长期不活跃的项目数据进行归档处理

### 索引构建

1. **初始化构建**：批量处理所有文件，生成向量并存入Milvus
2. **增量更新**：根据文件变更记录，只处理新增、修改和删除的文件

### 向量模型选择

1. 对于代码文件：使用代码专用的embedding模型
2. 对于markdown等文档：使用通用文本embedding模型

### 安全隔离

1. 始终验证用户对知识库的访问权限
2. 私有知识库检索时必须验证user_id+workspace_id+project_id
3. 公共知识库只允许读取，不允许普通用户修改
