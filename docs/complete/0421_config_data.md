## 配置快照表

```
CREATE TABLE `config_snapshot` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `config_name` varchar(255) NOT NULL COMMENT '配置名称',
  `config_type` int(11) NOT NULL DEFAULT '0' COMMENT '配置类型 1-plugin 2-lsp',
  `config_desc` varchar(255) DEFAULT NULL COMMENT '配置描述',
  `config_value` text COMMENT '配置信息',
  `config_version` varchar(255) DEFAULT NULL COMMENT '配置版本',
  `is_lasted` tinyint(1) DEFAULT '0' COMMENT '是否最新',
  `create_at` bigint(20) DEFAULT NULL COMMENT '创建时间',
  `update_at` bigint(20) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_config_name` (`config_name`),
  KEY `idx_config_type` (`config_type`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='配置快照表';
```

## 个人配置表

```
CREATE TABLE `person_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `config_snapshot_id` bigint(20) NOT NULL COMMENT '配置快照id',
  `config_value` text COMMENT '配置值',
  `user_email` varchar(255) NOT NULL COMMENT '用户邮箱',
  `create_at` bigint(20) DEFAULT NULL COMMENT '创建时间',
  `update_at` bigint(20) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_snapshot_user_email` (`config_snapshot_id`,`user_email`),
  KEY `idx_user_email` (`user_email`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='个人配置表';
```

## data insert sql

### config_snapshot

[{
"config_name" : "ide_plugin_config",
"config_type" : 1,
"config_desc" : "ide插件配置项",
"config_value" :{
"debounce": 75,
"auto_update": true
},
"config_version" : "1.0.0",
"is_lasted" : 1,
"create_at" : 1713542400000,
"update_at" : 1713542400000
},
{
"config_name" : "ide_lsp_config",
"config_type" : 2,
"config_desc" : "ide lsp配置项",
"config_value" :{
"completion_stream": false,
"max_token": 96,
"top_p": 0.9,
"temperature": 0.1,
"complele_server_type": 0
},
"config_version" : "1.0.0",
"is_lasted" : 1,
"create_at" : 1713542400000,
"update_at" : 1713542400000
}
]
// 把上述json转为sql插入语句

INSERT INTO `config_snapshot` (`config_name`, `config_type`, `config_desc`, `config_value`, `config_version`, `is_lasted`, `create_at`, `update_at`)
VALUES ('ide_plugin_config', 1, 'ide插件配置项', '{"debounce": 75, "auto_update": true}', '1.0.0', 1, 1713542400000, 1713542400000);

INSERT INTO `config_snapshot` (`config_name`, `config_type`, `config_desc`, `config_value`, `config_version`, `is_lasted`, `create_at`, `update_at`)
VALUES ('ide_lsp_config', 2, 'ide lsp配置项', '{"completion_stream": false, "max_token": 96, "top_p": 0.9, "temperature": 0.1, "complele_server_type":
0}', '1.0.0', 1, 1713542400000, 1713542400000);

### person_config

[{
"config_snapshot_id" : 1,
"config_value" :{
"debounce": 75,
"auto_update": false,
},
"user_email" : "<EMAIL>",
"create_at" : 1713542400000,
"update_at" : 1713542400000
},
{
"config_snapshot_id" : 2,
"config_value" :{
"completion_stream": false,
"max_token": 96,
"top_p": 0.9,
"temperature": 0.1,
"complele_server_type": 1
},
"user_email" : "<EMAIL>",
"create_at" : 1713542400000,
"update_at" : 1713542400000
}
]
// 把上述json转为sql插入语句

INSERT INTO `person_config` (`config_snapshot_id`, `config_value`, `user_email`, `create_at`, `update_at`) VALUES (1, '{"debounce": 75, "auto_update":
false}', '<EMAIL>', 1713542400000, 1713542400000);

INSERT INTO `person_config` (`config_snapshot_id`, `config_value`, `user_email`, `create_at`, `update_at`) VALUES (2, '{"completion_stream": false, "
max_token": 96, "top_p": 0.9, "temperature": 0.1, "complele_server_type": 1}', '<EMAIL>', 1713542400000, 1713542400000);



