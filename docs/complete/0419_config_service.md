## 任务描述

IDEConfigController,IDEConfigService我已经创建完成，请参考mysql表结构和接口定义，根据接口实现逻辑生成代码
仿照现有springboot服务定义

- 完善 controller,server,dao,mapper,entity
- 创建config_snapshot和person_config表的entity，mapper，dao
- 参考项目中已有的实现，尤其是接口调用，声明，数据集访问框架等内容，在合适的目录完善或者创建上述文件

## mysql表结构

### config_snapshot

```
CREATE TABLE `config_snapshot` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键',
  `config_name` VARCHAR(1024) NOT NULL COMMENT '配置名称',
  `config_type` INT NOT NULL DEFAULT 0 COMMENT '配置类型 1-plugin 2-lsp',
  `config_desc` VARCHAR(1024) COMMENT '配置描述',
  `config_value` TEXT COMMENT '配置信息',
  `config_version` VARCHAR(1024) COMMENT '配置版本',
  `is_lasted` TINYINT(1) DEFAULT 0 COMMENT '是否最新',
  `create_at` BIGINT COMMENT '创建时间',
  `update_at` BIGINT COMMENT '更新时间',
  PRIMARY KEY (`id`),
  INDEX `idx_config_name` (`config_name`),
  INDEX `idx_config_type` (`config_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='配置快照表';
```

### person_config

```
CREATE TABLE `person_config` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键',
  `config_snapshot_id` BIGINT NOT NULL COMMENT '配置快照id',
  `config_value` TEXT COMMENT '配置值',
  `user_email` VARCHAR(255) NOT NULL COMMENT '用户邮箱',
  `create_at` BIGINT COMMENT '创建时间',
  `update_at` BIGINT COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_snapshot_user_email` (`config_snapshot_id`, `user_email`),
  INDEX `idx_user_email` (`user_email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='个人配置表'; 
```

## 配置接口

### 获取配置  GET /config/query

#### request

```json
{
    "userEmail": "",
    "pluginVersion": "",
    "type": 1
}
```

#### response

```json
{
    "code": 0,
    "message": "success",
    "data": {
        "userEmail": "",
        "pluginVersion": "",
        "configs": [
            {
                "configName": "",
                "configType": 1,
                "configDesc": "",
                "configVersion": "",
                "configValue": {},
                "isLasted": true,
                "createAt": 1713542400000,
                "updateAt": 1713542400000
            }
        ]
    }
}
```

#### 实现逻辑

1. 根据插件版本和type去查询配置快照内容，如果类型不存在则返回当前版本的全部配置信息
2. 根据userEmail和configSnapshotId去个人配置表查询配置，如果查询到则覆盖对应配置快照id对应的配置
3. 返回配置信息

### 更新配置 POST /config/update

#### request

```json
{
    "userEmail": "",
    "pluginVersion": "",
    "configs": [
        {
            "configName": "",
            "configType": 1,
            "configDesc": "",
            "configVersion": "",
            "configValue": {},
            "isLasted": true,
            "createAt": 1713542400000,
            "updateAt": 1713542400000
        }
    ]
}
```

#### response

```json
{
    "code": 0,
    "message": "success",
    "data": null
}
```

#### 实现逻辑

1. 如果用户邮箱为空则返回，只支持更新用户配置
2. 根据pluginVersion和type去查询配置快照内容，如果查不到则返回
3. 查询到内容后，把参数中的value等信息组装为实体，根据userEmail和configSnapshotId去个人配置表，存在则更新，不存在则插入
4. 返回成功
