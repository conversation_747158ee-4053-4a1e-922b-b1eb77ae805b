## 任务
在codewiz-complete模块中实现一个接口，接口定义如下提供，提供feedback功能，用户可以提交反馈。
1. 实现参考接口/config/update，有controller，service，dao，entity，mapper，service,do,dto等结构定义，并且所存放位置也参考接口/config/update
2. 实现逻辑之后创建单测用来验证功能
3. 对象存储s3实现参考CompleteS3Client和GraphNodeS3Client，提供上传和下载两个能力
4. 在必要位置提供注释和日志，方便后续维护
5. 代码风格参考接口/config/update


## 字段定义
| 字段名称 | 字段code | 字段类型 |
| -------- | -------- | -------- |
| 反馈ID | feedbackId | String |
| 反馈 | feedback | object |
| 反馈描述 | feetback.description | string |
| 日志内容 | feedback.pluginLogFile | string |
| 用户邮箱 | feedback.userEmail | String |
| 编辑器信息 | feedback.editor | object |

## 接口定义

### 接口
/complete/issue/report/v1 POST

### 请求入参
{
    "feedbackId": "1234567890",
    "feedback": {
        "description": "这是一个反馈",
        "pluginLogFile": "这是一个日志内容",
        "userEmail": "这是一个用户邮箱",
        "editor": "这是一个编辑器信息"
    }
}

### 返回结果
{
    "code": 0,
    "message": "success",
    "data": null
}

### 实现逻辑
1. 用户提交反馈，将反馈信息保存到数据库中
2. 将pluginLogFile保存到对象存储中,设置key，把key存储在数据库中
3. 把feedback的所有信息保存在extra字段中，extra字段为json字符串，注意pluginLogFile需要替换为s3的key
4. 把反馈信息保存到数据库中
5. 返回成功



## 数据库表结构
CREATE TABLE `feedback` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `feedback_id` varchar(255) NOT NULL DEFAULT '' COMMENT '反馈ID',
    `feedback_desc` varchar(255) NOT NULL DEFAULT '' COMMENT '反馈描述',
    `feedback_log_file` varchar(255) NOT NULL DEFAULT '' COMMENT '日志内容, 对象存储key',
    `feedback_user_email` varchar(255) NOT NULL DEFAULT '' COMMENT '用户邮箱',
    `feedback_editor` varchar(255) NOT NULL DEFAULT '' COMMENT '编辑器信息',
    `feedback_extra` varchar(255) NOT NULL DEFAULT '' COMMENT '反馈扩展信息',
    `create_at` bigint(20) NOT NULL DEFAULT 0 COMMENT '创建时间',
    PRIMARY KEY (`id`),
    INDEX `idx_feedback_id` (`feedback_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;





